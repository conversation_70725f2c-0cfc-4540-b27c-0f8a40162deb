
import { Component, OnInit, Output } from '@angular/core';

import { Globals } from '@core/global';
import { EventEmitter } from "@angular/core";
import { InternationalService } from '@service/international.service';
import { SelectItem } from 'primeng/api';

@Component({
    // tslint:disable-next-line:component-selector
    selector: 'installment-search-form',
    templateUrl: './search.component.html',
    styleUrls: ['./search.component.css']
})


export class InstallmentTransactionApprovalSearchForm implements OnInit{

    public merchant_id: string;
    public from_date: string;
    public to_date: string;
    public transaction_id: string;
    public order_reference: string;
    public card_number: string;
    public authorisation_code: string;
    public currency: string;
    public channel: string;
    public referralPartner: string;
    public page_size: string;
    public first = 0;
    public card_type: string;
    public merchant_transaction_ref: string;
    public installment_bank: Array<string>;
    public installment_status: Array<string>;
    public refund_installment_status: Array<string>;

    public fromDate: Date;
    public toDate: Date;
    public merchantsDropdownList: SelectItem[] = [];
    public currencyList: Array<any>;
    public channelList: Array<any>;
    public referralPartnerList: Array<any>;
    public installmentList: Array<any>;
    public refundInstallmentList: Array<any>;
    public pageSizeList: Array<any>;
    public cardTypes: Array<any>;

    public installmentState: string;
    public optionInstallmentState: Array<any>;
    public transStatementDay: string;
    public optionTransStatementDay: Array<any>;
    public maxDateTransStatement: Date;
    public minDateTransStatement: Date;

    // INSTALLMENT
    public installmentBankList: SelectItem[] = [];

    @Output()
    private submitFinSearch = new EventEmitter<any>();

    constructor(public global: Globals, private internationalService: InternationalService) {
        // this.adapter.setLocale('en-in');
    }

    ngOnInit() {
        //giá trị mặc định cho dropdown gd sát sao kê
        this.transStatementDay = this.optionTransStatementDay[0].value;
    }

    init(param?: any) {
        this.fromDate = param['fromDate'] !== undefined ? new Date(param['fromDate']) : new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = param['toDate'] !== undefined ? new Date(param['toDate']) : new Date(new Date().setHours(23, 59, 59, 0));
        this.merchant_id = param['merchant_id'];

        this.transaction_id = param['transaction_id'];
        this.merchant_transaction_ref = param['merchant_transaction_ref'];
        this.order_reference = param['order_reference'];
        this.card_number = param['card_number'];
        this.authorisation_code = param['authorisation_code'];
        this.currency = param['currency'];
        this.channel = param['channel'];
        this.referralPartner = param['referralPartner'];
        this.page_size = param['page_size'];
        this.first = param['first'] !== undefined ? parseInt(param['first']) : 0;
        this.card_type = param['card_type'];
        this.installment_bank = (param['installment_bank'] !== '' && param['installment_bank'] !== undefined) ? param['installment_bank'].split(",") : [];
        this.installment_status = (param['installment_status'] !== '' && param['installment_status'] !== undefined) ? param['installment_status'].split(",") : [];
        this.refund_installment_status = (param['refund_installment_status'] !== '' && param['refund_installment_status'] !== undefined) ? param['refund_installment_status'].split(",") : [];
        this.currencyList = [
            {
                value: '',
                label: 'ALL'
            },
            {
                value: 'VND',
                label: 'VND'
            },
            {
                value: 'USD',
                label: 'USD'
            },
            {
                value: 'THB',
                label: 'THB'
            },
            {
                value: 'SGD',
                label: 'SGD'
            },
            {
                value: 'MYR',
                label: 'MYR'
            },
            {
                value: 'IDR',
                label: 'IDR'
            },
            {
                value: 'JPY',
                label: 'JPY'
            },
            {
                value: 'KRW',
                label: 'KRW'
            },
            {
                value: 'TWD',
                label: 'TWD'
            },
            {
                value: 'CNY',
                label: 'CNY'
            }
        ]

        this.channelList = [
            {
                value: '',
                label: 'ALL'
            },
            {
                value: 'UPOS',
                label: 'UPOS'
            },
            {
                value: 'ECOM',
                label: 'ECOM'
            }
        ];

        this.internationalService.getListPartnerIpp().subscribe(data => {
            this.referralPartnerList = data instanceof Array ? [{label: 'ALL', value: ''}].concat(data.map(m => {
              return {label: `${m.id}`, value: m.id};
            })) : [{label: 'ALL', value: ''}];
        });

        this.installmentList = [
            {
                value: 'created',
                label: 'Created'
            },
            {
                value: 'void',
                label: 'Void'
            },
            {
                value: 'failed',
                label: 'Reject'
            },
            {
                value: 'sent',
                label: 'Sent'
            },
            {
                value: 'approved',
                label: 'Approved'
            }
        ];
        this.refundInstallmentList = [
          {
              value: 'YES',
              label: 'Yes'
          },
          {
              value: 'NO',
              label: 'No'
          }
      ];


        this.cardTypes = [
            {
                value: '',
                label: 'ALL'
            },
            {
                value: 'Visa',
                label: 'Visa'
            }, {
                value: 'Mastercard',
                label: 'MasterCard'
            }, {
                value: 'JCB',
                label: 'JCB'
            }, {
                value: 'Amex,American Express',
                label: 'AmEx'
            }, {
                value: 'CUP',
                label: 'CUP'
            }, {
                value: 'PP',
                label: 'PayPal'
            }
        ]


        this.pageSizeList = [
            {
                label: '100',
                value: '100'
            },
            {
                label: '150',
                value: '150'
            },
            {
                label: '200',
                value: '200'
            }
        ];
        this.internationalService.getListInstallmentBank().subscribe(data => {

            this.installmentBankList = data.list instanceof Array ? data.list.map(m => {
                return { label: `${m.name}`, value: m.name };
            }) : [];
        });

        this.optionInstallmentState = [
            {label:'All', value:''},
            {label:'Unsent', value:'created,void'},
            {label:'Sent', value:'sent,approved,reject'}
        ];

        if(this.isActive('installment_sendtobank_button')){
            this.optionTransStatementDay = [
                {label:'Transactions close to Statement Date', value:'1'}
            ]

            //chỉ cho chọn 1 tuần nếu tài khoản này có quyền chọn gd sát sao kê
            this.setFromToOneWeek();
        }
        else{
            this.optionTransStatementDay = [
                {label:'All', value:'0'},
                {label:'Transactions close to Statement Date', value:'1'}
            ]
        }

    }


    redirectParams() {
        var params =
        {
            'merchant_id': this.merchant_id,
            'fromDate': this.fromDate,
            'toDate': this.toDate,
            'transaction_id': this.transaction_id,
            'order_reference': this.order_reference,
            'merchant_transaction_ref': this.merchant_transaction_ref,
            'card_number': this.card_number,
            'authorisation_code': this.authorisation_code,
            'card_type': this.card_type,
            'currency': this.currency,
            'channel': this.channel,
            'referralPartner': this.referralPartner,
            'page_size': this.page_size,
            'installment_bank': this.installment_bank.join(","),
            'first': this.first,
            'installment_status': this.installment_status.join(","),
            'refund_installment_status': this.refund_installment_status.join(",")
        };

        return params;
    }


    clear() {
        this.fromDate = new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = new Date(new Date().setHours(23, 59, 59, 0));
        this.merchant_id = undefined;

        this.transaction_id = undefined;
        this.order_reference = undefined;
        this.card_number = undefined;
        this.authorisation_code = undefined;
        this.currency = undefined;
        this.channel = undefined;
        this.referralPartner = undefined;
        this.merchant_transaction_ref = undefined;
        this.page_size = undefined;
        this.card_type = undefined;
        this.installment_bank = [];
        this.installment_status = [];
        this.refund_installment_status = [];

        this.installmentState = '';
        this.transStatementDay = this.optionTransStatementDay[0].value;

        if(this.isActive('installment_sendtobank_button')){
            this.setFromToOneWeek();
        }
        else{
            this.maxDateTransStatement = null;
            this.minDateTransStatement = null;
        }
    }

    onSubmit() {
        this.submitFinSearch.emit();
    }

    onSelectTransStatementDay(){
        //khi chọn gd sát ngày sao kê
        //thì chỉ cho chọn 1 tuần kể từ ngày hiện tại
        if(this.transStatementDay == '1'){
            this.setFromToOneWeek();
        }
        else{
            this.maxDateTransStatement = null;
            this.minDateTransStatement = null;
        }
    }

    isActive(functionName: string) {
        return this.global.isActive(functionName);
    }

    //set from_date and to_date chỉ cho chọn 1 tuần
    setFromToOneWeek(){
        this.maxDateTransStatement = new Date();
        this.toDate = new Date();
        this.minDateTransStatement = new Date();
        this.minDateTransStatement.setDate(this.maxDateTransStatement.getDate() - 7);
        this.fromDate = this.minDateTransStatement;
    }
}
