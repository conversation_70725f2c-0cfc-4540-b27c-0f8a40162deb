import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { InternationalService } from '@service/international.service';
import { ToastrService } from 'ngx-toastr';
import html2canvas from 'html2canvas';
import { DecimalPipe, DatePipe, Location, formatNumber } from '@angular/common';
import { InstallmentTransactionApprovalSearchForm } from './search/search.component';
import { Globals } from '@core/global';
import { HttpParams } from '@angular/common/http';
import { LazyLoadEvent } from 'primeng/api';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { constants } from '@shared/utils/constants';
import { DialogModule } from 'primeng/dialog';
import { installmentApprovalReasonConfig } from '../../../configs/installment-approval-reason-config';

@Component({
  selector: 'installment-transaction-detail',
  templateUrl: './installment-transaction-detail-component.html',
  styleUrls: ['./installment-transaction-detail-component.css'],
  providers: [InternationalService]
})

export class InstallmentTransactionDetailComponent implements OnInit, OnDestroy {

  constructor(private router: Router, private activatedRouter: ActivatedRoute, private internationalservice: InternationalService
    , public datepipe: DatePipe, private toastr: ToastrService, private dp: DecimalPipe, public global: Globals
    , private confirmService: ConfirmService
    , private location: Location) {

  }
  public back = 'Installment Approval';

  public title = 'Installment Approval Details';

  public readonly constants = constants;

  /* hien thi du lieu chi tiet giao dich */
  public _id: string;
  public subscription: Subscription;
  public subscription1: Subscription;
  public GetTransactionID: any;
  // public GetListHistoryTransaction: any;
  /* end hien thi chi tiet giao dich */

  // anhkh add
  private cscReponse: any;
  private avsReponse: any;
  private ipproxyReponse: any;
  private statusReponse: any;
  private statusReponseCode: any;
  public statusReponseKeys: Array<any>;
  private statusResp2: any;
  public refund_amount: number;
  public data_history = [];
  // mobile
  public loading: boolean;
  public data: Array<any>;
  public resultsLength: number;

  // duynq add Do Recharge

  public order_info: string;
  public csc = '';
  public recharge_amount: number;
  displayReject = false;
  displayApprove = false;
  dateApprove = new Date(new Date().setHours(0, 0, 0, 0));
  public installmentApproveDesc = '';
  public amountApprove = '';
  dateReject = new Date(new Date().setHours(0, 0, 0, 0));
  public installmentRejectDesc = '';
  public createRefundIta = true;
  public rejectReasons: Array<any>;

  @ViewChild(InstallmentTransactionApprovalSearchForm, { static: true }) searchForm: InstallmentTransactionApprovalSearchForm;

  private col = 'color: green';

  /* --- duynq add print screen */
  print_screen_transaction() {
    html2canvas(document.body).then((canvas) => {
      const getImage = canvas.toDataURL();
      const a = document.createElement('a');
      a.href = canvas.toDataURL('image/jpg').replace('image/jpeg', 'image/octet-stream');
      a.download = 'OPTransactionOrderDetail.jpg';
      // them javascrip them cho firefox khi chụp màn hình
      function fireEvent(obj, evt) {
        const fireOnThis = obj;
        if (document.createEvent) {
          const evObj = document.createEvent('MouseEvents');
          evObj.initEvent(evt, true, false);
          fireOnThis.dispatchEvent(evObj);
        }
      }
      fireEvent(a, 'click');
    });
  }
  /* --- End duynq add print screen */
  loadData() {
    this.subscription = this.activatedRouter.params.subscribe(params => {
      this._id = params['id'];
      this.internationalservice.GetInstallmentTransDetail(this._id).subscribe(data => {
        this.GetTransactionID = data;
        this.order_info = this.GetTransactionID.order_info;
        this.recharge_amount = this.GetTransactionID.amount.total;
        // this.refund_amount = parseFloat(this.GetTransactionID.amount.total) - parseFloat(this.GetTransactionID.wait_for_approval_amount) - parseFloat(this.GetTransactionID.amount.refund_total);
        this.refund_amount = Number(this.GetTransactionID.amount.total)
          - Number(this.GetTransactionID.wait_for_approval_amount)
          - Number(this.GetTransactionID.amount.refund_total);
        this.refund_amount = this.refund_amount < 0 ? 0 : this.refund_amount;
      });
      // this.internationalservice.GetlistHistoryTransaction(this._id).subscribe(data => {
      //   // this.dataSource.data = data.transactions;
      //   // console.log(this.dataSource);
      //   this.data_history = data.list;
      // });
    });
  }
  ngOnInit() {
    this.subscription1 = this.activatedRouter.queryParams
      .subscribe(params => {
        this.searchForm.init(params);
      });

    this.loadData();


    // anhkh add, dung bo
    this.internationalservice.getStatus().subscribe(status => {
      this.cscReponse = status.csc;
      this.avsReponse = status.avs;
      this.ipproxyReponse = status.ipproxy;
      this.statusReponse = status.international;

      this.statusReponseCode = status.international_v2;
      this.statusReponseKeys = Object.keys(this.statusReponseCode);
      this.statusResp2 = status.international_refund;

    });

    this.rejectReasons = installmentApprovalReasonConfig.rejectReason.map(m => {
      return { value: m, label: m };
    });
  }

  getIpLocation = function (ev) {
    const self = this;
    self.preventDefault(ev);
    this.internationalservice.GetIPAddress(this.GetTransactionID.ip_address).subscribe(resp => {
      this.GetTransactionID.ip_country = resp.country_name;
    });
    // return (vm.statusResp2[status]) ? vm.statusResp2[status].text : status;
  };
  preventDefault(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  repsonseRefundRender = function (status) {
    const vm = this;
    return (vm.statusResp2[status]) ? vm.statusResp2[status].text : status;
  };

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;

    const params = new HttpParams()
      ;
    // return this.internationalservice.GetlistHistoryTransaction(this._id).subscribe(data => {
    //   this.loading = false;
    //   this.resultsLength = data.total_items;
    //   this.data_history = data.transactions;
    //   // this.data_history = data.transaction;
    //   // console.log(this.data_history);
    // });

  }

  onSubmit() {
    this.router.navigate(['/international/installment'], { queryParams: this.searchForm.redirectParams() });
  }


  convertInstallmentState(responseCode: String) {
    if (responseCode === '' || responseCode == null || responseCode === undefined) { return ''; }
    return responseCode === 'approved' ? 'Approved' : responseCode === 'failed' ? 'Failed' : responseCode === 'void' ? 'Void' : 'Waiting for Approval';
  }
  canApproveReject() {
    if (this.GetTransactionID.installment_status === 'created') { return true; }
    return false;
  }

  approve(): void {
    this.displayApprove = true;
    this.amountApprove = this.GetTransactionID.amount.total;
  }

  checkChangeApproveDate() {
    console.log(this.dateApprove);
  }

  checkChangeRejectDate() {
    console.log(this.dateApprove);
  }

  ConfirmApprove(): void {
    console.log(parseInt(this.amountApprove));
    if (parseInt(this.amountApprove) === null || isNaN(parseInt(this.amountApprove)) || parseInt(this.amountApprove) === 0
      || parseInt(this.amountApprove) > this.GetTransactionID.amount.total) {
      this.toastr.error('Approve Amount must be between 1 and ' + parseInt(this.GetTransactionID.amount.total).toFixed(0).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'));
      return;
    }
    if (this.dateApprove !== undefined && this.dateApprove !== null) {
      this.displayApprove = false;
      this.confirmService.build()
        .message('<div class=\'content-confirm\'> Are you sure to approve this <br/> installment request ?  </div>')
        .title('Notice!')
        .yes('Yes')
        .no('No').confirm().subscribe(result => {
          if (result) {
            const values = {};
            const dateApprove = this.datepipe.transform(this.dateApprove, 'yyyy-MM-dd hh:mm a');

            const body = {
              id: this._id,
              op: 'replace',
              path: '/approve',
              value: values,
              approveDate: dateApprove,
              approveAmount: this.amountApprove.toString(),
              approveDesc: this.installmentApproveDesc,
              transType: this.GetTransactionID.transaction_type
            };

            return this.internationalservice.patchInstallmenttransaction(body, this._id).subscribe(data => {
              this.toastr.success('Installment approved');
              this.GetTransactionID.installment_status = 'approved';
              this.GetTransactionID.installment_reason = this.installmentApproveDesc;
            });

          }
        });
    }

  }

routerLinkRedirect(){
    const url = this.router.createUrlTree(['/iportal/response-code','QT']).toString();
    window.open(url, '_blank');
}

  reject(): void {
    this.displayReject = true;
  }

  confirmReject(): void {
    if (this.dateReject !== undefined && this.dateReject !== null) {
      this.displayReject = false;
      this.confirmService.build()
        .message('<div class=\'content-confirm\'> Are you sure to reject this <br/> installment request? </div>')
        .title('Notice!')
        .yes('Yes')
        .no('No').confirm().subscribe(result => {
          if (result) {
            const values = {
            };
            const dateReject = this.datepipe.transform(this.dateReject, 'yyyy-MM-dd hh:mm a');
            const emailDate = this.datepipe.transform(this.dateReject, 'dd-MM-yyyy hh:mm a');
            const body = {
              id: this._id,
              op: 'replace',
              path: '/reject',
              value: values,
              rejectDate: dateReject,
              emailDate: emailDate,
              rejectDesc: this.installmentRejectDesc,
              dateTransaction: this.formatDate(this.GetTransactionID.transaction_time),
              merchant_id: this.GetTransactionID.merchant_id,
              order_ref: this.GetTransactionID.order_info,
              installment_bank: this.GetTransactionID.installment_bank,
              transaction_id: this.GetTransactionID.transaction_id,
              card_number: this.GetTransactionID.card.card_number,
              card_type: this.GetTransactionID.card.card_type,
              amount: formatNumber(Number(this.GetTransactionID.amount.total), 'en-US', '1.0-0'),
              installment_period: this.GetTransactionID.installment_time,
              transaction_state: this.GetTransactionID.advance_status,
              authorization_code: this.GetTransactionID.authentication.authorization_code,
              transType: this.GetTransactionID.transaction_type
            };
            return this.internationalservice.patchInstallmenttransaction(body, this._id).subscribe(data => {
              this.toastr.warning('Installment rejected');
              this.GetTransactionID.installment_status = 'failed';
              this.GetTransactionID.installment_reason = this.installmentRejectDesc;
              if (this.createRefundIta) {
                this.processCreateRefundItaFee();
              }
            });
          }
        });
    }
  }
  validateAmount() {
    if (parseInt(this.amountApprove) === 0 || parseInt(this.amountApprove) > this.GetTransactionID.amount.total) {
      this.toastr.error('Approve Amount must be between 1 and ' + parseInt(this.GetTransactionID.amount.total).toFixed(0).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'));
      return false;
    }
    return true;
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.subscription1.unsubscribe();
  }

  responseRender(status) {
    const vm = this;
    if (status === '') { status = 'Blank'; }
    return (vm.statusReponse[status]) ? status + ' - ' + vm.statusReponse[status].text : status;
  }

  avsResponseRender(status) {
    const vm = this;
    if (status === '' || status == null || status === undefined) { return ''; }
    return (vm.avsReponse[status]) ? status + ' - ' + vm.avsReponse[status].text : status;
  }

  cscResponseRender(status) {
    const vm = this;
    if (status === '' || status == null || status === undefined) { return ''; }
    return (vm.cscReponse[status]) ? status + ' - ' + vm.cscReponse[status].text : status;
  }

  ipproxyResponseRender(status) {
    const vm = this;
    if (status === '' || status == null || status === undefined) { return ''; }
    return (vm.ipproxyReponse[status]) ? status + ' - ' + vm.ipproxyReponse[status].text : status;
  }

  formatDate(dateS: string) {
    if (!dateS)
      return;
    const date = new Date(dateS + ' UTC'),
      year = date.getUTCFullYear(),
      month = (date.getUTCMonth() + 1).toString(),
      formatedMonth = (month.length === 1) ? ('0' + month) : month,
      day = date.getUTCDate().toString(),
      formatedDay = (day.length === 1) ? ('0' + day) : day,
      hour = date.getUTCHours(),
      ampm = hour >= 12 ? 'PM' : 'AM',
      hours = hour % 12,
      formatedHour = ((hours ? hours : 12).toString().length === 1) ? ('0' + (hours ? hours : 12)) : (hours ? hours : 12),
      minute = date.getUTCMinutes().toString(),
      formatedMinute = (minute.length === 1) ? ('0' + minute) : minute,
      second = date.getUTCSeconds().toString(),
      formatedSecond = (second.length === 1) ? ('0' + second) : second;
    return formatedDay + '-' + formatedMonth + '-' + year + ' ' + formatedHour + ':' + formatedMinute + ' ' + ampm;
  }

  // getIpLocation(ev) {
  //    var self = this;
  //    var vm = this;
  //    self.preventDefault(ev);
  //    if(vm.submitted) return;
  //    vm.submitted = true;
  //    var param = { ip : vm.transaction.ip_address};
  //    self.msApi.request('international.transaction.ip@get', param, function(resp){
  //       vm.transaction.ip_country = resp.country_name;
  //       self.submitted = false;
  //       self.searching = false;
  //    }, function(err){
  //        vm.transaction.ip_country = undefined;
  //       self.submitted = false;
  //       self.searching = false;
  //    });
  // }

  isHyperLink(data_history) {
    if (data_history.transaction_id !== this._id) {
      return (data_history.transaction_type.toUpperCase() === 'PURCHASE'
        || (data_history.transaction_type.toUpperCase() === 'REFUND'
          && data_history.advance_status.toUpperCase().indexOf('REJECTED') === -1)
        || (data_history.transaction_type.toUpperCase() === 'REQUEST REFUND' && data_history.status === '401'));
    }
    return false;
  }

  processCreateRefundItaFee(): void {

          let transItaFeeRefund = [ {
              transaction_id: this.GetTransactionID.transaction_id,
              merchant_id: this.GetTransactionID.merchant_id,
              currency: this.GetTransactionID.currency,
              N_AMOUNT: Number(this.GetTransactionID.amount.total),
              amount_to_vnd: Number(this.GetTransactionID.amount.total),
              ita_fee_amount: this.GetTransactionID.installment_fee,
              transaction_state: this.GetTransactionID.installment_status,
              id: this.GetTransactionID.transaction_id,
              date_transaction: this.GetTransactionID.transaction_time,
              state: 'created',
              desc: 'Hoàn phí trả góp TransID ' + this.GetTransactionID.transaction_id + ' Auth code ' + this.GetTransactionID.authentication.authorization_code + ' ngày ' + this.formatDateDDMMYYYY(this.GetTransactionID.transaction_time),

              amount: formatNumber(Number(this.GetTransactionID.amount.total), 'en-US', '1.0-0'),
              order_ref: this.GetTransactionID.order_info,
              dateTransaction: this.formatDate(this.GetTransactionID.transaction_time),
              installment_bank: this.GetTransactionID.installment_bank,
              card_number: this.GetTransactionID.card.card_number,
              card_type: this.GetTransactionID.card.card_type,
              installment_period: this.GetTransactionID.installment_time,
              authorization_code: this.GetTransactionID.authentication.authorization_code,
              pay_channel: this.GetTransactionID.acquirer.acquirer_name ? 'ECOM' : 'UPOS'
          }
          ];

          const data = {
            trans: transItaFeeRefund
          };
          this.internationalservice.insertTransRefundIta(data).subscribe({
            next: (response: { map_res: any }) => {
              if (response && response.map_res && response.map_res.length > 0) {
                response.map_res.map(i => {
                  if (i.code === '406') {
                    this.toastr.warning(i.message + '', 'Fail');
                  } else if (i.code === '407') {
                    this.toastr.warning(i.message + '', 'Fail');
                  } else if (i.code === '408') {
                    this.toastr.warning(i.message + '', 'Fail');
                  } else if (i.code === '409') {
                    this.toastr.warning(i.message + '', 'Fail');
                  } else {
                    this.toastr.warning('System error!', 'Fail');
                  }
                });
              } else this.toastr.warning('No response!', 'Fail');
            },
            error: (error: any) => {
              this.toastr.warning('System Error', 'Fail');
            },
            complete: () => {
                this.toastr.success('Successfully created installment fee refund transaction', 'Success');
            }
          });
      
  }

  formatDateDDMMYYYY(dateS: string) {
    if (!dateS)
      return;
    const date = new Date(dateS + ' UTC'),
      year = date.getUTCFullYear(),
      month = (date.getUTCMonth() + 1).toString(),
      formatedMonth = (month.length === 1) ? ('0' + month) : month,
      day = date.getUTCDate().toString(),
      formatedDay = (day.length === 1) ? ('0' + day) : day;
    return formatedDay + '/' + formatedMonth + '/' + year;
  }

  backToPage() {
    this.location.back();
  }

  isActive(functionName: string): boolean {
    return this.global.isActive(functionName);
  }

}


