import { <PERSON>mpo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { DatePipe } from '@angular/common';
import { InternationalService } from '@service/international.service';
import { HttpParams } from '@angular/common/http';
import { Globals } from '@core/global';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { LazyLoadEvent } from 'primeng/api';
import {InstallmentMerchantSearch} from '@module/international/installment-merchant-report/search/installment-merchant-search.component';

@Component({
  // tslint:disable-next-line:component-selector
    selector: 'MerchantReportComponent',
    templateUrl: './merchant-report-component.html',
    styleUrls: ['./merchant-report-component.css'],
    providers: [InternationalService]
})


export class MerchantReportComponent implements OnInit {
    public title = 'Installment Merchant Report';

  public sub: Subscription;
  public merchant_id: string;
  public resultsLength: number;
  public loading: boolean;
  public data: Array<any>;
  private offsetHeight = 215;
  public flexScrollHeight = '200px';
  // public page: number;
  public purchase_quantity_wait_for_trans = 0;
  public purchase_total_wait_for_trans = 0;
  public purchase_quantity_approved_trans = 0;
  public purchase_total_approved_trans = 0;
  public purchase_quantity_failed_trans = 0;
  public purchase_total_failed_trans = 0;
  public purchase_quantity_void_trans = 0;
  public purchase_total_void_trans = 0;
  public refund_quantity_wait_for_trans = 0;
  public refund_total_wait_for_trans = 0;
  public refund_quantity_success_trans = 0;
  public refund_total_success_trans = 0;

  @ViewChild(InstallmentMerchantSearch, { static: true }) searchForm: InstallmentMerchantSearch;

  constructor(private internationalService: InternationalService, public datePipe: DatePipe
    , public global: Globals, private route: ActivatedRoute, private router: Router) {
  }

  ngOnInit() {

    this.sub = this.route
      .queryParams
      .subscribe(params => {
        this.searchForm.init(params);
      });

    this.resultsLength = 0;
    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
  }

  // tslint:disable-next-line:use-lifecycle-interface
  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  searchData() {
    const from_date = this.datePipe.transform(this.searchForm.fromDate, 'dd-MM-yyyy');
    const to_date = this.datePipe.transform(this.searchForm.toDate, 'dd-MM-yyyy');
    const params = new HttpParams()
      .set('type_filter', this.searchForm.type_filter.toString())
      .set('merchant_id', this.searchForm.merchant_id)
      .set('from_date', from_date)
      .set('to_date', to_date)
      .set('interval', this.searchForm.interval)
      .set('referral_partner', this.searchForm.referral_partner)
      // .set('page_size', this.searchForm.page_size === undefined ? '50' : this.searchForm.page_size)
      .set('list_bank', this.searchForm.list_bank.join(','))
      // .set('page', this.page.toString())
    ;

    return this.internationalService.searchInstallmentMerchantReport(params);
  }

  downloadInstallmentMerchant() {
    const from_date = this.datePipe.transform(this.searchForm.fromDate.toString(), 'dd-MM-yyyy');
    const to_date = this.datePipe.transform(this.searchForm.toDate.toString(), 'dd-MM-yyyy');
    const myBody = {
      'type_filter': this.searchForm.type_filter,
      'merchant_id': this.searchForm.merchant_id ? this.searchForm.merchant_id : '',
      'from_date': from_date,
      'to_date': to_date,
      'list_bank': this.searchForm.list_bank.join(','),
      'interval': this.searchForm.interval === undefined ? '1' : this.searchForm.interval,
      'referral_partner': this.searchForm.referral_partner ? this.searchForm.referral_partner : ''
    };
    return this.internationalService.dowloadInstallmentMerchant(myBody).subscribe(response => {
      this.global.downloadEmit.emit(true);
    });
  }

  onSubmit() {
    // this.searchForm.first = 0;
    // this.page = 0;
    this.router.navigate(['/international/installment-merchant-report'], { queryParams: this.searchForm.redirectParams() });
    return this.searchData().subscribe(data => {
      this.resultsLength = data.totalItems;
      this.data = data.list;
      if (data.code !== '500') {
        this.purchase_quantity_wait_for_trans = 0;
        this.purchase_total_wait_for_trans = 0;
        this.purchase_quantity_approved_trans = 0;
        this.purchase_total_approved_trans = 0;
        this.purchase_quantity_failed_trans = 0;
        this.purchase_total_failed_trans = 0;
        this.purchase_quantity_void_trans = 0;
        this.purchase_total_void_trans = 0;
        this.refund_quantity_wait_for_trans = 0;
        this.refund_total_wait_for_trans = 0;
        this.refund_quantity_success_trans = 0;
        this.refund_total_success_trans = 0;
        this.data.forEach(values => {
          this.purchase_quantity_wait_for_trans = values.purchase_quantity_wait_for_trans + this.purchase_quantity_wait_for_trans;
          this.purchase_total_wait_for_trans = values.purchase_total_wait_for_trans + this.purchase_total_wait_for_trans;
          this.purchase_quantity_approved_trans = values.purchase_quantity_approved_trans + this.purchase_quantity_approved_trans;
          this.purchase_total_approved_trans = values.purchase_total_approved_trans + this.purchase_total_approved_trans;
          this.purchase_quantity_failed_trans = values.purchase_quantity_failed_trans + this.purchase_quantity_failed_trans;
          this.purchase_total_failed_trans = values.purchase_total_failed_trans + this.purchase_total_failed_trans;
          this.purchase_quantity_void_trans = values.purchase_quantity_void_trans + this.purchase_quantity_void_trans;
          this.purchase_total_void_trans = values.purchase_total_void_trans + this.purchase_total_void_trans;
          this.refund_quantity_wait_for_trans = values.refund_quantity_wait_for_trans + this.refund_quantity_wait_for_trans;
          this.refund_total_wait_for_trans = values.refund_total_wait_for_trans + this.refund_total_wait_for_trans;
          this.refund_quantity_success_trans = values.refund_quantity_success_trans + this.refund_quantity_success_trans;
          this.refund_total_success_trans = values.refund_total_success_trans + this.refund_total_success_trans;
        });
      }
    });
  }

  loadLazy(event: LazyLoadEvent) {
    // this.searchForm.first = 0;
    this.loading = true;
    // this.page = event.first / event.rows;
    // this.searchForm.first = event.first;
    this.router.navigate(['/international/installment-merchant-report'], { queryParams: this.searchForm.redirectParams() });
    return this.searchData().subscribe(data => {
      this.loading = false;
      this.resultsLength = data.totalItems;
      this.data = data.list;
      if (data.code !== '500') {
        this.purchase_quantity_wait_for_trans = 0;
        this.purchase_total_wait_for_trans = 0;
        this.purchase_quantity_approved_trans = 0;
        this.purchase_total_approved_trans = 0;
        this.purchase_quantity_failed_trans = 0;
        this.purchase_total_failed_trans = 0;
        this.purchase_quantity_void_trans = 0;
        this.purchase_total_void_trans = 0;
        this.refund_quantity_wait_for_trans = 0;
        this.refund_total_wait_for_trans = 0;
        this.refund_quantity_success_trans = 0;
        this.refund_total_success_trans = 0;
        this.data.forEach(values => {
          this.purchase_quantity_wait_for_trans = values.purchase_quantity_wait_for_trans + this.purchase_quantity_wait_for_trans;
          this.purchase_total_wait_for_trans = values.purchase_total_wait_for_trans + this.purchase_total_wait_for_trans;
          this.purchase_quantity_approved_trans = values.purchase_quantity_approved_trans + this.purchase_quantity_approved_trans;
          this.purchase_total_approved_trans = values.purchase_total_approved_trans + this.purchase_total_approved_trans;
          this.purchase_quantity_failed_trans = values.purchase_quantity_failed_trans + this.purchase_quantity_failed_trans;
          this.purchase_total_failed_trans = values.purchase_total_failed_trans + this.purchase_total_failed_trans;
          this.purchase_quantity_void_trans = values.purchase_quantity_void_trans + this.purchase_quantity_void_trans;
          this.purchase_total_void_trans = values.purchase_total_void_trans + this.purchase_total_void_trans;
          this.refund_quantity_wait_for_trans = values.refund_quantity_wait_for_trans + this.refund_quantity_wait_for_trans;
          this.refund_total_wait_for_trans = values.refund_total_wait_for_trans + this.refund_total_wait_for_trans;
          this.refund_quantity_success_trans = values.refund_quantity_success_trans + this.refund_quantity_success_trans;
          this.refund_total_success_trans = values.refund_total_success_trans + this.refund_total_success_trans;
        });
      }
    });
  }
}
