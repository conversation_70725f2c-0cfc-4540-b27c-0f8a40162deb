
import { Component, OnInit, Output } from '@angular/core';
import { Globals } from '@core/global';
import { EventEmitter } from "@angular/core";

@Component({
    // tslint:disable-next-line:component-selector
    selector: 'refund-search-international-form-2',
    templateUrl: './search.component.html',
    styleUrls: ['./search.component.css']
})

export class RefundInterSearchForm2 implements OnInit{

    public merchantId: any;
    public transactionId: string;
    public networkTransId: string;
    public orderInfo: string;
    public cardNumber: string;
    public source: string;
    public sourceList: Array<any>;
    public tokenNumber: string;
    public currency: string;
    public gate: string;
    public first = 0;
    public acquirerId: Array<any> = [];
    public confirmable = 0;
    public merchantsList: any;
    public fromDate: Date;
    public toDate: Date;
    public currencyList: Array<any>;
    public gateList: Array<any>;
    public pageSizeList: Array<any>;
    public acquirerList: Array<any>;
    public authCode: string;
    public status: Array<any> = [];
    public statusList: Array<any>;
    public confirmRefundList: Array<any>;
    public actionType : string;
    public actionTypeList: Array<any>;

    public reviewTxn: boolean;


    @Output()
    private submitRefundIntSearch = new EventEmitter<any>();

    @Output()
    private clearRefundIntSearch = new EventEmitter<any>();

    constructor(private global: Globals) {
        // this.adapter.setLocale('en-in');
    }

    ngOnInit(): void {
        this.initOptions();
    }

    initOptions() {
      this.gateList = [
        {
            value: '',
            label: 'ALL'
        },
        {
            value: 'madm',
            label: 'MADM'
        },
        {
            value: 'invoice',
            label: 'Invoice'
        },
      ];

      this.acquirerList = [
          {
            label: 'Vietcombank - MIGS',
            value: '1'
          },
          {
            label: 'Vietinbank - Cyber',
            value: '2'
          },
          {
            label: 'CUP',
            value: '3'
          },
          {
            label: 'Vietcombank - Cyber',
            value: '4'
          },
          {
            label: 'Vietcombank - MPGS',
            value: '5'
          },
          {
            label: 'PayPal',
            value: '6'
          },
          {
            label: 'Sacombank - Cyber',
            value: '7'
          },
          {
            label: 'BIDV - Cyber',
            value: '8'
          },
          {
            label: 'Sacombank - MPGS',
            value: '9'
          },
          {
            label: 'Techcombank - Cyber',
            value: '10'
          },
          {
            label: 'VPB - MPGS',
            value: '11'
          },
          {
            label: 'KBank - Cyber',
            value: '12'
          },
          {
            label: 'VPB - Cyber',
            value: '13'
          }
      ];

      this.currencyList = [
        {
            value: '',
            label: 'ALL'
        },
        {
            value: 'VND',
            label: 'VND'
        },
        {
            value: 'USD',
            label: 'USD'
        },
        {
          value: 'THB',
          label: 'THB'
        },
        {
          value: 'SGD',
          label: 'SGD'
        },
        {
          value: 'MYR',
          label: 'MYR'
        },
        {
          value: 'IDR',
          label: 'IDR'
        },
        {
          value: 'JPY',
          label: 'JPY'
        },
        {
          value: 'KRW',
          label: 'KRW'
        },
        {
          value: 'TWD',
          label: 'TWD'
        },
        {
          value: 'CNY',
          label: 'CNY'
        }
      ];

      this.statusList = [
        {
            value: '300',
            label: "Waiting for OnePay's Approval"
        },
        {
            value: '320',
            label: "Waiting for OnePay's Update"
        },
      ];

      this.confirmRefundList = [
        {
            value: 0,
            label: 'ALL'
        },
        {
            value: 1,
            label: 'Đủ điều kiện refund'
        },
        {
            value: 2,
            label: 'Chưa đủ điều kiện refund'
        },
      ];

      this.sourceList = [
        {
            value: '',
            label: 'ALL'
        },
        {
            value: 'Apple Pay',
            label: 'Apple Pay'
        },
        {
            value: 'Google Pay',
            label: 'Google Pay'
        },
        {
          value: 'Samsung Pay',
          label: 'Samsung Pay'
        },
        {
            value: 'Direct',
            label: 'Direct'
        },
      ];

      this.actionTypeList = [
        {
          value: '',
          label: 'ALL'
        },

        {
          label: 'Auto',
          value: 'auto'
        },
        {
          label: 'Manual',
          value: 'manual'
        }
      ];
    }

    init(param?: any) {
        this.fromDate = param['fromDate'] !== undefined ? new Date(param['fromDate']) : new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = param['toDate'] !== undefined ? new Date(param['toDate']) : new Date(new Date().setHours(23, 59, 59, 0));
        this.merchantId = param['merchantId'];
        this.gate = param['gate'];
        this.transactionId = param['transactionId'];
        this.networkTransId = param['networkTransId'];
        this.orderInfo = param['orderInfo'];
        this.cardNumber = param['cardNumber'];
        this.source = param['source'];
        this,this.tokenNumber = param['tokenNumber'];
        this.currency = param['currency'];
        this.first = param['first'] !== undefined ? parseInt(param['first']) : 0;
        this.authCode = param['authCode'];
        this.confirmable = param['confirmable'] !== undefined ? parseInt(param['confirmable']) : 0;
        this.actionType = param['actionType'];
        this.acquirerId = (param['acquirerId'] !== '' && param['acquirerId'] !== undefined) ? this.acquirerList.filter(col => param['acquirerId'].split(',').includes(col.value)) : [];  
        this.status = (param['status'] !== '' && param['status'] !== undefined) ? this.statusList.filter(col => param['status'].split(',').includes(col.value)) : [];
        this.reviewTxn = (param['reviewTxn'] === undefined || param['reviewTxn'] == 'false') ? false : true;

      }

    clear() {
        this.fromDate = new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = new Date(new Date().setHours(23, 59, 59, 0));
        this.merchantId = undefined;
        this.transactionId = undefined;
        this.networkTransId = undefined;
        this.acquirerId = [];
        this.orderInfo = undefined;
        this.cardNumber = undefined;
        this.source = undefined;
        this.tokenNumber = undefined;
        this.currency = undefined;
        this.gate = undefined;
        this.authCode = undefined;
        this.status = [];
        this.actionType = undefined;
        this.reviewTxn = false;

        this.clearRefundIntSearch.emit();
    }

    redirectParams() {
        var params =
        {
            'merchantId': this.merchantId,
            'acquirerId': this.acquirerId ? this.acquirerId.map(x => x.value).join(",") : "",
            'fromDate': this.fromDate,
            'toDate': this.toDate,
            'transactionId': this.transactionId,
            'networkTransId': this.networkTransId,
            'orderInfo': this.orderInfo,
            'cardNumber': this.cardNumber,
            'source': this.source,
            'tokenNumber': this.tokenNumber,
            'currency': this.currency,
            'first': this.first,
            'gate': this.gate,
            'authCode': this.authCode,
            'status': this.status ? this.status.map(x => x.value).join(",") : '',
            'confirmable': this.confirmable,
            'actionType': this.actionType,
            'reviewTxn': this.reviewTxn

        };
        return params;
    }


    onSubmit() {
        this.submitRefundIntSearch.emit();
    }
}