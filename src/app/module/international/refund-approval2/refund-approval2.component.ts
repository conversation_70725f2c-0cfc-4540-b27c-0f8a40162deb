import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { InternationalService } from '@service/international.service';
import { ToastrService } from 'ngx-toastr';
import { RefundInterSearchForm2 } from './search/search.component';
import { Subscription, forkJoin, of } from 'rxjs';
import { constants } from '@shared/utils/constants';
import { DatePipe } from '@angular/common';
import { Globals } from '@core/global';
import { ActivatedRoute, Router } from '@angular/router';
import { LazyLoadEvent } from 'primeng/api';
import { catchError, finalize, map } from 'rxjs/operators';
import { HttpParams } from '@angular/common/http';

@Component({
    selector: 'app-refund-approval2',
    templateUrl: './refund-approval2.component.html',
    styleUrls: ['./refund-approval2.component.css'],
    providers: [InternationalService, ToastrService]
})
export class RefundApproval2Component implements OnInit {

    public title = 'Wait For OnePay Approval';
    public readonly constants = constants;

    public resultsLength: number;
    public sub: Subscription;
    public data: Array<any>;
    public page: number;
    public loading: boolean;
    public first = 0;
    public flexScrollHeight = '200px;'
    private offsetHeight = 130;
    public selectedData = [];
    public result: { [key: string]: number } = {};
    public checkAll: boolean = false;

    @ViewChild(RefundInterSearchForm2, { static: true }) searchForm: RefundInterSearchForm2;


    constructor(private internationalservice: InternationalService, public datepipe: DatePipe
        , private global: Globals, private route: ActivatedRoute, private router: Router, private toastr: ToastrService) {
    }

    ngOnInit() {
        this.sub = this.route
            .queryParams
            .subscribe(params => {
                this.searchForm.init(params);
            });
        this.resultsLength = 0;

        this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
    }

    @HostListener('window:resize', ['$event'])
    onResize(event) {
        this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
    }

    ngOnDestroy() {
        this.sub.unsubscribe();
    }

    approveBatch() {
        let requests = this.selectedData.map(data => {
            const body = {
                id: data.refundId,
                merchant_id: data.merchantIdMigs,
                merchant_txn_ref: data.refundRef,
            };
            return this.internationalservice.approveOP(body).pipe(
                map(res => {
                    if (res.name.toUpperCase() === 'APPROVED') {
                        this.removeBatchSuccess(data);
                        return 1;
                    } else {
                        this.formatBatchError(data);
                        return of(0);
                    }
                }),
                catchError(err => {
                    this.formatBatchError(data);
                    return of(0);
                })
            )
        });

        forkJoin(requests).subscribe(resList => {
            if (resList.filter(e => e === 1).length !== this.selectedData.length) {
                this.toastr.error('Approve List Failed');
            } else {
                this.toastr.success('Approve List Successfully');
            }
            this.selectedData = [];
            this.onSubmit();
        });
    }

    // approveBatch() {
    //     let refundData = this.selectedData.filter(item => item.refundId);
    //     let processedData;
    //     from(refundData).pipe(
    //       concatMap(data => {
    //         processedData = data;
    //         data.batchApprovalState = true;
    //         let body = {
    //             id: data.refundId,
    //             merchant_id: data.merchantIdMigs,
    //             merchant_txn_ref: data.refundRef,
    //         };
    //         return this.internationalservice.approveOP(body).pipe(
    //           map(res => {
    //             if (res.name.toUpperCase() === 'APPROVED') {
    //               this.removeBatchSuccess(data);
    //               return 1;
    //             } else {
    //               this.formatBatchError(data);
    //               return of(0);
    //             }
    //           }),
    //           finalize(() => data.batchApprovalState = false),
    //           catchError(err => {
    //             this.formatBatchError(data);
    //             return of(0);
    //           })
    //         )
    //       })
    //     ).subscribe({
    //       next: (v) => {
    //         if (v) {
    //           this.toastr.success('Update Successfully', null, { positionClass: 'toast-top-center' });
    //           this.data = this.data.filter(element => element.transaction_id !== processedData.transaction_id);
    //         }
    //       },
    //       error: (e) => {
    //         this.toastr.error('Update failed');
    //       },
    //       complete: () => {
    //         console.info('Processing complete');
    //         this.selectedData = [];
    //         this.onSubmit();
    //       }
    //     });
    // }

    rejectBatch() {
        let requests = this.selectedData.map(data => {
            const body = {
                id: data.refundId,
                merchant_id: data.merchantIdMigs,
                merchant_txn_ref: data.refundRef,
            };
            return this.internationalservice.rejectOP(body).pipe(
                map(res => {
                    if (res.name.toUpperCase() === 'APPROVED') {
                        this.removeBatchSuccess(data);
                        return 1;
                    } else {
                        this.formatBatchError(data);
                        return of(0);
                    }
                }),
                catchError(err => {
                    this.formatBatchError(data);
                    return of(0);
                })
            )
        });

        forkJoin(requests).subscribe(resList => {
            if (resList.filter(e => e === 1).length !== this.selectedData.length) {
                this.toastr.error('Reject List Failed');
            } else {
                this.toastr.success('Reject List Successfully');
            }
            this.selectedData = [];
            this.onSubmit();
        });
    }

    // rejectBatch() {
    //     let refundData = this.selectedData.filter(item => item.refundId);
    //     let processedData;
    //     from(refundData).pipe(
    //       concatMap(data => {
    //         processedData = data;
    //         data.batchApprovalState = true;
    //         let body = {
    //             id: data.refundId,
    //             merchant_id: data.merchantIdMigs,
    //             merchant_txn_ref: data.refundRef,
    //         };
    //         return this.internationalservice.rejectOP(body).pipe(
    //           map(res => {
    //             if (res.name.toUpperCase() === 'APPROVED') {
    //               this.removeBatchSuccess(data);
    //               return 1;
    //             } else {
    //               this.formatBatchError(data);
    //               return of(0);
    //             }
    //           }),
    //           finalize(() => data.batchApprovalState = false),
    //           catchError(err => {
    //             this.formatBatchError(data);
    //             return of(0);
    //           })
    //         )
    //       })
    //     ).subscribe({
    //       next: (v) => {
    //         if (v) {
    //           this.toastr.success('Update Successfully', null, { positionClass: 'toast-top-center' });
    //           this.data = this.data.filter(element => element.transaction_id !== processedData.transaction_id);
    //         }
    //       },
    //       error: (e) => {
    //         this.toastr.error('Update failed');
    //       },
    //       complete: () => {
    //         console.info('Processing complete');
    //         this.selectedData = [];
    //         this.onSubmit();
    //       }
    //     });
    // }

    updateSuccessBatch() {
        for (const data of this.selectedData) {
            this.updateToApproved(data);
        }
        this.selectedData = [];
    }

    updateFailedBatch() {
        for (const data of this.selectedData) {
            this.updateToFailed(data);
        }
        this.selectedData = [];
    }

    autoBatch() {
        let requests = this.selectedData.map(data => {
            const body = {
                id: data.refundId,
                merchant_id: data.merchantIdMigs,
                merchant_txn_ref: data.refundRef,
            };
            return this.internationalservice.autoOP(body).pipe(
                map(res => {
                    if (res.name.toUpperCase() === 'APPROVED') {
                        this.removeBatchSuccess(data);
                        return 1;
                    } else {
                        this.formatBatchError(data);
                        return of(0);
                    }
                }),
                catchError(err => {
                    this.formatBatchError(data);
                    return of(0);
                })
            )
        });

        forkJoin(requests).subscribe(resList => {
            if (resList.filter(e => e === 1).length !== this.selectedData.length) {
                this.toastr.error('Approve List Failed');
            } else {
                this.toastr.success('Approve List Successfully');
            }
            this.selectedData = [];
            this.onSubmit();
        });
    }

    manualBatch() {
        let requests = this.selectedData.map(data => {
            data.batchApprovalState = true;
            const body = {
                id: data.refundId,
                merchant_id: data.merchantIdMigs,
                merchant_txn_ref: data.refundRef,
            };
            return this.internationalservice.manualOP(body).pipe(
                map(res => {
                    if (res.name.toUpperCase() === 'APPROVED') {
                        this.removeBatchSuccess(data);
                        return 1;
                    } else {
                        this.formatBatchError(data);
                        return of(0);
                    }
                }),
                finalize(() => data.batchApprovalState = false),
                catchError(err => {
                    this.formatBatchError(data);
                    return of(0);
                })
            )
        });

        forkJoin(requests).subscribe(resList => {
            if (resList.filter(e => e === 1).length !== this.selectedData.length) {
                this.toastr.error('Update List Failed');
            } else {
                this.toastr.success('Update List Successfully');
            }
            this.selectedData = [];
            this.onSubmit();
        });
    }

    // manualBatch() {
    //     let refundData = this.selectedData.filter(item => item.refundId);
    //     let processedData;
    //     from(refundData).pipe(
    //       concatMap(data => {
    //         processedData = data;
    //         data.batchApprovalState = true;
    //         let body = {
    //             id: data.refundId,
    //             merchant_id: data.merchantIdMigs,
    //             merchant_txn_ref: data.refundRef,
    //         };
    //         return this.internationalservice.approveOP(body).pipe(
    //           map(res => {
    //             if (res.name.toUpperCase() === 'APPROVED') {
    //               this.removeBatchSuccess(data);
    //               return 1;
    //             } else {
    //               this.formatBatchError(data);
    //               return of(0);
    //             }
    //           }),
    //           finalize(() => data.batchApprovalState = false),
    //           catchError(err => {
    //             this.formatBatchError(data);
    //             return of(0);
    //           })
    //         )
    //       })
    //     ).subscribe({
    //       next: (v) => {
    //         if (v) {
    //           this.toastr.success('Update Successfully', null, { positionClass: 'toast-top-center' });
    //           this.data = this.data.filter(element => element.transaction_id !== processedData.transaction_id);
    //         }
    //       },
    //       error: (e) => {
    //         this.toastr.error('Update failed');
    //       },
    //       complete: () => {
    //         console.info('Processing complete');
    //         this.selectedData = [];
    //         this.onSubmit();
    //       }
    //     });
    // }

    approve(data): void {
        const body = {
            id: data.refundId,
            merchant_id: data.merchantIdMigs,
            merchant_txn_ref: data.refundRef,
        };

        this.internationalservice.approveOP(body).pipe(
            catchError(err => {
                this.formatBatchError(data);
                throw err;
            })
        ).subscribe(res => {
            if (res.name.toUpperCase() === 'APPROVED') {
                this.toastr.success('Refund Request approved');
                this.removeBatchSuccess(data);
            } else {
                this.toastr.error('Refund Request failed');
                this.formatBatchError(data);
            }
            this.onSubmit();
        });
    }

    reject(data): void {
        const body = {
            id: data.refundId,
            merchant_id: data.merchantIdMigs,
            merchant_txn_ref: data.refundRef,
        };
        this.internationalservice.rejectOP(body).pipe(
            catchError(err => {
                this.formatBatchError(data);
                throw err;
            })
        ).subscribe(res => {
            if (res.name.toUpperCase() === 'APPROVED') {
                this.toastr.warning('Refund Request rejected');
                this.removeBatchSuccess(data);
            } else {
                this.toastr.error('Refund Request failed');
                this.formatBatchError(data);
            }
            this.onSubmit();
        });
    }

    // reverseDue(data): void {
    //     data.batchApprovalState = true;

    //     const values = {
    //         merchant_id: data.merchantIdMigs,
    //         merchant_id_invoice: data.merchantIdMigs,
    //         transaction_reference: data.merchantTransactionRef,
    //         refund_reference: data.refundRef,
    //         currency: data.currency,
    //         hash_code: data.hashCode,
    //         access_code: data.accessCode,
    //         amount: data.currency,
    //         type: data.type
    //     };
    //     const body = {
    //         id: data.refundId,
    //         op: 'replace',
    //         path: '/reverseDue',
    //         type: data.type,
    //         value: values
    //     };
    //     this.internationalservice.PatchRefundApproval(body, data.refundId,).pipe(
    //         finalize(() => data.batchApprovalState = false),
    //     ).subscribe(data1 => {
    //         this.toastr.success('Reverse Due To Dispute Successful');
    //         for (let index = 0; index < this.data.length; index++) {
    //             const element = this.data[index];
    //             if (element.refundId == data.refundId) {
    //                 this.data.splice(index, 1);
    //                 break;
    //             }
    //         }
    //     });
    // }
    reverseDue(data): void {
        const body = {
            id: data.refundId,
            merchant_id: data.merchantIdMigs,
            merchant_txn_ref: data.refundRef,
        };

        this.internationalservice.PatchRefundApproval2(body).pipe(
            catchError(err => {
                this.formatBatchError(data);
                throw err;
            })
        ).subscribe(res => {
            if (res.name.toUpperCase() === 'APPROVED') {
                this.toastr.success('Refund Request approved');
                this.removeBatchSuccess(data);
            } else {
                this.toastr.error('Refund Request failed');
                this.formatBatchError(data);
            }
            this.onSubmit();
        });
    }

    updateToApproved(data: any): void {
        const body = {
            id: data.refundId,
            merchant_id: data.merchantIdMigs,
            state: 'approved',
        };

        this.internationalservice.UpdateStateOP(body).pipe(
            catchError(err => {
                this.formatBatchError(data);
                throw err;
            })
        ).subscribe(res => {
            if (res && res.name.toUpperCase() === 'APPROVED') {
                this.toastr.success('Update Successfully');
                this.removeBatchSuccess(data);
            } else {
                this.toastr.error('Update Failed');
                this.formatBatchError(data);
            }
            this.onSubmit();
        });
    }

    updateToFailed(data: any): void {
        const body = {
            id: data.refundId,
            merchant_id: data.merchantIdMigs,
            state: 'failed',
        };

        this.internationalservice.UpdateStateOP(body).pipe(
            catchError(err => {
                this.formatBatchError(data);
                throw err;
            })
        ).subscribe(res => {
            if (res.name.toUpperCase() === 'APPROVED') {
                this.toastr.success('Update Successfully');
                this.removeBatchSuccess(data);
            } else {
                this.toastr.error('Update Failed');
                this.formatBatchError(data);
            }
            this.onSubmit();
        });
    }

    checkAutoBatch() {
        for (const data of this.selectedData) {
            const body = {
                id: data.refundId,
                merchant_id: data.merchantIdMigs,
            };
            this.internationalservice.checkAutoOP(body).subscribe(res => {
                if (res) {
                    this.toastr.success('Check Successfully');
                    data.actionType = res.process_type;
                }
            })
        }
    }

    searchData() {
        const from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyy hh:mm aaa');
        const to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyy hh:mm aaa');

        let params = new HttpParams()
            .set('merchantId', this.searchForm.merchantId === undefined ? '' : this.searchForm.merchantId.trim())
            .set('fromDate', from_date)
            .set('toDate', to_date)
            .set('transactionId', this.searchForm.transactionId === undefined ? '' : this.searchForm.transactionId.trim())
            .set('networkTransactionId', this.searchForm.networkTransId === undefined ? '' : this.searchForm.networkTransId.trim())
            .set('orderInfo', this.searchForm.orderInfo === undefined ? '' : this.searchForm.orderInfo.trim())
            .set('authCode', this.searchForm.authCode === undefined ? '' : this.searchForm.authCode.trim())
            .set('acquirerId', this.searchForm.acquirerId ? this.searchForm.acquirerId.map(x => x.value).join(",") : "")
            .set('cardNumber', this.searchForm.cardNumber === undefined ? '' : this.searchForm.cardNumber.trim())
            .set('source', this.searchForm.source === undefined ? '' : this.searchForm.source.trim())
            .set('tokenNumber', this.searchForm.tokenNumber === undefined ? '' : this.searchForm.tokenNumber.trim())
            .set('currency', this.searchForm.currency === undefined ? '' : this.searchForm.currency)
            .set('gate', this.searchForm.gate === undefined ? '' : this.searchForm.gate)
            .set('status', this.searchForm.status ? this.searchForm.status.map(x => x.value).join(",") : '')
            .set('actionType', this.searchForm.actionType === undefined ? '' : this.searchForm.actionType)
            .set('reviewTxn',this.searchForm.reviewTxn==true? "check":"")
            ;

        return this.internationalservice.GetRefundInternationalV2(params);
    }


    download() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate.toString(), 'dd/MM/yyy hh:mm aaa');
        let to_date = this.datepipe.transform(this.searchForm.toDate.toString(), 'dd/MM/yyy hh:mm aaa');

        var params =
        {
            'merchantId': this.searchForm.merchantId === undefined ? '' : this.searchForm.merchantId.trim(),
            'fromDate': from_date,
            'toDate': to_date,
            'transactionId': this.searchForm.transactionId === undefined ? '' : this.searchForm.transactionId.trim(),
            'networkTransactionId': this.searchForm.networkTransId === undefined ? '' : this.searchForm.networkTransId.trim(),
            'orderInfo': this.searchForm.orderInfo === undefined ? '' : this.searchForm.orderInfo.trim(),
            'authCode': this.searchForm.authCode === undefined ? '' : this.searchForm.authCode.trim(),
            'acquirerId': this.searchForm.acquirerId ? this.searchForm.acquirerId.map(x => x.value).join(",") : "",
            'cardNumber': this.searchForm.cardNumber === undefined ? '' : this.searchForm.cardNumber.trim(),
            'source': this.searchForm.source === undefined ? '' : this.searchForm.source.trim(),
            'tokenNumber': this.searchForm.tokenNumber === undefined ? '' : this.searchForm.tokenNumber.trim(),
            'currency': this.searchForm.currency === undefined ? '' : this.searchForm.currency.trim(),
            'gate': this.searchForm.gate === undefined ? '' : this.searchForm.gate.trim(),
            'status': this.searchForm.status ? this.searchForm.status.map(x => x.value).join(",") : "",
            'actionType': this.searchForm.actionType === undefined ? '' : this.searchForm.actionType.trim(),
            'reviewTxn': this.searchForm.reviewTxn == true ? "check" : "",

        };
        return this.internationalservice.DownloadRefundInternational2(params).subscribe(response => {
            this.global.downloadEmit.emit(true);
        });
    }

    loadLazy(event: LazyLoadEvent) {
        this.loading = true;
        this.page = event.first / event.rows;
        this.first = event.first;
        return this.searchData().subscribe(data => {
            this.loading = false;
            // this.resultsLength = data?.totalItems;
            this.data = data.list;
            this.selectedData = [];
        });
    }

    onSubmit() {
        this.router.navigate(['/international/refund-approval2'], { queryParams: this.searchForm.redirectParams() });
        this.first = 0;
        this.page = 0;
        return this.searchData().subscribe(data => {
            // this.resultsLength = data.totalItems;
            this.data = data.list;
            this.selectedData = [];
        });

    }

    formatDate(dateS: string) {
        const date = new Date(dateS),
            year = date.getUTCFullYear(),
            month = (date.getUTCMonth() + 1).toString(),
            formatedMonth = (month.length === 1) ? ('0' + month) : month,
            day = date.getUTCDate().toString(),
            formatedDay = (day.length === 1) ? ('0' + day) : day,
            hour = date.getUTCHours(),
            ampm = hour >= 12 ? 'PM' : 'AM',
            hours = hour % 12,
            formatedHour = ((hours ? hours : 12).toString().length === 1) ? ('0' + (hours ? hours : 12)) : (hours ? hours : 12),
            minute = date.getUTCMinutes().toString(),
            formatedMinute = (minute.length === 1) ? ('0' + minute) : minute,
            second = date.getUTCSeconds().toString(),
            formatedSecond = (second.length === 1) ? ('0' + second) : second;
        return formatedDay + '/' + formatedMonth + '/' + year + ' ' + formatedHour + ':' + formatedMinute + ' ' + ampm;
    }

    formatStatus(status) {
        if (status === '401') {
            // Request sent, not approve yet
            return '';
        } if (status === '404') {
            // Request rejected
            return '';
        } if (status === '403') {
            // Request approved
            return '';
        } if (status === '405') {
            // Request merchant approved, waiting for OnePay approval
            return '';
        }
        if (status === '0') {
            return 'Successful';
        }

        return 'Failed';
    }

    selectRow() {
        this.result = {};
        if (this.selectedData.length > 0) {
            this.selectedData.forEach(row => {
              if (!this.result[row.currency]) {
                this.result[row.currency] = 0;
              }
              this.result[row.currency] += row.refundAmount? Number(row.refundAmount): 0;
            }); 
        }
        const lengthTotal = this.data.filter(x => this.canSelect(x)).length;
        const lengthSelect = this.selectedData.filter(x => this.canSelect(x)).length;
        if (lengthSelect !== 0 && lengthSelect === lengthTotal) {
            this.checkAll = true;
        } else {
            this.checkAll = false;
        }
    }

    onRowSelect(event: any) {
        this.result = {};
        if (this.selectedData.length > 0) {
            this.selectedData.forEach(row => {
              if (!this.result[row.currency]) {
                this.result[row.currency] = 0;
              }
              this.result[row.currency] += row.refundAmount? Number(row.refundAmount): 0;
            }); 
        }
        const lengthTotal = this.data.filter(x => this.canSelect(x)).length;
        const lengthSelect = this.selectedData.filter(x => this.canSelect(x)).length;
        if (lengthSelect !== 0 && lengthSelect === lengthTotal) {
            this.checkAll = true;
        } else {
            this.checkAll = false;
        }
    }

    onRowUnselect(event: any) {
        this.result = {};
        if (this.selectedData.length > 0) {
            this.selectedData.forEach(row => {
              if (!this.result[row.currency]) {
                this.result[row.currency] = 0;
              }
              this.result[row.currency] += row.refundAmount ? Number(row.refundAmount): 0;
            }); 
        }
        const lengthTotal = this.data.filter(x => this.canSelect(x)).length;
        const lengthSelect = this.selectedData.filter(x => this.canSelect(x)).length;
        if (lengthSelect !== 0 && lengthSelect === lengthTotal) {
            this.checkAll = true;
        } else {
            this.checkAll = false;
        }
    }

    disabledApprove() {
        return this.selectedData.length === 0 || !this.selectedData.every(e => e.status == '300');
    }

    disabledUpdate() {
        return this.selectedData.length === 0 || !this.selectedData.every(e => e.status == '320');
    }

    formatBatchError(data: any) {
        for (let index = 0; index < this.data.length; index++) {
            const element = this.data[index];
            if (element.refundId == data.refundId) {
                element.isError = true;
                break;
            }
        }
    }

    removeBatchSuccess(data: any) {
        for (let index = 0; index < this.data.length; index++) {
            const element = this.data[index];
            if (element.refundId == data.refundId) {
                this.data.splice(index, 1);
                break;
            }
        }
    }

    canSelect(data: any): boolean {
        return (data.status == '300' && (this.global.isActive('installment_approval_button') || this.global.isActive('installment_reject_button') || this.global.isActive('international_manual_button')))
            || (data.status == '320' && (this.global.isActive('international_update_success_button') || this.global.isActive('international_update_failed_button')))
            || (!data.actionType && (data.status == '300' || data.status == '320') && this.global.isActive('international_check_auto_button'))
    }

    selectAll(event: any) {
        if (this.checkAll) {
            this.selectedData = this.data.filter(x => this.canSelect(x));
        } else {
            this.selectedData = [];
        }
        this.selectRow();
    }

    onClear(){
        this.selectedData = [];
    }

    convertStatusType(status){
        let typeResult = '';
        if (status) {
          if (status == '300') {
            typeResult = "Waiting for OnePay's Approval";
          } else if (status == "320") {
            typeResult = "Waiting for OnePay's Update";
          } else {
            typeResult = status;
          }
        }
        return typeResult;
    }

}
