<div id="copy-rules-merchant">
  <div class="d-flex" style="margin-bottom: 28px">
    <div class="d-flex flex-column justify-content-around" style="padding-right: 30px">
      <label class="label-header" for="merchant-id" pTooltip="Chọn Merchant ID muốn copy bộ rule" >Merchant ID Copied</label>
      <div style="height: 10px"></div>
      <label class="label-header" for="merchant-group" pTooltip="Chọn Merchant Group muốn gắn cho các Merchant ID được chọn bên dưới">Merchant Group*</label>
    </div>
    <div class="d-flex flex-column justify-content-around" style="padding-right: 52px">
      <p-dropdown [options]="merchants" [(ngModel)]="merchant.merchantId" [filter]="true" optionLabel="merchantId"
        optionValue="merchantId" [style]="{width:'314px', height: '38px'}" placeholder="--All--"
        inputId="merchant-id"></p-dropdown>
      <div style="height: 10px"></div>
      <p-dropdown [options]="groups" [(ngModel)]="groupId" [filter]="true" optionLabel="groupName" optionValue="groupId"
        [style]="{width:'314px', height: '38px'}" placeholder="--All--" [showClear]="true"
        inputId="merchant-group">
        <ng-template let-option pTemplate="item">
          <div [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
            {{ option.groupName }}
          </div>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="d-flex flex-column justify-content-around" style="padding-right: 20px">
      <label class="label-header" for="merchant-info" pTooltip="Tìm Merchant muốn gán bộ rule">Merchant Info Search</label>
      <div style="height: 10px"></div>
      <label class="label-header" for="merchant-group-search">Merchant Group Search</label>
    </div>
    <form (keydown.enter)="refresh()">
      <div class="d-flex flex-column justify-content-around" style="padding-right: 52px">
        <input [(ngModel)]="params.text" type="text" pInputText
          placeholder="Press Enter to Search by Merchant ID or Merchant Name" id="merchant-info"
          style="width:365px; height: 38px">
        <div style="height: 10px"></div>
        <p-dropdown (onChange)="refresh()" [options]="groups" [(ngModel)]="params.groupId" [filter]="true"
          optionLabel="groupName" optionValue="groupId" [style]="{width:'365px', height: '38px'}" placeholder="--All--"
          [showClear]="true" inputId="merchant-group-search">
          <ng-template let-option pTemplate="item">
            <div [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
              {{ option.groupName }}
            </div>
          </ng-template>
        </p-dropdown>
      </div>
    </form>
    <div class="d-flex justify-content-end" style="flex: 1;height: 38px;">
      <div style="height: 38px" class="d-flex align-items-center">
        <button pButton style="height: 29px; margin-right: 10px" type="button" (click)="submit()"
          class="p-button-success" [disabled]="!selection || !selection.length || !groupId">Save</button>
        <button pButton style="height: 29px" class="p-button-secondary" type="button"
          (click)="onCancel.emit()">Cancel</button>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-between" style="margin-bottom: 17px">
    <span class="label-table"></span>
    <span class="label-table">* Tick ✔ to apply Merchant ID for current rule</span>
  </div>
  <div class="d-flex justify-content-between" style="margin-bottom: 17px">
    <span class="label-table"></span>
    <span class="label-table">{{selection.length}} Merchant ID đã chọn/{{filteredMerchants.length - selection.length}} Merchant ID chưa
      chọn/{{filteredMerchants.length}} total Merchant ID</span>
  </div>
  <div style="display:flex; flex-direction: row; flex: 1">
    <div style="width: 34%; border-right: #D9D9DC 0.5px solid">
      <p-table [value]="rules" [totalRecords]="rules.length" [paginator]="true" [rows]="10"
        [showCurrentPageReport]="true" [scrollable]="true" scrollHeight="calc(100vh - 247px)"
        currentPageReportTemplate="{first} - {last} in {totalRecords} Rule" [rowsPerPageOptions]="[10,25,50]">
        <ng-template pTemplate="header">
          <tr>
            <th class="column-num" scope="col">No</th>
            <th scope="col">Fraud Rule ID</th>
            <th scope="col">Fraud Type</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rule let-rowIndex="rowIndex">
          <tr>
            <td class="column-num optomize-padding">{{rowIndex + 1}}</td>
            <td class="optomize-padding">{{rule.name}}</td>
            <td class="optomize-padding">{{rule.typeName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: 66%" *ngIf="merchant">
      <p-table *ngIf="merchant.merchantId" [value]="filteredMerchants" selectionMode="multiple"
        [(selection)]="selection" dataKey="merchantId" [scrollable]="true" scrollHeight="calc(100vh - 385px)">
        <ng-template pTemplate="header">
          <tr>
            <th class="column-checkbox" scope="col">
              <p-checkbox (onChange)="onChange($event)" [(ngModel)]="selectAll" [binary]="true"></p-checkbox>
              &nbsp;No
            </th>
            <th scope="col">Merchant ID</th>
            <th scope="col">Merchant Name</th>
            <th scope="col">Merchant Group</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-merchant let-rowIndex="rowIndex">
          <tr [ngClass]="{'text-muted': merchant.requestApproval != null || merchant.numOfRule > 0}">
            <td class="column-checkbox optomize-padding" style="color: inherit">
              <p-tableCheckbox *ngIf="merchant.requestApproval == null && merchant.numOfRule == 0"
                [value]="merchant"></p-tableCheckbox>&nbsp;{{rowIndex + 1}}
            </td>
            <td class="optomize-padding" style="color: inherit">{{merchant.merchantId}}</td>
            <td class="optomize-padding" style="color: inherit">{{merchant.merchantName}}</td>
            <td class="optomize-padding" style="color: inherit">{{merchant.groupName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '589px'}"></p-confirmDialog>
