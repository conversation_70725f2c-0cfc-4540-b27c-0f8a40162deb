<div id="review-rules-detail-wrapper">
  <div class="d-flex flex-row" style="padding-bottom: 15px; border-bottom: #D9D9DC 0.5px solid;">
    <button type="button" class="btn btn-primary update-button-common-fdm" style="margin-right: 10px;" [disabled]="!merchant.merchantId || merchant.requestApproval?.includes('Waiting for Approval')"
      (click)="updateMerchant()">Update</button>
    <button type="button" class="btn btn-warning" style="margin-right: 10px;"
      [disabled]="!configuredRules || !configuredRules?.size" (click)="onMultiPaste.emit()"
      [pTooltip]="(configuredRules && configuredRules?.size) ? 'Nhấn để copy bộ rule của Merchant ' + merchant.merchantId + ' gắn cho nhiều Merchant ID khác' : null">
      Multi Paste
    </button>
  </div>
  <div id="mcc-info-wrapper">
    <table>
      <tr>
        <td>
          <div class="form-group">
            <span class="input-group">
              <input type="text" id="merchantId" [(ngModel)]="merchant.merchantId" #MerchantId="ngModel" [disabled]="true"
                name="merchantId" maxlength="400" tabindex="1" autocomplete="off" autocapitalize="off"
                class="form-control" style="width:100%; height: 38px" pInputText readonly>
              <label class="label-custom" for="merchantId">Merchant ID</label>
            </span>
          </div>
        </td>
        <td style="padding: 0 17px 0 9px">
          <div style="display: flex; justify-content: space-between;">
            <i [pTooltip]="'Nhấn để copy toàn bộ luật rủi ro đang được cấu hình cho Merchant ' + merchant.merchantId"
              *ngIf="configuredRules?.size" (click)="copy()" class="pi pi-copy cursor-pointer"
              style="font-size: 20px"></i>
            <svg *ngIf="!configuredRules?.size && merchant?.merchantId" (click)="paste()"
              xmlns="http://www.w3.org/2000/svg" height="20px" class="cursor-pointer"
              viewBox="0 0 448 512"><!--! Font Awesome Free 6.4.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
              <path
                d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z" />
            </svg>
          </div>
        </td>
        <td rowspan="3">
          <div class="form-group">
            <span class="input-group" style="height: 158px;">
              <textarea [(ngModel)]="merchant.ruleNote" style="width: 100%; height: 100%" pInputTextarea
                id="ruleNote"></textarea>
              <label class="label-custom" for="ruleNote">Rule Note</label>
            </span>
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <div class="form-group">
            <span class="input-group">
              <p-dropdown appendTo="body" [filter]="true" filterBy="mccDescription" [options]="mccs"
                optionLabel="mccDescription" optionValue="mccId" [(ngModel)]="merchant.categoryCode" placeholder=" "
                dropdownIcon="pi pi-angle-down" name="internalMCC" [disabled]="true" [style]="{width:'100%', height:'38px'}"></p-dropdown>
              <label class="label-custom" for="internalMCC">Internal MCC</label>
            </span>
          </div>
        </td>
        <td style="padding: 0 17px 0 9px">
          <div style="display: flex; justify-content: space-between;">
            <img id="wH3Qfv" style="cursor:pointer;" class="z-image" alt="Default value for MCC"
              pTooltip="Nhấn để thiết lập giá trị mặc định [5969] cho cấu hình Internal MCC của Merchant hiện tại."
              src="assets/img/ResetDefaultValue.png" (click)="setDefaultInternalMCC()">
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <div class="form-group">
            <span class="input-group">
              <p-dropdown appendTo="body" [filter]="true" filterBy="groupName" [options]="groups"
                optionLabel="groupName" optionValue="groupId" [(ngModel)]="merchant.groupId" placeholder=" "
                dropdownIcon="pi pi-angle-down" name="merchantGroup" id="merchantGroup"
                [style]="{width:'100%', height:'38px'}">
                <ng-template let-option pTemplate="item">
                  <div [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
                    {{ option.groupName }}
                  </div>
                </ng-template>
              </p-dropdown>
              <label class="label-custom" for="merchantGroup">Merchant Group</label>
            </span>
          </div>
        </td>
      </tr>
      <tr>
        <td style="vertical-align: top;">
          <div style="display: flex; align-items: center; width: 100%;">
            <input (input)="refresh()" [(ngModel)]="text"
              placeholder="Press Enter to Search by Fraud Type; Fraud Rule ID or Description" type="text"
              style="flex: 1; margin-right: 5px; height: 34px;" pInputText maxlength="100" />
            <p-dropdown (onChange)="refresh()" [options]="fraudTypes" [filter]="true" filterBy="name" optionLabel="name"
              [(ngModel)]="selectedFraudType" [showClear]="true" [style]="{width:'169px', height:'34px', 'flex-shrink':'0'}" placeholder="--All--">
            </p-dropdown>
          </div>
        </td>
        <td></td>
        <td>
          <div style="font-weight: normal;">
            <p *ngIf="merchant.noteUpdate">{{merchant.noteUpdate}}</p>
            <p *ngIf="merchant.ruleUpdate">{{merchant.ruleUpdate}}</p>
            <p *ngIf="merchant.requestApproval" class="text-red">{{merchant.requestApproval}}</p>
            <p *ngIf="merchant.updatedBy">Updated by: {{merchant.updatedBy}}</p>
            <p *ngIf="merchant.updatedOn">Updated time: {{merchant.updatedOn | date: 'dd/MM/yyyy hh:mm:ss'}}</p>
          </div>
        </td>
      </tr>
    </table>
  </div>
  <div class="d-flex justify-content-between" style="margin-bottom: 17px">
    <span class="label-table">{{selectedSource.length}} Merchant ID đã chọn/{{source.length - selectedSource.length}} Merchant ID chưa chọn/{{source.length}} total Merchant ID</span>
    <span class="label-table">{{selectedTarget.length}} Merchant ID đã chọn/{{target.length - selectedTarget.length}} Merchant ID chưa chọn/{{target.length}} total Merchant ID</span>
  </div>
  <div style="display: flex; justify-content: space-between;">
    <div style="width: calc(50% - 3.5px)">
      <p-table #sourceTable [(selection)]="selectedSource" dataKey="id" [value]="source" [scrollable]="true" [resizableColumns]="true"
        selectionMode="multiple" scrollHeight="calc(100vh - 489px)">
        <ng-template pTemplate="colgroup">
          <colgroup>
            <col style="width:65px" />
            <col style="width:170px" />
            <col style="width:170px" />
          </colgroup>
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th pResizableColumn>
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>&nbsp;No
            </th>
            <th pResizableColumn>Fraud Rule ID</th>
            <th pResizableColumn>Fraud Type</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rule let-rowIndex="rowIndex">
          <tr>
            <td class="column-checkbox optomize-padding">
              <p-tableCheckbox [value]="rule"></p-tableCheckbox>&nbsp;{{rowIndex + 1}}
            </td>
            <td class="optomize-padding">
              <span class="clickable-text" (click)="onConfigOneRule.emit(rule)">{{rule.name}}</span>
            </td>
            <td class="optomize-padding">{{rule.typeName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: calc(50% - 3.5px)">
      <p-table #targetTable [(selection)]="selectedTarget" dataKey="id" [value]="target" [scrollable]="true" [resizableColumns]="true"
        selectionMode="multiple" scrollHeight="calc(100vh - 489px)">
        <ng-template pTemplate="colgroup">
          <colgroup>
            <col style="width:65px" />
            <col style="width:170px" />
            <col style="width:170px" />
          </colgroup>
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th pResizableColumn>
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>&nbsp;No
            </th>
            <th pResizableColumn>Fraud Rule ID</th>
            <th pResizableColumn>Fraud Type</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rule let-rowIndex="rowIndex">
          <tr>
            <td class="optomize-padding">
              <p-tableCheckbox [value]="rule"></p-tableCheckbox>&nbsp;{{rowIndex + 1}}
            </td>
            <td class="optomize-padding"
              [pTooltip]="'- Fraud Rule ID: ' + rule.name + '\n- Description: ' + rule.desc + '\n- Parameters: ' + rule.params"
              tooltipPosition="bottom">
              <span class="clickable-text" (click)="onConfigOneRule.emit(rule)">{{rule.name}}</span>
            </td>
            <td class="optomize-padding">{{rule.typeName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '50vw'}"></p-confirmDialog>
