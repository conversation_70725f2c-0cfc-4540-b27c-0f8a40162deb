<div id="review-rules-config">
  <h1>Config Review Rule</h1>
  <div style="display:flex; flex-direction: row; flex: 1">
    <div style="width: 33%; border-right: #D9D9DC 0.5px solid">
      <form
        (keydown.enter)="params.text == '' ? this.page = 1 : this.page = this.page; this.params.first = 0; refresh();">
        <div id="list-url-header">
          <div pTooltip="Refresh the Merchant list"
            (click)="this.toastr.success('Đồng bộ thành công!', 'Success'); refresh()" id="sync-button"
            style='background-image: url("./assets/img/sync.svg");'></div>
          <p-dropdown (onChange)="refresh()" [options]="groups" [filter]="true" optionLabel="groupName"
            optionValue="groupId" [(ngModel)]="params.groupId" [style]="{width:'151px', height: '100%'}"
            placeholder="--All--" [showClear]="true">
            <ng-template let-option pTemplate="item">
              <div [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
                {{ option.groupName }}
              </div>
            </ng-template>
          </p-dropdown>
          <input [(ngModel)]="params.text" style="flex: 1" type="text" pInputText maxlength="100"
            placeholder="Press Enter to Search by Merchant ID; Merchant Name" />
        </div>
      </form>
      <p-table #dt [value]="value" [paginator]="true" [totalRecords]="totalRecords" [showPageLinks]="false"
        [showFirstLastIcon]="false" [lazy]="true" [scrollable]="true" scrollHeight="calc(100vh - 247px)" [rows]="50"
        paginatorPosition="bottom" selectionMode="single" [(selection)]="selection" dataKey="merchantId"
        (onLazyLoad)="onLazyLoad($event)" (onPage)="paginator($event)" (selectionChange)="onSelectionChange($event)">
        <ng-template pTemplate="paginatorright" let-state>
          <div class="ui-helper-clearfix clearfix " id="paging-controler">
            <i style="float:right; margin-top: 7px; font-size: 1.5rem; margin-left: 0.5rem; position: relative;"
              class="pi pi-angle-double-right" pTooltip="last page" (click)="goToLastPage()"></i>
            <div class="total-item" style="float:right; margin-right: -30px; margin-top: -5px;">
              <table-paginator [state]="state" [totalRecords]="totalRecords"></table-paginator>
            </div>
            <i style="float:right; margin-top: 7px; font-size: 1.5rem; margin-right: -1rem;"
              class="pi pi-angle-double-left" pTooltip="first page" (click)="goToFirstPage()"></i>
            <div style="float:right; font-size: 14px">
              <div class="form-group">
                <span class="input-group">
                  <p-dropdown appendTo="body" [options]="pageList" [style]="{'width':'85%'}" [(ngModel)]="page"
                    [virtualScroll]="true" itemSize="10" dropdownIcon="pi pi-angle-down" #pageT="ngModel" name="page"
                    [disabled]="pageList.length < 2" (onChange)="onChangePage()">
                  </p-dropdown>
                  <label class="label-custom" for="">Page</label>
                </span>
              </div>
            </div>
          </div>
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 60px" scope="col">No</th>
            <th scope="col">Merchant ID</th>
            <th scope="col">Merchant Name</th>
            <th scope="col">Merchant Group</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-merchant>
          <tr [pSelectableRow]="merchant" [ngClass]="log1(merchant)" (click)="refreshDetail(merchant)">
            <td style="width: 60px;" class="optomize-padding" [ngStyle]="changeColor(merchant.numOfRule)">{{merchant.rowNum}}</td>
            <td class="optomize-padding" [ngStyle]="changeColor(merchant.numOfRule)">{{merchant.merchantId}}</td>
            <td class="optomize-padding" [ngStyle]="changeColor(merchant.numOfRule)">{{merchant.merchantName}}</td>
            <td class="optomize-padding" [ngStyle]="changeColor(merchant.numOfRule)" [pTooltip]="merchant.managedByUserName">
              {{merchant.groupName}}
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: 67%; padding: 0 7px;">
      <app-config-review-rule-detail *ngIf="ruleMapId?.size" [merchant]="selection" [groups]="groups"
        [ruleMapId]="ruleMapId" (onConfigOneRule)="showDialog($event)" (onMultiPaste)="visibleCopy = true"
        (onRefresh)="refresh()" [configuredRules]="configuredRules"></app-config-review-rule-detail>
    </div>
  </div>
</div>

<p-dialog header="Config One Review Rule For Multiple Merchant IDs Simultaneously" [(visible)]="visible" [modal]="true" *ngIf="listAllMerchants && selectedRule"
  [style]="{ width: '80vw' }" [draggable]="false" [resizable]="false">
  <app-config-one-rule-multiple-merchant [rules]="rules" [selectedRule]="selectedRule" [groups]="groups" [listAllMerchants]="listAllMerchants"
    (onCancel)="visible = false && refresh()"></app-config-one-rule-multiple-merchant>
</p-dialog>

<p-dialog header="Copy Merchant ID's Review rules to multiple Merchant ID" [(visible)]="visibleCopy" [modal]="true" *ngIf="listAllMerchants"
  [style]="{ width: '80vw' }" [draggable]="false" [resizable]="false">
  <app-copy-rules-merchant [merchants]="merchants" [merchant]="selection" [groups]="groups"
    (onCancel)="visibleCopy = false && refresh()" [ruleMapId]="ruleMapId" [listAllMerchants]="listAllMerchants"
    [configuredRules]="configuredRules"></app-copy-rules-merchant>
</p-dialog>
