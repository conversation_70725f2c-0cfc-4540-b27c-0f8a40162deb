import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CreateBlockRuleService } from '@service/fdm/domestic/create-block-rule.service';
import { ReviewRule } from 'app/model/fdm/config-review-rule';
import { FraudType } from 'app/model/fdm/fraud-type';
import { MessageService, ConfirmationService } from 'primeng/api';
import { Observable } from 'rxjs';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-create-block-rule-detail',
  templateUrl: './create-block-rule-detail.component.html',
  styleUrls: ['./create-block-rule-detail.component.scss'],
  providers: [
    MessageService,
    ConfirmationService
  ]
})
export class CreateBlockRuleDetailComponent implements OnInit {

  constructor(private createBlockRuleService: CreateBlockRuleService, private confirmationService: ConfirmationService, private messageService: MessageService, private toast: ToastrService) { }

  message: string;

  private _rule: ReviewRule;

  public get rule(): ReviewRule {
    return this._rule;
  }

  @Input() set rule(rule: ReviewRule) {
    this._rule = { ...rule || this.initRule() };
  }

  @Input() fraudTypes: FraudType[];

  @Output() onRefresh = new EventEmitter<ReviewRule>();
  @Output() onCopy = new EventEmitter<ReviewRule>();
  @Output() onReset = new EventEmitter<void>();

  ngOnInit(): void {
    this._rule = this.initRule();
  }

  initRule(): ReviewRule {
    return {
      id: 0,
      name: '',
      desc: '',
      typeName: '',
      typeId: 0,
      createdOn: '',
      createdBy: ''
    }
  }
  disabledButtom() {
    if (this.rule.desc == undefined || this.rule.desc.length < 1 || this.rule.name.length < 1 || this.rule.typeId < 1) {
      return true;
    }

    return false;
  }

  submit() {
    if (this.disabledButtom()) {
      return;
    }

    this.message = '';
    let header = this.rule.id ? 'Update Block Rule' : 'Add new Block Rule';
    let detail = this.rule.id ? `You've just successfully updated Scan Rule ${this.rule.name} into the List.` : `You've just successfully added Scan Rule ${this.rule.name} into the List.`
    this.confirmationService.confirm({
      rejectButtonStyleClass: "p-button p-button-secondary",
      message: `Are you sure to save all the current information of Block Rule ${this.rule.name}?`,
      header: header,
      acceptLabel: 'Save',
      rejectLabel: 'Cancel',
      dismissableMask: true,
      accept: () => {
        let obser: Observable<void>
        let params = {
          "description": this.rule.desc,
          "id": this.rule.id,
          "paramters": this.rule.params,
          "ruleName": this.rule.name,
          "ruleType": this.rule.typeId,
          "channels": ["ND"]
        };
        if (this.rule.id) {
          obser = this.createBlockRuleService.updateBlockRule(params);
        } else {
          obser = this.createBlockRuleService.createBlockRule(params);
        }
        obser.subscribe((res: any) => {
          if (res  && res.message != 'Success') {
            this.toast.error(res.message);
            return;
          }
          this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: detail });
          this.onRefresh.emit(this.rule);
        }, (err: HttpErrorResponse) => {
          console.log(err.error);
          if (err.error instanceof Array) {
            for (let e of err.error) {
              this.message += ', ' + e.message;
            }
            this.message = this.message.substring(2);
          } else {
            this.message = err.error.message;
          }
        })
      }
    });
  }

  deleteRule() {
    if (!this.rule.id) {
      return;
    }
    this.confirmationService.confirm({
      rejectButtonStyleClass: "p-button p-button-secondary",
      message: `Are you sure to remove Block Rule ${this.rule.name} out of the List?`,
      header: 'Delete Block Rule',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      dismissableMask: true,
      accept: () => {
        this.createBlockRuleService.deleteBlockRule(this.rule.id).subscribe(() => {
          this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: `You've just successfully removed Scan Rule ${this.rule.name} out of the List.` });
          this.onRefresh.emit(this.rule);
        }, (err: HttpErrorResponse) => {
          console.log(err.error);
          this.message = err.error.message;
        })
      }
    });
  }

}
