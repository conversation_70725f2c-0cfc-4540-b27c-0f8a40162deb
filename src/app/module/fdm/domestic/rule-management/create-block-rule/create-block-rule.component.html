<div id="create-block-rule">
  <h1>Create Block Rule</h1>
  <div style="display:flex; flex-direction: row; flex: 1">
    <div style="width: 50%; border-right: #D9D9DC 0.5px solid">
      <form (keydown.enter)="refresh()">
        <div id="create-block-rule-header">
          <div (click)="resetSearch()" id="sync-button" style='background-image: url("./assets/img/sync.svg");'
            pTooltip="Refresh the Merchant list">
          </div>
          <p-dropdown (onChange)="refresh()" [options]="fraudTypes" [(ngModel)]="selectedFraudType"
            [virtualScroll]="true" itemSize="30" [filter]="true" filterBy="name" optionLabel="name" optionValue="id"
             [style]="{'min-width':'320px', height: '100%'}" placeholder="--All--" [showClear]="true">
            <ng-template let-fraud pTemplate="item">
              <div [pTooltip]="fraud.desc" tooltipPosition="left">
                {{fraud.name}}
              </div>
            </ng-template>
          </p-dropdown>
          <input [(ngModel)]="text" style="margin-right:auto; width: 480px" type="text" pInputText
            placeholder=" Press Enter to Search by Fraud Type; Fraud Rule ID or Description" maxlength="100" />
        </div>
      </form>
      <p-table [value]="value" [lazy]="true" (onLazyLoad)="onLazyLoad($event)" [totalRecords]="totalRecords"
        [paginator]="true" [rows]="rows" [showCurrentPageReport]="true" [scrollable]="true" [(first)]="first"
        scrollHeight="calc(100vh - 247px)" currentPageReportTemplate="{first} - {last} in {totalRecords} Block Rules"
        [rowsPerPageOptions]="[10,25,50]" selectionMode="single" [(selection)]="selection" dataKey="id">
        <ng-template pTemplate="header">
          <tr>
            <th scope="col" style="width: 45px">No</th>
            <th scope="col" style="width: 35%">Fraud Rule ID</th>
            <th scope="col">Fraud Type</th>
            <th scope="col">Description</th>
            <th scope="col">Parameters</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rule>
          <tr (click)="resetMessage(rule)" [pSelectableRow]="rule">
            <td style="width: 45px;" class="optomize-padding">
              <img *ngIf="rule.isToday" src="./assets/img/new_icon.png" alt="this block rule was created today">
              &nbsp;
              {{rule.rowNum}}
            </td>
            <td class="optomize-padding" style="width: 35%; white-space: nowrap;overflow: hidden; color: blue;" (click)="showDialog(rule)">{{rule.name}}</td>
            <td class="optomize-padding" style="white-space: nowrap;overflow: hidden;">{{rule.typeName}}</td>
            <td class="optomize-padding">{{rule.desc}}</td>
            <td class="optomize-padding" style="white-space: nowrap;overflow: hidden;">{{rule.params}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: 50%; padding: 0 9px;">
      <app-create-block-rule-detail [rule]="selection" [fraudTypes]="fraudTypes" (onReset)="reset()"
        (onRefresh)="refresh($event); refreshRules()" (onCopy)="onCopy()"></app-create-block-rule-detail>
    </div>
  </div>
</div>

<p-dialog header="Config One Block Rule For Multiple Merchant IDs Simultaneously" [(visible)]="visible" [modal]="true"
  [style]="{ width: '80vw' }" [draggable]="false" [resizable]="false">
  <app-config-one-block-rule-multiple-merchant [rules]="rules" [selectedRule]="selectedRule" [groups]="groups"
    (onCancel)="visible = false"></app-config-one-block-rule-multiple-merchant>
</p-dialog>
