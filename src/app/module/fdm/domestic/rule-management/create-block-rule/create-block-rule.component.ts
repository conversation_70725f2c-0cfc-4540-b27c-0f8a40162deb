import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonService } from '@service/fdm/common.service';
import { ConfigBlockRuleService } from '@service/fdm/domestic/config-block-rule.service';
import { CreateBlockRuleService } from '@service/fdm/domestic/create-block-rule.service';
import { FraudType } from 'app/model/fdm/fraud-type';
import { Group } from 'app/model/group';
import { Rule } from '../config-review-rule/config-review-rule.component';
import { CreateBlockRuleDetailComponent } from './create-block-rule-detail/create-block-rule-detail.component';
import { TransactionScreeningService } from '@service/fdm/domestic/transaction-screening.service';
import { ReviewRule } from 'app/model/fdm/config-review-rule';

@Component({
  selector: 'app-create-block-rule',
  templateUrl: './create-block-rule.component.html',
  styleUrls: ['./create-block-rule.component.scss']
})
export class CreateBlockRuleComponent implements OnInit {

  @ViewChild(CreateBlockRuleDetailComponent, { static: true }) detailForm: CreateBlockRuleDetailComponent;

  constructor(private commonService: CommonService, private createBlockRuleService: CreateBlockRuleService, private datePipe: DatePipe, private configBlockRuleService: ConfigBlockRuleService, private transactionScreeningService: TransactionScreeningService) { }

  fraudTypes: FraudType[];

  selectedFraudType: number;

  value: ReviewRule[];

  selection: ReviewRule;

  text: string;

  page: number;
  rows = 10;
  totalRecords: number;
  first: number = 0;

  visible: boolean;
  selectedRule: Rule;
  groups: Group[];
  rules: Rule[];

  ngOnInit(): void {
    this.commonService.listBlockFraudType(['id', 'name', 'desc']).subscribe(fraudTypes => this.fraudTypes = fraudTypes);
    this.transactionScreeningService.getMerchantGroupList().subscribe(data => {
      this.groups = data.data.map((item) => ({
        groupId: parseInt(item.optionValue),
        groupName: item.optionLabel,
        priority: item.priority
      }));
    });

    this.refreshRules();
  }

  refreshRules(): void {
    this.configBlockRuleService.listRule().subscribe(rules => {
      this.rules = rules;
    });
  }

  onLazyLoad(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    this.refresh();
  }

  refresh(rule?: Rule) {
    if (rule) {
      this.text = rule.name;
      this.first = 0;
    }
    this.createBlockRuleService.searchBlockRules({
      text: this.text || '',
      fraudType: this.selectedFraudType || '',
      page: this.first / this.rows,
      pageSize: this.rows
    }).subscribe(res => {
      this.value = res.data;
      const currentDate: Date = new Date();
      this.value.forEach(rule => {
        const createdOnDate: Date = new Date(rule.createdOn);
        rule.isToday = createdOnDate.getDate() === currentDate.getDate() &&
          createdOnDate.getMonth() === currentDate.getMonth() &&
          createdOnDate.getFullYear() === currentDate.getFullYear();

        rule.createdOn = this.datePipe.transform(rule.createdOn, 'dd/MM/yyyy HH:mm:ss');
        if (rule.updatedOn) {
          rule.updatedOn = this.datePipe.transform(rule.updatedOn, 'dd/MM/yyy HH:mm:ss');
        }
      })
      this.totalRecords = res.total;
      if (rule) {
        this.selection = this.value[0];
      }
    });
  }

  refreshDetail(rule?: Rule) {
    this.createBlockRuleService.blockRulesByMerchantId(rule.id.toString()
    ).subscribe(res => {
      if (res) {
        this.selection = res[0];
      }
    });
  }

  onCopy() {
    this.selection = {
      id: 0,
      name: this.selection.name,
      typeId: this.selection.typeId,
      typeName: this.selection.typeName,
      params: this.selection.params,
      desc: this.selection.desc,
      createdBy: null,
      createdOn: null
    };
  }

  resetSearch() {
    this.text = '';
    this.selectedFraudType = null;
    this.first = 0;
    this.refresh();
  }

  resetMessage(rule) {
    this.detailForm.message = '';
    this.refreshDetail(rule);
  }

  reset() {
    this.selection = undefined;
    // Reset về trạng thái ban đầu hoàn toàn
    this.detailForm.rule = this.detailForm.initRule();
    // Clear message nếu có
    this.detailForm.message = '';
  }

  showDialog(rule: Rule) {
    this.selectedRule = Object.assign({}, rule);  // Assign object mới để force event setter
    this.visible = true;
  }
}
