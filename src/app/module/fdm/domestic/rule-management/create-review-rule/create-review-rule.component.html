<div id="create-review-rule">
  <h1>Create Review Rule</h1>
  <div style="display:flex; flex-direction: row; flex: 1">
    <div style="width: 50%; border-right: #D9D9DC 0.5px solid">
      <form (keydown.enter)="refresh()">
        <div id="create-review-rule-header">
          <div (click)="resetSearch()" id="sync-button" style='background-image: url("./assets/img/sync.svg");'
            pTooltip="Refresh the Merchant list">
          </div>
          <p-dropdown (onChange)="refresh()" [options]="fraudTypes" [(ngModel)]="selectedFraudType"
            [virtualScroll]="true" itemSize="30" [filter]="true" filterBy="name" optionLabel="name"
            [style]="{'min-width':'320px', height: '100%'}" placeholder="--All--" [showClear]="true">
            <ng-template let-fraud pTemplate="item">
              <div [pTooltip]="fraud.desc" tooltipPosition="left">
                {{fraud.name}}
              </div>
            </ng-template>
          </p-dropdown>
          <input [(ngModel)]="text" style="margin-right:auto; width: 480px" type="text" pInputText
            placeholder=" Press Enter to Search by Fraud Type; Fraud Rule ID or Description"  maxlength="100"/>
        </div>
      </form>
      <p-table [value]="value" [lazy]="true" (onLazyLoad)="onLazyLoad($event)" [totalRecords]="totalRecords"
        [paginator]="true" [rows]="rows" [showCurrentPageReport]="true" [scrollable]="true" [(first)]="first"
        scrollHeight="calc(100vh - 247px)" currentPageReportTemplate="{first} - {last} in {totalRecords} Review Rules"
        [rowsPerPageOptions]="[10,25,50]" selectionMode="single" [(selection)]="selection" dataKey="id">
        <ng-template pTemplate="header">
          <tr>
            <th scope="col" style="width: 45px">No</th>
            <th scope="col" style="width: 35%">Fraud Rule ID</th>
            <th scope="col">Fraud Type</th>
            <th scope="col">Description</th>
            <th scope="col">Parameters</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rule>
          <tr (click)="resetMessage(rule)" [pSelectableRow]="rule">
            <td class="optomize-padding" style="width: 45px;">{{rule.rowNum}}</td>
            <td class="optomize-padding" style="width: 35%;white-space: nowrap;overflow: hidden; color: blue;" (click)="showDialog(rule)">{{rule.name}}</td>
            <td class="optomize-padding" style="white-space: nowrap;overflow: hidden;">{{rule.typeName}}</td>
            <td class="optomize-padding">{{rule.desc}}</td>
            <td class="optomize-padding" style="white-space: nowrap;overflow: hidden;">{{rule.params}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: 50%; padding: 0 9px;">
      <app-create-review-rule-detail [rule]="selection" [fraudTypes]="fraudTypes" (onReset)="reset()"
        (onRefresh)="refresh($event); refreshRules()" (onCopy)="onCopy()"></app-create-review-rule-detail>
    </div>
  </div>
</div>

<p-dialog header="Config One Review Rule For Multiple Merchant IDs Simultaneously" [(visible)]="visible" [modal]="true"
  [style]="{ width: '80vw' }" [draggable]="false" [resizable]="false">
  <app-config-one-rule-multiple-merchant [rules]="rules" [selectedRule]="selectedRule" [groups]="groups"
    (onCancel)="visible = false"></app-config-one-rule-multiple-merchant>
</p-dialog>
