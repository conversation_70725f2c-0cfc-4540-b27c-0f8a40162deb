<div id="create-review-rule-detail-wrapper">
  <div class="d-flex flex-row" style="padding-bottom: 15px; border-bottom: #D9D9DC 0.5px solid;">
    <button (click)="onReset.emit()" type="button" class="btn btn-dark reset-button-common-fdm"
      style="width: 55px; height: 34px; margin-right: 10px;" pTooltip="Click to reset all content below">Reset</button>
    <button (click)="submit()" type="button" class="btn btn-primary text-white update-button-common-fdm"
      style="width: 74px; margin-right: 10px;" [disabled]="disabledButtom()" [pTooltip]="rule.id ? 'Click to update the current Review Rule' : 'Click to create a new Review Rule'">
      {{rule.id ? 'Update' : 'Add New'}}
    </button>
    <button (click)="onCopy.emit()" [disabled]="!rule.id" type="button" class="btn btn-primary2 cancel-button-common-fdm"
      style="width: 65px; margin-right: 9px;" pTooltip="Click to copy all configs of this current Review Rule">Copy</button>
    <button (click)="deleteRule()" [disabled]="!rule.id" type="button" class="btn btn-error cancel-button-common-fdm"
      style="width: 65px; margin-right: 9px;" pTooltip="Click to delete this Review Rule">Delete</button>
    <span class="d-inline-flex align-items-center"
      style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color:red">
      {{message}}
    </span>
  </div>
  <div style="padding: 5px 6px 0 0">
    <table>
      <tr>
        <td>Fraud Rule ID*</td>
        <td>
          <input [(ngModel)]="rule.name" type="text" style="width:100%;" maxlength="50" pInputText [disabled]="rule.id" [ngClass]="{'empty-input': !rule.name}"/>
        </td>
      </tr>
      <tr>
        <td>
          Fraud Type*
        </td>
        <td>
          <p-dropdown [options]="fraudTypes" [(ngModel)]="rule.typeId" [style]="{'width':'100%'}" [virtualScroll]="true"
            itemSize="30" [filter]="true" filterBy="name" optionLabel="name" optionValue="id" [disabled]="rule.id"
            placeholder="Choose Fraud Type" [ngClass]="{'empty-dropdown': rule.typeId < 1}">
            <ng-template let-fraud pTemplate="item">
              <div [pTooltip]="fraud.desc" tooltipPosition="left">
                {{fraud.name}}
              </div>
            </ng-template>
          </p-dropdown>
        </td>
      </tr>
      <tr *ngIf="rule.typeId">
        <td>
        </td>
        <td [innerHTML]="rule.typeId | desc:fraudTypes"></td>
      </tr>
      <tr>
        <td>Parameters</td>
        <td>
          <input [(ngModel)]="rule.params" type="text" style="width:100%;" pInputText />
        </td>
      </tr>
      <tr>
        <td>Description*</td>
        <td>
          <input [(ngModel)]="rule.desc" type="text" style="width:100%;" maxlength="200" pInputText [ngClass]="{'empty-input': !rule.desc}"/>
        </td>
      </tr>
      <tr>
        <td>Created Time</td>
        <td>
          <input [(ngModel)]="rule.createdOn" disabled type="text" style="width:100%;" pInputText />
        </td>
      </tr>
      <tr>
        <td>Created By</td>
        <td>
          <input [(ngModel)]="rule.createdBy" disabled type="text" style="width:100%;" pInputText />
        </td>
      </tr>
      <ng-template [ngIf]="rule.updatedOn">
        <tr>
          <td>Updated Time</td>
          <td>
            <input [(ngModel)]="rule.updatedOn" disabled type="text" style="width:100%;" pInputText />
          </td>
        </tr>
        <tr>
          <td>Updated By</td>
          <td>
            <input [(ngModel)]="rule.updatedBy" disabled type="text" style="width:100%;" pInputText />
          </td>
        </tr>
      </ng-template>
    </table>
  </div>
</div>

<p-toast></p-toast>
<p-confirmDialog [style]="{width: '589px'}"></p-confirmDialog>
