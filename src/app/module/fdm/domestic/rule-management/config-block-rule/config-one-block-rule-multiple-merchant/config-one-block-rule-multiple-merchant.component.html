<div id="config-one-rule-multiple-merchant">
  <div class="d-flex" style="margin-bottom: 28px">
    <div class="d-flex flex-column justify-content-around" style="padding-right: 30px">
      <label class="label-header" for="rule-id">Block Rule ID</label>
      <div style="height: 10px"></div>
      <label class="label-header" for="fraud-type">Fraud Type</label>
    </div>
    <div class="d-flex flex-column justify-content-around" style="padding-right: 52px">
      <p-dropdown [options]="rules" [(ngModel)]="selectedRule" [filter]="true" optionLabel="name"
        [style]="{width:'314px', height: '38px'}" placeholder="--All--" inputId="rule-id"></p-dropdown>
      <div style="height: 10px"></div>
      <input pInputText [value]="selectedRule?.typeName" type="text" id="fraud-type" disabled style="height: 38px;">
    </div>
    <div class="d-flex flex-column justify-content-around" style="padding-right: 20px">
      <label class="label-header" for="merchant-info">Merchant Info</label>
      <div style="height: 10px"></div>
      <label class="label-header" for="merchant-group">Merchant Group</label>
    </div>
    <div class="d-flex flex-column justify-content-around" style="padding-right: 52px">
      <input (input)="refreshTables()" [(ngModel)]="text" type="text" pInputText
        placeholder="Press Enter to Search by Merchant ID or Merchant Name" id="merchant-info"
        style="width:365px; height: 38px">
      <div style="height: 10px"></div>
      <p-dropdown (onChange)="refreshTables()" [options]="groups" [(ngModel)]="selectedGroup" [filter]="true"
        optionLabel="groupName" optionValue="groupId" [style]="{width:'365px', height: '38px'}" placeholder="--All--"
        [showClear]="true" inputId="merchant-group">
        <ng-template let-option pTemplate="item">
          <div [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
            {{ option.groupName }}
          </div>
        </ng-template>
      </p-dropdown>
    </div>
    <div style="padding-right: 27px">
      <label class="label-header" for="search-screen">Search Screen</label>
    </div>
    <div>
      <p-dropdown (onChange)="refreshTables()" [options]="searchScreenList" [(ngModel)]="selectedSearchScreen"
        [filter]="true" optionLabel="label" optionValue="value" [style]="{width:'181px', height: '38px'}"
        placeholder="--All--" [showClear]="true" inputId="search-screen">
        <ng-template let-side pTemplate="item">
          <div [pTooltip]="side.tooltip" tooltipPosition="left">
            {{side.label}}
          </div>
        </ng-template>
      </p-dropdown>
    </div>
    <div class="d-flex justify-content-end" style="flex: 1;height: 38px;">
      <div style="height: 38px" class="d-flex align-items-center">
        <button class="save-button-common-fdm" style="height: 29px; margin-right: 10px" pButton type="button"
          (click)="updateRule()" [disabled]="requestApproval">Save</button>
        <button class="cancel-button-common-fdm" style="height: 29px" pButton type="button"
          (click)="onCancel.emit()">Cancel</button>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-between" style="margin-bottom: 17px">
    <span class="label-table">* Un-Tick ✔ to remove Merchant ID for current rule</span>
    <span class="label-table" *ngIf="ruleUpdate">{{ ruleUpdate}}</span>
    <span class="label-table">* Tick ✔ to apply Merchant ID for current rule</span>
  </div>
  <div class="d-flex justify-content-between" style="margin-bottom: 17px">
    <span class="label-table">{{selectedSource.length}} Merchant ID  đã chọn/{{leftMerchants.length -
      selectedSource.length}} Merchant ID  chưa chọn/{{leftMerchants.length}} total Merchant ID </span>
      <span class="label-table"  style="color: red" *ngIf="requestApproval">{{ requestApproval}}</span>
    <span class="label-table">{{selectedTarget.length}} Merchant ID  đã chọn/{{rightMerchants.length -
      selectedTarget.length}} Merchant ID  chưa chọn/{{rightMerchants.length}} total Merchant ID </span>
  </div>
  <div style="display:flex; flex-direction: row; flex: 1">
    <div style="width: 50%; border-right: #D9D9DC 0.5px solid">
      <p-table [value]="leftMerchants" [(selection)]="selectedSource" [scrollable]="true"
        scrollHeight="calc(100vh - 385px)" selectionMode="multiple" dataKey="merchantId">
        <ng-template pTemplate="header">
          <tr>
            <th class="column-checkbox" scope="col">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
              &nbsp;No
            </th>
            <th scope="col">Merchant ID</th>
            <th scope="col">Merchant Name</th>
            <th scope="col">Merchant Group</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-merchant>
          <tr>
            <td class="column-checkbox optomize-padding">
              <p-tableCheckbox [value]="merchant"></p-tableCheckbox>
              &nbsp;{{merchant.rowNum}}
            </td>
            <td class="optomize-padding">{{merchant.merchantId}}</td>
            <td class="optomize-padding">{{merchant.merchantName}}</td>
            <td class="optomize-padding">{{merchant.groupName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div style="width: 50%">
      <p-table [value]="rightMerchants" [(selection)]="selectedTarget" [scrollable]="true"
        scrollHeight="calc(100vh - 385px)" selectionMode="multiple" dataKey="merchantId">
        <ng-template pTemplate="header">
          <tr>
            <th class="column-checkbox" scope="col">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
              &nbsp;No
            </th>
            <th scope="col">Merchant ID</th>
            <th scope="col">Merchant Name</th>
            <th scope="col">Merchant Group</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-merchant>
          <tr>
            <td class="column-checkbox optomize-padding">
              <p-tableCheckbox [value]="merchant"></p-tableCheckbox>
              &nbsp;{{merchant.rowNum}}
            </td>
            <td class="optomize-padding">{{merchant.merchantId}}</td>
            <td class="optomize-padding">{{merchant.merchantName}}</td>
            <td class="optomize-padding">{{merchant.groupName}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '589px'}"></p-confirmDialog>
