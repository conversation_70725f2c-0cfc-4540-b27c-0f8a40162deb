import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Params } from '@angular/router';
import { ConfigBlockRuleService } from '@service/fdm/domestic/config-block-rule.service';
import { RequestCopyRules } from 'app/model/fdm/review-rule';
import { Group } from 'app/model/group';
import { Merchant } from 'app/model/fdm/merchant';
import { MessageService, ConfirmationService } from 'primeng/api';
import { Rule } from '../config-block-rule.component';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-copy-block-rules-merchant',
  templateUrl: './copy-block-rules-merchant.component.html',
  styleUrls: ['./copy-block-rules-merchant.component.scss'],
  providers: [
    MessageService,
    ConfirmationService
  ]
})
export class CopyBlockRulesMerchantComponent implements OnInit {

  constructor(private configBlockRuleService: ConfigBlockRuleService, private confirmationService: ConfirmationService, private messageService: MessageService,  
    private toast: ToastrService) { }

  params: Params = {}

  value: Merchant[] = [];
  merchantHasRules: Merchant[] = [];
  filteredMerchants: Merchant[] = [];
  selection: Merchant[];
  selectAll: boolean;

  rules: Rule[] = [];

  _configuredRules: Set<number>;
  @Input() set configuredRules(ruleIds: Set<number>) {
    this._configuredRules = ruleIds ?? new Set();
    this.rules = [...this._configuredRules].map(ruleId => this.ruleMapId.get(ruleId)).filter(rule => rule != undefined);
  }

  private _merchant?: Merchant;
  @Input() set merchant(value: Merchant) {
    this.selection = [];
    this._merchant = value ?? {
      merchantId: null,
      merchantName: null
    };
    this.refresh();
    this.groupId = this.merchant.groupId
  }

  get merchant(): Merchant {
    return this._merchant;
  }

  @Input() merchants: Merchant[];

  @Input() groups: Group[];

  groupId?: number;

  @Input() ruleMapId: Map<number, Rule>;

  @Output() onClose = new EventEmitter<void>();
  @Input() listAllMerchants: Merchant[];

  ngOnInit(): void {
    this.selection = [];
    this.value = this.listAllMerchants;
    if(this.listAllMerchants && this.listAllMerchants.length > 0){
      this.listAllMerchants.forEach((value) => {
        if (value.numOfRule == 0){
          return
        }
        
        this.merchantHasRules.push(value);
      } )
    }
  }

  refreshDetail(merchant) {
    if (!merchant) {
      return;
    }

    this.groupId = undefined;
    this.rules = [];
    this.configBlockRuleService.listRuleByMerchantId(merchant.merchantId).subscribe(ruleIds => {
      this.rules = ruleIds.map(ruleId => this.ruleMapId.get(ruleId)).filter(rule => rule != undefined);
    });

    this.configBlockRuleService.blockRulesByMerchantId(merchant.merchantId
    ).subscribe(res => {
      if (res) {
        this._merchant = res[0];
      }
    });
  }

  onLazyLoad({ first, rows }) {
    this.params.first = first;
    this.params.rows = rows;
    this.refresh();
  }

  refresh() {
    this.filteredMerchants = this.value
      .filter(x => x.merchantId != this.merchant.merchantId)
      .filter(x => {
        if (this.params.text) {
          return x.merchantId.toUpperCase().includes(this.params.text.toUpperCase()) || x.merchantName?.toUpperCase().includes(this.params.text.toUpperCase())
        }
        return true;
      }).filter(x => {
        if (this.params.groupId) {
          return x.groupId == this.params.groupId
        }
        return true;
      })
      .sort((a, b) => {
        // First, sort by requestApproval == null and numOfRule === 0
        if ((a.requestApproval === null && b.requestApproval !== null) || (a.numOfRule === 0 && b.numOfRule > 0)) {
          return -1;
        }
        if ((a.requestApproval !== null && b.requestApproval === null) || (a.numOfRule > 0 && b.numOfRule === 0)) {
          return 1;
        }

        // If requestApproval is the same or both are not null, sort by merchantId
        return a.merchantId.localeCompare(b.merchantId);
      });
  }

  submit() {
    if(this.selection.length > 500){
      this.toast.warning("MerchantId exceed 500")
      return true;
    }
    let request: RequestCopyRules = {
      groupId: this.groupId,
      sourceMerchantId: this.merchant?.merchantId,
      targetMerchantIds: this.selection,
      internalMccId: this.merchant?.categoryCode
    }
    this.confirmationService.confirm({
      rejectButtonStyleClass: "p-button p-button-secondary",
      message: `All configured rule of selected merchant will be replaced.
      Are you sure to paste from Block Rule ${this.merchant?.merchantId} and update these merchant group name?`,
      header: 'Copy set of rule',
      acceptLabel: 'Save',
      rejectLabel: 'Cancel',
      dismissableMask: true,
      accept: () => {
        this.configBlockRuleService.copyMerchantRulesToMerchants(request).subscribe((res:any) => {
          if (res  && res.message != 'Success') {
            this.toast.error(res.message);
            return;
          }

          this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: `You've just successfully copy all rules to selected Merchant`});
          this.selection = [];
        }, error => {
          console.log(error);
        })
      }
    });
  }

  onChange({ checked }) {
    if (checked) {
      this.selection = this.value.filter(x => (x.requestApproval == null && x.numOfRule == 0) );
    } else {
      this.selection = [];
    }
  }
}