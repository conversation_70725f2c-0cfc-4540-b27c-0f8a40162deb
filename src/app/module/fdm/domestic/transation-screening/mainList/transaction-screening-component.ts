import { DatePipe, Location } from '@angular/common';
import { HttpParams } from '@angular/common/http';
import { AfterViewInit, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { TransactionScreeningService } from '@service/fdm/domestic/transaction-screening.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationService, LazyLoadEvent, MessageService, SelectItem } from 'primeng/api';
import { Dropdown } from 'primeng/dropdown';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { OverlayPanel } from 'primeng/overlaypanel';
import { InvoiceDetailComponent } from '../invoice-detail-dialog/invoice-detail-component';
import { SaveFilterComponent } from '../save-filter-dialog/save-filter-component';
import { TransactionScreeningSearch } from '../search/transaction-screening-search.component';
import { CommonService } from '@service/fdm/common.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'transaction-screening',
  templateUrl: './transaction-screening-component.html',
  styleUrls: ['./transaction-screening-component.css'],
  providers: [TransactionScreeningService, DialogService, ConfirmationService, MessageService],
})

export class TransactionScreeningComponent implements OnInit, OnDestroy, AfterViewInit {
  public title = "Domestic Transaction Screening";
  public pageSize: number;
  public resultsLength = 0;
  public loading: boolean;
  public data: Array<any>;
  private offsetHeight = 530;
  public flexScrollHeight = '200px';
  public showFormSearch = true;

  public orderRef: string;
  public merchantTransRef: string;
  public amount: string;
  public currencyList: Array<any>;
  public currencySelected: Array<any> = [];
  public cardNumber: string;
  public bankInfoTelephone: string;
  public merchantEmail: string;
  public fraud: string;
  public mcc: string;
  public mccDescription: string;
  public url: string;
  public ipAddress: string;
  public ipCountry: string;
  public cardVerificationInfo: string;
  public qrId: string;
  public qrChannelList: Array<any>;
  public transId: string;
  public partnerName: string;
  public customerEmail: string;
  public nameOnCard: string;
  public customerName: string;
  public customerPhone: string;

  public invoiceId: string;
  public promotionCode: string;
  public promotionName: string;
  public originalAmount: string;
  public authoCode: string;
  public otpChannel: string;
  public customerId: string;

  private delayTimer: any;
  public numberColumn: any;
  public coLumnSelected: any;
  public coLumnSelectedArray: Array<any> = [];

  public myFilter: Filter;
  public myFilterList: SelectItem<Filter>[] = [];

  public selectedFunctionList: SelectItem[] = [
    {
      value: selectAllLabel,
      label: 'Select all ' + (this.resultsLength ?? '0') + ' items'
    }, {
      value: unselectedLabel,
      label: 'Unselected'
    }];

  public selectedFunction: string;
  public noteEditable: Array<string> = [];

  public invoiceDetail: DynamicDialogRef;
  public saveMyFilter: DynamicDialogRef;
  public orderRefTooltip: string = "* Invoice Found for ";
  public blacklist: Array<string>;
  public tooltipStyle = 'color: blue;';
  public blacklistList: SelectItem[] = [
    {
      value: 'ip',
      label: 'IP'
    }, {
      value: 'email',
      label: 'EMAIL'
    }, {
      value: 'phone',
      label: 'PHONE'
    }, {
      value: 'card',
      label: 'CARD'
    }
  ];
  public searchBref: string;

  public page: {
    label: string,
    value: number
  }[];
  public selectedTrans: any[] = [];

  paramGet = new HttpParams;
  @ViewChild(TransactionScreeningSearch, { static: true }) searchForm: TransactionScreeningSearch;

  @ViewChild('card', { static: true }) card: OverlayPanel;
  @ViewChild('ip', { static: true }) ip: OverlayPanel;
  @ViewChild('email', { static: true }) email: OverlayPanel;
  @ViewChild('phone', { static: true }) phone: OverlayPanel;
  @ViewChild('invoice', { static: true }) invoice: OverlayPanel;
  @ViewChild('elm', { static: true }) elm: ElementRef;

  @ViewChild('dropdown', { static: false }) dropdown: Dropdown;

  tooltipModifiedText: any;
  tooltipContent: any;

  fromRouterLink: boolean = false;

  public merchantIdList: SelectItem[] = [];
  public selectedTransIds = [];
  console = console;

  constructor(
    private transactionScreeningService: TransactionScreeningService,
    public datepipe: DatePipe,
    private ConfirmService: ConfirmService,
    public global: Globals,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    public dialogService: MatDialog,
    public location: Location,
    public dialogPrime: DialogService,
    private confirmationService: ConfirmationService,
    private commonService: CommonService) {
  }

  ngAfterViewInit(): void {
    this.setWidthColumn();
  }

  ngOnInit() {
    let params = this.route.snapshot.queryParams
    if (params['encryptedCardNo']) {
      this.searchForm.encryptedCardNo = params['encryptedCardNo']
    }
    if (params['showFormSearch']) {
      this.showFormSearch = params['showFormSearch'] != 'false'
    }

    if (params['transIds']) {
      this.selectedTransIds = params['transIds'].split(",");
    }

    this.searchForm.init(params);
    this.resultsLength = 0;

    this.checkFormSearch(false);
    this.showFormSearch = this.searchForm.showFormSearch;

    if (this.showFormSearch) {
      this.offsetHeight = 530;
    } else {
      this.offsetHeight = 305;
    }

    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';

    this.dataComboboxData();

    this.fromRouterLink = params['fromRouterLink'];
    if (!this.fromRouterLink) {
      this.myFilter = localStorage.getItem('domestic_transaction_screening_filter') ? JSON.parse(atob(localStorage.getItem('domestic_transaction_screening_filter'))) : this.myFilter;
    }
    if (localStorage.getItem('domestic_transaction_screening_filter') && this.myFilter) {
      this.changeFilter();
    }

    this.currencyList = [
      {
        label: 'VND',
        value: 'VND'
      }
    ];
    this.searchForm.columnItems.sort((a, b) => (a.order > b.order) ? 1 : -1);

    this.searchForm.columnMap = new Map();
    for (const iterator of this.searchForm.columnItems) {
      this.searchForm.columnMap.set(iterator.code, iterator);
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.showFormSearch) {
      this.offsetHeight = 530;
    } else {
      this.offsetHeight = 305;
    }
    this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
  }

  ngOnDestroy() {
    if (this.invoiceDetail) {
      this.invoiceDetail.close();
    }

  }

  filter() {
    let self = this;
    this.searchForm.first = 0;
    this.searchForm.page = 1;
    clearTimeout(this.delayTimer);
    this.delayTimer = setTimeout(function () {
      return self.searchData();
    }, 3000);

  }

  onClear() {
    this.searchForm.merchantGroup = [];
    this.changeFilter();
  }


  searchData(): void {
    //validate email
    if (this.searchForm.customerEmail && this.searchForm.customerEmail !== '') {
      let expression: RegExp = /^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+(\.[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/;
      let result: boolean = expression.test(this.searchForm.customerEmail);
      if (!result) {
        this.toastr.error('Email input incorrect, please recheck!');
        return
      }
    }
    //validate number
    if (this.searchForm.customerPhone && this.searchForm.customerPhone != '') {
      // const expression = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;
      // const expression = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-\s\./0-9]*$/g;
      const expression = /^[0-9*]+$/;
      const result: boolean = expression.test(this.searchForm.customerPhone)
      if (!result) {
        this.toastr.error('Phone input incorrect, please recheck!');
        return
      }
    }

    this.router.navigate(['/fdm/domestic/transaction-screening'], { queryParams: this.redirectParams() });
    let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
    let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');

    this.paramGet = new HttpParams()
      .set('fromDate', from_date)
      .set('toDate', to_date)
      .set('gate', this.searchForm.gateSelected?.join(",") ?? "")
      .set('merchantGroup', this.searchForm.merchantGroup?.join(",") ?? "")
      .set('issuer', this.searchForm.issuerSelected?.join(",") ?? "")
      .set('fraudStatus', this.searchForm.fraudStatus?.join(",") ?? "")
      .set('responseCode', this.searchForm.responseCodeSelected?.join(",") ?? "")
      .set('transState', this.searchForm.transStateSelected?.join(",") ?? "")
      .set('pic', this.searchForm.pic?.join(",") ?? "")
      .set('transType', this.searchForm.transTypeSelected?.join(",") ?? "")
      .set('markFraud', this.searchForm.markFraud?.join(",").toString() ?? "")
      .set('amount', this.searchForm.amountMore ? this.searchForm.amountMore : 0)
      .set('merchantId', this.searchForm.merchantId && this.searchForm.merchantId !== "" ? this.searchForm.merchantId.trim() : "")
      .set('orderRef', this.searchForm.orderRef && this.searchForm.orderRef !== "" ? this.searchForm.orderRef.trim() : "")
      .set('ipAddress', this.searchForm.ipAddress && this.searchForm.ipAddress !== "" ? this.searchForm.ipAddress.trim() : "")
      .set('customerPhone', this.searchForm.customerPhone && this.searchForm.customerPhone !== "" ? this.searchForm.customerPhone.trim() : "")
      .set('customerEmail', this.searchForm.customerEmail && this.searchForm.customerEmail !== "" ? this.searchForm.customerEmail.trim() : "")
      .set('merchantTxnRef', this.searchForm.merchantTxnRef && this.searchForm.merchantTxnRef !== "" ? this.searchForm.merchantTxnRef.trim() : "")
      .set('customerName', this.searchForm.customerName && this.searchForm.customerName !== "" ? this.searchForm.customerName.trim() : "")
      .set('partnerName', this.searchForm.partnerName && this.searchForm.partnerName !== "" ? this.searchForm.partnerName.trim() : "")
      .set('qrId', this.searchForm.qrId && this.searchForm.qrId !== "" ? this.searchForm.qrId.trim() : "")
      .set('cardNumber', this.searchForm.cardNumber && this.searchForm.cardNumber !== "" ? this.searchForm.cardNumber.trim() : "")
      .set('accountNumber', this.searchForm.accountNumber && this.searchForm.accountNumber !== "" ? this.searchForm.accountNumber.trim() : "")
      .set('nameOnCard', this.searchForm.nameOnCard && this.searchForm.nameOnCard !== "" ? this.searchForm.nameOnCard.trim() : "")
      .set('transId', this.searchForm.transId && this.searchForm.transId !== "" ? this.searchForm.transId.trim() : "")
      .set('epp', this.searchForm.epp && this.searchForm.epp !== "" ? this.searchForm.epp.trim() : "")
      .set('qrChannel', this.searchForm.qrChannelSelected?.join(",") ?? "")
      .set('cardList', this.searchForm.cardSelected?.join(",") ?? "")
      .set('encryptedCardNo', this.searchForm.encryptedCardNo && this.searchForm.encryptedCardNo !== "" ? this.searchForm.encryptedCardNo.toString().trim() : "")
      .set('merchantEmail', this.merchantEmail && this.merchantEmail !== "" ? this.merchantEmail.toString().trim() : "")
      .set('fraud', this.fraud && this.fraud !== "" ? this.fraud.toString().trim() : "")
      .set('mcc', this.mcc && this.mcc !== "" ? this.mcc.toString().trim() : "")
      .set('mccDescription', this.searchForm.mccDescription && this.searchForm.mccDescription !== "" ? this.searchForm.mccDescription.toString().trim() : "")
      .set('url', this.searchForm.url && this.searchForm.url !== "" ? this.searchForm.url.toString().trim() : "")
      .set('ipCountry', this.ipCountry && this.ipCountry !== "" ? this.ipCountry.toString().trim() : "")
      .set('cardVerificationInfo', this.cardVerificationInfo && this.cardVerificationInfo !== "" ? this.cardVerificationInfo.toString().trim() : "")
      .set('otpChannel', this.searchForm.otpChannel && this.searchForm.otpChannel !== "" ? this.searchForm.otpChannel.toString().trim() : "")
      .set('customerId', this.customerId && this.customerId !== "" ? this.customerId.toString().trim() : "")
      .set('cof', this.searchForm.cof && this.searchForm.cof !== "" ? this.searchForm.cof.toString().trim() : "")
      // .set('sourceOfFund', this.searchForm.sourceOfFundSelected?.join(",") ?? "")
      .set('deviceId', this.searchForm.deviceId && this.searchForm.deviceId !== "" ? this.searchForm.deviceId.trim() : "")
      .set('pageSize', this.searchForm.pageSize ? this.searchForm.pageSize : 200)
      .set('page', (this.searchForm.page - 1) + '')
      .set('showFormSearch', this.searchForm.showFormSearch)
      .set('integrationType', this.searchForm.integrationTypeSelected?.join(",") ?? "")
      .set('fromRouterLink', this.fromRouterLink);
    this.searchForm.searchBrefInfo();
    this.transactionScreeningService.getList(this.paramGet).subscribe(responses => {
      this.resultsLength = responses.total;
      this.data = responses.data;
      let selectAll = this.selectedFunctionList.find(x => x.value == selectAllLabel);
      if (selectAll) {
        selectAll.label = 'Select all ' + (this.resultsLength ?? '0') + ' items';
      }
      this.initPage(this.resultsLength);
    });
  }

  download() {
    let params: any = this.getParams();
    params.fromDate = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
    params.toDate = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
    params.amount = Number(params.amount);
    
    let column_active = this.searchForm.columnItems.filter(a => a.active).map(element => element.code);
    let column_list = this.searchForm.columnItems.map(element => element.code);

    let paramsDownload = {
      "ColumnActive": column_active,
      "columnDefault": column_list,
      "transId": this.selectedFunction == selectAllLabel || this.selectedTrans.length == 0 ? [] : this.selectedTrans.map(element => element.transId),
      "txnScreeningSearch": params
    };
    return this.transactionScreeningService.download(paramsDownload).subscribe(response => {
      this.global.downloadEmit.emit(true);
    });
  }

  getParams() {
    return {
      'fromDate': this.searchForm.fromDate,
      'toDate': this.searchForm.toDate,
      'merchantGroup': this.searchForm.merchantGroup ? this.searchForm.merchantGroup.join(",") : "",
      'gate': this.searchForm.gateSelected ? this.searchForm.gateSelected.join(",") : "",
      'issuer': this.searchForm.issuerSelected ? this.searchForm.issuerSelected.join(",") : "",
      'fraudStatus': this.searchForm.fraudStatus ? this.searchForm.fraudStatus.join(",") : "",
      'responseCode': this.searchForm.responseCodeSelected ? this.searchForm.responseCodeSelected.join(",") : "",
      'transState': this.searchForm.transStateSelected ? this.searchForm.transStateSelected.join(",") : "",
      'pic': this.searchForm.pic ? this.searchForm.pic.join(",") : "",
      'transType': this.searchForm.transTypeSelected ? this.searchForm.transTypeSelected.join(",") : "",
      'markFraud': this.searchForm.markFraud ? this.searchForm.markFraud.join(",").toString() : "",
      'amount': this.searchForm.amountMore ? this.searchForm.amountMore : 0,
      'merchantId': this.searchForm.merchantId && this.searchForm.merchantId !== "" ? this.searchForm.merchantId.trim() : "",
      'orderRef': this.searchForm.orderRef && this.searchForm.orderRef !== "" ? this.searchForm.orderRef.trim() : "",
      'ipAddress': this.searchForm.ipAddress && this.searchForm.ipAddress !== "" ? this.searchForm.ipAddress.trim() : "",
      'customerPhone': this.searchForm.customerPhone && this.searchForm.customerPhone !== "" ? this.searchForm.customerPhone.trim() : "",
      'customerEmail': this.searchForm.customerEmail && this.searchForm.customerEmail !== "" ? this.searchForm.customerEmail.trim() : "",
      'merchantTxnRef': this.searchForm.merchantTxnRef && this.searchForm.merchantTxnRef !== "" ? this.searchForm.merchantTxnRef : "",
      'customerName': this.searchForm.customerName && this.searchForm.customerName !== "" ? this.searchForm.customerName.trim() : "",
      'partnerName': this.searchForm.partnerName && this.searchForm.partnerName !== "" ? this.searchForm.partnerName.trim() : "",
      'qrId': this.searchForm.qrId && this.searchForm.qrId !== "" ? this.searchForm.qrId.trim() : "",
      'cardNumber': this.searchForm.cardNumber && this.searchForm.cardNumber !== "" ? this.searchForm.cardNumber.trim() : "",
      'accountNumber': this.searchForm.accountNumber && this.searchForm.accountNumber !== "" ? this.searchForm.accountNumber.trim() : "",
      'nameOnCard': this.searchForm.nameOnCard && this.searchForm.nameOnCard !== "" ? this.searchForm.nameOnCard.trim() : "",
      'transId': this.searchForm.transId && this.searchForm.transId !== "" ? this.searchForm.transId.trim() : "",
      'epp': this.searchForm.epp && this.searchForm.epp !== "" ? this.searchForm.epp.trim() : "",
      'qrChannel': this.searchForm.qrChannelSelected ? this.searchForm.qrChannelSelected.join(",") : "",
      'cardList': this.searchForm.cardSelected ? this.searchForm.cardSelected.join(",") : "",
      'encryptedCardNo': this.searchForm.encryptedCardNo && this.searchForm.encryptedCardNo !== "" ? this.searchForm.encryptedCardNo.toString().trim() : "",
      'merchantEmail': this.merchantEmail && this.merchantEmail !== "" ? this.merchantEmail.toString().trim() : "",
      'fraud': this.fraud && this.fraud !== "" ? this.fraud.toString().trim() : "",
      'mcc': this.mcc && this.mcc !== "" ? this.mcc.toString().trim() : "",
      'mccDescription': this.searchForm.mccDescription && this.searchForm.mccDescription !== "" ? this.searchForm.mccDescription.toString().trim() : "",
      'url': this.searchForm.url && this.searchForm.url !== "" ? this.searchForm.url.toString().trim() : "",
      'ipCountry': this.ipCountry && this.ipCountry !== "" ? this.ipCountry.toString().trim() : "",
      'cardVerificationInfo': this.cardVerificationInfo && this.cardVerificationInfo !== "" ? this.cardVerificationInfo.toString().trim() : "",
      'otpChannel': this.searchForm.otpChannel && this.searchForm.otpChannel !== "" ? this.searchForm.otpChannel.toString().trim() : "",
      'customerId': this.customerId && this.customerId !== "" ? this.customerId.toString().trim() : "",
      'cof': this.searchForm.cof && this.searchForm.cof !== "" ? this.searchForm.cof.toString().trim() : "",
      // 'sourceOfFund': this.searchForm.sourceOfFundSelected?.join(",") ?? "",
      'deviceId': this.searchForm.deviceId && this.searchForm.deviceId !== "" ? this.searchForm.deviceId.trim() : "",
      'integrationType': this.searchForm.integrationTypeSelected ? this.searchForm.integrationTypeSelected.join(",") : ""
    };
  }

  paramsToJson(params: HttpParams) {
    const jsonObj = {};

    params.keys().forEach(key => {
      let value = params.get(key);
      if (value == '') {
        jsonObj[key] = '';
        return;
      }
      let checkNum = Number(value);
      //merchantGroup is String
      if (!isNaN(checkNum) && key != "merchantGroup") {
        jsonObj[key] = checkNum;
      } else {
        jsonObj[key] = value;
      }
    });
    jsonObj['page'] = 0;
    jsonObj['pageSize'] = 999999;

    return jsonObj;
  }

  redirectParams() {
    let params: any = this.getParams();
    params.pageSize = this.searchForm.pageSize;
    params.page = this.searchForm.page;
    params.first = this.searchForm.first;
    params.showFormSearch = this.searchForm.showFormSearch;
    params.fromRouterLink = this.fromRouterLink;
    return params;
  }

  onSubmit() {
    let params = this.route.snapshot.queryParams
    if (!this.searchForm.page) {
      this.searchForm.page = params['page'] || 1;
    }

    this.searchData()
    this.searchForm.showFormSearch = false;
    this.showFormSearch = this.searchForm.showFormSearch;
  }

  onChangePage() {
    this.searchForm.page = this.searchForm.page ?? 1;
    this.searchForm.first = this.searchForm.page == undefined ? 0 : ((this.searchForm.page - 1) * parseInt(this.searchForm.pageSize));
    this.searchData()
  }

  initPage(resultsLength: any) {
    this.selectedReload();
    if (resultsLength && resultsLength > parseInt(this.searchForm.pageSize)) {
      let numberPage = Math.ceil(resultsLength / parseInt(this.searchForm.pageSize));
      this.page = [];
      for (let i = 1; i <= numberPage; i++) {
        this.page.push({
          value: i,
          label: i + ''
        });
      }
    } else {
      this.page = [];
      this.page.push({
        value: 1,
        label: '1'
      });
    }
  }

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;
    this.searchForm.page = (event.first / event.rows) + 1;
    this.searchForm.first = event.first;
    let params = this.route.snapshot.queryParams
    if (params['encryptedCardNo']) {
      this.searchForm.encryptedCardNo = params['encryptedCardNo']
    }
    if (params['showFormSearch']) {
      this.showFormSearch = params['showFormSearch'] != 'false'
    }
    this.searchData();
  }

  getPage(): number {
    return Math.floor(this.searchForm.first / this.pageSize);
  }

  getPageCount() {
    return Math.ceil(this.resultsLength / this.pageSize) || 1;
  }

  isFirstPage() {
    return this.getPage() === 0;
  }

  isLastPage() {
    return this.getPage() === this.getPageCount() - 1;
  }

  checkColumn(code: string): boolean {
    return this.searchForm.columnMap.get(code).active;
  }

  checkFormSearch(type: boolean) {
    this.showFormSearch = type;
    if (this.showFormSearch) {
      this.offsetHeight = 530;
    } else {
      this.offsetHeight = 305;
    }
    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
    this.searchForm.checkFormSearch(type);
  }

  getTotalPage(totalRecords: number, rows: number) {
    return Math.ceil(totalRecords / rows);
  }

  clear() {
    const setFromDate = new Date(new Date().setHours(0, 0, 0, 0));
    this.searchForm.fromDate = setFromDate;
    this.searchForm.toDate = new Date(new Date().setHours(23, 59, 59, 0));
    this.searchForm.page = 1;
    this.searchForm.gateSelected = [];
    this.searchForm.merchantGroup = [];
    this.searchForm.issuerSelected = [];
    this.searchForm.fraudStatus = [];
    this.searchForm.responseCodeSelected = [];
    this.searchForm.transStateSelected = [];
    this.searchForm.integrationTypeSelected = [];
    this.searchForm.pic = [];
    this.searchForm.transTypeSelected = [];
    this.searchForm.markFraud = [];
    this.searchForm.amountMore = 0;
    this.searchForm.merchantId = ''
    this.searchForm.orderRef = '';
    this.searchForm.ipAddress = '';
    this.searchForm.customerPhone = '';
    this.searchForm.customerEmail = '';
    this.searchForm.merchantTxnRef = '';
    this.searchForm.customerName = '';
    this.searchForm.partnerName = '';
    this.searchForm.qrId = '';
    this.searchForm.cardNumber = '';
    this.searchForm.accountNumber = '';
    this.searchForm.nameOnCard = '';
    this.searchForm.transId = '';
    this.searchForm.epp = '';
    this.searchForm.qrChannelSelected = [];
    this.searchForm.cardSelected = [];
    this.currencySelected = [];
    this.searchForm.encryptedCardNo = '';
    this.bankInfoTelephone = '';
    this.merchantEmail = '';
    this.fraud = '';
    this.mcc = '';
    this.searchForm.mccDescription = '';
    this.searchForm.url = '';
    this.ipCountry = '';
    this.cardVerificationInfo = '';
    this.searchForm.sourceOfFundSelected = [];
    this.searchForm.deviceId = '';
    this.searchForm.pageSize = '200';
    this.selectedTrans = [];
    this.searchForm.otpChannel = '';
    this.searchForm.cof = '';
    this.customerId = '';
    let self = this;
    self.searchData();
  }

  markColorFraudStatus(fraudStaus: string, fraud: string) {
    var otherRules = "";
    if (fraud) {
      otherRules = fraud
      .replace(/RULE_GROUP\([^()]*\)(,|$)/g, "") // Loại bỏ RULE_GROUP chính xác
      .replace(/,,+/g, ",") // Loại bỏ dấu phẩy dư
      .trim() // Loại bỏ khoảng trắng thừa
      .replace(/^,|,$/g, "")
    }

    if (fraudStaus == "2") {
      return { color: '#0000FF' };
    } else if (fraudStaus == "3") {
      if (otherRules.includes("CARD_BLACK_LIST") || otherRules.includes("EMAIL_BLACK_LIST") || otherRules.includes("PHONE_BLACK_LIST") || otherRules.includes("IP_BLACK_LIST")) {
        return { color: '#FF0000' };
      } else {
        return { color: '#FF9900' };
      }
    } else if (fraudStaus == "" || fraudStaus == "4" || fraudStaus == "0") {
      return { color: '#666666' };
    } else if (fraudStaus == "1") {
      return { color: '#0C912D' };
    } else {
      return { color: '#000000' };
    }
  }

  pTooltipOrderRef(orderRef, merchantId) {
    let tooltipText = this.orderRefTooltip + orderRef

    return tooltipText;
  }

  pTooltiCardNumber(encryptCardNo, merchantId) {
    // encryptCardNo.style.setProperty
    return "- Encrypted Card No: " + encryptCardNo + "\n- Add Card into Blacklist for All Merchant ID or only Merchant " + merchantId
  }

  pTooltiIpAddress(ip, merchantId) {
    // encryptCardNo.style.setProperty
    return "- Ip Address: " + ip + "\n- Add Card into Blacklist for All Merchant ID or only Merchant " + merchantId
  }

  pTooltiPhone(phone, merchantId) {
    // encryptCardNo.style.setProperty
    return "- Phone: " + phone + "\n- Add Card into Blacklist for All Merchant ID or only Merchant " + merchantId
  }

  pTooltiEmail(email, merchantId) {
    // encryptCardNo.style.setProperty
    return "- Email: " + email + "\n- Add Card into Blacklist for All Merchant ID or only Merchant " + merchantId
  }

  detailInvoiceOrderRef(merchantId, merchantTxnRef) {
    const paramGetInvoiceInfo = new HttpParams()
      .set('merchantId', merchantId)
      .set('merchantTxnRef', merchantTxnRef);

    this.transactionScreeningService.getInvoiceInfo(paramGetInvoiceInfo).subscribe(dataInvoice => {

      if (dataInvoice.data && dataInvoice.data !== null && JSON.stringify(dataInvoice.data) !== '{}') {
        this.invoiceDetail = this.dialogPrime.open(InvoiceDetailComponent, {
          width: '70%',
          contentStyle: { "max-height": "600px", "overflow": "auto" },
          closable: true,
          dismissableMask: true,
          data: {
            data: dataInvoice
          }
        })
        this.invoiceDetail.onClose.subscribe(res => {
        });
      } else {
        this.orderRefTooltip = "* No Invoice Found for ";
      }
    });
  }

  checkEditNote(column, transId) {
    let keyEdit = column + '|' + transId;
    if (this.noteEditable && this.noteEditable.length > 0) {
      if (this.noteEditable.includes(keyEdit)) {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  //disable note when empty
  disableNote(note) {
    if (note && note.trim() !== '') {
      return true;
    } else {
      return false;
    }
  }

  clickEdit(column, transId) {
    let keyEdit = column + '|' + transId;
    if (!this.noteEditable.includes(keyEdit)) {
      this.noteEditable.push(keyEdit)
    }
  }

  clearEdit(column, transId) {
    let keyEdit = column + '|' + transId;
    if (this.noteEditable.includes(keyEdit)) {
      this.noteEditable = this.noteEditable.filter(item => item !== keyEdit);
    }
  }

  onEnterOrderRef(orderRef, merchantId, orderNote, transId) {
    const body = {
      'orderRef': orderRef,
      'merchantId': merchantId,
      'note': orderNote
    }
    this.transactionScreeningService.editNoteOrderRef(body).subscribe(resp => {
      if (resp.message === 'Success') {
        this.toastr.success('Note Order Ref add successful');
        this.clearEdit('orderRef', transId);
        this.onSubmit();
      } else {
        this.toastr.error('Note Order Ref add fail');
      }
    });
  }

  onEnterCardNumber(hashCardNo, cardNum, cardNote, transId) {
    const body = {
      'hashCardNo': hashCardNo,
      'cardNum': cardNum,
      'note': cardNote
    }
    this.transactionScreeningService.editNoteCardNumber(body).subscribe(resp => {
      if (resp.message === 'Success') {
        this.toastr.success('Note Card Number add successful');
        this.clearEdit('cardNumber', transId);
        this.onSubmit();
      } else {
        this.toastr.error('Note Card Number add fail');
      }
    });
  }

  onEnterIp(ip, ipNote, transId) {
    const body = {
      'ip': ip,
      'note': ipNote
    }
    this.transactionScreeningService.editNoteIp(body).subscribe(resp => {
      if (resp.message === 'Success') {
        this.toastr.success('Note IP add successful');
        this.clearEdit('ip', transId);
        this.onSubmit();
      } else {
        this.toastr.error('Note IP add fail');
      }
    });
  }

  onEnterPhone(phone, phoneNote, transId) {
    const body = {
      'phone': phone,
      'note': phoneNote
    }
    this.transactionScreeningService.editNotePhone(body).subscribe(resp => {
      if (resp.message === 'Success') {
        this.toastr.success('Note Phone add successful');
        this.clearEdit('phone', transId);
        this.onSubmit();
      } else {
        this.toastr.error('Note Phone add fail');
      }
    });
  }

  onEnterEmail(email, emailNote, transId) {
    const body = {
      'email': email,
      'note': emailNote
    }
    this.transactionScreeningService.editNoteEmail(body).subscribe(resp => {
      if (resp.message === 'Success') {
        this.toastr.success('Note Email add successful');
        this.clearEdit('email', transId);
        this.onSubmit();
      } else {
        this.toastr.error('Note Email add fail');
      }
    });
  }

  dataComboboxData() {
    this.transactionScreeningService.getFilterByUserSession().subscribe(data => {
      this.myFilterList = data.data.map((item) => ({
        label: item.name,
        value: item
      }));
    });
  }

  routerLinkSearch(filter, filterContent) {
    let currentDate = new Date();
    let oneLastYear = new Date();
    oneLastYear.setDate(currentDate.getDate() - 365);

    let params = this.paramGet;
    if (filter == 'orderRef') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('orderRef', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'merchantId') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('merchantId', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'cardNumber') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('cardNumber', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'encryptedCardNo') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('encryptedCardNo', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'ipAddress') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('ipAddress', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'customerPhone') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('customerPhone', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'customerEmail') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('email', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'customerId') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('customerId', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    } else if (filter == 'deviceId') {
      params = params.set('fromDate', oneLastYear.toString());
      params = params.set('toDate', currentDate.toString());
      params = params.set('deviceId', filterContent.toString());
      params = params.set('showFormSearch', 'false');
      params = params.set('fromRouterLink', 'true');
    }
    const baseUrl = this.location['_baseHref'] + '/fdm/domestic/transaction-screening';

    const url = baseUrl + '?' + params;

    window.open(url, '_blank');
  }

  serializeParams(params: HttpParams): string {
    return Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
  }

  selectedReload(click = false) {
    if (this.selectedFunction == selectAllLabel) {
      this.selectedTrans = this.data;
    } else if (this.selectedFunction == unselectedLabel) {
      this.selectedFunction = undefined;
      this.selectedTrans = [];
    }

    // set when click
    if (this.selectedTrans.length >0) {
      this.selectedTransIds = [];
      this.selectedTrans.forEach((val) => {
        this.selectedTransIds.push(val.transId);
      })
    } else if (click) {
      this.selectedTransIds = [];
    }

    // set value selectedTrans by transIds from URL.
    // Prioritize the click, then from the URL.
    if (this.data && this.selectedTransIds.length > 0 && !click) {
      this.selectedTrans = [];
      const mapTransIds = {};
      this.selectedTransIds.forEach((val) => {
        mapTransIds[val] = true;
      })

      this.data.forEach((val) => {
        if (mapTransIds[val.transId]) {
          this.selectedTrans.push(val);
        }
      })      
    }

    this.updateUrlParam();
  }

  updateUrlParam() {
    const transIds = [];
    if (this.selectedTrans.length > 0) {
      this.selectedTrans.forEach((val) => {
        transIds.push(val.transId);
      })
    }

    this.router.navigate([], {
      queryParams: { transIds: transIds.join(",") },
      queryParamsHandling: 'merge' // giữ nguyên các query params cũ
    });
  }

  markUnmarkSelected(type) {
    let selectedArray: Array<string> = [];
    if (this.selectedTrans.length > 0) {
      this.selectedTrans.forEach(eachSelectedData =>
        selectedArray.push(eachSelectedData['transId'])
      )
    }
    let paramGet2 = this.paramGet;
    let paramMarkUnmarkFraud = {
      "transId": selectedArray,
      "status": type,
      "selectAll": this.selectedFunction == selectAllLabel,
      "query": this.paramsToJson(paramGet2.set("markFraud",""))
    }
    this.transactionScreeningService.markUnmarkFraud(paramMarkUnmarkFraud).subscribe(resp => {
      this.selectedTrans = [];
      this.selectedReload();
      this.onSubmit();
      if (resp.message === 'Success') {
        this.toastr.success('Mark change Successful');
      } else {
        this.toastr.error('Mark change Fail');
      }
    });
  }

  addBlacklistSelected(data, type, merchant) {
    const selectedMap: any[] = [];
    let message = ''
    let paramAddBlacklistAllMerchant;
    let paramAddBlacklistCard;
    let paramAddBlacklistIpAdress;
    let paramAddBlacklistEmail;
    let paramAddBlacklistPhone;
    if (type == 'multiselect') {
      for (const trans of this.selectedTrans) {
        const tempTrans: any = {
          "merchantName": trans['merchantName'],
          "cardNumber": trans['cardNumber'],
          "hashCardNo": trans['hashCardNo'],
          "ipAddress": trans['ipAddress'],
          "email": trans['customerEmail'],
          "customerPhone": trans['customerPhone']
        };
        selectedMap.push(tempTrans);
      }
      message = "Are you sure want to add all selected items to blacklist?"
      paramAddBlacklistAllMerchant = {
        "lstData": selectedMap,
        "blackListType": (this.blacklist && this.blacklist.length > 0) ? this.blacklist.join(',') : '',
        "selectAll": this.selectedFunction == selectAllLabel,
        "query": this.paramsToJson(this.paramGet)
      }
    } else {
      const tempTrans: any = {
        "merchantName": data['merchantName'],
        "cardNumber": data['cardNumber'],
        "hashCardNo": data['hashCardNo'],
        "ipAddress": data['ipAddress'],
        "email": data['customerEmail'],
        "customerPhone": data['customerPhone']
      };
      //   message = "Are you sure want to add this items to blacklist?"
      message = merchant == 'all' ? "Are you sure want to add this " + type + " to blacklist for All Merchant?" : "Are you sure want to add this " + type + " to blacklist for only Merchant " + merchant + "?"
      paramAddBlacklistAllMerchant = {
        "lstData": tempTrans,
        "blackListType": type
      }
      paramAddBlacklistCard = {
        "CardNo": data['cardNumber'],
        "HashCardNo": data['hashCardNo'],
        "merchantIdList": merchant == 'all' ? '' : data['merchantId'],
        "MerchantName": data['merchantName']
      }
      paramAddBlacklistIpAdress = {
        "ipAddress": data['ipAddress'],
        "merchantIdList": merchant == 'all' ? '' : data['merchantId'],
        "merchantName": data['merchantName']
      }
      paramAddBlacklistEmail = {
        "email": data['customerEmail'],
        "merchantIdList": merchant == 'all' ? '' : data['merchantId'],
        "merchantName": data['merchantName']
      }
      paramAddBlacklistPhone = {
        "phone": data['customerPhone'],
        "merchantIdList": merchant == 'all' ? '' : data['merchantId'],
        "merchantName": data['merchantName']
      }
    }
    this.ConfirmService.build().message(message)
      .title("Add Blacklist")
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (!result) {
          return;
        }
        let observable: Observable<any>;
        switch (type) {
          case 'multiselect':
            paramAddBlacklistAllMerchant.selectAll = this.selectedFunction == selectAllLabel;
            paramAddBlacklistAllMerchant.query = this.paramsToJson(this.paramGet);
            observable = this.transactionScreeningService.addBlackListAllMerchant(paramAddBlacklistAllMerchant);
            break;
          case 'Card':
            observable = this.transactionScreeningService.addBlackListCardOnlyMerchant(paramAddBlacklistCard);
            break;
          case 'IP':
            observable = this.transactionScreeningService.addBlackListIpOnlyMerchant(paramAddBlacklistIpAdress);
            break;
          case 'Email':
            observable = this.transactionScreeningService.addBlackListEmailOnlyMerchant(paramAddBlacklistEmail);
            break;
          case 'Phone':
            observable = this.transactionScreeningService.addBlackListPhoneOnlyMerchant(paramAddBlacklistPhone);
            break;
          default:
            break;
        }
        observable.subscribe(resp => {
          if (resp.message === 'Success') {
            this.toastr.success('Add Black List successful!');
          } else {
            this.toastr.error('Add Black List fail!');
          }
          
          this.selectedReload();
        });
      });
  }

  goToFirstPage() {
    this.searchForm.first = 0;
    this.searchForm.page = 1
    this.searchData();
  }

  goToLastPage() {
    const totalPages = Math.ceil(this.resultsLength / parseInt(this.searchForm.pageSize));
    this.searchForm.first = (totalPages - 1) * parseInt(this.searchForm.pageSize);
    this.searchForm.page = totalPages;

    this.searchData();
  }

  enter(index) {
    this.card.toggle(null, this.elm.nativeElement);
    this.ip.toggle(null, this.elm.nativeElement);
    this.email.toggle(null, this.elm.nativeElement);
    this.phone.toggle(null, this.elm.nativeElement);
    this.invoice.toggle(null, this.elm.nativeElement);
  }

  saveFilter(myFilter: Filter) {
    if (!myFilter) {
      this.saveNewFilter();
    }
    let params: any = this.getParams();
    params.pageSize = 200;
    params.page = 0;
    let column_active = this.searchForm.columnItems.filter(a => a.active).map(element => element.code);

    let paramsFilter = {
      "id": myFilter.id,
      "data": {
        "ColumnActive": column_active,
        "txnScreeningSearch": params
      }
    };
    this.transactionScreeningService.updateMyFilter(paramsFilter).subscribe(resp => {
      if (resp.message === 'Success') {
        this.transactionScreeningService.getFilterByUserSession().subscribe(response => {
          this.myFilterList = response.data.map((item) => ({
            label: item.name,
            value: item
          }));
          this.myFilter = this.myFilterList.find(x => x.value.id == myFilter.id)?.value;
          localStorage.setItem('domestic_transaction_screening_filter', btoa(JSON.stringify(this.myFilter)));
        });
        this.toastr.success('Save Filter successful!');
      } else {
        this.toastr.error('Save Filter fail!');
      }
    });
  }

  saveNewFilter() {
    let filterNumber = this.myFilterList.length;
    if (filterNumber >= 20) {
      this.toastr.error('Bạn chỉ có thể lưu tối đa 20 filters!', 'Lỗi');
      return;
    }

    let column_active = this.searchForm.columnItems.filter(a => a.active).map(element => element.code);

    this.saveMyFilter = this.dialogPrime.open(SaveFilterComponent, {
      header: 'Filter Name',
      width: '15%',
      contentStyle: { "max-height": "600px", "overflow": "auto" },
      closable: true,
      dismissableMask: true,
      data: {
        data: ''
      }
    })
    this.saveMyFilter.onClose.subscribe((filterName: string) => {
      if (!filterName) {
        return;
      }
      let params: any = this.getParams();
      params.pageSize = 200;
      params.page = 0;
      let paramsFilter = {
        "name": filterName,
        "data": {
          "ColumnActive": column_active,
          "txnScreeningSearch": params
        }
      };
      this.transactionScreeningService.saveMyFilter(paramsFilter).subscribe(resp => {
        if (resp.message === 'Success') {
          this.transactionScreeningService.getFilterByUserSession().subscribe(response => {
            this.myFilterList = response.data.map((item) => ({
              label: item.name,
              value: item
            }));
            this.myFilter = this.myFilterList.find(x => x.value.name == filterName)?.value;
            localStorage.setItem('domestic_transaction_screening_filter', btoa(JSON.stringify(this.myFilter)));
          });
          this.toastr.success('Save Filter successful!');
        } else {
          this.toastr.error('Save Filter fail!');
        }
      });
    });
  }

  changeFilter() {
    this.clear();
    this.searchForm.merchantGroup = this.myFilter?.data.txnScreeningSearch.merchantGroup == "" ? this.searchForm.merchantGroup : this.myFilter?.data.txnScreeningSearch.merchantGroup.split(',');
    this.searchForm.gateSelected = this.myFilter?.data.txnScreeningSearch.gate == "" ? this.searchForm.gateSelected : this.myFilter?.data.txnScreeningSearch.gate.split(',');
    this.searchForm.issuerSelected = this.myFilter?.data.txnScreeningSearch.issuer == "" ? this.searchForm.issuerSelected : this.myFilter?.data.txnScreeningSearch.issuer.split(',');
    this.searchForm.fraudStatus = this.myFilter?.data.txnScreeningSearch.fraudStatus == "" ? this.searchForm.fraudStatus : this.myFilter?.data.txnScreeningSearch.fraudStatus.split(',');
    this.searchForm.responseCodeSelected = this.myFilter?.data.txnScreeningSearch.responseCode == "" ? this.searchForm.responseCodeSelected : this.myFilter?.data.txnScreeningSearch.responseCode.split(',');
    this.searchForm.transStateSelected = this.myFilter?.data.txnScreeningSearch.transState == "" ? this.searchForm.transStateSelected : this.myFilter?.data.txnScreeningSearch.transState.split(',');
    this.searchForm.pic = this.myFilter?.data.txnScreeningSearch.pic == "" ? this.searchForm.pic : this.myFilter?.data.txnScreeningSearch.pic.split(',');
    this.searchForm.transTypeSelected = this.myFilter?.data.txnScreeningSearch.transType == "" ? this.searchForm.transTypeSelected : this.myFilter?.data.txnScreeningSearch.transType.split(',');
    this.searchForm.markFraud = this.myFilter?.data.txnScreeningSearch.markFraud == "" ? this.searchForm.markFraud : this.myFilter?.data.txnScreeningSearch.markFraud.split(',');
    this.searchForm.amountMore = this.myFilter?.data.txnScreeningSearch.amount == 0 ? this.searchForm.amountMore : this.myFilter?.data.txnScreeningSearch.amount;
    this.searchForm.merchantId = this.myFilter?.data.txnScreeningSearch.merchantId == "" ? this.searchForm.merchantId : this.myFilter?.data.txnScreeningSearch.merchantId;
    this.searchForm.orderRef = this.myFilter?.data.txnScreeningSearch.orderRef == "" ? this.searchForm.orderRef : this.myFilter?.data.txnScreeningSearch.orderRef;
    this.searchForm.ipAddress = this.myFilter?.data.txnScreeningSearch.ipAddress == "" ? this.searchForm.ipAddress : this.myFilter?.data.txnScreeningSearch.ipAddress;
    this.searchForm.customerPhone = this.myFilter?.data.txnScreeningSearch.customerPhone == "" ? this.searchForm.customerPhone : this.myFilter?.data.txnScreeningSearch.customerPhone;
    this.searchForm.customerEmail = this.myFilter?.data.txnScreeningSearch.customerEmail == "" ? this.searchForm.customerEmail : this.myFilter?.data.txnScreeningSearch.customerEmail;
    this.searchForm.merchantTxnRef = this.myFilter?.data.txnScreeningSearch.merchantTxnRef == "" ? this.searchForm.merchantTxnRef : this.myFilter?.data.txnScreeningSearch.merchantTxnRef;
    this.searchForm.customerName = this.myFilter?.data.txnScreeningSearch.customerName == "" ? this.searchForm.customerName : this.myFilter?.data.txnScreeningSearch.customerName;
    this.searchForm.partnerName = this.myFilter?.data.txnScreeningSearch.partnerName == "" ? this.searchForm.partnerName : this.myFilter?.data.txnScreeningSearch.partnerName;
    this.searchForm.qrId = this.myFilter?.data.txnScreeningSearch.qrId == "" ? this.searchForm.qrId : this.myFilter?.data.txnScreeningSearch.qrId;
    this.searchForm.cardNumber = this.myFilter?.data.txnScreeningSearch.cardNumber == "" ? this.searchForm.cardNumber : this.myFilter?.data.txnScreeningSearch.cardNumber;
    this.searchForm.accountNumber = this.myFilter?.data.txnScreeningSearch.accountNumber == "" ? this.searchForm.accountNumber : this.myFilter?.data.txnScreeningSearch.accountNumber;
    this.searchForm.nameOnCard = this.myFilter?.data.txnScreeningSearch.nameOnCard == "" ? this.searchForm.nameOnCard : this.myFilter?.data.txnScreeningSearch.nameOnCard;
    this.searchForm.transId = this.myFilter?.data.txnScreeningSearch.transId == "" ? this.searchForm.transId : this.myFilter?.data.txnScreeningSearch.transId;
    this.searchForm.epp = this.myFilter?.data.txnScreeningSearch.epp == "" ? this.searchForm.epp : this.myFilter?.data.txnScreeningSearch.epp;
    this.searchForm.qrChannelSelected = this.myFilter?.data.txnScreeningSearch.qrChannel == "" ? this.searchForm.qrChannelSelected : this.myFilter?.data.txnScreeningSearch.qrChannel.split(',');
    this.searchForm.cardSelected = this.myFilter?.data.txnScreeningSearch.cardList == "" ? this.searchForm.cardSelected : this.myFilter?.data.txnScreeningSearch.cardList.split(',');
    this.searchForm.encryptedCardNo = this.myFilter?.data.txnScreeningSearch.encryptedCardNo == "" ? this.searchForm.encryptedCardNo : this.myFilter?.data.txnScreeningSearch.encryptedCardNo;
    this.merchantEmail = this.myFilter?.data.txnScreeningSearch.merchantEmail == "" ? this.merchantEmail : this.myFilter?.data.txnScreeningSearch.merchantEmail;
    this.fraud = this.myFilter?.data.txnScreeningSearch.fraud == "" ? this.fraud : this.myFilter?.data.txnScreeningSearch.fraud;
    this.mcc = this.myFilter?.data.txnScreeningSearch.mcc == "" ? this.mcc : this.myFilter?.data.txnScreeningSearch.mcc;
    this.searchForm.mccDescription = this.myFilter?.data.txnScreeningSearch.mccDescription == "" ? this.searchForm.mccDescription : this.myFilter?.data.txnScreeningSearch.mccDescription;
    this.searchForm.url = this.myFilter?.data.txnScreeningSearch.url == "" ? this.searchForm.url : this.myFilter?.data.txnScreeningSearch.url;
    this.ipCountry = this.myFilter?.data.txnScreeningSearch.ipCountry == "" ? this.ipCountry : this.myFilter?.data.txnScreeningSearch.ipCountry;
    this.cardVerificationInfo = this.myFilter?.data.txnScreeningSearch.cardVerificationInfo == "" ? this.cardVerificationInfo : this.myFilter?.data.txnScreeningSearch.cardVerificationInfo;
    this.searchForm.otpChannel = this.myFilter?.data.txnScreeningSearch.otpChannel == "" ? this.searchForm.otpChannel : this.myFilter?.data.txnScreeningSearch.otpChannel;
    this.customerId = this.myFilter?.data.txnScreeningSearch.customerId == "" ? this.customerId : this.myFilter?.data.txnScreeningSearch.customerId;
    this.searchForm.cof = this.myFilter?.data.txnScreeningSearch.cof == "" ? this.searchForm.cof : this.myFilter?.data.txnScreeningSearch.cof;
    // this.searchForm.sourceOfFundSelected = this.myFilter?.data.txnScreeningSearch.sourceOfFund == "" ? this.searchForm.sourceOfFundSelected : this.myFilter?.data.txnScreeningSearch.sourceOfFund.split(',');
    this.searchForm.deviceId = this.myFilter?.data.txnScreeningSearch.deviceId == "" ? this.searchForm.deviceId : this.myFilter?.data.txnScreeningSearch.deviceId;
    this.searchForm.page = this.myFilter?.data.txnScreeningSearch.page;
    this.searchForm.pageSize = '200';

    localStorage.setItem('domestic_transaction_screening_filter', btoa(JSON.stringify(this.myFilter)));
  }

  deleteFilter(filterId: string) {
    this.confirmationService.confirm({
      rejectButtonStyleClass: "p-button p-button-secondary",
      message: `Are you sure to delete this filter?`,
      header: 'Delete filter',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      dismissableMask: true,
      accept: () => {
        this.transactionScreeningService.deleteMyFilter(filterId).subscribe(resp => {
          if (resp.message === 'Success') {
            this.dataComboboxData();
            this.toastr.success('Delete Filter successful!');
            this.clear();
            localStorage.clear();
          } else {
            this.toastr.error('Delete Filter fail!');
          }
        });
      }
    });
  }

  getLabel(object: any): string {
    let label = '';
    if (object) {
      label = object.label;
    }
    return label;
  }

  // saveResizeColumn(column: string) {
  //   this.searchForm.cols.forEach(e => {
  //     if (e.code == column
  //       && (document.getElementById(column+'Col') != null)
  //       && (document.defaultView.getComputedStyle(document.getElementById(column+'Col'), null).getPropertyValue('width') != '0px')) {
  //       e.width = document.defaultView.getComputedStyle(document.getElementById(column+'Col'), null).getPropertyValue('width');
  //     }
  //   });
  //   localStorage.setItem('domestic_transaction_screening_size_columns', btoa(JSON.stringify(this.searchForm.cols)));
  // }

  saveResizeColumn(e) {
    console.log("e.element.id: " + e.element.id);
    this.searchForm.columnItems.find(x => (x.code + 'Col') == e.element.id).width = document.defaultView.getComputedStyle(document.getElementById(e.element.id), null).getPropertyValue('width');
    this.searchForm.columnMap.get(e.element.id.replace("Col", "")).width = document.defaultView.getComputedStyle(document.getElementById(e.element.id), null).getPropertyValue('width');
    localStorage.setItem('fdm_domestic_transaction_screening_columns', btoa(JSON.stringify(this.searchForm.columnItems)));
  }

  convertFraudStatus(status): string {
    if (status == '1' || status == '2') {
      return 'Passed';
    } else if (status == '3') {
      return 'Reviewed';
    } else if (status == '4') {
      return 'Blocked';
    } else if (status == '0') {
      return 'No scan';
    }
  }

  setWidthColumn() {
    this.searchForm.columnItems.forEach(e => {
      if (e.active) document.getElementById(e.code + "Col").style.width = e.width;
    });
  }

}

export interface Filter {
  id: number
  name: string
  userName: string
  data: Data
}

export interface Data {
  columnDefault: any
  columnActive: string[]
  txnScreeningSearch: TxnScreeningSearch
  transId: any
}

export interface TxnScreeningSearch {
  fromDate: string
  toDate: string
  merchantGroup: string
  gate: string
  issuer: string
  fraudStatus: string
  responseCode: string
  transState: string
  pic: string
  transType: string
  markFraud: string
  amount: number
  merchantId: string
  orderRef: string
  ipAddress: string
  customerPhone: string
  customerEmail: string
  merchantTxnRef: string
  customerName: string
  partnerName: string
  qrId: string
  cardNumber: string
  accountNumber: string
  nameOnCard: string
  transId: string
  epp: string
  qrChannel: string
  cardList: string
  integrationType: string
  encryptedCardNo: string
  merchantEmail: string
  fraud: string
  mcc: string
  mccDescription: string
  url: string
  ipCountry: string
  cardVerificationInfo: string
  otpChannel: string
  customerId: string
  cof: string
  sourceOfFund: string
  deviceId: string
  page: number
  pageSize: number
}

const selectAllLabel = 'selectAll';
const unselectedLabel = 'unselected';
