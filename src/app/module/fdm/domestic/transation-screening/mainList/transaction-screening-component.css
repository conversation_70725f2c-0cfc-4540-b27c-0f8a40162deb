.text_width {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::ng-deep .p-tooltip {
  max-width: fit-content;
}

/* Ẩn button X trong search filter của p-multiSelect */
::ng-deep .p-multiselect-filter-container .p-multiselect-close {
  display: none !important;
}

/* Alternative selectors nếu trên không work */
::ng-deep .p-multiselect-panel .p-multiselect-close {
  display: none !important;
}

::ng-deep .p-multiselect-filter-container .p-inputtext-sm + .p-multiselect-close {
  display: none !important;
}

/* Hoặc ẩn icon clear trong input */
::ng-deep .p-multiselect-filter-container .pi-times {
  display: none !important;
}

/* Ẩn toàn bộ clear button */
::ng-deep .p-multiselect-filter-container button {
  display: none !important;
}

@media(max-width:480px ){

  .btn_download{
      font-weight: bold;
      color: #00529C;
  }

  .btn_search {
      width: 150px;
  }

  .input_search{
      width: 160px;
  }

  .row_iteam_search{
      margin-left: 0px;
      padding-top: 8px
  }


  .table_content{
      font-size: 12.39px;
      color: #333333;
      opacity: 100%;
  }

  .btn_navigation{
      padding-top: 2px;
  }

  .ps--active-y > .ps__rail-y {
    top: 0px !important;
  }

  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }


  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }

  }

  @media (max-width: 992px) and (min-width: 480px){


  .btn_download{
      font-weight: bold;
      color: #00529C;
  }

  .btn_search {
      width: 150px;
  }

  .input_search{
      width: 160px;
  }

  .row_iteam_search{
      margin-left: 0px;
      padding-top: 8px
  }


  .table_content{
      font-size: 12.39px;
      color: #333333;
      opacity: 100%;
  }

  .btn_navigation{
      padding-top: 2px;
  }


  .ps--active-y > .ps__rail-y {
    top: 0px !important;
  }

  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }


  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }

  }

  @media(min-width:992px){


  .btn_download{
      font-weight: bold;
      color: #00529C;
  }

  .btn_search {
      width: 150px;
  }

  .input_search{
      width: 160px;
  }

  .row_iteam_search{
      margin-left: 0px;
      padding-top: 8px
  }


  .table_content{
      font-size: 12.39px;
      color: #333333;
      opacity: 100%;
  }

  .btn_navigation{
      padding-top: 2px;
  }


  .ps--active-y > .ps__rail-y {
    top: 0px !important;
  }

  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }


  .mat-raised-button {
    width: 250px;
  }

  .mat-form-field{
    width: 250px;
  }

  .mat-input-element{
    color: #bbbbbb;
  }

  .mat-form-field-label{
    color: #bbbbbb !important;
  }

  .input.mat-input-element{
    color: #bbbbbb;
  }

  }

  #transaction-screening  .table {
    display: flex;
    flex-direction: column;
    max-height: 450px;
  }

  #transaction-screening   .mat-table {
    overflow: auto;
    max-height: 480px;
    border-width : 1px;
    border-color: #000000;
  }

  #transaction-screening   .mat-header-row {
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  background-color: inherit;
  }

    #transaction-screening {
      margin: 10px !important;
    }

    #transaction-screening .padding-left-right-0{
      padding-left: 0px !important;
    }

    #transaction-screening button {
      height: 30px !important;
    }

    #transaction-screening   #btn-download, #transaction-screening   #btn-down, #transaction-screening   #btn-up,#transaction-screening .btn-check{
      background-color: #00529c !important;
      border-color: #00529c !important;
    }

    #transaction-screening   #btn-down:hover{
      background-color: #0070d3 !important;
    }

    #transaction-screening   #btn-download:hover{
      background-color: #0070d3 !important;
    }

    #transaction-screening   #btn-up:hover{
      background-color: #0070d3 !important;
    }

    ::ng-deep  #transaction-screening .p-dropdown-label{
      width: 30px !important;
    }

    #transaction-screening .colunm-filter{
      height: 0px !important;
      padding: 0px !important;
      background-color: white !important;
    }

    ::ng-deep  #transaction-screening  .colunm-filter  .p-multiselect-trigger{
      background-color: #dcf0ff  !important;
    }

    #transaction-screening input{
      width: 100% !important;
    }

    ::ng-deep  #transaction-screening  .btn-paginator{
      background: none !important;
      border: none !important;
      padding: 0px 2px 2px 2px !important;
    }

    #transaction-screening  .btn-paginator, #transaction-screening  button{
      outline: none !important;
    }

    ::ng-deep  #transaction-screening .p-dropdown{
      height: 30px !important;
    }

    ::ng-deep  #transaction-screening .paginator-icon{
      color: black !important;
    }

    ::ng-deep  #transaction-screening .p-button:focus{
      box-shadow: none !important;
    }

    .hide-form-search #search-form {
      display: none !important;
      animation-delay: 2s;
    }

    ::ng-deep #show-form-search .p-datatable-scrollable-body {
      overflow: auto !important;
      height: calc(110vh - 480px) !important;
      max-height: calc(110vh - 480px) !important;
    }

    ::ng-deep #hide-form-search .p-datatable-scrollable-body {
      overflow: auto !important;
      height: calc(93vh - 240px) !important;
      max-height: calc(93vh - 240px) !important;
    }

    ::ng-deep  #transaction-screening .txt-filter{
      border: none !important;
    }

    /* ::ng-deep  #transaction-screening .btn-check{
      margin-left: -10px !important;
      margin-top: -10px !important;
    }     */
    ::ng-deep #transaction-screening  .p-multiselect{
      width: 100%;
    }
    ::ng-deep #transaction-screening .paginator-wrap span:first-child {
      display: none;
    }

    ::ng-deep #transaction-screening .paginator-icon {
      display: block !important;
    }

    .existedDispute {
      background-color: #ff7878 !important;
    }

    ::ng-deep #transaction-screening .ui-state-highlights td {
      font-weight: bold !important;
      background-color: var(--blue-100) !important;
      color: #0064d4 !important;
    }

    td, a {
      font-size: 11px;
    }

    th {
      height: 30px;
    }

    ::ng-deep  #transaction-screening .p-datatable .p-datatable-thead > .tr_header1 > th {
      padding: 10px 10px 0px 10px !important;
    }

    ::ng-deep  #transaction-screening .p-datatable .p-datatable-thead > .tr_header2 > th {
      padding: 0px 10px 10px 10px !important;
    }

    /* ::ng-deep  #transaction-screening .p-datatable-scrollable-body>table>.p-datatable-tbody>tr>td {
      padding: 5px 10px !important;
    } */

    #transaction-screening #search-form-hide-show {
      float: left;
      /* margin-top: 0.7rem; */
      width: 2.5rem;
    }
    #transaction-screening #selectedItem {
      width: 85%;
      float: right;
    }
    #transaction-screening #selected-number {
      width: 60%;
      float: left;
      text-align: right;
      margin-top: 0.5rem;
    }
    #transaction-screening #clear-selected {
      width: 6rem;
      float: right;
    }

    #transaction-screening #mark-fraud {
      width: 18%;
      float: left;
      margin-right: 1%;
    }
    #transaction-screening #unmark-fraud {
      width: 20%;
      float: left;
      margin-right: 1%;
    }

    #transaction-screening #add-blacklist {
      float: left;
      height: 1.4rem;
      width: 16%;
      margin-right: 1%;
    }
    #transaction-screening #blacklist {
      float: left;
      width:20%;
    }
    #transaction-screening #download {
      width: 9rem;
      float: right;
    }
    #transaction-screening #btn-search {
      margin-top: -10px;
      float: right;
      width: 10rem;
    }
    #transaction-screening #btn-clear {
      width: 10rem;
    }
    #transaction-screening #my-filter {
      width: 30%;
      float: left;
      /* text-align: right;
      margin-top: 0.5rem; */
    }
    #transaction-screening #btn-save-filter,#btn-save-as-filter {
      width: 8rem;
      float: left;
      margin-left: 1rem;
      margin-top: -0.7rem;
    }

    #transaction-screening #paging-controler {
      text-align: left;
      margin-top: -4.8rem;
    }

    .custom-tooltip .ui-tooltip-text {
      background-color: #f0f0f0;
      color: #333;
      font-size: 14px;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }

.mat-column-50 {
  width: 50px;
}

.mat-column-150 {
  width: 150px;
}

.mat-column-checkbox {
  width: 50px;
}

.mat-column-issuer {
  width: 150px;
}

.mat-column-order-reference {
  width: 150px;
}

.mat-column-merchant-txn-ref {
  width: 150px;
}

.mat-column-order-date {
  width: 150px;
}

.mat-column-amount {
  width: 150px;
}

.mat-column-currency {
  width: 150px;
}

.mat-column-card-number {
  width: 150px;
}

.mat-column-card-list {
  width: 150px;
}

.mat-column-tracking {
  width: 150px;
}

.mat-column-encrypted-card-no {
  width: 150px;
}

.mat-column-bank-info-telephone {
  width: 150px;
}

.mat-column-merchant-email {
  width: 150px;
}

.mat-column-fraud {
  width: 150px;
}

.mat-column-mcc-description {
  width: 150px;
}

.mat-column-merchant-id {
  width: 150px;
}

.mat-column-url {
  width: 150px;
}

.mat-column-ip-address {
  width: 150px;
}

.mat-column-ip-country {
  width: 150px;
}

.mat-column-card-verification-info {
  width: 150px;
}

.mat-column-trans-state {
  width: 150px;
}

.mat-column-qr-id {
  width: 150px;
}

.mat-column-qr-channel {
  width: 150px;
}

.mat-column-trans-id {
  width: 150px;
}

.mat-column-partner-name {

  width: 150px;
}

.mat-column-transaction-type {
  width: 150px;
}

.mat-column-response-code {
  width: 150px;
}

.mat-column-customer-email {
  width: 150px;
}

.mat-column-name-on-card {
  width: 150px;
}

.mat-column-expiry-date {
  width: 150px;
}

.mat-column-customer-name {
  width: 150px;
}

.mat-column-customer-phone {
  width: 150px;
}

.mat-column-merchant-group {
  width: 150px;
}

.mat-column-fraud-status {
  width: 150px;
}

.mat-column-pic {
  width: 150px;
}

.mat-column-epp {
  width: 150px;
}

.mat-column-integration-type {
  width: 150px;
}

#transaction-screening {
  margin: 10px !important;
}

::ng-deep #transaction-screening .p-datatable {
  .p-datatable-thead {
    .tr_header1>th {
      padding: 10px 10px 0px 10px !important;
    }

    .tr_header2>th {
      padding: 0px 10px 10px 10px !important;
    }
  }

  .p-datatable-tbody {
    font-size: 11px;
    tr>td {
      border-right: #D9D9DC 0.5px solid;
      /* padding: 5px 10px !important; */
      a {
        font-size: 11px;
      }
    }
  }

  .paginator-wrap>span:first-child {
    display: none;
  }

  .p-paginator-bottom {
    height: 40px;
    border: solid #e9ecef;
    border-width: 0px 0px 1px 0px;
  }
  .underline-on-hover:hover {
    text-decoration: underline;
  }

  .highlight {
    text-decoration: underline;
    color: blue;
  }
  .hightlineTooltip {
    color: blue;
    text-decoration: underline;
  }
  .tooltip-inner {
    max-width: 600px;
    background-color: #FFFFFF;
    color: black;
    box-shadow: 0 0 3px #00000040;
    text-align: left;
  }

  /* .custom-tooltip-container {
    max-width: 400px;
    background-color: #FFFFFF;
    color: black;
  } */

}

.addNote {
  display: none;
}

.i {
  display: none;
}

.orderRef:hover .addNote {
  display: inline-block;
}

.orderRef:hover .i {
  display: inline-block;
}

.cardNumber:hover .addNote {
  display: inline-block;
}

.cardNumber:hover .i {
  display: inline-block;
}

.ipAddress:hover .addNote {
  display: inline-block;
}

.ipAddress:hover .i {
  display: inline-block;
}

.customerEmail:hover .addNote {
  display: inline-block;
}

.customerEmail:hover .i {
  display: inline-block;
}

.customerPhone:hover .addNote {
  display: inline-block;
}

.customerPhone:hover .i {
  display: inline-block;
}

.tr_body td {
  height: 70px;
}

.tr_body:hover {
  background-color: #eaf3f7;
}

.row_column {
  padding: 0 !important;
  height: 3rem;
}

::ng-deep {
  .p-dialog-footer {
    display: flex;
    flex-direction: row-reverse;
  }

}