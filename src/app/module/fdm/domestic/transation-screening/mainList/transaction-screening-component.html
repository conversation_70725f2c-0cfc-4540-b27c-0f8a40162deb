<div id="transaction-screening" [ngClass]="'wrapper'">
  <div class="row">
    <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12 ">
      <span class="title_menu" style="margin-left: 10px !important;"
        (click)="console.log(searchForm.columnItems)">{{title}}</span>
    </div>
    <div class="padding-right-0 col-sm-12 col-md-12 col-lg-12" id="search-form">
      <transaction-screening-search (submitGeneralSearch)="onSubmit()" (clearGeneralSearch)="onClear()"
        (changeDisplayColumn)="setWidthColumn()" [merchantIdList]="merchantIdList">
      </transaction-screening-search>
    </div>
  </div>

  <br>

  <div class="container-fluid" style="margin-top: -10px !important;"
    [id]="showFormSearch == true ? 'show-form-search' : 'hide-form-search'">
    <div class="row">
      <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12">
        <p-table class="sticky-headers-table google-table" [(selection)]="selectedTrans" [value]="data"
          [rows]="searchForm.pageSize ? searchForm.pageSize : '200' " [lazy]="true" (onLazyLoad)="loadLazy($event)"
          [totalRecords]="resultsLength" pageLinks="0" [paginator]="true" [first]="searchForm.first" [scrollable]="true"
          [scrollHeight]="flexScrollHeight" [resizableColumns]="true" paginatorPosition="top" columnResizeMode="expand"
          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
          [columns]="searchForm.columnItems" (onColResize)="saveResizeColumn($event)">
          <ng-template pTemplate="caption">
            <div class="row">
              <div class="padding-right-0 col-sm-2 col-md-2 col-lg-2 ">
                <div class="ui-helper-clearfix clearfix" id="search-form-hide-show">
                  <button type="button" pButton label="" icon="pi pi-arrow-up" iconPos="center"
                    pTooltip="hide search form" tooltipPosition="top" (click)="checkFormSearch(false)"
                    class="download-button p-button-success btn-check" id="hide-search-form"
                    *ngIf="showFormSearch"></button>
                  <button type="button" pButton label="" icon="pi pi-arrow-down" iconPos="center"
                    pTooltip="show search form" tooltipPosition="top" (click)="checkFormSearch(true)"
                    class="download-button p-button-success btn-check" id="show-search-form"
                    *ngIf="!showFormSearch"></button>
                </div>
                <p-dropdown [options]="selectedFunctionList" [(ngModel)]="selectedFunction" [style]="{'width':'85%'}" placeholder="Select all page"
                  (onChange)="selectedReload(true)" appendTo="body">
                </p-dropdown>
              </div>
              <div class="padding-right-0 col-sm-4 col-md-4 col-lg-4 ">
                <button type="button" pButton label="Mark Fraud" icon="pi pi-check" iconPos="left" tooltipPosition="top"
                  [disabled]="this.selectedTrans.length < 1" (click)="markUnmarkSelected(1)" class="p-button-secondary"
                  id="mark-fraud"></button>
                <button type="button" pButton label="Unmark Fraud" icon="pi pi-times" iconPos="left"
                  tooltipPosition="top" [disabled]="this.selectedTrans.length < 1" (click)="markUnmarkSelected(0)"
                  class="p-button-secondary" id="unmark-fraud"></button>
                <button type="button" pButton label="Add Blacklist" class="p-button-danger"
                  [disabled]="this.selectedTrans.length < 1" (click)="addBlacklistSelected('','multiselect','')"
                  id="add-blacklist"></button>
                <!-- Blacklist multiselect -->
                <p-multiSelect appendTo="body" selectedItemsLabel="{0} items selected" defaultLabel="All"
                  [disabled]="this.selectedTrans.length < 1" [options]="blacklistList" [(ngModel)]="blacklist"
                  dropdownIcon="pi pi-angle-down" id="blacklist" #Blacklist="ngModel" name="blacklist"
                  maxSelectedLabels="1"></p-multiSelect>
                <button type="button" pButton label="Download" icon="pi pi-download" iconPos="left"
                  tooltipPosition="top" (click)="download()" class="download-button p-button-success btn-check"
                  id="download" style="width: 20%;"></button>
              </div>

              <div class="padding-right-0 col-sm-1 col-md-1 col-lg-1 ">
                <div class="button-group">
                  <div class="form-group">
                    <button type="button" pButton label="Search" class="download-button p-button-info" id="btn-search"
                      (click)="onSubmit()"></button>
                  </div>
                </div>
              </div>
              <div class="padding-right-0 col-sm-1 col-md-1 col-lg-1 ">
                <div class="button-group">
                  <div class="form-group">
                    <button type="button" pButton label="Clear" (click)="clear(); $event.preventDefault()"
                      class="download-button p-button-secondary" id="btn-clear"></button>
                  </div>
                </div>
              </div>

              <div class="padding-right-0 col-sm-2 col-md-2 col-lg-2 ">
                <!-- My filter -->

                <div class="form-group">
                  <span class="input-group">
                    <p-dropdown #dropdown appendTo="body" [style]="{'width':'150%'}" [options]="myFilterList"
                      [(ngModel)]="myFilter" dropdownIcon="pi pi-angle-down" #Myfilter="ngModel" id="my-filter"
                      name="myFilter" placeholder="Select a Filter" [showClear]="true" (onChange)="changeFilter()">
                      <ng-template pTemplate="selectedItem">
                        <div *ngIf="myFilter">
                          {{myFilter.name}}
                        </div>
                      </ng-template>
                      <ng-template let-filter pTemplate="item">
                        <div>
                          {{filter.label}}
                          <i class="pi pi-trash" style="float:right" (click)="deleteFilter(filter.value.id)"></i>
                        </div>
                      </ng-template>
                    </p-dropdown>
                    <label class="label-custom" for="myFilter">My filter</label>
                  </span>
                </div>

                <button type="submit" pButton label="Save" class="p-button-success" style="width: 50%; float: right;"
                  id="btn-save-filter" (click)="saveFilter(myFilter)">
                </button>
              </div>
            </div>
          </ng-template>
          <ng-template pTemplate="paginatorright" let-state>
            <div class="ui-helper-clearfix clearfix " id="paging-controler">
              <i style="float:right; margin-top: 5px; font-size: 1.5rem; margin-left: 1rem; position: relative;"
                class="pi pi-angle-double-right" (click)="goToLastPage()"></i>
              <div class="total-item" style="float:right; margin-right: -30px; margin-top: -5px;">
                <table-paginator [state]="state" [totalRecords]="resultsLength"></table-paginator>
              </div>
              <i style="float:right; margin-top: 5px; font-size: 1.5rem; margin-right: -2rem;"
                class="pi pi-angle-double-left" (click)="goToFirstPage()"></i>
              <div style="float:right; margin-left: 40px; margin-top: 5px;">
                <label class="label-custom" for="">of {{getTotalPage(state.totalRecords, state.rows)}}</label>
              </div>

              <div style="float:right; font-size: 14px; margin-right: -50px;">
                <div class="form-group">
                  <span class="input-group">
                    <p-dropdown appendTo="body" [style]="{'width':'69%'}" [options]="page" [(ngModel)]="searchForm.page"
                      [virtualScroll]="true" itemSize="10" (onChange)="onChangePage()" dropdownIcon="pi pi-angle-down"
                      #pageT="ngModel" name="page">
                    </p-dropdown>
                    <label class="label-custom" for=""></label>
                  </span>
                </div>
              </div>

              <div style="float:right; margin-top: 5px;">
                <label class="label-custom" for="">Selected {{selectedFunction == 'selectAll' ? resultsLength : selectedTrans.length}}. Page</label>
              </div>
            </div>
          </ng-template>

          <ng-template pTemplate="colgroup" let-columns>
            <colgroup>
              <ng-container *ngFor="let col of columns">
                <col *ngIf="checkColumn(col.code)" [id]="col.code + 'Col'">
              </ng-container>
            </colgroup>
          </ng-template>
          <ng-template pTemplate="header" let-columns>
            <tr class="tr_header1">
              <ng-container *ngFor="let col of columns">
                <th *ngIf="col.active === true" class="text-center" [id]="col.code + 'Col'" pResizableColumn>
                  <div *ngIf="col.active === true">{{col.name}}</div>
                </th>
              </ng-container>
            </tr>
            <tr class="tr_header2">
              <ng-container *ngFor="let col of columns">
                <th *ngIf="col.active === true" class="text-center" [id]="col.code + 'Col'" pResizableColumn>
                  <p-tableHeaderCheckbox (click)="selectedReload(true)" *ngIf="col.code === ('no') && col.active === true">
                  </p-tableHeaderCheckbox>

                  <p-multiSelect [(ngModel)]="searchForm.gateSelected" [options]="searchForm.gateList" appendTo="body"
                    placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('gate') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter"
                      [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.issuerSelected" [options]="searchForm.issuerList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('issuer') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.merchantIdSelected" [options]="searchForm.merchantIdFilteredList"
                    [virtualScroll]="true" [virtualScrollItemSize]="43" appendTo="body" placeholder="All"
                    maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                    [filter]="true" [filterBy]="'label'" (onFilter)="searchForm.searchMerchantId($event)"
                    (onShow)="searchForm.onShowDropdown()" resetFilterOnHide="true"
                    [loading]="searchForm.merchantIdLoading"
                    *ngIf="col.code === ('merchantId') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.orderRef"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('orderReference') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.merchantTxnRef"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('merchantTxnRef') && col.active === true" />

                  <input pInputNumber type="number" class="txt-filter" [(ngModel)]="searchForm.amountMore"
                    style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('amount') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.cardNumber"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('cardNumber') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.cardSelected" [options]="searchForm.cardList" appendTo="body"
                    placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('cardList') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.encryptedCardNo"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('encryptedCardNo') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="bankInfoTelephone"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('bankInfoTelephone') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="merchantEmail"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('merchantEmail') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="fraud" (ngModelChange)="filter()"
                    autocomplete="off" maxlength="50" *ngIf="col.code === ('fraud') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="mcc" (ngModelChange)="filter()"
                    autocomplete="off" maxlength="50" *ngIf="col.code === ('mcc') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.mccDescription" autocomplete="off"
                    (ngModelChange)="filter()" maxlength="250"
                    *ngIf="col.code === ('mccDescription') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.url" (ngModelChange)="filter()"
                    autocomplete="off" maxlength="50" *ngIf="col.code === ('url') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.ipAddress" autocomplete="off"
                    (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('ipAddress') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="ipCountry" (ngModelChange)="filter()"
                    autocomplete="off" maxlength="50" *ngIf="col.code === ('ipCountry') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="cardVerificationInfo" autocomplete="off"
                    (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('cardVerificationInfo') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.transStateSelected" [options]="searchForm.transStateList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('transState') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.qrId" autocomplete="off"
                    (ngModelChange)="filter()" maxlength="50" *ngIf="col.code === ('qrId') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.qrChannelSelected" [options]="searchForm.qrChannelList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('qrChannel') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.transId" autocomplete="off"
                    (ngModelChange)="filter()" maxlength="50" *ngIf="col.code === ('transId') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.partnerNameSelected" [options]="searchForm.partnerNameList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('partnerName') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.transTypeSelected" [options]="searchForm.transTypeList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('transactionType') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.responseCodeSelected" [options]="searchForm.responseCodeList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('responseCode') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.customerEmail"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('customerEmail') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.nameOnCard"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('nameOnCard') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.customerName"
                    (ngModelChange)="filter()" maxlength="50" autocomplete="off"
                    *ngIf="col.code === ('customerName') && col.active === true" />

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.customerPhone"
                    autocomplete="off" style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('customerPhone') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.merchantGroup" [options]="searchForm.merchantGroupList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('merchantGroup') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1" [ngStyle]="{'color': option.priority == 2 ? 'gray' : 'black'}">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.fraudStatus" [options]="searchForm.fraudStatusList"
                    appendTo="body" placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('fraudStatus') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <p-multiSelect [(ngModel)]="searchForm.pic" [options]="searchForm.picList" appendTo="body"
                    placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                    selectedItemsLabel="{0} values selected" *ngIf="col.code === ('pic') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>

                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.epp" style="height: 2.5rem;"
                    autocomplete="off" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('epp') && col.active === true" />

                  <p-multiSelect [(ngModel)]="searchForm.integrationTypeSelected"
                    [options]="searchForm.integrationTypeList" appendTo="body" placeholder="All" maxSelectedLabels="1"
                    (onChange)="filter()" selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('integrationType') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect>
                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.otpChannel"
                    style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('otpChannel') && col.active === true" />
                  <input pInputText type="text" class="txt-filter" [(ngModel)]="customerId"
                    style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('customerId') && col.active === true" />
                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.cof"
                    style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('cof') && col.active === true" />
                  <!-- <p-multiSelect [(ngModel)]="searchForm.sourceOfFundSelected"
                    [options]="searchForm.sourceOfFundList" appendTo="body" placeholder="All" maxSelectedLabels="2"
                    (onChange)="filter()" selectedItemsLabel="{0} values selected"
                    *ngIf="col.code === ('sourceOfFund') && col.active === true">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-multiSelect> -->
                  <input pInputText type="text" class="txt-filter" [(ngModel)]="searchForm.deviceId"
                    style="height: 2.5rem;" (ngModelChange)="filter()" maxlength="50"
                    *ngIf="col.code === ('deviceId') && col.active === true" />
                </th>
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-data let-rowIndex="rowIndex" let-columns="columns">
            <tr class="tr_body">
              <ng-container *ngFor="let col of columns">
                <td class="text-center ui-resizable-column" *ngIf="col.active === true" style="height: 30px;"
                  [ngStyle]="{'width': col.width,'padding':'0 !important'}">
                  <div *ngIf="col.code === 'no' && col.active === true">
                    <p-tableCheckbox [value]="data" (click)="selectedReload(true)"></p-tableCheckbox>
                    {{ rowIndex + 1 }} <b *ngIf="data.markFraud == 1" style="color: red;">F</b>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'gate' && col.active === true">{{data.gate}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'issuer' && col.active === true">{{data.issuer}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'merchantId' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('merchantId',data.merchantId)">{{data.merchantId}}</a>
                  </div>

                  <div class="orderRefWrapper text-left" style="position: relative;">
                    <div class="orderRef text-left" [ngStyle]="markColorFraudStatus(data.fraudStatus, data.fraud)"
                      *ngIf="col.code === 'orderReference' && col.active === true"
                      style="min-height: 30px; display: flex; justify-content: left; align-items: center;"> <!-- Cố định chiều cao -->
                      
                      <a [routerLink]="" class="underline-on-hover"
                        (click)="routerLinkSearch('orderRef', data.orderRef)">
                        {{ data.orderRef }}
                      </a>

                      <!-- Sửa: ẩn bằng visibility thay vì *ngIf -->
                      <p-button class="addNote" icon="pi pi-pencil" styleClass="p-button-rounded p-button-text"
                        pTooltip="Add Note"
                        (click)="clickEdit('orderRef', data.transId)"
                        [ngStyle]="{
                          visibility: checkEditNote('orderRef', data.transId) ? 'visible' : 'hidden',
                          position: 'absolute',
                          right: '30px',
                          top: '0px'
                        }">
                      </p-button>

                      <p-overlayPanel #invoice appendTo="body">
                        <ng-template pTemplate>
                          <div class="custom-tooltip-container">
                            {{ orderRefTooltip }}
                            <b [ngStyle]="markColorFraudStatus(data.fraudStatus, data.fraud)">
                              {{ data.orderRef == '' ? '' : data.orderRef }}
                            </b>
                          </div>
                        </ng-template>
                      </p-overlayPanel>

                      <!-- Nút info - cũng ẩn bằng visibility -->
                      <p-button class="i" icon="pi pi-info-circle i" styleClass="p-button-rounded p-button-text"ddddd
                        (click)="detailInvoiceOrderRef(data.merchantId, data.merchantTxnRef)"
                        (mouseover)="invoice.toggle($event)" (mouseleave)="invoice.hide()"
                        [ngStyle]="{
                          position: 'absolute',
                          right: '0px',
                          top: '0px'
                        }">
                      </p-button>
                    </div>
                    <div *ngIf="col.code === 'orderReference' && col.active === true">
                      <!-- Ô ghi chú -->
                      <ng-container *ngIf="!checkEditNote('orderRef', data.transId) && (disableNote(data.orderNote) || !checkEditNote('orderRef', data.transId)); else elseBlock">
                        <span class="input-group">
                          <input type="text" id="orderNote" [(ngModel)]="data.orderNote" #OrderNote="ngModel"
                            style="height: 15px;" name="orderNote" maxlength="200"
                            [disabled]="checkEditNote('orderRef', data.transId)"
                            (keyup.enter)="onEnterOrderRef(data.orderRef, data.merchantId, data.orderNote, data.transId)"
                            tabindex="1" autocomplete="off" autocapitalize="off" pInputText>
                        </span>
                      </ng-container>

                      <!-- Else -->
                      <ng-template #elseBlock>
                        <span class="input-group" style="color: black; word-wrap: break-word; word-break: break-word; white-space: normal; display: block;">
                          {{ data.orderNote }}
                        </span>
                      </ng-template>
                    </div>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'merchantTxnRef' && col.active === true">{{data.merchantTxnRef}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'orderDate' && col.active === true">
                    {{data.orderDate | date: 'dd/MM/yyyy HH:mm:ss'}}</div>

                  <div class="text-right ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'amount' && col.active === true">{{data.amount | number : '1.0-0'}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'currency' && col.active === true">{{data.currency}}</div>

                  <div class="cardNumber" [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'cardNumber' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('encryptedCardNo',data.hashCardNo)">{{data.cardNumber}}</a>
                    <p-button class="addNote" icon="pi pi-pencil" styleClass="p-button-rounded p-button-text"
                      pTooltip="Add Note" (click)="clickEdit('cardNumber',data.transId)"
                      *ngIf="checkEditNote('cardNumber',data.transId)"></p-button>
                    <p-overlayPanel [id]='"panel_"+rowIndex' #card appendTo='body' (mouseleave)="card.hide()">
                      <ng-template pTemplate>
                        <span>- Encrypted Card No: <b style="color: blue;">{{data.hashCardNo}}</b></span>
                        <br>- Add Card into Blacklist for <a [routerLink]=""
                          (click)="addBlacklistSelected(data,'Card','all')"
                          style="color:blue;text-decoration: underline">All Merchant</a>
                        or only Merchant <a [routerLink]="" (click)="addBlacklistSelected(data,'Card',data.merchantId)"
                          style="color:blue;text-decoration: underline">{{data.merchantId}}</a>
                      </ng-template>
                    </p-overlayPanel>
                    <p-button class="i" icon="pi pi-ellipsis-v" styleClass="p-button-rounded p-button-text"
                      (click)="card.toggle($event)"></p-button>
                    <span class="input-group"
                      *ngIf="disableNote(data.cardNote) || !checkEditNote('cardNumber',data.transId)">
                      <input type="text" id="cardNumber" [(ngModel)]="data.cardNote" #CardNumber="ngModel"
                        style="height: 15px;" name="cardNumber" maxlength="200"
                        [disabled]="checkEditNote('cardNumber',data.transId)"
                        (keyup.enter)="onEnterCardNumber(data.hashCardNo,data.cardNumber,data.cardNote,data.transId)"
                        tabindex="1" autocomplete="off" autocapitalize="off" pInputText>
                    </span>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'cardList' && col.active === true">{{data.cardList}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'encryptedCardNo' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('encryptedCardNo',data.hashCardNo)">{{data.hashCardNo}}</a>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'bankInfoTelePhone' && col.active === true">{{data.bankInfoTelePhone}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'merchantEmail' && col.active === true">{{data.merchantEmail}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'fraud' && col.active === true">{{data.fraud}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'mcc' && col.active === true">{{data.mcc}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'mccDescription' && col.active === true">{{data.mccDesc}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'url' && col.active === true">{{data.url}}</div>

                  <div class="ipAddress text-left" [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'ipAddress' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('ipAddress',data.ipAddress)">{{data.ipAddress}}</a>
                    <p-button class="addNote" icon="pi pi-pencil" styleClass="p-button-rounded p-button-text"
                      pTooltip="Add Note" (click)="clickEdit('ip',data.transId)"
                      *ngIf="checkEditNote('ip',data.transId)"></p-button>
                    <p-overlayPanel [id]='"panel_"+rowIndex' #ip appendTo='body' (mouseleave)="ip.hide()">
                      <ng-template pTemplate>
                        <span>- Ip Address: <b style="color: blue;">{{data.ipAddress}}</b></span>
                        <br>- Add Ip Adress into Blacklist for <a [routerLink]=""
                          (click)="addBlacklistSelected(data,'IP','all')"
                          style="color:blue;text-decoration: underline">All
                          Merchant</a>
                        or only Merchant <a [routerLink]="" (click)="addBlacklistSelected(data,'IP',data.merchantId)"
                          style="color:blue;text-decoration: underline">{{data.merchantId}}</a>
                      </ng-template>
                    </p-overlayPanel>
                    <p-button class="i" icon="pi pi-ellipsis-v" styleClass="p-button-rounded p-button-text"
                      (click)="ip.toggle($event)"></p-button>
                    <span class="input-group" *ngIf="disableNote(data.ipNote) || !checkEditNote('ip',data.transId)">
                      <input type="text" id="ip" [(ngModel)]="data.ipNote" #IP="ngModel" name="ip" maxlength="200"
                        style="height: 15px;" [disabled]="checkEditNote('ip',data.transId)"
                        (keyup.enter)="onEnterIp(data.ipAddress,data.ipNote,data.transId)" tabindex="1"
                        autocomplete="off" autocapitalize="off" pInputText>
                    </span>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'ipCountry' && col.active === true">{{data.ipCountry}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'cardVerificationInfo' && col.active === true">{{data.cardVerificationInfo}}
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'transState' && col.active === true">{{data.transState}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'qrId' && col.active === true">{{data.qrId}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'qrChannel' && col.active === true">{{data.qrChannel}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'transId' && col.active === true">{{data.transId}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'partnerName' && col.active === true">{{data.partnerName}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'transactionType' && col.active === true">{{data.transType}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'responseCode' && col.active === true">{{data.responseCode}}</div>

                  <div class="customerEmail" [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'customerEmail' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('customerEmail',data.customerEmail)">{{data.customerEmail}}</a>
                    <p-button class="addNote" icon="pi pi-pencil" styleClass="p-button-rounded p-button-text"
                      pTooltip="Add Note" (click)="clickEdit('customerEmail',data.transId)"
                      *ngIf="checkEditNote('customerEmail',data.transId)"></p-button>
                    <p-overlayPanel [id]='"panel_"+rowIndex' #email appendTo='body' (mouseleave)="email.hide()">
                      <ng-template pTemplate>
                        <span>- Email: <b style="color: blue;">{{data.customerEmail}}</b></span>
                        <br>- Add Email into Blacklist for <a [routerLink]=""
                          (click)="addBlacklistSelected(data,'Email','all')"
                          style="color:blue;text-decoration: underline">All Merchant</a>
                        or only Merchant <a [routerLink]="" (click)="addBlacklistSelected(data,'Email',data.merchantId)"
                          style="color:blue;text-decoration: underline">{{data.merchantId}}</a>
                      </ng-template>
                    </p-overlayPanel>
                    <p-button class="i" icon="pi pi-ellipsis-v" styleClass="p-button-rounded p-button-text"
                      (click)="email.toggle($event)"></p-button>
                    <span class="input-group"
                      *ngIf="disableNote(data.emailNote) || !checkEditNote('customerEmail',data.transId)">
                      <input type="text" id="email" [(ngModel)]="data.emailNote" #Email="ngModel" name="email"
                        style="height: 15px;" maxlength="200" [disabled]="checkEditNote('customerEmail',data.transId)"
                        (keyup.enter)="onEnterEmail(data.customerEmail,data.emailNote,data.transId)" tabindex="1"
                        autocomplete="off" autocapitalize="off" pInputText>
                    </span>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'nameOnCard' && col.active === true">{{data.nameOnCard}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'expireDate' && col.active === true">{{data.expireDate}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'customerName' && col.active === true">{{data.customerName}}</div>

                  <div class="customerPhone" [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'customerPhone' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                      (click)="routerLinkSearch('customerPhone',data.customerPhone)">{{data.customerPhone}}</a>
                    <p-button class="addNote" icon="pi pi-pencil" styleClass="p-button-rounded p-button-text"
                      pTooltip="Add Note" (click)="clickEdit('phone',data.transId)"
                      *ngIf="checkEditNote('phone',data.transId)"></p-button>
                    <p-overlayPanel [id]='"panel_"+rowIndex' #phone appendTo='body' (mouseleave)="phone.hide()">
                      <ng-template pTemplate>
                        <span>- Phone: <b style="color: blue;">{{data.customerPhone}}</b></span>
                        <br>- Add Phone into Blacklist for <a [routerLink]=""
                          (click)="addBlacklistSelected(data,'Phone','all')"
                          style="color:blue;text-decoration: underline">All Merchant</a>
                        or only Merchant <a [routerLink]="" (click)="addBlacklistSelected(data,'Phone',data.merchantId)"
                          style="color:blue;text-decoration: underline">{{data.merchantId}}</a>
                      </ng-template>
                    </p-overlayPanel>
                    <p-button class="i" icon="pi pi-ellipsis-v" styleClass="p-button-rounded p-button-text"
                      (click)="phone.toggle($event)"></p-button>
                    <span class="input-group"
                      *ngIf="disableNote(data.phoneNote) || !checkEditNote('phone',data.transId)">
                      <input type="text" id="phone" [(ngModel)]="data.phoneNote" #Phone="ngModel" name="phone"
                        style="height: 15px;" maxlength="200" [disabled]="checkEditNote('phone',data.transId)"
                        (keyup.enter)="onEnterPhone(data.customerPhone,data.phoneNote,data.transId)" tabindex="1"
                        autocomplete="off" autocapitalize="off" pInputText>
                    </span>
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'merchantGroup' && col.active === true">{{data.merchantGroup}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'fraudStatus' && col.active === true">{{convertFraudStatus(data.fraudStatus)}}
                  </div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'pic' && col.active === true">{{data.pic}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'epp' && col.active === true">{{data.epp}}</div>

                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'integrationType' && col.active === true">{{data.integrationType}}</div>
                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'otpChannel' && col.active === true">{{data.otpChannel}}</div>
                  <div class="customerId" [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                  *ngIf="col.code === 'customerId' && col.active === true">
                    <a [routerLink]="" class="underline-on-hover"
                    (click)="routerLinkSearch('customerId',data.customerId)">{{data.customerId}}</a>
                  </div>
                  <div class="text-left ui-resizable-column"
                    [ngStyle]="markColorFraudStatus(data.fraudStatus,data.fraud)"
                    *ngIf="col.code === 'cof' && col.active === true">{{data.cof}}</div>
                  <div class="text-left ui-resizable-column"
                    *ngIf="col.code === 'sourceOfFund' && col.active === true">{{data.sourceOfFund}}
                  </div>
                  <div class="text-left ui-resizable-column"
                    *ngIf="col.code === 'deviceId' && col.active === true">{{data.deviceId}}
                  </div>
                </td>
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>

</div>

<p-toast></p-toast>
<p-confirmDialog [style]="{width: '300'}"></p-confirmDialog>
