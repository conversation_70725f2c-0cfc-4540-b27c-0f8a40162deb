import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TransactionScreeningService } from '@service/fdm/domestic/transaction-screening.service';
import { SelectItem } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { ColumnDisplayComponent } from '../display-column/column-display.component';
import { CommonService } from '@service/fdm/common.service';

export interface Column {
  name: string,
  code: string,
  order: number,
  active: boolean,
  width: string
}

export class Dropdowns {
  label: string;
  value: string;

  constructor(data?: Dropdowns) {
    this.label = data === undefined ? '' : this.getDataValue(data.label);
    this.value = data === undefined ? '' : this.getDataValue(data.value);
  }

  private getDataValue(val) {
    return val || '';
  }
}
@Component({
  selector: 'transaction-screening-search',
  templateUrl: './transaction-screening-search.component.html',
  styleUrls: ['./transaction-screening-search.component.css'],
  providers: [TransactionScreeningService, CommonService, DialogService]
})

export class TransactionScreeningSearch {

  public from_date: string;
  public to_date: string;
  public fromDate: Date;
  public toDate: Date;
  public columnItems: Column[];
  public originColumnItems: Column[];
  public columnItemsMap: Map<string, any>;
  public columnMap: Map<string, any>;
  public coLumnSelected: any;
  // public columnList: Array<any>;

  public pageSizeList: Array<any>;
  public merchantGroupList: SelectItem[] = [];
  public gateList: SelectItem[] = [];
  public issuerList: SelectItem[] = [];
  public fraudStatusList: SelectItem[] = [];
  public responseCodeList: SelectItem[] = [];
  public transStateList: SelectItem[] = [];
  public picList: SelectItem[] = [];
  public transTypeList: SelectItem[] = [];
  public markFraudList: SelectItem[] = [];
  public merchantIdSelected: Array<any> = [];
  @Input() merchantIdList: SelectItem[] = [];
  public merchantIdFilteredList: SelectItem[] = [];
  public merchantIdLoading: boolean = false;
  private searchTimeout: any;
  public orderRef: string;
  public ipAddress: string;
  public customerPhone: string;
  public customerEmail: string;
  public merchantTxnRef: string;
  public customerName: string;
  public partnerNameSelected: Array<any> = [];
  public partnerNameList: SelectItem[] = [];
  public qrId: string;
  public cardNumber: string;
  public accountNumber: string;
  public nameOnCard: string;
  public transId: string;
  public epp: string;
  public userId: string;
  public cof: string;
  public otpChannel: string;
  public url: string;
  public fraud: string;
  public cardVerificationInfo: string;
  public mccDescription: string;

  public merchantGroup: Array<any> = [];
  public gateSelected: Array<any>;
  public otpChannelSelected: Array<any>;
  public issuerSelected: Array<any>;
  public fraudStatus: Array<any>;
  public responseCodeSelected: Array<any>;
  public transStateSelected: Array<any>;
  public pic: Array<any>;
  public transTypeSelected: Array<any>;
  public markFraud: Array<any>;
  // public currentcy: Array<any>;
  public amountMore: any;
  public encryptedCardNo: string;
  public customerId: string;
  public sourceOfFundList: Array<any>;
  public sourceOfFundSelected: Array<any>;
  public deviceId: string;

  public qrChannelList: Array<any>;
  public qrChannelSelected: Array<any> = [];
  public cardList: Array<any>;
  public cardSelected: Array<any> = [];
  public integrationTypeList: Array<any>;
  public integrationTypeSelected: Array<any> = [];

  public showFormSearch = true;
  public searchBref: string = '';

  pipe = new DatePipe('en-US');

  public page = 1;
  public first = 0;
  public pageSize = '200';

  private columnDisplayRef: any;
  // public cols: Column[];

  @Output()
  private submitGeneralSearch = new EventEmitter<any>();

  @Output()
  private clearGeneralSearch = new EventEmitter<any>();

  @Output()
  private syncTransaction = new EventEmitter<any>();


  constructor(public dialogService: DialogService
    , private transactionScreeningService: TransactionScreeningService
    , private commonService: CommonService) {
    this.columnItems = [
      { name: 'No.', code: 'no', order: 0, active: true, width: '60.69px' },
      { name: 'Gate', code: 'gate', order: 1, active: true, width: '132.05px' },
      { name: 'Issuer', code: 'issuer', order: 2, active: false, width: '182.05px' },
      { name: 'Merchant ID', code: 'merchantId', order: 3, active: true, width: '182.05px' },
      { name: 'Order Reference', code: 'orderReference', order: 4, active: true, width: '182.05px' },
      { name: 'Merchant Txn Ref', code: 'merchantTxnRef', order: 5, active: false, width: '182.05px' },
      { name: 'Order Date', code: 'orderDate', order: 6, active: true, width: '182.05px' },
      { name: 'Amount', code: 'amount', order: 7, active: true, width: '182.05px' },
      { name: 'Currency', code: 'currency', order: 8, active: true, width: '182.05px' },
      { name: 'Card Number', code: 'cardNumber', order: 9, active: true, width: '182.05px' },
      { name: 'Card List', code: 'cardList', order: 10, active: false, width: '182.05px' },
      { name: 'Encrypted Card No', code: 'encryptedCardNo', order: 11, active: false, width: '182.05px' },
      // { name: 'Bank Info Telephone', code: 'bankInfoTelephone', order: 12, active: false, width: '182.05px' },
      // { name: 'Merchant Email', code: 'merchantEmail', order: 13, active: false, width: '182.05px' },
      { name: 'Fraud', code: 'fraud', order: 14, active: true, width: '182.05px' },
      { name: 'MCC Description', code: 'mccDescription', order: 15, active: false, width: '182.05px' },
      { name: 'URL', code: 'url', order: 16, active: false, width: '182.05px' },
      { name: 'IP Address', code: 'ipAddress', order: 17, active: true, width: '182.05px' },
      { name: 'IP Country', code: 'ipCountry', order: 18, active: false, width: '182.05px' },
      { name: 'Card Verification Info', code: 'cardVerificationInfo', order: 19, active: false, width: '182.05px' },
      { name: 'Trans. State', code: 'transState', order: 20, active: true, width: '182.05px' },
      { name: 'QR ID', code: 'qrId', order: 21, active: false, width: '182.05px' },
      { name: 'QR Channel', code: 'qrChannel', order: 22, active: false, width: '182.05px' },
      { name: 'Trans. ID', code: 'transId', order: 23, active: true, width: '182.05px' },
      { name: 'Partner Name', code: 'partnerName', order: 24, active: false, width: '182.05px' },
      { name: 'Transaction Type', code: 'transactionType', order: 25, active: false, width: '182.05px' },
      { name: 'Response Code', code: 'responseCode', order: 26, active: false, width: '182.05px' },
      { name: 'Customer Email', code: 'customerEmail', order: 27, active: false, width: '182.05px' },
      { name: 'Name on Card', code: 'nameOnCard', order: 28, active: false, width: '182.05px' },
      // { name: 'Expiry Date', code: 'expiryDate', order: 29, active: false, width: '182.05px' },
      { name: 'Customer Name', code: 'customerName', order: 30, active: false, width: '182.05px' },
      { name: 'Customer Phone', code: 'customerPhone', order: 31, active: false, width: '182.05px' },
      { name: 'Device ID', code: 'deviceId', order: 32, active: false, width: '182.05px' },
      { name: 'Merchant Group', code: 'merchantGroup', order: 33, active: false, width: '182.05px' },
      { name: 'Fraud Status', code: 'fraudStatus', order: 34, active: false, width: '182.05px' },
      { name: 'PIC', code: 'pic', order: 35, active: false, width: '182.05px' },
      { name: 'EPP', code: 'epp', order: 36, active: false, width: '182.05px' },
      { name: 'Integration Type', code: 'integrationType', order: 37, active: false, width: '182.05px' },
      { name: 'OTP Channel', code: 'otpChannel', order: 38, active: false, width: '182.05px' },
      { name: 'User Id', code: 'customerId', order: 39, active: false, width: '182.05px' },
      { name: 'COF', code: 'cof', order: 40, active: false, width: '112.05px' },
      // { name: 'Card Type', code: 'sourceOfFund', order: 41, active: false, width: '112.05px' },
    ];
  }

  async init(param?: any) {

    const setFromDate = new Date(new Date().setHours(0, 0, 0, 0));
    this.fromDate = param['fromDate'] !== undefined ? new Date(param['fromDate']) : setFromDate;
    this.toDate = param['toDate'] !== undefined ? new Date(param['toDate']) : new Date(new Date().setHours(23, 59, 59, 0));
    this.page = param['page'] ? parseInt(param['page']) : 1;
    this.pageSize = param['pageSize'] ? param['pageSize'] : '200';
    this.first = param['first'] ? parseInt(param['first']) : 0;

    this.orderRef = param['orderRef'] ? param['orderRef'] : '';
    this.cardNumber = param['cardNumber'] ? param['cardNumber'] : '';
    this.ipAddress = param['ipAddress'] ? param['ipAddress'] : '';
    this.showFormSearch = param['showFormSearch'] ? (param['showFormSearch'] != 'true') : false;
    this.merchantIdSelected = param['merchantId'] ? param['merchantId'].split(',') : [];
    this.customerPhone = param['customerPhone'] ? param['customerPhone'] : "";
    this.customerEmail = param['customerEmail'] ? param['customerEmail'] : "";
    this.partnerNameSelected = param['partnerName'] ? param['partnerName'].split(',') : [];
    this.encryptedCardNo = param['encryptedCardNo'] ?? '';
    this.customerId = param['customerId'] ??'';
    this.gateSelected = param['gate'] ? param['gate'].split(',') : [];
    // this.sourceOfFundSelected = param['sourceOfFund'] ? param['sourceOfFund'].split(',') : [];
    this.deviceId = param['deviceId'] || '';
    this.merchantTxnRef = param['merchantTxnRef'] || '';
    this.cardSelected = param['cardList'] ? param['cardList'].split(',') : [];
    this.issuerSelected = param['issuer'] ? param['issuer'].split(',') : [];
    this.transStateSelected = param['transState'] ? param['transState'].split(',') : [];
    this.pic = param['pic'] ? param['pic'].split(',') : [];
    this.transTypeSelected = param['transType'] ? param['transType'].split(',') : [];
    this.fraudStatus = this.parseFraudStatus(param['fraudStatus']);
    this.integrationTypeSelected = param['integrationType'] ? param['integrationType'].split(',') : [];
    this.otpChannel = param['otpChannel'] ?? '';
    this.qrChannelSelected = param['qrChannel'] ? param['qrChannel'].split(',') : [];
    this.cof = param['cof'] ?? '';
    this.epp = param['epp'] ?? '';
    this.transId = param['transId'] ?? '';
    this.amountMore = param['amount'] ?? 0;
    this.qrId = param['qrId'] ?? '';  
    this.customerName = param['customerName'] ?? '';
    this.nameOnCard = param['nameOnCard'] ?? '';
    this.accountNumber = param['accountNumber'] ?? '';
    this.merchantGroup = param['merchantGroup'] ? param['merchantGroup'].split(',') : [];
    this.url = param['url'] ?? '';
    this.fraud = param['fraud'] ?? '';
    this.cardVerificationInfo = param['cardVerificationInfo'] ?? '';
    this.responseCodeSelected = param['responseCode'] ? param['responseCode'].split(',') : [];
    this.mccDescription = param['mccDescription'] ?? '';

  
    this.pageSizeList = [
      {
        label: '100',
        value: '100'
      },
      {
        label: '150',
        value: '150'
      },
      {
        label: '200',
        value: '200'
      }
    ];

    this.fraudStatusList = [
      {
        value: '1,2',
        label: 'Passed'
      },
      {
        value: '3',
        label: 'Reviewed'
      },
      {
        value: '4',
        label: 'Blocked'
      },
      {
        value: '0',
        label: 'No scan'
      }
    ];

    this.markFraudList = [
      {
        value: '1',
        label: 'Mark fraud'
      },
      {
        value: '0',
        label: 'Unmark fraud'
      }
    ];

    this.integrationTypeList = [
      {
        value: 'Website / App',
        label: 'Website / App'
      },
      {
        value: 'Invoice',
        label: 'Invoice'
      },
      {
        value: 'Quicklink',
        label: 'Quicklink'
      }
    ];
    this.sourceOfFundList = [
      {
        value: 'debit',
        label: 'Debit'
      },
      {
        value: 'credit',
        label: 'Credit'
      }
    ];

    this.columnItems = this.checkVersionLocalStorage();

    await this.dataComboboxData();
    this.searchBrefInfo();
  }

  parseFraudStatus(input: string): string[] {
  if (!input) return [];

  const parts = input.split(',');
  const result: string[] = [];
  let i = 0;

  while (i < parts.length) {
    if (parts[i] === '1' && parts[i + 1] === '2') {
      result.push('1,2');
      i += 2; // bỏ qua cả 1 và 2
    } else {
      result.push(parts[i]);
      i++;
    }
  }

  return result;
}

  checkVersionLocalStorage(): Column[] {
    let currentVersion = this.columnItems.map(x => x.name + '|' + x.code).sort((a, b) => a.localeCompare(b));
    let local = localStorage.getItem('fdm_domestic_transaction_screening_columns');
    if (local == null) {
      return this.columnItems;
    }

    let localStorageColumns: Column[];
    try {
      localStorageColumns = JSON.parse(atob(local));
    } catch (e) {
      console.warn('parse local column', e);
      return this.columnItems;
    }
    let localStorageVersion = localStorageColumns.map(x => x.name + '|' + x.code).sort((a, b) => a.localeCompare(b));

    if (currentVersion.length !== localStorageVersion.length) {
      return this.columnItems;
    }

    // Compare the sorted arrays element by element
    for (let i = 0; i < currentVersion.length; i++) {
      if (currentVersion[i] !== localStorageVersion[i]) {
        return this.columnItems;
      }
    }

    return localStorageColumns;
  }

  sync() {
    this.syncTransaction.emit();
  }

  onSubmit() {
    this.toDate = this.toDate ? this.toDate : new Date();
    this.fromDate = this.fromDate ? this.fromDate : this.toDate;

    this.submitGeneralSearch.emit();
  }

  checkFormSearch(type: boolean) {
    this.showFormSearch = type;
  }

  searchBrefInfo() {
    this.searchBref = "From " + this.pipe.transform(this.fromDate, 'dd/MM/yyyy HH:mm') + " To " + this.pipe.transform(this.toDate, 'dd/MM/yyyy HH:mm');
    if (this.merchantIdSelected && this.merchantIdSelected.length != 0) {
      this.searchBref += " | Merchant ID = " + this.merchantIdSelected.join(',');
    }
    if (this.orderRef && this.orderRef !== '') {
      this.searchBref += " | Order ref = " + this.orderRef;
    }
    if (this.ipAddress && this.ipAddress !== '') {
      this.searchBref += " | IP = " + this.ipAddress;
    }
    if (this.customerPhone && this.customerPhone !== '') {
      this.searchBref += " | Phone = " + this.customerPhone;
    }
    if (this.customerEmail && this.customerEmail !== '') {
      this.searchBref += " | Email = " + this.customerEmail;
    }
    if (this.merchantTxnRef && this.merchantTxnRef !== '') {
      this.searchBref += " | Merchant Txn Ref = " + this.merchantTxnRef;
    }
    if (this.customerName && this.customerName !== '') {
      this.searchBref += " | Customer Name = " + this.customerName;
    }
    if (this.partnerNameSelected && this.partnerNameSelected.length != 0) {
      this.searchBref += " | Partner Name = " + this.partnerNameSelected.join(',');
    }
    if (this.qrId && this.qrId !== '') {
      this.searchBref += " | Qr = " + this.qrId;
    }
    if (this.cardNumber && this.cardNumber !== '') {
      this.searchBref += " | Card Number = " + this.cardNumber;
    }
    if (this.accountNumber && this.accountNumber !== '') {
      this.searchBref += " | Acc Number = " + this.accountNumber;
    }
    if (this.nameOnCard && this.nameOnCard !== '') {
      this.searchBref += " | Name on Card = " + this.nameOnCard;
    }
    if (this.transId && this.transId !== '') {
      this.searchBref += " | Trans ID =" + this.transId;
    }
    if (this.epp && this.epp !== '') {
      this.searchBref += " | Epp = " + this.epp;
    }
    if (this.merchantGroup && this.merchantGroup.length != 0 && this.merchantGroupList && this.merchantGroupList.length != 0) {
      this.searchBref += " | Merchant Group = " + this.merchantGroup.map(value => this.merchantGroupList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.gateSelected && this.gateSelected.length != 0 && this.gateList && this.gateList.length != 0) {
      this.searchBref += " | Gate = " + this.gateSelected.map(value => this.gateList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.issuerSelected && this.issuerSelected.length != 0 && this.issuerList && this.issuerList.length != 0) {
      this.searchBref += " | Issuer = " + this.issuerSelected.map(value => this.issuerList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.fraudStatus && this.fraudStatus.length != 0) {
      this.searchBref += " | Fraud Status = " + this.fraudStatus.map(value => this.fraudStatusList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.responseCodeSelected && this.responseCodeSelected.length != 0 && this.responseCodeList && this.responseCodeList.length != 0) {
      this.searchBref += " | Response Code = " + this.responseCodeSelected.map(value => this.responseCodeList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.transStateSelected && this.transStateSelected.length != 0 && this.transStateList && this.transStateList.length != 0) {
      this.searchBref += " | Trans State =" + this.transStateSelected.map(value => this.transStateList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.pic && this.pic.length != 0 && this.picList && this.picList.length != 0) {
      this.searchBref += " | Pic = " + this.pic.map(value => this.picList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.transTypeSelected && this.transTypeSelected.length != 0 && this.transTypeList && this.transTypeList.length != 0) {
      this.searchBref += " | Trans Type = " + this.transTypeSelected.map(value => this.transTypeList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.markFraud && this.markFraud.length != 0) {
      this.searchBref += " | Mark Fraud = " + this.markFraud.map(value => this.markFraudList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.amountMore && this.amountMore > 0) {
      this.searchBref += " | Amount >" + this.amountMore;
    }
    if (this.qrChannelSelected && this.qrChannelSelected.length != 0 && this.qrChannelList && this.qrChannelList.length != 0) {
      this.searchBref += " | QR Channel = " + this.qrChannelSelected.map(value => this.qrChannelList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.cardSelected && this.cardSelected.length != 0 && this.cardList && this.cardList.length != 0) {
      this.searchBref += " | Card List = " + this.cardSelected.map(value => this.cardList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.integrationTypeSelected && this.integrationTypeSelected.length != 0) {
      this.searchBref += " | Integration Type = " + this.integrationTypeSelected.map(value => this.integrationTypeList.find(item => item.value === value)?.label).filter(label => label !== undefined).join(',');
    }
    if (this.otpChannel && this.otpChannel.length != 0) {
      this.searchBref += " | OTP Channel = " + this.otpChannel;
    }
    if (this.otpChannelSelected && this.otpChannelSelected.length != 0) {
      this.searchBref += " | User Id = " + this.userId;
    }
    if (this.encryptedCardNo?.length) {
      this.searchBref += " | Encrypted Card No = " + this.encryptedCardNo;
    }
    if (this.cof?.length) {
      this.searchBref += " | COF = " + this.cof;
    }
    // if (this.sourceOfFundSelected?.length) {
    //   this.searchBref += " | Card Type = " + this.sourceOfFundSelected;
    // }
    if (this.deviceId?.length) {
      this.searchBref += " | Device ID = " + this.deviceId;
    }
    if (this.customerId?.length) {
      this.searchBref += " | User ID > " + this.customerId;
    }
    if (this.url?.length) {
      this.searchBref += " | URL = " + this.url;
    }
    if (this.fraud?.length) {
      this.searchBref += " | Fraud = " + this.fraud;
    }
    if (this.cardVerificationInfo?.length) {
      this.searchBref += " | Card Verification Info = " + this.cardVerificationInfo;
    }
    if (this.mccDescription?.length) {
      this.searchBref += " | MCC Description = " + this.mccDescription;
    }
  }

  async dataComboboxData() {
    try {
      //filter data merchant group
      const merchantGroupData = await this.transactionScreeningService.getMerchantGroupList().toPromise();
      this.merchantGroupList = merchantGroupData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
        priority: item.priority
      }));

      //filter data issuer
      const issuerData = await this.transactionScreeningService.getIssuerList().toPromise();
      this.issuerList = issuerData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      //filter data response code
      const responseCodeData = await this.transactionScreeningService.getResponsecodeList().toPromise();
      this.responseCodeList = responseCodeData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      //filter data trans state
      const transStateData = await this.transactionScreeningService.getTransStateList().toPromise();
      this.transStateList = transStateData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      //filter data pic
      const picData = await this.transactionScreeningService.getPicList().toPromise();
      this.picList = picData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      //filter data trans type
      const transTypeData = await this.transactionScreeningService.getTransTypeList().toPromise();
      this.transTypeList = transTypeData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      // filter data qr channel
      const qrChannelData = await this.transactionScreeningService.getQRChannelList().toPromise();
      this.qrChannelList = qrChannelData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      // filter data gate list
      const gateData = await this.transactionScreeningService.getGateList().toPromise();
      this.gateList = gateData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      // filter data card list
      const cardData = await this.transactionScreeningService.getCardList().toPromise();
      this.cardList = cardData.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue
      }));

      // Load only first 50 merchants initially
      const merchantData = await this.commonService.listMerchant(['merchantId']).toPromise();
      this.merchantIdList = merchantData.slice(0, 50).map((item) => ({
        label: item.merchantId,
        value: item.merchantId
      }));
      this.merchantIdFilteredList = [...this.merchantIdList];

      const partnerData = await this.commonService.listPartner(['partnerName', 'shortName']).toPromise();
      this.partnerNameList = partnerData.map((item) => ({
        label: item.shortName,
        value: item.shortName
      }));
    } catch (error) {
      console.error('Error loading combobox data:', error);
    }
  }

  merchantGroupHighline(priority) {
    if (priority == '1') {
      return { color: '#D733FF' };
    } else {
      return ''
    }
  }

  searchMerchantId(event: any) {
    const query = event.filter?.trim();

    console.log('query: ', query);

    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Nếu không có query hoặc query rỗng -> hiển thị danh sách mặc định
    if (!query || query.length === 0) {
      this.merchantIdFilteredList = this.merchantIdList.slice(0, 50);
      console.log('merchantIdList: ', this.merchantIdList);
      console.log('merchantIdFilteredList: ', this.merchantIdFilteredList);
      return;
    }

    // Nếu query < 2 ký tự -> không search, giữ nguyên list hiện tại
    if (query.length < 2) {
      return;
    }

    this.merchantIdLoading = true;

    // Debounce search to avoid too many API calls
    this.searchTimeout = setTimeout(async () => {
      try {
        // Dynamic limit based on query specificity
        const limit = query.length >= 4 ? 1000 : query.length >= 3 ? 500 : 200;

        // Use search API if available, otherwise fallback to filter
        let merchantData: any;
        try {
          merchantData = await this.commonService.searchMerchant(['merchantId'], query, limit).toPromise();
        } catch (searchError) {
          // Fallback to full list and filter client-side
          const allMerchants = await this.commonService.listMerchant(['merchantId']).toPromise();
          merchantData = allMerchants
            .filter((item: any) => item.merchantId.toLowerCase().includes(query.toLowerCase()))
            .slice(0, limit);
        }

        this.merchantIdFilteredList = merchantData.map((item: any) => ({
          label: item.merchantId,
          value: item.merchantId
        }));
      } catch (error) {
        console.error('Error searching merchants:', error);
        this.merchantIdFilteredList = [];
      } finally {
        this.merchantIdLoading = false;
      }
    }, 300); // 300ms debounce
  }

  onClearSearch() {
    console.log('onClearSearch');
    // Khi user bấm button X để clear search
    this.merchantIdFilteredList = this.merchantIdList.slice(0, 50);
  }

  onHideDropdown() {
    // Khi dropdown bị hide, reset về danh sách mặc định nếu không có filter
    if (!this.merchantIdFilteredList || this.merchantIdFilteredList.length === 0) {
      this.merchantIdFilteredList = this.merchantIdList.slice(0, 50);
    }
  }

  setWidthColumn() {
    this.columnItems.forEach(e => {
      if (e.active) {
        if (document.getElementById(e.code + "Col") != null) {
          document.getElementById(e.code + "Col").style.width = e.width;
        }
      }
    });
  }

  openColumnDisplay() {
    this.originColumnItems =  JSON.parse(JSON.stringify(this.columnItems));
    this.columnDisplayRef = this.dialogService.open(ColumnDisplayComponent, {
      header: 'Column Display',
      contentStyle: { "max-height": "90%", "width": "400px", "overflow": "auto" },
      baseZIndex: 10000,
      data: {
        columnItems: this.originColumnItems,
      }
    });

    this.columnDisplayRef.onClose.subscribe(result => {
      // danh sach code
      if (result) {
        this.columnItems = result;
        this.columnMap = new Map();
        for (const iterator of this.columnItems) {
          this.columnMap.set(iterator.code, iterator);
        }
        setTimeout(() => {
          this.setWidthColumn();
        }, 200);
        localStorage.setItem('fdm_domestic_transaction_screening_columns', btoa(JSON.stringify(this.columnItems)));
      }
    });
  }

}
