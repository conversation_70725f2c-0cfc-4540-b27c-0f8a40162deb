import { DomesticService } from '@service/domestic.service';
import { Globals } from './../../../../core/global';
import { Component, OnInit, Output } from '@angular/core';
import { EventEmitter } from '@angular/core';
import { SelectItem } from 'primeng/api';

@Component({
    // tslint:disable-next-line:component-selector
    selector: 'domestic-refund-form-2',
    templateUrl: './search.component.html',
    styleUrls: ['./search.component.css']
})

export class DomesticRefundForm2 implements OnInit {

    public merchant_id: any;
    public transaction_id: string;
    public refund_transaction_id: string;
    public order_reference: string;
    public card_number: string;
    public bank_id: Array<string>;
    public transaction_state: Array<string>;
    public caic: string;
    public page_size: string;
    public first = 0;
    public merchant_trans_ref: string;
    public contract_type: string;
    public acquirer_bank: string;
    public platform: number;
    public transaction_type : string;
    public actionType : string;
    public source: string;
    public contractTypeList: SelectItem[] = [];
    public fromDate: Date;
    public toDate: Date;
    public transaction_stateList: Array<any>;
    public bankIdList: SelectItem[] = [];
    public acquirerList: SelectItem[] = [];
    public pageSizeList: Array<any>;
    public platformArray: SelectItem[] = [];
    public confirmRefundList: Array<any>;
    public transTypes: SelectItem[] = [];
    public actionTypeList: SelectItem[] = [];
    public sourceList: SelectItem[] = [];

    @Output()
    private submitDomesticTransactionSearch = new EventEmitter<any>();

    @Output()
    private clearDomesticTransactionSearch = new EventEmitter<any>();

    // tslint:disable-next-line:max-line-length
    constructor(private global: Globals, private domesticService: DomesticService) {
        // this.adapter.setLocale('en-in');
    }

    ngOnInit(): void {
        this.initDefault();
    }

    initDefault() {
        this.contractTypeList = [
            {
                label: 'All',
                value: ''
            },
            {
                label: '2b',
                value: '2b'
            },
            {
                label: '3b',
                value: '3b'
            }
        ];

        this.transaction_stateList = [
            {
                value : '200',
                label : 'Failed'
            },
            {
                value : '210',
                label : 'Waiting for BIDV\'s Approval'
            },
            {
                value : '300',
                label : 'Waiting for OnePay\'s Approval'
            },
            {
                value : '310',
                label : 'OnePay Approved'
            },
            {
                value : '320',
                label : 'Waiting for OnePay\'s Update'
            },
            {
                value : '400',
                label : 'Successful'
            }
        ];

        this.sourceList = [
            {
                label: 'All',
                value: ''
            },
            {
                label: 'Apple Pay',
                value: 'Apple Pay'
            },
            {
                label: 'Direct',
                value: 'Direct'
            }
        ];

        this.acquirerList = [
            {
                label: 'ALL',
                value: ''
            },
            {
                label: 'None',
                value: 'none'
            },
            {
                label: 'OnePay',
                value: 'onepay'
            },
            {
                label: 'VCB',
                value: 'vcb'
            },
            {
                label: 'BIDV',
                value: 'bidv'
            }
        ];

        this.pageSizeList = [
            {
                label: '100',
                value: '100'
            },
            {
                label: '150',
                value: '150'
            },
            {
                label: '200',
                value: '200'
            },
            {
              label: '500',
              value: '500'
            },
            {
              label: '700',
              value: '700'
            },
            {
              label: '1000',
              value: '1000'
            }
        ];

        this.platformArray = [
            {
                value: '',
                label: 'All'
            }
        ];
        
        this.domesticService.searchReferralPartner().subscribe(data => {
            this.platformArray = this.platformArray.concat(
                data.listItems.map(m => ({
                    label: `${m.name}`,
                    value: m.id
                }))
            );
        });

        this.transTypes = [
            {
              value: '',
              label: 'ALL'
            },

            {
              label: 'Refund',
              value: 'Refund'
            },
            {
              label: 'Refund Dispute',
              value: 'Refund Dispute'
            }
        ];

        this.actionTypeList = [
            {
                value: '',
                label: 'ALL'
            },

            {
                label: 'Auto',
                value: 'auto'
            },
            {
                label: 'Manual',
                value: 'manual'
            }
        ];

        // tslint:disable-next-line:prefer-const
        this.domesticService.Getlist_Bank().subscribe(data => {
            this.bankIdList = data.list instanceof Array ? data.list.map(m => {
                return { label: `${m.name}`, value: m.id.toString() };
            }) : [];
        });
    }

    init(param?: any) {
        this.fromDate = param['fromDate'] !== undefined ? new Date(param['fromDate']) : new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = param['toDate'] !== undefined ? new Date(param['toDate']) : new Date(new Date().setHours(23, 59, 59, 0));
        this.merchant_id = param['merchant_id'];
        this.bank_id = (param['bank_id'] !== '' && param['bank_id'] !== undefined) ? param['bank_id'].split(',') : [];
        this.caic = param['caic'];
        this.transaction_id = param['transaction_id'];
        this.refund_transaction_id = param['refund_transaction_id'];
        this.order_reference = param['order_reference'];
        this.merchant_trans_ref = param['merchant_trans_ref'];
        this.card_number = param['card_number'];
        this.transaction_state = (param['transaction_state'] !== '' && param['transaction_state'] !== undefined) ? param['transaction_state'].split(',') : [];
        this.contract_type = param['contract_type'];
        this.acquirer_bank = param['acquirer_bank'];
        this.platform = param['platform'] ? Number(param['platform']) : null;
        this.source = param['source'];
        this.transaction_type = param['transaction_type'];
        this.actionType = param['actionType'];
        this.page_size = param['page_size'];
        // tslint:disable-next-line:radix
        this.first = param['first'] !== undefined ?  parseInt(param['first'] ) : 0;
    }

    clear() {
        this.fromDate = new Date(new Date().setHours(0, 0, 0, 0));
        this.toDate = new Date(new Date().setHours(23, 59, 59, 0));
        this.merchant_id = undefined;
        this.caic = undefined;
        this.transaction_id = undefined;
        this.refund_transaction_id = undefined;
        this.order_reference = undefined;
        this.acquirer_bank = undefined;
        this.card_number = undefined;
        this.merchant_trans_ref = undefined;
        this.transaction_state = [];
        this.bank_id = [];
        this.page_size = undefined;
        this.contract_type = undefined;
        this.platform = undefined;
        this.transaction_type = undefined;
        this.actionType = undefined;
        this.source = undefined;
        this.clearDomesticTransactionSearch.emit;
    }

    redirectParams() {
        const params = {
            'merchant_id': this.merchant_id,
            'fromDate': this.fromDate,
            'toDate': this.toDate,
            'caic': this.caic,
            'contract_type': this.contract_type,
            'acquirer_bank': this.acquirer_bank,
            'platform' : this.platform,
            'transaction_id': this.transaction_id,
            'refund_transaction_id': this.refund_transaction_id,
            'order_reference': this.order_reference,
            'card_number': this.card_number,
            'merchant_trans_ref': this.merchant_trans_ref,
            'bank_id': this.bank_id.join(','),
            'transaction_state': this.transaction_state.join(','),
            'page_size': this.page_size ,
            'first': this.first,
            'transaction_type': this.transaction_type,
            'actionType': this.actionType,
            'source': this.source,
        };
        return params;
    }

    merchantsData(): any {
        let returnMerchant;
        const merchantList = this.global.activeProfile.merchants.filter(it => it.type === 'domestic');
        if (merchantList.length === 1) {
            const merchant = merchantList[0];
            if (merchant.merchant_id.toUpperCase() === 'ALL') {
                returnMerchant = 'ALL';
            } else {
                returnMerchant = merchantList;
            }
        } else {
            returnMerchant = merchantList;

            // case lenght >1 add ALL
            // if (returnMerchant.length > 1) {
            //     returnMerchant.splice(0, 0, ({ 'merchant_id': '', 'merchant_name': 'All' }));
            // }
        }
        return returnMerchant;
    }

    onSubmit() {
        this.submitDomesticTransactionSearch.emit();
    }
}
