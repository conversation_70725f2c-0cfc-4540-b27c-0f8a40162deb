import { HttpParams } from '@angular/common/http';
import { Component, HostListener, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { BankMidManagementService } from '@service/bank-mid-management.service';
import { LazyLoadEvent } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastrService } from 'ngx-toastr';
import { BankMidDetailComponent } from './form-dialog/bank-mid-detail/bank-mid-detail.component';
import { BankMidDropdownModel } from 'app/model/fee-dropdown.model';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
@Component({
  selector: 'app-bank-mid-management',
  templateUrl: './bank-mid-management.component.html',
  styleUrls: ['./bank-mid-management.component.css'],
  providers: [BankMidManagementService, DialogService]
})
export class BankMidManagementComponent implements OnInit, OnDestroy {

  constructor(
    private bankMidManagementService: BankMidManagementService,
    private global: Globals,
    private activatedRouter: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    public dialogService: DialogService,
    private confirmService: ConfirmService,
  ) {
  }
  public loading: boolean;
  public flexScrollHeight = '300px';
  public listData: Array<any>;
  public keyword = '';
  public payChannel: Array<string>;
  public acquirer: any;
  public contractType: Array<string>;
  public status: Array<string>;
  public page = 0;
  public pageSize = 20;
  public first = 0;
  public tableDataTotal: number;
  public selectedData: Array<any> = [];
  public acquirers: Array<any>;
  private offsetHeight = 360;
  public subscription: Subscription;
  ref: DynamicDialogRef;
  public dropdownData: BankMidDropdownModel = new BankMidDropdownModel();

  ngOnInit() {
    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
    this.innitParams();
    this.initDropdownData();
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
  }
  getBankMid() {
    this.router.navigate(['/bank-mid-management'], { queryParams: this.redirectParams() });
    var params = new HttpParams()
      .set('keyword', this.keyword.trim())
      .set('pay_channel', this.payChannel.join(','))
      .set('acquirer', this.acquirer.join(','))
      .set('contract_type', this.contractType.join(','))
      .set('status', this.status.join(','))
      .set('page', this.page)
      .set('page_size', this.pageSize);
    return this.bankMidManagementService.search(params);
  }

  changePageSize() {
    this.searchData();
  }

  redirectParams() {
    const params = {
      'keyword': this.keyword.trim(),
      'pay_channel': this.payChannel.join(','),
      'acquirer': this.acquirer.join(','),
      'contract_type': this.contractType.join(','),
      'status': this.status.join(','),
      'page': this.page,
      'page_size': this.pageSize,
      'first': this.first,
    };

    return params;
  }

  innitParams() {
    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.keyword = params['keyword'] === undefined ? '' : params['keyword'].trim();
        this.payChannel = (params['pay_channel'] !== '' && params['pay_channel'] !== undefined) ? params['pay_channel'].split(',') : [];
        this.acquirer = (params['acquirer'] !== '' && params['acquirer'] !== undefined) ? params['acquirer'].split(',') : [];
        this.contractType = (params['contract_type'] !== '' && params['contract_type'] !== undefined) ? params['contract_type'].split(',') : [];
        this.status = (params['status'] !== '' && params['status'] !== undefined) ? params['status'].split(',') : [];
        this.page = params['page'] === undefined ? '0' : params['page'];
        this.pageSize = params['page_size'] === undefined ? '20' : params['page_size'];
        this.first = params['first'] !== undefined ? parseInt(params['first']) : 0;
      });

  }



  ngOnDestroy() {
    this.subscription.unsubscribe();
    if (this.ref) {
      this.ref.close();
    }
  }

  searchData() {
    this.first = 0;
    this.page = 0;
    return this.getBankMid().subscribe(responses => {
      this.listData = responses.response.data.list;
      this.tableDataTotal = responses.response.data.total;
    });
  }

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;
    this.page = event.first / event.rows;
    this.first = event.first;
    return this.getBankMid().subscribe(responses => {
      this.loading = false;
      this.listData = responses.response.data.list;
      this.tableDataTotal = responses.response.data.total;
    });
  }

  public isRowSelected(sData) {

    if (!this.selectedData || this.selectedData.length === 0) {
      return false;
    }
    return this.selectedData.findIndex(data => data === sData) === -1 ? false : true;
  }

  save(data: any) {
    let body = {};
    if (data !== 'Add') {
      body = {
        obj: data,
        action: 'Edit'
      };
      this.ref = this.dialogService.open(BankMidDetailComponent, {
        header: 'Edit Bank MID',
        width: '1000px',
        contentStyle: { "max-height": "1300px", "overflow": "auto" },
        baseZIndex: 10000,
        data: {
          body: body,
        }
      });

      this.ref.onClose.subscribe((body: any) => {
        if (body) {

          if (body == 'close') {
            this.searchData();
          } else {
            this.bankMidManagementService.update(body).subscribe((data: any) => {
              if (data) {
                const result = parseInt(data.code);
                if (result === 200) {
                  this.toastr.success('Update successfully', null, { positionClass: 'toast-top-right' });
                } else if (result === 201) {
                  this.toastr.warning('Bank mid is not exitst', null, { positionClass: 'toast-top-right' });
                } else {
                  this.toastr.warning('System error', null, { positionClass: 'toast-top-right' });
                }
                this.searchData();
              }
            });
          }

        };
      })
    } else {
      body = {
        obj: {
          active: "ACTIVE",
          currency: "VND",
          status: "approved",
          contractType: "2B"
        },
        action: 'Add'
      };
      this.ref = this.dialogService.open(BankMidDetailComponent, {
        header: 'Add Bank MID',
        width: '1000px',
        contentStyle: { "max-height": "1300px", "overflow": "auto" },
        baseZIndex: 10000,
        data: {
          body: body,
        }
      });

      this.ref.onClose.subscribe((body: any) => {
        if (body && body.obj.bankMid != null && body.obj.bankMid != "") {

          this.bankMidManagementService.save(body).subscribe((data: any) => {
            if (data) {
              const result = parseInt(data.code);
              if (result === 200) {
                this.toastr.success('Created successfully', null, { positionClass: 'toast-top-right' });
              } else if (result === 201) {
                this.toastr.warning('Bank mid is exitst', null, { positionClass: 'toast-top-right' });
              } else {
                this.toastr.warning('System error', null, { positionClass: 'toast-top-right' });
              }
              this.searchData();
            }
          })

        };
      })
    }
  }
  convertString(string: any) {
    if (string) {
      if (string === 'approved') {
        return 'Đã duyệt';
      } else if (string === 'wait_for_approve') {
        return 'Chờ duyệt';
      } else if (string === 'ACTIVE') {
        return 'Hoạt động';
      } else if (string === 'INACTIVE') {
        return 'Không hoạt động';
      } else {
        return '';
      }
    } else {
      return '';
    }

  }
  initDropdownData() {
    this.bankMidManagementService.getAcquirer().subscribe(responses => {
      if (responses) {
        this.dropdownData.acquirers = responses.response.data.list.map((bank) => {
          return { value: bank.id.toString(), label: `${bank.name}` };
        });
        this.dropdownData.acquirers = this.dropdownData.get(this.dropdownData.acquirers,
          undefined);
      }
    });

  }


}
