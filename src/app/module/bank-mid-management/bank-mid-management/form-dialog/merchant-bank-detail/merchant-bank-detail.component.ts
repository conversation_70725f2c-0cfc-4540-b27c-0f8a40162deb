import { Component, OnInit, OnDestroy } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastrService } from 'ngx-toastr';
import { DatePipe, DecimalPipe, Location } from '@angular/common';
import { BankMidDropdownModel } from 'app/model/fee-dropdown.model';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-merchant-bank-detail',
  templateUrl: './merchant-bank-detail.component.html',
  styleUrls: ['./merchant-bank-detail.component.css']
})
export class MerchantBankDetailComponent implements OnInit, OnDestroy {
  public optionList: Array<any>;
  public bankMid: any;
  public bankMids: Array<any>;
  public dropdownData: BankMidDropdownModel = new BankMidDropdownModel();
  public payChannelDropdown = [];
  public payChannelsStr = '';
  public subscription: Subscription;
  public sysdate = new Date();
  public PAY_CHANNEL_BANK_MID = [
    { value: 'QT', label: 'International' },
    { value: 'ND', label: 'Domestic' }
  ];
  public merchantbankPrev: string;

  constructor(private activatedRouter: ActivatedRoute, public ref: DynamicDialogRef, public config: DynamicDialogConfig, public toastr: ToastrService, public datepipe: DatePipe, private decimalPipe: DecimalPipe, private confirmService: ConfirmService, private location: Location) { }

  ngOnInit() {
    this.innitParams()
    this.merchantbankPrev = this.config.data.body.obj.bankMerchantId;
  }
  innitParams() {
    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.payChannelsStr = (params['payChannels'] !== '' && params['payChannels'] !== undefined) ? params['payChannels'] : '';
        this.PAY_CHANNEL_BANK_MID.forEach(item => {
          if (this.payChannelsStr.includes(item.value)) {
            this.payChannelDropdown.push(item);
          }
        })
        if (!this.config.data.body.obj.payChannel) { this.config.data.body.obj.payChannel = this.payChannelDropdown ? this.payChannelDropdown[0].value : ''; }

      });

  }
  onSubmit() {
    this.confirmService.build()
      .message('<div class=\'content-confirm\'> Confirm MBGS/CBS ID?  </div>')
      .title('Confirm!')
      .yes('Yes')
      .no('No')
      .confirm().subscribe(ok => {
        if (ok) {
          this.config.data.body.obj.bankMerchantIdPrev = this.merchantbankPrev;
          this.config.data.body.obj.expireDate = this.config.data.body.obj.expireDate ? this.convertToUTC7(this.config.data.body.obj.expireDate) : null
          console.log('body update : ',  this.config.data.body);
          this.ref.close(this.config.data.body);
        }
      });
  }

  convertToUTC7(date: Date): String {
    const utcOffset = 7 * 60 * 60 * 1000; // 7 hours in milliseconds
    return new Date(date.getTime() + utcOffset).toLocaleDateString('en-GB');
  }

  ngOnDestroy() {
    this.ref.close('close');
  }

  resetForm(){
    this.config.data.body.obj.activeNone3ds = 'yes'
    this.config.data.body.obj.enableNoneCsc = 'yes'
    this.config.data.body.obj.bankMerchantId = null
    this.config.data.body.obj.desc = null
    this.config.data.body.obj.expireDate = null
    this.config.data.body.obj.merchantBankKey = null
    this.config.data.body.obj.priority = 1
  }
}
