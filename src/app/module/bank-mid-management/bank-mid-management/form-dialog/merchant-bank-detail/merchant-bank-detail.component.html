<form class="formwidth" #myForm="ngForm">
    <div class="filter-group row" style="overflow: auto;">
        <div class="col-md-12 col-lg-12 cal-sm-12">
            
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9_-]*$" title="Only letters and numbers are allowed" maxlength="50" required class="form-control" [(ngModel)]="this.config.data.body.obj.bankMerchantId" #bankMerchantIdF="ngModel" name="bankMerchantIdF" pInputText >
                    <label class="label-custom" for="bankMerchantIdF">MPGS/CBS ID<span class="error-message">(*)</span></label>
                </span>
                <div *ngIf="!bankMerchantIdF.valid && bankMerchantIdF.touched" class="error-message">Please enter a value for this MPGS/CBS ID</div>
                <div *ngIf="bankMerchantIdF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="payChannelDropdown"
                        [(ngModel)]="this.config.data.body.obj.payChannel" dropdownIcon="pi pi-angle-down"
                        #payChannelF="ngModel" name="payChannelF">
                    </p-dropdown>
                    <label class="label-custom" for="payChannelF">Dịch vụ<span class="error-message">(*)</span></label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input  pattern="^.*$" title="Only letters and numbers are allowed" maxlength="500" class="form-control" [(ngModel)]="this.config.data.body.obj.merchantBankKey" #merchantBankKeyF="ngModel" name="merchantBankKeyF" pInputText>
                    <label class="label-custom" for="merchantBankKeyF">Merchant Bank Key</label>
                </span>
                <div *ngIf="merchantBankKeyF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
            </div>

            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.prioritys"
                        [(ngModel)]="this.config.data.body.obj.priority" dropdownIcon="pi pi-angle-down"
                        #priorityF="ngModel" name="priorityF">
                    </p-dropdown>
                    <label class="label-custom" for="priorityF">Priority<span class="error-message">(*)</span></label>
                </span>
            </div>

            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.enables"
                        [(ngModel)]="this.config.data.body.obj.activeNone3ds" dropdownIcon="pi pi-angle-down"
                        #activeNone3dsF="ngModel" name="activeNone3dsF">
                    </p-dropdown>
                    <label class="label-custom" for="activeNone3dsF">Active None 3DS</label>
                </span>
            </div>


            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.enables"
                        [(ngModel)]="this.config.data.body.obj.enableNoneCsc" dropdownIcon="pi pi-angle-down"
                        #enableNoneCscF="ngModel" name="enableNoneCscF">
                    </p-dropdown>
                    <label class="label-custom" for="enableNoneCscF">Enable None CSC</label>
                </span>
            </div>

            <div class="form-group">
                <span class="input-group">
                    <textarea class="form-control" [(ngModel)]="this.config.data.body.obj.desc" maxlength="300"
                        #descF="ngModel" name="descF" pInputTextarea></textarea>
                    <label class="label-custom" for="descF">Desc</label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-calendar [style]="{'width':'100%'}"  [showIcon]="true" 
                    showButtonBar="true" [yearNavigator]="true" yearRange="2024:2100" monthNavigator="true"  dateFormat="dd/mm/yy"
                    inputStyleClass="form-control" hideOnDateTimeSelect="true" appendTo="body"
                    [(ngModel)]="this.config.data.body.obj.expireDate"  name="expireDate" [minDate]="this.sysdate"></p-calendar>
                    <label class="label-custom" for="descF">Expire Date</label>
                </span>
            </div>

            <div class="row modal-footer">
                <div class="col-md-12 col-lg-12 cal-sm-12" id="modal-footer">
                    <div class="row">
                        <div class="col-md-6 col-lg-6 cal-sm-6 text-center">
                            <button pButton pRipple label="Clear" class="button-group"
                                (click)= 'resetForm()'>
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-6 cal-sm-6 text-center">
                            <button [disabled]="myForm.invalid" pButton pRipple label="Save" class="button-group"
                                type="submit" (click)= 'onSubmit()'>
                            </button>
                        </div>
                    </div>
                </div>
            </div> 
              
        </div>
    </div>
</form>