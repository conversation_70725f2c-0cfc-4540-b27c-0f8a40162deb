<form (ngSubmit)="onSubmit()" #myForm="ngForm">
    <div class="filter-group row" style="overflow: auto;">

        <div class="col-md-12 col-lg-12 cal-sm-12">
            <div class="form-group">
                <span class="input-group">
                    <!-- <p-multiSelect required (onChange)="changePaychannels()" placeholder="Select pay channel" [style]="{'width':'100%'}" 
                                    selectedItemsLabel="{0} Selected pay channel" [showClear]="true"
                                    [options]="dropdownData.payChannels" [(ngModel)]="payChannels" 
                                    dropdownIcon="pi pi-angle-down" #payChannelF="ngModel" name="payChannelF"
                                    maxSelectedLabels="1" ></p-multiSelect> -->
                    <p-dropdown required [style]="{'width':'100%'}" [options]="dropdownData.payChannels"
                                    [(ngModel)]="this.config.data.body.obj.payChannels"
                                    placeholder="Select pay channel" dropdownIcon="pi pi-angle-down" #payChannelF="ngModel"
                                    name="payChannelF"  >
                    </p-dropdown>
                    <label class="label-custom" for="payChannelF">Pay channel<span class="error-message">(*)</span></label>
                    <div *ngIf="!payChannelF.valid && payChannelF.touched" class="error-message">Please enter a value for this Pay Channel</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown required (onChange)="changeAcquirer($event.value)" [style]="{'width':'100%'}" [options]="getDropdowAcquirerByService()"
                        [(ngModel)]="this.config.data.body.obj.acquirerId" optionValue="id" optionLabel="name"
                        placeholder="Select acquirer" dropdownIcon="pi pi-angle-down" #acquirerIdF="ngModel"
                        name="acquirerIdF"  >
                    </p-dropdown>
                    <label class="label-custom" for="acquirerIdF">Acquirer<span class="error-message">(*)</span></label>
                    <div *ngIf="!acquirerIdF.valid && acquirerIdF.touched" class="error-message">Please enter a value for this Acquirer</div>
                </span>
            </div>

            <div class="form-group">
                <span class="input-group">

                    <input pattern="^[a-zA-Z0-9-]*$" title="Only letters and numbers are allowed" maxlength="50" required class="form-control" [(ngModel)]="this.config.data.body.obj.bankMid" #bankMidF="ngModel"
                        name="bankMidF" pInputText >
                    <label class="label-custom" for="bankMidF">Bank MID<span class="error-message">(*)</span></label>
                    <div *ngIf="!bankMidF.valid && bankMidF.touched" class="error-message">Please enter a value for this Bank MID</div>
                    <div *ngIf="bankMidF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9]*$" title="Only letters and numbers are allowed" maxlength="50" class="form-control" [(ngModel)]="this.config.data.body.obj.tid" #tidF="ngModel" name="tidF"
                        pInputText>
                    <label class="label-custom" for="tidF">TID</label>
                    <div *ngIf="tidF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9]*$" title="Only letters and numbers are allowed"  maxlength="50" class="form-control" [(ngModel)]="this.config.data.body.obj.bankMCC" #bankMCCF="ngModel"
                        name="bankMCCF" pInputText>
                    <label class="label-custom" for="bankMCCF">Bank MCC</label>
                    <div *ngIf="bankMCCF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="partners"
                        [(ngModel)]="this.config.data.body.obj.partnerId" optionValue="partnerId"
                        optionLabel="shortName" placeholder="Select partner" dropdownIcon="pi pi-angle-down"
                        #partnerIdF="ngModel" name="partnerIdF" >
                    </p-dropdown>
                    <label class="label-custom" for="partnerIdF">Partner</label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9]*$" title="Only letters and numbers are allowed"  maxlength="50" class="form-control" [(ngModel)]="this.config.data.body.obj.se"
                        #seF="ngModel" name="seF" pInputText>
                    <label class="label-custom" for="seF">SE</label>
                    <div *ngIf="seF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9]*$" title="Only letters and numbers are allowed"  maxlength="50" class="form-control" [(ngModel)]="this.config.data.body.obj.merchantAccountName"
                        #merchantAccountNameF="ngModel" name="merchantAccountNameF" pInputText>
                    <label class="label-custom" for="merchantAccountNameF">SE</label>
                    <div *ngIf="merchantAccountNameF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <input pattern="^[a-zA-Z0-9 ,]*$" title="Only letters and numbers are allowed"  maxlength="50" class="form-control" [(ngModel)]="this.config.data.body.obj.cardType" #cardTypeF="ngModel"
                        name="cardTypeF" pInputText>
                    <label class="label-custom" for="cardTypeF">Card type</label>
                    <div *ngIf="cardTypeF.errors?.pattern" class="error-message"> Only letters and numbers are allowed</div>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.contractTypes"
                        [(ngModel)]="this.config.data.body.obj.contractType" dropdownIcon="pi pi-angle-down"
                        #contractTypeF="ngModel" name="contractTypeF">
                    </p-dropdown>
                    <label class="label-custom" for="contractTypeF">Contract type<span class="error-message">(*)</span></label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.actives"
                        [(ngModel)]="this.config.data.body.obj.active" dropdownIcon="pi pi-angle-down"
                        #activeF="ngModel" name="activeF">
                    </p-dropdown>
                    <label class="label-custom" for="activeF">Active<span class="error-message">(*)</span></label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.statuss"
                        [(ngModel)]="this.config.data.body.obj.status" dropdownIcon="pi pi-angle-down"
                        #statusF="ngModel" name="statusF">
                    </p-dropdown>
                    <label class="label-custom" for="statusF">Status<span class="error-message">(*)</span></label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <p-dropdown [style]="{'width':'100%'}" [options]="dropdownData.currencys"
                        [(ngModel)]="this.config.data.body.obj.currency" dropdownIcon="pi pi-angle-down"
                        #currencyF="ngModel" name="currencyF">
                    </p-dropdown>
                    <label class="label-custom" for="currencyF">Currency<span class="error-message">(*)</span></label>
                </span>
            </div>
            <div class="form-group">
                <span class="input-group">
                    <textarea class="form-control" [(ngModel)]="this.config.data.body.obj.desc" maxlength="300"
                        #descF="ngModel" name="descF" pInputTextarea></textarea>
                    <label class="label-custom" for="descF">Desc</label>
                </span>
            </div>
            <div class="row modal-footer">
                <div class="col-md-12 col-lg-12 cal-sm-12" id="modal-footer">
                    <div class="row">
                        <div class="col-md-6 col-lg-6 cal-sm-6 text-center">
                            <button pButton pRipple label="Clear" class="button-group"
                                type="reset">
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-6 cal-sm-6 text-center">
                            <button pButton pRipple label="Save" class="button-group" 
                                type="submit" [disabled]="myForm.invalid">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>