import { HttpParams } from '@angular/common/http';
import { Component, HostListener, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { MerchantBankService } from '@service/merchant-bank.service';
import { LazyLoadEvent } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastrService } from 'ngx-toastr';
import { MerchantBankDetailComponent } from '../form-dialog/merchant-bank-detail/merchant-bank-detail.component';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { DatePipe, Location } from '@angular/common';
@Component({
  selector: 'app-merchant-bank-id',
  templateUrl: './merchant-bank-id.component.html',
  styleUrls: ['./merchant-bank-id.component.css'],
  providers: [MerchantBankService, DialogService]
})
export class MerchantBankIdComponent implements OnInit, OnDestroy {
  constructor(
    private merchantBankService: MerchantBankService,
    private global: Globals,
    private activatedRouter: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    public dialogService: DialogService,
    private confirmService: ConfirmService,
    private location: Location
  ) {
  }
  public loading: boolean;
  public flexScrollHeight = '300px';
  public listData: Array<any>;
  public keyword = '';
  public bankMidId;
  public page = 0;
  public pageSize = 20;
  public first = 0;
  public tableDataTotal: number;
  public selectedData: Array<any> = [];
  private offsetHeight = 360;
  public subscription: Subscription;
  public bankMid: any;
  ref: DynamicDialogRef;
  public pageSizeList = [
    {
      label: '20',
      value: '20'
    },
    {
      label: '50',
      value: '50'
    },
    {
      label: '100',
      value: '100'
    },
    {
      label: '200',
      value: '200'
    }
  ];

  ngOnInit() {
    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
    this.innitParams();
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
  }
  getMerchantBank() {
    this.bankMidId = this.activatedRouter.snapshot.params['id'];
    var params = new HttpParams()
      .set('keyword', this.keyword.trim())
      .set('page', this.page)
      .set('page_size', this.pageSize)
    return this.merchantBankService.search(params, this.bankMidId);
  }

  changePageSize() {
    this.searchData();
  }

  redirectParams() {
    const params = {
      'keyword': this.keyword.trim(),
      'page': this.page,
      'page_size': this.pageSize,
      'first': this.first,
    };

    return params;
  }



  innitParams() {
    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.keyword = params['keyword'] === undefined ? '' : params['keyword'].trim();
        this.page = params['page'] === undefined ? '0' : params['page'];
        this.pageSize = params['page_size'] === undefined ? '20' : params['page_size'];
        this.first = params['first'] !== undefined ? parseInt(params['first']) : 0;
        this.bankMid = {
          payChannels: params['payChannels'] === undefined ? '' : params['payChannels'], acquirer: params['acquirer'] === undefined ? '' : params['acquirer'], bankMCC: params['bankMCC'] === undefined ? '' : params['bankMCC'],
          bankMid: params['bankMid'] === undefined ? '' : params['bankMid'], contractType: params['contractType'] === undefined ? '' : params['contractType'], partnerShortName: params['partnerShortName'] === undefined ? '' : params['partnerShortName'], partnerId: params['partnerId'] === undefined ? '' : params['partnerId']
        };
        ;

      });
  }

  searchData() {
    this.first = 0;
    this.page = 0;
    return this.getMerchantBank().subscribe(responses => {
      this.listData = responses.response.data.list;
      this.setExpireDate()
      this.tableDataTotal = responses.response.data.total;
    });
  }

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;
    this.page = event.first / event.rows;
    this.first = event.first;
    return this.getMerchantBank().subscribe(responses => {
      this.loading = false;
      this.listData = responses.response.data.list;
      this.setExpireDate()
      this.tableDataTotal = responses.response.data.total;
    });
  }

  setExpireDate(){
    for (const data of this.listData) {
        data.expireDateString = data.expireDate ? new Date(data.expireDate).toLocaleDateString('en-GB') : "";
        data.expireDate = data.expireDate ? new Date(data.expireDate) : null
    }
  }

  isRowSelected(sData: any) {
    if (!this.selectedData || this.selectedData.length === 0) { return false; }
    return this.selectedData.findIndex(data => data === sData) === -1 ? false : true;
  }

  selectRow(checkValue) {
    if (checkValue) {
      this.selectedData = this.listData.filter(value => value);
    } else {
      this.selectedData = [];
    }
  }

  save(data: any) {
    let body = {};
    if (data !== 'Add') {
      body = {
        obj: data,
        action: 'Edit'
      };
      this.ref = this.dialogService.open(MerchantBankDetailComponent, {
        header: 'Edit MPGS/CBS ID',
        width: '1000px',
        contentStyle: { "max-height": "1300px", "overflow": "visible" },
        baseZIndex: 10000,
        data: {
          body: body,
        }
      });

      this.ref.onClose.subscribe((body: any) => {
        if (body) {

          if (body == 'close') {
            this.searchData();
          } else {
            this.merchantBankService.update(body, this.bankMidId).subscribe((data: any) => {
              if (data) {
                const result = parseInt(data.code);
                if (result === 200) {
                  this.toastr.success('Update successfully', null, { positionClass: 'toast-top-right' });
                } else if (result === 201) {
                  this.toastr.warning('Bank mid is not exitst', null, { positionClass: 'toast-top-right' });
                } else {
                  this.toastr.warning('System error', null, { positionClass: 'toast-top-right' });
                }
                this.searchData();
              }

            });
          }
        };
      })


    } else {
      console.log('this.bankMid', this.bankMid);
      
      body = {
        obj: {
          activeNone3ds: "yes",
          enableNoneCsc: "yes",
          priority: 1,
          payChannel: this.bankMid.payChannels,
        },
        action: 'Add'
      };


      this.ref = this.dialogService.open(MerchantBankDetailComponent, {
        header: 'Add MPGS/CBS ID',
        width: '1000px',
        contentStyle: { "max-height": "1300px", "overflow": "visible" },
        baseZIndex: 10000,
        data: {
          body: body,
        }
      });

      this.ref.onClose.subscribe((body: any) => {
        if (body && body.obj.bankMerchantId != null && body.obj.bankMerchantId != "") {

          this.merchantBankService.save(body, this.bankMidId).subscribe((data: any) => {
            if (data) {
              const result = parseInt(data.code);
              if (result === 200) {
                this.toastr.success('Created successfully', null, { positionClass: 'toast-top-right' });
              } else if (result === 201) {
                this.toastr.warning('Bank mid is exitst', null, { positionClass: 'toast-top-right' });
              } else {
                this.toastr.warning('System error', null, { positionClass: 'toast-top-right' });
              }
              this.searchData();
            }

          });

        };
      });



    }
  }

  deleteSelected() {

    if (this.selectedData.length > 0) {
      this.confirmService.build()
        .message('<div class=\'content-confirm\'> Confirm deletion of selected record(' + this.selectedData.length + ')?  </div>')
        .title('Warning!')
        .yes('Confirm')
        .no('Cancel')
        .confirm().subscribe(ok => {

          if (ok) {
            const selectedDataId = { ids: this.selectedData.map(d => d.id) };
            this.merchantBankService.deleteSelected(selectedDataId).subscribe(response => {
              this.selectedData = [];
              this.toastr.success('Deleted successfully');
              this.searchData();
            });

          }
        });
    } else {
      this.toastr.warning('Please select the record to delete');
    }


  }

  backToPage() {
    this.location.back();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    if (this.ref) {
      this.ref.close();
    }
  }

  convertString(value) {
    if (value == 'yes') {
      return 'Yes'
    } else if (value == 'no') {
      return 'No'
    } else {
      return ''
    }
  }

}
