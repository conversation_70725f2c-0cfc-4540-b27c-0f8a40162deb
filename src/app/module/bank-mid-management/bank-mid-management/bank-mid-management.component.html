<div class="wrapper" id="bank-mid">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-2 col-md-2 col-lg-2">
                <span class="title">Bank MID Management</span>
            </div>
        </div>

    </div>

    <div class="container-fluid">

        <p-table class="sticky-headers-table p-datatable-scrollable-body hiden_paginator_volume_default" [(first)]="first" [value]="listData"
            [rows]="pageSize" [style]="{width:'100%'}" styleClass="p-datatable-gridlines" responsiveLayout="scroll" [resizableColumns]="true"
            [lazy]="true" [style]="{width:'100%'}" columnResizeMode="expand" [totalRecords]="tableDataTotal"
            [scrollable]="true" scrollHeight="200px" [scrollHeight]="flexScrollHeight" (onLazyLoad)="loadLazy($event)"
            [paginator]="true" pageLinks="0" paginatorPosition="top">

            <ng-template pTemplate="paginatorleft" let-state>
                <div class="ui-helper-clearfix clearfix-01">
                    <div class="div-01">
                        <form (ngSubmit)="searchData()">
                            <div class="p-inputgroup flex-gap-10">
                                <input type="text" pInputText placeholder="Key word"
                                    class="form-control action-item action-search-box" [(ngModel)]="keyword" #keywordF
                                    id="txt-search" name="merchant_search" maxlength="50" />
                                <p-multiSelect appendTo="body" placeholder="Pay channel"
                                    selectedItemsLabel="{0} Selected pay channel" defaultLabel="ALL" [showClear]="true"
                                    [options]="dropdownData.payChannels" [(ngModel)]="payChannel" scrollHeight="250px"
                                    dropdownIcon="pi pi-angle-down" #payChannelM="ngModel" name="payChannel"
                                    maxSelectedLabels="1" id="txt-search-payChannel"></p-multiSelect>
                                <p-multiSelect appendTo="body" placeholder="Acquirer"
                                    selectedItemsLabel="{0} Selected acquirer" defaultLabel="ALL" [showClear]="true"
                                    [options]="dropdownData.acquirers" [(ngModel)]="acquirer" scrollHeight="250px"
                                    dropdownIcon="pi pi-angle-down" #acquirerM="ngModel" name="acquirerM"
                                    maxSelectedLabels="1" id="txt-search-service">
                                </p-multiSelect>
                                <p-multiSelect appendTo="body" placeholder="Contract type"
                                    selectedItemsLabel="{0} Selected contract type" defaultLabel="ALL"
                                    [showClear]="true" [options]="dropdownData.contractTypes" [(ngModel)]="contractType"
                                    dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="contractType"
                                    maxSelectedLabels="1" id="txt-search-contractType"></p-multiSelect>
                                <p-multiSelect appendTo="body" placeholder="Status"
                                    selectedItemsLabel="{0} Selected status" defaultLabel="ALL" [showClear]="true"
                                    [options]="dropdownData.statuss" [(ngModel)]="status"
                                    dropdownIcon="pi pi-angle-down" #statusM="ngModel" name="status"
                                    maxSelectedLabels="1" id="txt-search-status"></p-multiSelect>


                                <button type="submit" pButton pRipple icon="pi pi-search" styleClass="p-button-warn"
                                    id="btn-search" label="Search" class="download-button ui-button-success"></button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="mt-3"></div>
                <div class="ui-helper-clearfix clearfix-01">
                </div>
            </ng-template>

            <ng-template pTemplate="paginatorright" let-state>
                <div class="ui-helper-clearfix clearfix-02 ">
                    <div class="div-03">
                        <button type="submit" pButton pRipple style="margin-right: 0.5em;" (click)="save('Add')"
                            id="btn-search" label="Add" class="btn-create"></button>

                    </div>
                </div>
                <div class="mt-3"></div>
                <div class="ui-helper-clearfix clearfix-02">
                    <div class="total-item">
                        <table-paginator [state]="state" [totalRecords]="tableDataTotal"></table-paginator>
                    </div>
                    <div class="div-02">
                        <span class="input-group">
                            <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="dropdownData.pageSizes"
                                [(ngModel)]="pageSize" (onChange)="changePageSize()" dropdownIcon="pi pi-angle-down"
                                #display="ngModel" name="display">
                            </p-dropdown>
                        </span>
                    </div>
                    <div class="div-03">
                        <span class="label-custom" for="Display">Số dòng trên trang</span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="colgroup" let-columns>
                <colgroup>
                    <col class="mat-column-no">
                    <col class="mat-column-common-100">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-150">
                    <col class="mat-column-common-150">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-200">
                    <col class="mat-column-common-100">
                    <col class="mat-column-common-150">
                    <col class="mat-column-common-100">
                    <!-- <col class="mat-column-common-150">
                    <col class="mat-column-common-150">
                    <col class="mat-column-common-150"> -->
                    <col class="mat-column-common-100">
                </colgroup>
            </ng-template>

            <ng-template pTemplate="header" let-columns>
                <tr class="ml-1 ">
                    <th pResizableColumn class="text-center header">No</th>
                    <th pResizableColumn class="text-center header">Pay Channels</th>
                    <th pResizableColumn class="text-center header">Acquirer</th>
                    <th pResizableColumn class="text-left header">Bank MID</th>
                    <th pResizableColumn class="text-center header">TID</th>
                    <th pResizableColumn class="text-center header">Bank Mcc</th>
                    <th pResizableColumn class="text-center header">Partner</th>
                    <th pResizableColumn class="text-center header">MPGS/CBS IDS</th>
                    <th pResizableColumn class="text-center header">SE</th>
                    <th pResizableColumn class="text-center header">Merchant Account Name</th>
                    <th pResizableColumn class="text-center header">Contract Type</th>
                    <th pResizableColumn class="text-center header">Card Type</th>
                    <th pResizableColumn class="text-center header">Currency</th>
                    <th pResizableColumn class="text-center header">Active</th>
                    <th pResizableColumn class="text-center header">Status</th>
                    <!-- <th pResizableColumn class="text-center header">User created</th>
                    <th pResizableColumn class="text-center header">Update date</th>
                    <th pResizableColumn class="text-center header">Desc</th> -->
                    <th pResizableColumn class="text-center header">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-i="rowIndex" let-data>
                <tr class="tr_body" [ngClass]="{'ui-state-highlights': isRowSelected(data) }">
                    <td class="text-center ui-resizable-column">{{i + 1}}</td>
                    <td class="text-center ui-resizable-column">{{data.payChannels}}</td>
                    <td class="text-center ui-resizable-column">{{data.acquirer}}</td>
                    <td class="text-left ui-resizable-column">
                        <a [routerLink]="['/bank-mid-management/' + data.id, ]"
                            [queryParams]="{ payChannels: data.payChannels, acquirer: data.acquirer,bankMCC: data.bankMCC,
                            bankMid: data.bankMid,contractType: data.contractType,partnerShortName: data.partnerShortName,partnerId: data.partnerId}"
                            class="link-style">{{data.bankMid}}</a>
                    </td>
                    <td class="text-center ui-resizable-column">{{data.tid}}</td>
                    <td class="text-center ui-resizable-column">{{data.bankMCC}}</td>
                    <td class="text-center ui-resizable-column" appendTo="body" [pTooltip]="data.partnerShortName"
                        tooltipStyleClass="t-tooltip"><span *ngIf="data.partnerId !==0">{{data.partnerShortName}}</span>
                        <span *ngIf="data.partnerId ===0">OnePay</span>
                    </td>
                    <td class="text-center ui-resizable-column" appendTo="body" [pTooltip]="data.merchantBankIds"
                        tooltipStyleClass="t-tooltip">{{data.merchantBankIds}}</td>
                    <td class="text-center ui-resizable-column" appendTo="body" [pTooltip]="data.se"
                        tooltipStyleClass="t-tooltip">{{data.se}}</td>
                        <td class="text-center ui-resizable-column" appendTo="body" [pTooltip]="data.merchantAccountName"
                        tooltipStyleClass="t-tooltip">{{data.merchantAccountName}}</td>
                    <td class="text-center ui-resizable-column">{{data.contractType}}</td>
                    <td class="text-center ui-resizable-column">{{data.cardType}}</td>
                    <td class="text-center ui-resizable-column">{{data.currency}}</td>
                    <td class="text-center ui-resizable-column">{{convertString(data.active)}}</td>
                    <td class="text-center ui-resizable-column">{{convertString(data.status)}}</td>
                    <!-- <td class="text-center ui-resizable-column">{{data.userCreated}}</td>
                    <td class="text-center ui-resizable-column" [pTooltip]="data.update" tooltipStyleClass="t-tooltip"
                        appendTo="body">
                        {{data.update}}
                    </td>
                    <td class="text-center ui-resizable-column">{{data.desc}}</td> -->
                    <td class="text-center ui-resizable-column"><button type="button" (click)="save(data)"
                            pTooltip="Edit" icon="pi pi-pencil" iconPos="left" iconPos="left" class="btn-edit"
                            pButton></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage" let-columns>
                <tr>
                    <td [attr.colspan]="16" class="text-center empty_results">
                        Không có bản ghi nào được tìm thấy
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>