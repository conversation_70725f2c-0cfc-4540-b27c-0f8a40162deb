import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Location } from '@angular/common';
import html2canvas from 'html2canvas';
import { constants } from '@shared/utils/constants';
import { Globals } from '@core/global';
import { AccountingTransactionSearchForm } from '../search/search.component';
import { PaymentTransactionAccountingService } from '@service/payment_reconciliation/payment-transaction-accounting.service';
import * as moment from 'moment';
import {refundConfig} from 'app/configs/refund-config';
import {ToastrService} from 'ngx-toastr';
import {ConfirmService} from '@shared/confirm/confirm-dialogs.service';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {InternationalRefundDialog} from './refund-dialog/refund-dialog.component';
import {CurrencyPipe} from '@shared/pipe/CurrencyPipe';

@Component({
    selector: 'transaction-accounting-detail',
    templateUrl: './transaction-accounting-detail-component.html',
    styleUrls: ['./transaction-accounting-detail-component.css'],
    providers: [PaymentTransactionAccountingService,CurrencyPipe]
})

export class TransactionAccounting18DetailComponent implements OnInit, OnDestroy {
    public back = 'Transaction Accounting Search';
    public title = 'Transaction Accounting Details';
    public readonly constants = constants;
    public paramSearch;
    /* hien thi du lieu chi tiet giao dich */
    public subscription: Subscription;
    public subscription1: Subscription;
    public GetTransactionID: any;
    public data: any;
    public dataHistory: any;
    public loading: boolean;
    /* end hien thi chi tiet giao dich */
    public transId: string;

    // Request refund variable
    public flagApi: boolean=false;
    public refundAmount: number;
    public note='';
    public _id: string;
    private currencyConfig: any;

    @ViewChild(AccountingTransactionSearchForm, { static: true }) searchForm: AccountingTransactionSearchForm;

    constructor(private router: Router,
        private activatedRouter: ActivatedRoute,
        private transAccService: PaymentTransactionAccountingService,
        private global: Globals,
        private toastr: ToastrService,
        private confirmService: ConfirmService,
        public  dialog: MatDialog,
        private currencyPipe: CurrencyPipe,
        private location: Location) {
    }

    ngOnInit() {
        this.subscription1 = this.activatedRouter
            .queryParams
            .subscribe(params => {
                this.searchForm.init(params);
            });
        this.paramSearch = this.searchForm.redirectParams();
        this.subscription = this.activatedRouter.params.subscribe(params => {
            this.transId = params['id'];
            this.transAccService.getlistTransactionID(this.transId).subscribe(data => {
                this.data = data;

                this.transAccService.getlistHistoryTransaction(data.originalId).subscribe(dataHistory => {
                    this.dataHistory = dataHistory;

                    // if(this.dataHistory) {
                        // this.dataHistory.forEach(item => {
                            // if(item.transType==='Refund'&&item.advanceStatus==='Successful') {
                            //     this.refundAmount-=Number(item.amount);
                            // } else if(item.transType==='Request Refund'&&item.advanceStatus==='Successful') {
                            //     this.refundAmount-=Number(item.amount);
                            // }
                        // });
                    // }
                });

                this.refundAmount = Number(this.data.amount)
                    - Number(this.data.wfaAmount)
                    - Number(this.data.refundAmount);
                    
                this.refundAmount = this.refundAmount < 0 ? 0 : this.refundAmount;
                this.refundAmount = parseFloat(this.refundAmount.toFixed(2));
            });
        });

        this.transAccService.getCurrencyConfig().subscribe(currency => {
            this.currencyConfig=currency.currency;
        });


    }

    isHyperLink(dataHistory) {
        if (dataHistory.transactionId !== this.transId) {
            return true;
        }
        return false;
    }

    convertTransType(transType: string) {
        let transactionType = ''
        if (transType) {
            transactionType = transType.replaceAll('_', ' ').toLowerCase();
            transactionType = transactionType.charAt(0).toUpperCase() + transactionType.slice(1);
        }
        return transactionType;

    }

    convertAcq(value) {
        if (value && value == '1') {
            return 'Vietcombank - MIGS';
        }
        else if (value && value == '2') {
            return 'Vietinbank - CyberSource';
        }
        else if (value && value == '3') {
            return 'CUP';
        }
        else if (value && value == '4') {
            return 'Vietcombank - CyberSource';
        }
        else if (value && value == '5') {
            return 'Vietcombank - MPGS';
        }
        else if (value && value == '6') {
            return 'PayPal';
        }
        else if (value && value == '7') {
            return 'Sacombank - CyberSource';
        }
        else if (value && value == '8') {
            return 'BIDV - CyberSource';
        }
        else if (value && value == '9') {
            return 'Sacombank - MPGS';
        }
        else if (value && value == '10') {
            return 'Techcombank - CyberSource';
        }
        else if (value && value == '11') {
            return 'VPB - MPGS';
        }
        else if (value && value == '12') {
            return 'KBank - Cyber';
        }
        else if (value && value == '13') {
            return 'VPB - Cyber';
        } else {
            return '';
        }
    }
    ngOnDestroy() {
        this.subscription.unsubscribe();
        this.subscription1.unsubscribe();
    }

    /* --- duynq add print screen */
    print_screen_transaction() {
        html2canvas(document.body).then((canvas) => {
            let getImage = canvas.toDataURL();
            let a = document.createElement('a');
            a.href = canvas.toDataURL('image/jpg').replace('image/jpeg', 'image/octet-stream');
            a.download = 'FinancicalTransactionDetail.jpg';
            // them javascrip them cho firefox khi chụp màn hình
            function fireEvent(obj, evt) {
                let fireOnThis = obj;
                if (document.createEvent) {
                    let evObj = document.createEvent('MouseEvents');
                    evObj.initEvent(evt, true, false);
                    fireOnThis.dispatchEvent(evObj);
                }
            }
            fireEvent(a, 'click');
        });
    }

    onSubmit() {
        this.router.navigate(['/payment2-transaction-accounting/18'], { queryParams: this.searchForm.redirectParams() });
    }

    formatDate(dateS: string) {
        const date = new Date(dateS),
            year = date.getUTCFullYear(),
            month = (date.getUTCMonth() + 1).toString(),
            formatedMonth = (month.length === 1) ? ('0' + month) : month,
            day = date.getUTCDate().toString(),
            formatedDay = (day.length === 1) ? ('0' + day) : day,
            hour = date.getUTCHours(),
            ampm = hour >= 12 ? 'PM' : 'AM',
            hours = hour % 12,
            formatedHour = ((hours ? hours : 12).toString().length === 1) ? ('0' + (hours ? hours : 12)) : (hours ? hours : 12),
            minute = date.getUTCMinutes().toString(),
            formatedMinute = (minute.length === 1) ? ('0' + minute) : minute,
            second = date.getUTCSeconds().toString(),
            formatedSecond = (second.length === 1) ? ('0' + second) : second;
        return formatedDay + '/' + formatedMonth + '/' + year + ' ' + formatedHour + ':' + formatedMinute + ' ' + ampm;
    }

    formatRefundType(refundType: string, transType: string): string {
        if (transType === 'Refund') {
            if (refundType === 'ONEPAY_MANUAL') {
                return 'Refund Manual';
            } else if (refundType === 'DISPUTE_MANUAL') {
                return 'Reversal due to Dispute';
            } else if (refundType === undefined || refundType === null || refundType === '') {
                return '';
            } else {
                return 'Online';
            }
        } else {
            return refundType;
        }
    }

    isActive(functionName: string) {
        return this.global.isActive(functionName);
    }

    formatParentId(data: any) {
        if (data) {
            let transTypeArray: string[] = ['VOID_AUTHORIZE', 'REFUND_CAPTURE', 'REFUND', 'VOID_PURCHASE', 'VOID_CAPTURE', 'VOID_REFUND_CAPTURE'];
            if (transTypeArray.includes(data.transType)) {
                return data.parentId;
            } else if (data.transType === 'VOID_REFUND' || data.transType == 'Request Refund') {
                return data.originalId;
            } else {
                return data.transactionId;
            }

        }
    }

    backToPage() {
        this.location.back();
    };

    checkJcb1Year(transactionDate,cardType) {
        if(cardType!=='JCB') return false;
        const transactionDateM=moment(transactionDate);
        transactionDateM.subtract(7,'h').set({hour: 0,minute: 0,second: 0,millisecond: 0});
        const now=moment().set({hour: 0,minute: 0,second: 0,millisecond: 0});
        // var duration = moment.duration(now.diff(transactionDateM));
        // var years = duration.asYears();

        var years=new Number((now.toDate().getTime()-transactionDateM.toDate().getTime())/31536000000);
        // console.log('n: ' + now.format('DD/MM/YYYY HH:mm:ss') + ' t: '+ transactionDateM.format('DD/MM/YYYY HH:mm:ss')+ ' d: '+ years)   ;

        if(years.valueOf()>=1) return true;
        return false;
    }

    canRefund() {

        if(parseFloat(this.data.amount)<=
            (parseFloat(this.data.wfaAmount)
                +parseFloat(this.data.refundAmount))) {
            return false;
        }
        
        // if(this.refundAmount <= 0) {return false;}
        if(this.data.cardType==='PayPal') {return false;}
        if(this.data.transactionType.toUpperCase() == 'PURCHASE'
            &&this.data.responseCode==='0 (Approved)'
            &&parseFloat(this.data.amount)>(parseFloat(this.data.wfaAmount)+parseFloat(this.data.refundAmount))
            &&this.data.checkRefundPermit == true)
        {
            return true;
        }
        return false;
    }


    disableRefund() {
        if(this.checkJcb1Year(this.data.date,this.data.cardType)) {
            return true;
        }

        // Check if merchant_id is in list of merchants that allow sending multiple refund request
        if(refundConfig.midMultipleRRCons.indexOf(this.data.merchantId)>-1) {
            return false;
        }

        if(parseFloat(this.data.amount)<=
            (parseFloat(this.data.wfaAmount)
                +parseFloat(this.data.refundAmount))) {
            return false;
        }

        if(this.dataHistory) {
            const array_state: string[]=['401','405'];
            const array_type: string[]=['Refund','Request Refund'];
            const datawtf1=this.dataHistory.filter(s => (array_state.includes(s.status)&&s.response_code=='0'&&array_type.includes(s.transactionType)));
            let datawtf=0;
            if(datawtf1.length>0) {
                datawtf=datawtf1.map((item) => +item.amount).reduce((sum,current) => sum+current);
            }
            if(datawtf>=parseFloat(this.data.amount)) {
                return true;
            }
        }

        if(this.flagApi===true) {
            return this.flagApi;
        }
    }

    refund(): void {

        if(this.checkJcb1Year(this.data.date,this.data.cardType)) {
            this.toastr.error('Refund transaction failed because the refund deadline has expired. Please contact OnePay for asistance');
            return;
        }
        if(this.refundAmount>parseFloat(this.data.amount)||this.refundAmount===0
            ||this.refundAmount===undefined||this.refundAmount===null) {
            this.toastr.error("Invalid refund amount");
            return;
        }
        // case expire cancel date intstalment
        if(parseFloat(this.data.amount)<
            (parseFloat(this.data.wfaAmount)
                +parseFloat(this.data.refundAmount)
                +Number(this.refundAmount))) {
            this.toastr.error("Invalid refund amount");
            return;
        }
        /*
        this.refundAmount da duoc tinh toan tu du lieu API tra ve o ham init
        if (this.dataHistory) {
            const array_state: string[] = ['401', '405'];
            const array_type: string[] = ['Refund', 'Request Refund'];
            const datawtf1 = this.dataHistory.filter(s => ((array_state.includes(s.status) || s.response_code == '0') && array_type.includes(s.transactionType)));
            let datawtf = 0;
            if (datawtf1.length > 0) {
                datawtf = datawtf1.map((item) => +item.amount).reduce((sum, current) => sum + current);
            }
            console.log('third: ' + datawtf + Number(this.refundAmount));
            if ((datawtf + Number(this.refundAmount)) > parseFloat(this.data.amount)) {
                this.toastr.error(this.translate.instant('common.invalid_refundAmount'));
                return;
            }
        }
        */

        // case expire cancel date intstalment
        const temp=moment(this.data.date,"DD/MM/YYYY HH:mm:ss");
        
        // temp.subtract(7,'h');
        const advanceTime=temp.add(this.data.itaCancelDays,'d').add(1,'d').set({hour: 0,minute: 0,second: 0,millisecond: 0}).toDate();

        if((this.data.itaState !== undefined )&&(this.data.itaState==="created")&&new Date()<advanceTime) {
            let param={
                width: '634px',
                height: '200px',
                panelClass: 'international-refund-modalbox',
                disableClose: false
            };
            this.confirmService.openPopup(param)
                .message("The purchase which has been refunded (partially or fully) will <b>NOT</b> be converted to installment.<br/>Are you sure you want to refund this transaction?")
                .title("Notice!")
                .yes("Yes")
                .no("No").confirm().subscribe(result => {
                    if(result) {
                        this.flagApi=true;
                        const values={
                            merchant_id: this.data.merchantId,
                            amount: this.refundAmount,
                            transaction_reference: this.data.transactionRef,
                            currency: this.data.currency,
                            note: this.note,
                            // hisHash: '['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']',
                            // stringHash: this.global.encodeHmacSha256Base64Url('['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']'),
                        };
                        const body={
                            id: this.data.transactionId,
                            op: 'replace',
                            path: '/refund',
                            value: values,
                            skipCallSynchronize: false
                        };
                        return this.transAccService.PatchRefundInternational(body,this.data.transactionId).subscribe(data => {

                            // if(data.status=='401') {
                            //     data['advanceStatus']='Waiting for Approval';
                            //     this.toastr.success("Request refund sent");
                            //     this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                            //     // data.note=JSON.parse(data.data).note;
                            //     this.refundAmount=Number(this.data.amount)
                            //         -Number(this.data.wfaAmount)
                            //         -Number(this.data.refundAmount);
                            // } else 
                            if(data.status=='405') {
                                data['advanceStatus']='Waiting for onepays approval';
                                this.toastr.success("Request refund sent");
                                data['transType']='Request Refund';
                                this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                                // data.note=JSON.parse(data.data).note;
                                this.refundAmount=Number(this.data.amount)
                                    -Number(this.data.wfaAmount)
                                    -Number(this.data.refundAmount);
                                this.data.itaState='void';
                            } else {
                                this.toastr.success("Refund Successfully");
                                data['transType']='REFUND';
                                data['advanceStatus']='Successful';
                                data.note=this.note;
                                this.data.refundAmount=this.refundAmount+parseFloat(this.data.refundAmount);
                                this.refundAmount=Number(this.data.amount)
                                    -Number(this.data.wfaAmount)
                                    -Number(this.data.refundAmount);
                            }
                            // data['financial_transaction_id']=data.reference_number;
                            this.note='';
                            this.flagApi=false;
                            // if(data.transType==='refund'||data.transType==='Refund') {
                                this.data.itaState='void';
                            // }

                            data.currency=data.amount.currency;
                            data.amount = data.amount.total;
                            data.operatorId=data.operator_id;
                            data.originalId=data.original_transaction_id;
                            data.transRef=data.merchant_transaction_ref;
                            data.transTime=data.transaction_time;
                            data.transactionId=data.transaction_id;
                            data.originalAmount=this.data.amount;

                            // data.advanceStatus:"Waiting for onepays approval"
                            // data.note = 
                            // data.parentId:"0"
                            // data.transType:"Request Refund"
                            this.dataHistory.unshift(data);
                            // this.transAccService.getlistHistoryTransaction(data.original_transaction_id).subscribe(dataHistory => {
                            //     this.dataHistory=dataHistory;
                            // });
                        },
                            err => {
                                console.log('HTTP Error',err)
                                this.flagApi=false;
                            });
                    }
                });
        } else if((this.data.itaState !== undefined)&&(this.data.itaState==="created")&&new Date()>=advanceTime) {
            let param={
                width: '634px',
                height: '250px',
                panelClass: 'international-refund-modalbox',
                disableClose: false
            };
            this.confirmService.openPopup(param)
                .message("This transaction has been sent to the bank for installment registration.<br/>The installment scheme cannot be canceled and installment conversion fee is still applied for this transaction.<br/><br/>Are you sure you want to refund this transaction?")
                .title("Notice!")
                .yes("Yes")
                .no("No").confirm().subscribe(result => {
                    if(result) {
                        this.flagApi=true;
                        const values={
                            merchant_id: this.data.merchantId,
                            amount: this.refundAmount,
                            transaction_reference: this.data.transactionRef,
                            currency: this.data.currency,
                            note: this.note,
                            // hisHash: '['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']',
                            // stringHash: this.global.encodeHmacSha256Base64Url('['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']'),
                        };
                        const body={
                            id: this.data.transactionId,
                            op: 'replace',
                            path: '/refund',
                            value: values,
                            skipCallSynchronize: false
                        };
                        return this.transAccService.PatchRefundInternational(body,this.data.transactionId).subscribe(data => {
                            
                            
                            // if(data.status=='401') {
                                // data['advanceStatus']='Waiting for Approval';
                                // this.toastr.success("Request refund sent");
                                // this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                                // // data.note=JSON.parse(data.data).note;
                                // this.refundAmount=Number(this.data.amount)
                                //     -Number(this.data.wfaAmount)
                                //     -Number(this.data.refundAmount);
                            // } else 
                            if(data.status=='405') {
                                data['advanceStatus']='Waiting for onepays approval';
                                this.toastr.success("Request refund sent");
                                data['transType']='Request Refund';
                                this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                                // data.note=JSON.parse(data.data).note;
                                this.refundAmount=Number(this.data.amount)
                                    -Number(this.data.wfaAmount)
                                    -Number(this.data.refundAmount);
                                this.data.itaState='void';
                            } else {
                                this.toastr.success("Refund Successfully");
                                data['transType']='REFUND';
                                data['advanceStatus']='Successful';
                                // data.note=this.note;
                                this.data.refundAmount=this.refundAmount+parseFloat(this.data.refundAmount);
                                this.refundAmount=Number(this.data.amount)
                                    -Number(this.data.wfaAmount)
                                    -Number(this.data.refundAmount);
                            }
                            // data['financial_transaction_id']=data.reference_number;
                            this.note='';
                            this.flagApi=false;
                            // if(data.transaction_type==='refund'||data.transaction_type==='Refund') {
                                this.data.itaState='void';
                            // }

                            data.currency=data.amount.currency;
                            data.amount=data.amount.total;
                            data.operatorId=data.operator_id;
                            data.originalId=data.original_transaction_id;
                            data.transRef=data.merchant_transaction_ref;
                            data.transTime=data.transaction_time;
                            data.transactionId=data.transaction_id;
                            data.originalAmount=this.data.amount;

                            this.dataHistory.unshift(data);
                            // this.transAccService.getlistHistoryTransaction(data.original_transaction_id).subscribe(dataHistory => {
                            //     this.dataHistory=dataHistory;
                            // });
                        },
                            err => {
                                console.log('HTTP Error',err)
                                this.flagApi=false;
                            });
                    }
                });
        } else {
            let dialogRef: MatDialogRef<InternationalRefundDialog>;
            const purchaseDate=new Date(this.data.date);
            const now=new Date();
            purchaseDate.setDate(purchaseDate.getDate()+90);
            if(this.refundAmount==parseFloat(this.data.amount)
                &&((this.data.currency==='VND'&&this.refundAmount>=2000000)
                    ||(this.data.currency==='USD'&&this.refundAmount>=100)
                    ||(this.data.currency==='THB'&&this.refundAmount>=3000)
                    ||(this.data.currency==='SGD'&&this.refundAmount>=115)
                    ||(this.data.currency==='MYR'&&this.refundAmount>=380)
                    ||(this.data.currency==='IDR'&&this.refundAmount>=1302000)
                    ||(this.data.currency==='JPY'&&this.refundAmount>=11200)
                    ||(this.data.currency==='KRW'&&this.refundAmount>=111000)
                    ||(this.data.currency==='TWD'&&this.refundAmount>=2600)
                    ||(this.data.currency==='CNY'&&this.refundAmount>=600))
                &&purchaseDate>=now
                &&this.data.merchantId.substring(0,3)!=='OP_'
                &&this.data.currency!=='VND'&&(this.data.bin_country!=='Vietnam'&&this.data.bin_country!=='VNM'&&this.data.bin_country!=='VIET NAM')) {
                dialogRef=this.dialog.open(InternationalRefundDialog,{
                    width: '400px',
                    height: '233px',
                    panelClass: 'international-refund-modalbox'
                });
                const keyCurrency=Object.keys(this.currencyConfig).filter(key => key===this.data.currency);

                dialogRef.componentInstance.amount=this.currencyPipe.transform(this.refundAmount+'',this.data.currency);
                dialogRef.componentInstance.amountLimit=(keyCurrency&&this.currencyConfig[keyCurrency[0]])? this.currencyPipe.transform(this.currencyConfig[keyCurrency[0]],this.data.currency):0;
                dialogRef.componentInstance.currency=this.data.currency;
                dialogRef.componentInstance.isOver=true;

            } else {
                dialogRef=this.dialog.open(InternationalRefundDialog,{
                    width: '300px',
                    height: '200px',
                    panelClass: 'international-refund-modalbox',
                });
                dialogRef.componentInstance.amount=this.currencyPipe.transform(this.refundAmount+'',this.data.currency);
                dialogRef.componentInstance.currency=this.data.currency;
                dialogRef.componentInstance.isOver=false;
            }

            dialogRef.afterClosed().subscribe(result => {
                if(result) {
                    this.flagApi=true;
                    const values={
                        merchant_id: this.data.merchantId,
                        amount: this.refundAmount,
                        transaction_reference: this.data.transactionRef,
                        currency: this.data.currency,
                        note: this.note,
                        // hisHash: '['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']',
                        // stringHash: this.global.encodeHmacSha256Base64Url('['+this.createDataHashHistoryRefund().map(item => item.join(',')).join('][')+']')
                    };
                    const body={
                        id: this.data.transactionId,
                        op: 'replace',
                        path: '/refund',
                        value: values,
                        skipCallSynchronize: false
                    };
                    return this.transAccService.PatchRefundInternational(body,this.data.transactionId).subscribe(data => {
                        // if(data.status=='401') {
                        //     data['advanceStatus']='Waiting for Approval';
                        //     this.toastr.success("Request refund sent");
                        //     this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                        //     // data.note=JSON.parse(data.data).note;
                        //     this.refundAmount=Number(this.data.amount)
                        //         -Number(this.data.wfaAmount)
                        //         -Number(this.data.refundAmount);
                        // } else 
                        if(data.status=='405') {
                            data['advanceStatus']='Waiting for onepays approval';
                            this.toastr.success("Request refund sent");
                            data['transType']='Request Refund';
                            this.data.wfaAmount=this.refundAmount+parseFloat(this.data.wfaAmount);
                            // data.note=JSON.parse(data.data).note;
                            this.refundAmount=Number(this.data.amount)
                                -Number(this.data.wfaAmount)
                                -Number(this.data.refundAmount);
                        } else {
                            data['advanceStatus']='Successful';
                            data['transType']='Refund';
                            // data.note=this.note;
                            this.data.refundAmount=this.refundAmount+parseFloat(this.data.refundAmount);
                            this.refundAmount=Number(this.data.amount)
                                -Number(this.data.wfaAmount)
                                -Number(this.data.refundAmount);
                        }
                        // data['financial_transaction_id']=data.reference_number;
                        this.note='';
                        this.flagApi=false;

                        data.currency=data.amount.currency;
                        data.amount=data.amount.total;
                        data.operatorId=data.operator_id;
                        data.originalId=data.original_transaction_id;
                        data.transRef=data.merchant_transaction_ref;
                        data.transTime=data.transaction_time;
                        data.transactionId=data.transaction_id;
                        data.originalAmount=this.data.amount;

                        this.dataHistory.unshift(data);
                        // this.transAccService.getlistHistoryTransaction(data.original_transaction_id).subscribe(dataHistory => {
                        //     this.dataHistory=dataHistory;
                        // });
                    },
                        err => {
                            console.log('HTTP Error',err)
                            this.flagApi=false;
                        });
                }
            });
        }
    }

    requestRefundRouter(transId, transTime){

        const timeFixed=moment(transTime).subtract(7,'h').toDate();
        const fromDate=new Date(timeFixed.setHours(0,0,0,0));
        const toDate=new Date(timeFixed.setHours(23,59,59,0));
        
        const url=this.router.serializeUrl(
            this.router.createUrlTree(['/iportal/international/refund-approval2'],{
                queryParams: {'fromDate': fromDate,'toDate': toDate,'transactionId': transId}
            })
        );
        window.open(url,'_blank');

    }
}
