<div class="wrapper" id="transaction-acc-detail">
    <div class="container-fluid">
        <div class="row">
            <div class="padding-left-right-0 col-sm-2 col-md-2 col-lg-2">
                <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12 ">
                    <span class="title_menu">{{title}}</span>
                </div>
                <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12 ">
                    <transaction-accounting-search-form
                        (submitFinSearch)="onSubmit()"></transaction-accounting-search-form>
                </div>
            </div>
            <div class="padding-left-right-10 col-sm-10 col-md-10 col-lg-10 detail-info">
                <div class="detail-top-button padding-left-right-0 col-sm-12 col-md-12 col-lg-12">
                    <div style="text-align: left; margin: 10px 0px;">
                        <button type="button" pTooltip="Back" pButton label="Back" icon="pi pi-angle-left"
                            iconPos="left" (click)="backToPage()" class=" download-button p-button-success"></button>
                    </div>
                    <div style="text-align: right;">
                    </div>
                </div>
                <div class="row detail-box" *ngIf="data" id="frm_display">
                    <div class="padding-left-right-10 col-md-6 col-xl-6 col-lg-6">
                        <div class="padding-left-right-10 col-md-12 col-xl-12 col-lg-12 box-shadow border rounded">
                            <div class="row" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Order Details</h3>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Merchant ID</td>
                                            <td class="text-left ui-resizable-column content">{{data?.merchantId}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Acquirer ID</td>
                                            <td class="text-left ui-resizable-column content">{{convertAcq(data?.acquirer)}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Transaction ID</td>
                                            <td class="text-left ui-resizable-column content">{{data?.transactionId}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Transaction No</td>
                                            <td class="text-left ui-resizable-column">{{data?.transactionNo}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Network Transaction ID</td>
                                            <td class="text-left ui-resizable-column content">{{data?.netWorkTransId}}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Date</td>
                                            <td class="text-left ui-resizable-column content">{{data?.date}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Order Reference</td>
                                            <td class="text-left ui-resizable-column">{{data?.orderInfo}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Ticket Number</td>
                                            <td class="text-left ui-resizable-column content">{{data?.ticketNumber}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">IP Address</td>
                                            <td class="text-left ui-resizable-column content">{{data?.ipAddress}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Transaction Reference</td>
                                            <td class="text-left ui-resizable-column content">{{data?.transactionRef}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Transaction Type</td>
                                            <td class="text-left ui-resizable-column content">{{data?.transactionType}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Amount</td>
                                            <td class="text-left ui-resizable-column content">{{data.currency}}
                                                {{data.amount | CurrencyPipe: data.currency }}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Authorization Code</td>
                                            <td class="text-left ui-resizable-column content">{{data?.authCode}}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Original Auth Code</td>
                                            <td class="text-left ui-resizable-column content">{{data?.originalAuthCode}}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Response Code</td>
                                            <td class="text-left ui-resizable-column content">{{data?.responseCode}}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Acquirer Response Code</td>
                                            <td class="text-left ui-resizable-column content">{{data?.acquirerResponseCode}} <span *ngIf="data.transactionType == 'REFUND' && data.acquirerDescription"> ({{data?.acquirerDescription}})</span>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Return URL</td>
                                            <td class="text-left ui-resizable-column content">{{data?.returnUrl}}</td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Merchant Bank ID</td>
                                            <td class="text-left ui-resizable-column content">{{data?.bankMerchantId}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Contract Type</td>
                                            <td class="text-left ui-resizable-column content">{{data?.contractType}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Card Details</h3>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Card Type</td>
                                            <td class="text-left ui-resizable-column content">{{data?.cardType}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Source</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.source}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Card No</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cardNumber}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Card EXP</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cardExp}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Card Name</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cardName}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Email</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cardEmail}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Phone</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cardPhone}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Commercial Card</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.commerCard}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Commercial Card Indicator</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.commerCardCator}}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">CSC Result Code</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cscResult}}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="padding-left-right-10 col-md-6 col-xl-6 col-lg-6">
                        <div class="padding-left-right-10 col-md-12 col-xl-12 col-lg-12 box-shadow border rounded">
                            <div class="row" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Address Verification Details</h3>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Address</td>
                                            <td class="text-left ui-resizable-column content">{{data?.address}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">City/town</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.city}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">State/Province</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.state}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Zip/Postal Code</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.zip}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Country</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.country}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">AVS Result Code</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.avsCode}}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <br>
                            <div class="row" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Customer information</h3>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">ID</td>
                                            <td class="text-left ui-resizable-column content">{{data?.cusId}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Email</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.email}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Address</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cusAdd}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Province</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.province}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">City</td>
                                            <td class="text-left ui-resizable-column">
                                                {{data?.city}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Country</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.cusCountry}}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <br>
                            <div class="row" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Payment Authentication Details</h3>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Authentication State</td>
                                            <td class="text-left ui-resizable-column content">{{data?.authState}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">3-D Secure VERes.enrolled</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.dVer}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">3-D Secure XID</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.dXId}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">3-D Secure ECI</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.dEci}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">3-D Secure PARes.status</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.dPStatus}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Verification Security Level
                                            </td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.verySeLevel}}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <br *ngIf="data.source == 'Apple Pay'">
                            <div class="row" *ngIf="data.source == 'Apple Pay'" style="margin-top: 15px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5">
                                    <h3 class="">Token Info</h3>
                                </div>
                            </div>
                            <div class="row" *ngIf="data.source == 'Apple Pay'" style="margin-top: 5px;">
                                <div class="padding-left-right-12 col-md-12 col-xl-12 col-lg-12">
                                    <table class="wapper-content">
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Token Number</td>
                                            <td class="text-left ui-resizable-column content">{{data?.tokenNumber}}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Token Expiry</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.tokenExpiry}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left ui-resizable-column left-title">Type</td>
                                            <td class="text-left ui-resizable-column content">
                                                {{data?.type}}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <br>
                        <!-- <div class="row" style="display: flex;justify-content:center">
                            <button type="button" pButton label="Request Refund"
                                icon="pi pi-check-circle" iconPos="left" (click)="requestRefund()"
                                style="margin-left: 0.5em;"></button>
                        </div> -->
                        <!-- *ngIf="canRefund()" -->
                        <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12 refund-box" *ngIf="canRefund() && global.isActive('payment2_transaction_acc_rr')"> 
                            <div class="row">
                                <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12">
                                    <p class="text_action">DO REFUND</p>
                                </div>
                            </div>                        
                            <div class="row">
                                <div class="padding-left-right-12 col-sm-12 col-md-12 col-lg-12 form-row"
                                    style="align-self: center; margin-bottom: 10px;">
                                    <span class="input-group">
                                        <p-inputNumber  id="refund_amount" autocomplete="off"
                                            name="refund_amount" [disabled]="flagApi"
                                            *ngIf="data.currency !== 'VND' && data.currency !== 'IDR' && data.currency !== 'JPY' && data.currency !== 'KRW'"
                                            [(ngModel)]="refundAmount" style="background-color: #fff;"
                                            [ngModelOptions]="{standalone: true}"
                                            locale="en-US" [minFractionDigits]="2"
                                            ></p-inputNumber>
                                        <p-inputNumber id="refund_amount"
                                            autocomplete="off" name="refund_amount" [disabled]="flagApi"
                                            *ngIf="(data.currency === 'VND' || data.currency === 'IDR'  || data.currency === 'JPY' || data.currency === 'KRW')"
                                            [(ngModel)]="refundAmount"
                                            ></p-inputNumber>
                                            <label class="label-custom" for="csc">Refunded amount</label>
                                    </span>
                                </div>
                                <br>
                                <div class="padding-left-right-12 col-sm-12 col-md-12 col-lg-12 form-row"
                                    style="align-self: center;margin-bottom: 10px;">
                                    <span class="input-group">
                                        <input class="" [(ngModel)]="note" id="note" name="note" maxlength="200"
                                        [disabled]="flagApi" pInputText>
                                        <label class="label-custom" for="csc">Note (Optional)</label>
                                    </span>
                                </div>
                                <br>
                                <div class="padding-left-right-12 col-sm-12 col-md-12 col-lg-12">
                                    <button class="refund-button" mat-raised-button color="primary"
                                        [disabled]="disableRefund()" (click)="refund()">DO REFUND</button>
                                </div>

                            </div>
                        </div>                        

                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12">
                        <p class="text_action">History</p>
                    </div>
                </div>
                <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12 scrol_mobile">
                    <p-table [value]="dataHistory">
                        <ng-template pTemplate="header">
                            <tr class="tr_header">
                                <th class="text-center mat-column-trans-id">Transaction ID</th>
                                <th class="text-center mat-column-trans-id">Original Trans ID</th>
                                <th class="text-center mat-column-trans-ref">Merchant Transaction Ref</th>
                                <th class="text-center mat-column-trans-time">Date</th>
                                <th class="text-center mat-column-amount">Original Amount</th>
                                <th class="text-center mat-column-amount">Payment Amount</th>
                                <th class="text-center mat-column-trans-type">Transaction Type</th>
                                <th class="text-center mat-column-operator-id">Operator ID</th>
                                <th class="text-center mat-column-trans-state">Trans State</th>
                                <th class="text-center mat-column-desc">Description</th>
                                <th class="text-center mat-column-desc">Authorization Code</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-data let-expanded="expanded">
                            <tr class="tr_body">
                                <td class="text-center mat-column-trans-id">
                                    <span *ngIf="!isHyperLink(data)">
                                        {{ data.transactionId }}
                                    </span>
                                    <a *ngIf="isHyperLink(data) && data?.transType != 'Request Refund'"
                                        [routerLink]="['/payment2-transaction-accounting/18/transaction/',data.transactionId]"
                                        [queryParams]="searchForm.redirectParams()">{{data.transactionId}}</a>
                                    <a *ngIf="isHyperLink(data) && data?.transType == 'Request Refund' && data?.advanceStatus == 'Waiting for onepays approval'" 
                                        (click)="requestRefundRouter(data?.transactionId, data?.transTime)">{{data.transactionId}}</a>
                                </td>
                                <td class="text-center mat-column-amount">
                                    {{formatParentId(data)}}
                                </td>
                                <td class="text-center split_text mat-column-trans-ref" [pTooltip]="data?.transRef">
                                    {{data?.transRef}}</td>
                                <td class="text-center mat-column-trans-time">
                                    {{formatDate(data?.transTime)}}</td>
                                <td class="text-right mat-column-amount"> {{ data?.currency }}
                                    {{data?.originalAmount | CurrencyPipe: data.currency }}
                                </td>
                                <td class="text-right mat-column-amount"> {{ data?.currency }}
                                    {{data?.amount | CurrencyPipe: data.currency }}
                                </td>

                                <td class="text-center mat-column-trans-type">{{data?.transType}}</td>
                                <td class="text-center mat-column-operator-id">{{data?.operatorId}}</td>
                                <td class="text-center mat-column-trans-state">
                                    {{data?.advanceStatus}}
                                </td>
                                <td class="text-center split_text mat-column-desc">
                                    {{ data?.note }}
                                </td>
                                <td class="text-center split_text mat-column-desc">
                                    {{ data?.authCode }}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>
    </div>