import { SharedModule } from '@shared/shared.module';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { PaymentTransactionAccountingSearchComponent } from '@module/payment_merchant_accounting/payment-accounting-transaction-search/payment-transaction-accounting-search-component';
import { TransactionAccountingDetailComponent } from '@module/payment_merchant_accounting/payment-accounting-transaction-search/detail/payment-transaction-accounting-detail-component';
import { PaymentAccountingTransactionSearchForm } from '@module/payment_merchant_accounting/payment-accounting-transaction-search/search/search.component';
import { AccountingTransactionSearchForm } from './transaction-accounting-18/search/search.component';
import { TransactionAccountingSearchComponent } from './transaction-accounting-18/transaction-accounting-search-component';
import { TransactionAccounting18DetailComponent } from './transaction-accounting-18/detail/transaction-accounting-detail-component';
import { PaymentTransactionAccountingRoutingModule } from '@module/payment_merchant_accounting/payment-transaction-accounting-routing';
import { PaymentTransactionAccountingService } from '@service/payment_reconciliation/payment-transaction-accounting.service';
import { DialogModule } from 'primeng/dialog';
import { PaymentAccoutingDatePipe } from './pipes/date.pipe';
import { InternationalService } from '@service/international.service';
import { OverLimitService } from '@service/payment_reconciliation/over-limit';
import { InternationalRefundDialog } from './transaction-accounting-18/detail/refund-dialog/refund-dialog.component';
const PIPE_COMPONENTS = [
  PaymentAccoutingDatePipe
]
@NgModule({
  imports: [
    PaymentTransactionAccountingRoutingModule,
    SharedModule,
    DialogModule,
  ],
  exports: [],
  declarations: [
    PaymentTransactionAccountingSearchComponent,
    PaymentAccountingTransactionSearchForm,
    TransactionAccountingDetailComponent,
    AccountingTransactionSearchForm,
    TransactionAccountingSearchComponent,
    TransactionAccounting18DetailComponent,
    InternationalRefundDialog,
    PIPE_COMPONENTS
  ],
  providers: [
    PaymentTransactionAccountingService,
    OverLimitService,
    InternationalService
  ],
  entryComponents: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class PaymentTransactionAccountingModule {
  constructor() {
  }
}
