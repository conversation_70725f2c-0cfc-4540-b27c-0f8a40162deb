import { HttpClient,HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { asBlob } from 'html-docx-js-typescript-papersize-thenn';
import { saveAs } from 'file-saver'

@Injectable()
export class ContractService {
  private _editing: boolean;
  public EXPORT_CONTRACT_HTML = 'contract/export-html';
  private EXPORT_CONTRACT_BY_TEMPLATE = 'contract-export-by-template-docx';
  /* load list contract template */
  private CONTRACT_TEMPLATE = 'contract-template/load-list-by-type';
  /* download contract pdf */
  private CONTRACT_PDF = 'contract-template/download-pdf';
  /* load list contract history by id contract */
  private CONTRACT_HISTORY = 'contract-history/load-list-by-id';
  /* insert log contract cancel/approve/reject contract */
  private CONTRACT_HISTORY_INSERT = 'contract-history/insert';
  /* download contract compared */
  private CONTRACT_COMPARED = 'contract-template/download-compared';
  /* get contract file scan information */
  private CONTRACT_FILE_SCAN_INFORMATION = 'contract-template/get-file-scan-information';

  public static HTML_DOCX_OPTION_A4 = {
    margins: {
      top: 1132,
      left: 1698,
      right: 849,
      bottom: 1132
    },
    orientation: 'portrait' as const,
    paperWidth: 11905.511,
    paperHeight: 16837.795,
  };

  constructor(private _http: HttpClient) {

  }

  public setEditing(editing: boolean): void {
    this._editing = editing;
    // console.log('setEditing', this._editing);
  }

  public isEditing(): boolean {
    return this._editing;
  }

  public getContractHtml(body: any): Observable<any> {
    return this._http.post(this.EXPORT_CONTRACT_HTML, body);
  }

  public exportContractHtml(contractId: string, contractCode: string, version: string, bilingual?: boolean): void {
    let body = {
      'id': contractId,
      'contractCode': contractCode,
      'version': version,
      'bilingual': bilingual || false,
    }

    this.getContractHtml(body).subscribe((response) => {
      if (response && response.templateHtml && response.fileName) {
        let text = response.templateHtml;
        asBlob(text, ContractService.HTML_DOCX_OPTION_A4).then(data => {
          saveAs(data, response.fileName + '.docx') // save as docx file
        })
      }
    });
  }

  exportContractBytemplateDocx(params : HttpParams): Observable<any> {
    return this._http.get(this.EXPORT_CONTRACT_BY_TEMPLATE, {params: params});
  }

  /* load list contract template */
  loadContractTemplate(type:any,contractCode:any): Observable<any> {
    return this._http.get(this.CONTRACT_TEMPLATE + '?S_TYPE=' + type+'&S_CONTRACT_CODE='+contractCode);
  }

  /* download contract pdf */
  downloadContractPdf(): Observable<any> {
    return this._http.get(this.CONTRACT_PDF, { responseType: 'blob' });
  }

  /* load list contract history by id contract */
  loadContractHistory(contractId: any): Observable<any> {
    return this._http.get(this.CONTRACT_HISTORY + '?contractId=' + contractId);
  }

  /* insert log contract cancel/approve/reject contract */
  insertContractHistory(body: any): Observable<any> {
    return this._http.post(this.CONTRACT_HISTORY_INSERT, body);
  }

  /* download contract compared */
  downloadContractCompared(contractId: any, type: any): Observable<any> {
    return this._http.get(this.CONTRACT_COMPARED + '?id=' + contractId + '&type=' + type, { responseType: 'blob' });
  }

  /* get contract file scan information */
  getContractFileScanInformation(contractId: any): Observable<any> {
    return this._http.get(this.CONTRACT_FILE_SCAN_INFORMATION + '?id=' + contractId);
  }
}
