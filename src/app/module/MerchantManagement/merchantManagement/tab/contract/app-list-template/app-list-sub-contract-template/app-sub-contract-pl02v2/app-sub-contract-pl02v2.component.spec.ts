import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppSubContractPl02v2Component } from './app-sub-contract-pl02v2.component';

describe('AppSubContractPl02v2Component', () => {
  let component: AppSubContractPl02v2Component;
  let fixture: ComponentFixture<AppSubContractPl02v2Component>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AppSubContractPl02v2Component ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AppSubContractPl02v2Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
