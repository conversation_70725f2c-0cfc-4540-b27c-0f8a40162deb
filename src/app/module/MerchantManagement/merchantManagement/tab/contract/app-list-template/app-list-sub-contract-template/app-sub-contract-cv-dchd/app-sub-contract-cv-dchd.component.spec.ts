import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppSubContractCvDchdComponent } from './app-sub-contract-cv-dchd.component';

describe('AppSubContractCvDchdComponent', () => {
  let component: AppSubContractCvDchdComponent;
  let fixture: ComponentFixture<AppSubContractCvDchdComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AppSubContractCvDchdComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AppSubContractCvDchdComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
