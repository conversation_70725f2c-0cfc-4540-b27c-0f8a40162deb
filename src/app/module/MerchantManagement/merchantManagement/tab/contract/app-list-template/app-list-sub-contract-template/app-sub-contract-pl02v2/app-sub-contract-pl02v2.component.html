<div id="form-pl02v2">
    <form class="contract-information-form" #contractpl02Form="ngForm">
        <div *ngIf="parentContractCode === 'HD14'">
            <div *ngIf="isActive('general_contract_information') || isCreateOrEdit">
                <div class="row info-panel-height format-fields" style="margin-top: 10px !important;">

                    <div class="form-group col-md-4" style="margin-top: 5px !important;">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="contractNumber"
                                pInputTextarea id="contractNumber" type="text"
                                [(ngModel)]="contractOriginal.contractNumber" #contractNumberT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractOriginal.contractNumber = contractOriginal.contractNumber.trim()"></textarea>
                            <label class="label-custom fixPositionLabel" for="contractNumber">Số phụ lục</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                                dateFormat="dd/mm/yy" [(ngModel)]="contractOriginal.signatureDate"
                                #signatureDateT="ngModel" name="signatureDate" hideOnDateTimeSelect="true"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="signatureDate">Ngày ký</label>
                        </span>
                    </div>

                </div>
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-1" style="margin-top: -3px;">
                        <span class="input-group">
                            <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="danhXungList"
                                [(ngModel)]="contractDetail.danhXung" #danhXungT="ngModel"
                                dropdownIcon=" pi pi-sort-down" name="danhXung" id="danhXung" [autoDisplayFirst]="false"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-dropdown>
                            <label [ngClass]="(contractDetail.danhXung === undefined || contractDetail.danhXung === '' || contractDetail.danhXung === null) && validatePoint === true
                                  ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="danhXung" style="margin-top: -4px !important;">Danh xưng *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" id="representative"
                                name="representative" pInputTextarea type="text"
                                [(ngModel)]="contractOriginal.representative"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractOriginal.representative === undefined || contractOriginal.representative === '' || contractOriginal.representative === null) && validatePoint === true
                            ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="representative">Người đại diện *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-8">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="permanentAddress"
                                pInputTextarea id="permanentAddress" type="text"
                                [(ngModel)]="contractDetail.permanentAddress"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(!contractDetail.permanentAddress) && validatePoint === true
                              ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="permanentAddress">
                                Địa chỉ thường trú *</label>
                        </span>
                    </div>
                </div>
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea
                                id="phone" type="text" [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                        </span>
                    </div>

                    <div class="form-group col-md-8">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="email" pInputTextarea
                                id="email" type="text" [(ngModel)]="contractDetail.email"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.email === undefined || contractDetail.email === '' || contractDetail.email === null) && validatePoint === true
                          ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="email">Email *</label>
                        </span>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" id="peopleId" name="peopleId"
                                type="text" pInputTextarea [(ngModel)]="contractDetail.peopleId"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.peopleId === undefined || contractDetail.peopleId === '' || contractDetail.peopleId === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="peopleId">CMND/CCCD hoặc Hộ chiếu *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group" style="margin-top: -3px;">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                                [(ngModel)]="contractOriginal.rangeDate" #rangeDateT="ngModel" name="rangeDate"
                                hideOnDateTimeSelect="true" [monthNavigator]=true [yearNavigator]="true"
                                yearRange="1900:2050"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="rangeDate">Ngày cấp</label>
                        </span>
                    </div>

                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" id="issuedBy" name="issuedBy"
                                type="text" pInputTextarea [(ngModel)]="contractDetail.issuedBy"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.issuedBy === undefined || contractDetail.issuedBy === '' || contractDetail.issuedBy === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="issuedBy">Nơi cấp *</label>
                        </span>
                    </div>
                </div>
            </div>

        
            <div *ngIf="isActive('fee_contract_information') || isCreateOrEdit">
            <div class="format-fields">
            <span [ngClass]="(this.contractDetail.cardListArray === undefined || this.contractDetail.cardListArray === null || this.contractDetail.cardListArray.length < 1) && validatePoint === true
                ? 'inValid' : ''"
                style="color: #0089D0; margin-top: 20px !important;font-weight: bold;font-size: 12px !important;">
                Phạm vi hợp tác *
            </span>
            <div class="row info-panel-height">
                <div class="form-group col-md-8" style="margin-top: -10px !important;margin-bottom: 0px !important;">
                <label class="label-custom fixPositionLabel" for="cardType" style="margin-left: 0px !important;">Thẻ
                    quốc tế</label>
                <span class="input-group">
                    <div>
                    <p-checkbox name="Visa" value="Visa" [(ngModel)]="contractDetail.cardListArray" inputId="Visa"
                        #cardListT="ngModel" (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="Visa" class="card-label">Visa</label>
                    <p-checkbox name="MasterCard" value="MasterCard" [(ngModel)]="contractDetail.cardListArray"
                        inputId="MasterCard" #cardListT="ngModel" (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="MasterCard" class="card-label">MasterCard</label>
                    <p-checkbox name="JCB" value="JCB" [(ngModel)]="contractDetail.cardListArray" inputId="JCB"
                        #cardListT="ngModel" (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="JCB" class="card-label">JCB</label>
                    <p-checkbox name="UnionPay" value="UnionPay" [(ngModel)]="contractDetail.cardListArray"
                        inputId="UnionPay" #cardListT="ngModel" (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="UnionPay" class="card-label">UnionPay</label>
                    <p-checkbox name="AmericanExpress" value="AmericanExpress" (onChange)="checkFeeType()"
                        [(ngModel)]="contractDetail.cardListArray" inputId="AmericanExpress" #cardListT="ngModel"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="AmericanExpress" class="card-label">American Express</label>
                    </div>
    
                    <div style="margin-top: 5px !important;">
                    <p-checkbox name="ApplePay" value="ApplePay" (onChange)="checkFeeType()"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApplePay" #cardListT="ngModel"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApplePay" class="card-label">Apple Pay</label>
                    <p-checkbox name="GooglePay" value="GooglePay" (onChange)="checkFeeType()"
                        [(ngModel)]="contractDetail.cardListArray" inputId="GooglePay" #cardListT="ngModel"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="GooglePay" class="card-label">Google Pay</label>
                    <p-checkbox name="SamsungPay" value="SamsungPay" (onChange)="checkFeeType()"
                        [(ngModel)]="contractDetail.cardListArray" inputId="SamsungPay" #cardListT="ngModel"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="SamsungPay" class="card-label">Samsung Pay</label>
                    </div>
                </span>
                </div>
    
                <div class="form-group col-md-4" style="margin-top: -10px !important;margin-bottom: 0px !important;">
                <label [ngClass]="checkInternationalFee && checkPhamViTheQuocTe() === false && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'" for="cardType"
                    style="margin-left: 0px !important;">Phạm vi chấp nhận của Thẻ quốc tế
                    {{checkInternationalFee ? '*' : ''}}</label>
                <span class="input-group">
                    <div class="combobox-label">
                    <p-checkbox name="ApproveDomesticCard" value="ApproveDomesticCard"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveDomesticCard" #cardListT="ngModel"
                        (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveDomesticCard" class="card-label">Thẻ quốc tế phát hành
                        trong
                        nước</label>
                    </div>
                    <div class="combobox-label">
                    <p-checkbox name="ApproveInternationalCard" value="ApproveInternationalCard"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveInternationalCard" #cardListT="ngModel"
                        (onChange)="changeCardType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveInternationalCard" class="card-label">Thẻ quốc tế phát hành
                        ở
                        nước ngoài</label>
                    </div>
                </span>
                </div>
    
                <div class="form-group col-md-12" style="margin-top: -5px !important;">
                <label class="label-custom fixPositionLabel" for="cardType"
                    style="margin-left: 0px !important; margin-top: 10px !important;">Thẻ nội địa</label>
                <span class="input-group">
                    <div class="combobox-label">
                    <p-checkbox name="ApproveOnepayDomesticCard" value="ApproveOnepayDomesticCard"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveOnepayDomesticCard" #cardListT="ngModel"
                        (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveOnepayDomesticCard" class="card-label">Thẻ nội địa được ONEPAY chấp
                        nhận
                        thanh toán</label>
                    </div>
                    <div class="combobox-label">
                    <p-checkbox name="ApproveSamsungPay" value="ApproveSamsungPay"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveSamsungPay" #cardListT="ngModel"
                        (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveSamsungPay" class="card-label">Samsung Pay</label>
                    </div>
    
                    <br>
                    <div class="combobox-label">
                    <p-checkbox name="ApproveOnepayMobileApp" value="ApproveOnepayMobileApp"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveOnepayMobileApp" #cardListT="ngModel"
                        (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveOnepayMobileApp" class="card-label">Ứng dụng di động được ONEPAY chấp
                        nhận thanh toán</label>
                    </div>
    
                    <div class="combobox-label">
                    <p-checkbox name="dischargeVietQR" value="ApproveDischargeVietQR"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveDischargeVietQR" #cardListT="ngModel"
                        (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveDischargeVietQR" class="card-label">Thanh toán VietQR</label>
                    </div>
    
                    <div class="combobox-label">
                    <p-checkbox name="ApproveInstallment" value="ApproveInstallment"
                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveInstallment" #cardListT="ngModel"
                        (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveInstallment" class="card-label">Chấp nhận triển khai dịch vụ thanh
                        toán
                        trả góp</label>
                    </div>
                    <div class="combobox-label">
                    <p-checkbox name="ApproveBNPL" value="ApproveBNPL" [(ngModel)]="contractDetail.cardListArray"
                        inputId="ApproveBNPL" #cardListT="ngModel" (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveBNPL" class="card-label">Chấp nhận triển khai dịch vụ mua trước
                        trả sau</label>
                    </div>
                    <div class="combobox-label" style="display:none">
                    <p-checkbox name="ApproveShopify" value="ApproveShopify" [(ngModel)]="contractDetail.cardListArray"
                        inputId="ApproveShopify" #cardListT="ngModel" (onChange)="checkFeeType()"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </p-checkbox>
                    <label for="ApproveShopify" class="card-label">Thanh toán trên nền tảng Shopify</label>
                    </div>
                </span>
                </div>
            </div>
            </div>
    
            <!-- Child Table -->
    
            <div id="table-hd14" style="margin-bottom: 5px !important;margin-top: -10px !important;"
            class="row info-panel-height format-fields">
            <div class="form-group col-md-12">
                <app-child-table-merchant-id-v3 [tableData]="contractDetail.subTableMerchant" [validatePoint]="validatePoint"
                [checkEditContractDetail]="checkEditContractDetail" (deleteMerchantById)="deleteMerchant()"
                (changeData)="autoFillKyQuy()">
                </app-child-table-merchant-id-v3>
            </div>
            </div>
    
            <div class="format-fields">
            <span [ngClass]="(this.contractDetail.tgTamUngSelection === undefined || this.contractDetail.tgTamUngSelection === null || this.contractDetail.tgTamUngSelection === '') && validatePoint === true
                ? 'inValid' : ''" style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                Thời gian tạm ứng *
            </span>
            </div>
            <div class="row info-panel-height format-fields">
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="t1"
                    inputId="t1" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (ngModelChange)="checkOthers()">
                </p-radioButton>
                <label for="t1" class="card-label">T+1</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="t2"
                    inputId="t2" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (ngModelChange)="checkOthers()">
                </p-radioButton>
                <label for="t2" class="card-label">T+2</label>
                </span>
            </div>
            <div class="form-group col-md-1">
                <span class="input-group">
                <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="other"
                    inputId="other" (ngModelChange)="checkOthers()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="other" class="card-label">Khác</label>
                </span>
            </div>
            <div class="form-group col-md-3">
                <span class="input-group"
                *ngIf="contractDetail.tgTamUngSelection !== undefined && contractDetail.tgTamUngSelection === 'other'"
                style="margin-top: -3px !important;">
                <textarea rows="1" autoResize="true" class="form-control" name="inputTgTamUngKhac" pInputTextarea
                    id="inputTgTamUngKhac" type="text" [(ngModel)]="contractDetail.inputTgTamUngKhac"
                    #inputTgTamUngKhacT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                </span>
            </div>
            </div>
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
            && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true  || checkAppFee === true || checkDischargeVietQR === true)">
            <div class="form-group col-md-4">
                <span class="input-group">
                <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                    Phí dịch vụ (đã bao gồm VAT)
                </span>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="feeService" [(ngModel)]="contractDetail.feeService" value="normalFee"
                    inputId="normalFee"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (click)="changeFeeType('normalFee')">
                </p-radioButton>
                <label for="normalFee" class="card-label">Phí thông thường</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="feeService" [(ngModel)]="contractDetail.feeService" value="specialFee"
                    inputId="specialFee" (click)="changeFeeType('specialFee')"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="specialFee" class="card-label">Phí đặc biệt</label>
                </span>
            </div>
            <div class="form-group col-md-12" style="padding-top: 0px !important;"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'">
                <span class="input-group">
                <p-checkbox name="contractDetail.autoFillStandardFee" value="autoFillStandardFee"
                    [(ngModel)]="contractDetail.autoFillStandardFee" inputId="autoFill" #autoFillT="ngModel"
                    (onChange)="changeFeeType('autoFill')" inputId="autoFillStandardFee"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-checkbox>
                <label for="autoFillStandardFee" class="card-label">Tự động fill mức phí chuẩn</label>
                </span>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields" style="margin-top: -15px !important;"
            *ngIf="contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee')
            && (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0)">
            <div class="form-group col-md-4" style="margin-bottom: -5px !important;"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group first-label" style="padding-top: 15px !important;">
                <span>
                    Phí đăng ký dịch vụ
                </span>
                </span>
            </div>
            <div class="form-group col-md-4" [class.col-md-8]="checkShopifyFee === false"
                style="margin-bottom: -5px !important;"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group first-label" style="padding-top: 15px !important;">
                <span>
                    Phí hàng tháng
                </span>
                </span>
            </div>
            <div class="form-group col-md-4" style="margin-bottom: -5px !important;" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                <span class="input-group first-label" style="padding-top: 15px !important;">
                <span>
                    Phí Shopify
                </span>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                    class="form-control" name="registerFee" pInputTextarea id="registerFee" type="text"
                    [(ngModel)]="contractDetail.registerFee" #registerFeeT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <p-inputNumber locale='en-US' name="registerFee" id="registerFee"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    #registerFeeT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                    [(ngModel)]="contractDetail.registerFee" (onBlur)="uncheckManual()">
                </p-inputNumber>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group" style="padding-top: 5px !important;">
                <span>
                    VND
                </span>
                </span>
            </div>
    
            <div class="form-group col-md-2"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="monthFee" pInputTextarea id="monthFee"
                    type="text" [(ngModel)]="contractDetail.monthFee" #monthFeeT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (input)="uncheckManual()">
                            </textarea>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                <span class="input-group" style="padding-top: 5px !important;">
                <!-- <span>
                                VND
                            </span> -->
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="shopifyFee" pInputTextarea id="shopifyFee"
                    type="text" [(ngModel)]="contractDetail.shopifyFee" #shopifyFeeT="ngModel" [readOnly]="true">
                            </textarea>
                <!-- <p-inputNumber locale='en-US' name="shopifyFee" id="shopifyFee"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                [disabled]="true"
                                #shopifyFeeT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.shopifyFee">
                            </p-inputNumber> -->
                </span>
            </div>
            <div class="form-group col-md-2" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                <span class="input-group" style="padding-top: 5px !important;">
                <!-- <span>
                                VND
                            </span> -->
                </span>
            </div>
            </div>
    
            <!-- start danhnt -->
            <!-- phí xử lý giao dịch -->
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee === true || checkDomesticFee === true  || checkAppFee === true || checkDischargeVietQR === true)">
            <div class="form-group col-md-12">
                <span class="input-group first-label">
                <span>
                    Phí xử lý giao dịch
                </span>
                </span>
            </div>
            <div
                [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                *ngIf="checkInternationalFee === true">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                    class="form-control" name="feeTransInternational" pInputTextarea id="feeTransInternational" type="text"
                    maxlength="100" [(ngModel)]="contractDetail.feeTransInternational" #feeTransInternationalT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <p-inputNumber locale='en-US' name="feeTransInternational" id="feeTransInternational"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    #feeTransInternationalT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                    [(ngModel)]="contractDetail.feeTransInternational" (onBlur)="uncheckManual()">
                </p-inputNumber>
                <label class="label-custom fixPositionLabel" for="feeTransInternational">Thẻ quốc tế</label>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkInternationalFee === true">
                <span class="input-group" style="padding-top: 5px !important;">
                <span>
                    VND/giao dịch
                </span>
                </span>
            </div>
    
            <div
                [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                *ngIf="checkDomesticFee === true">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                    class="form-control" name="feeTransDomesticAndApp" pInputTextarea id="feeTransDomesticAndApp" type="text"
                    maxlength="100" [(ngModel)]="contractDetail.feeTransDomesticAndApp" #feeTransDomesticT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <p-inputNumber locale='en-US' name="feeTransDomesticAndApp" id="feeTransDomesticAndApp"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    #feeTransDomesticAndAppT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                    [(ngModel)]="contractDetail.feeTransDomesticAndApp" (onBlur)="uncheckManual()">
                </p-inputNumber>
                <label class="label-custom fixPositionLabel" for="feeTransDomesticAndApp">Thẻ nội địa</label>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticFee === true">
                <span class="input-group" style="padding-top: 5px !important;">
                <span>
                    VND/giao dịch
                </span>
                </span>
            </div>
    
            <div
                [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                *ngIf="checkAppFee === true">
                <span class="input-group">
                <!-- <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                                class="form-control" maxlength="100" name="feeApp" pInputTextarea id="feeApp" type="text"
                                [(ngModel)]="contractDetail.feeApp" #feeAppT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                            <p-inputNumber locale='en-US' name="feeApp" id="feeApp"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #feeTransDomesticAndAppT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.feeApp"></p-inputNumber>
                            <label class="label-custom fixPositionLabel" for="feeApp">Ứng dụng di động</label> -->
                <textarea rows="1" autoResize="true" *ngIf="contractDetail.feeService !== undefined " class="form-control"
                    maxlength="150" name="feeApp" pInputTextarea id="feeApp" type="text" [(ngModel)]="contractDetail.feeApp"
                    #feeAppT="ngModel" (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="feeApp">Ứng dụng di động</label>
                </span>
            </div>
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkAppFee === true">
                <span class="input-group" style="padding-top: 5px !important;">
                <span>
                    VND/giao dịch
                </span>
                </span>
            </div>
    
            <div [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
            *ngIf="checkDischargeVietQR === true">
            <span class="input-group">
                <textarea rows="1" autoResize="true" *ngIf="contractDetail.feeService !== undefined " class="form-control"
                maxlength="150" name="feeVietQR" pInputTextarea id="feeVietQR" type="text" [(ngModel)]="contractDetail.feeVietQR"
                #feeVietQRT="ngModel" (input)="uncheckManual()"
                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </textarea>
                <label class="label-custom fixPositionLabel" for="feeVietQR">Thanh toán VietQR</label>
            </span>
            </div>
            <div class="form-group col-md-2"
            *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDischargeVietQR === true">
            <span class="input-group" style="padding-top: 5px !important;">
                <span>
                VND/giao dịch
                </span>
            </span>
            </div>
    
            </div>
            <!-- end phí xử lý giao dịch -->
    
            <!-- phí thanh toán -->
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee || checkDischargeVietQR)">
    
            <div class="form-group col-md-12">
                <span class="input-group first-label">
                <span>
                    Phí thanh toán
                </span>
                </span>
            </div>
            </div>
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee)">
            <!-- header -->
            <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                <span class="input-group" style="padding-top: 5px !important;">
                <span style="font-style: italic">
                    Thẻ quốc tế BIN VN
                </span>
                </span>
            </div>
    
            <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayInternationalFee">
                <span class="input-group" style="padding-top: 5px !important;">
                <span style="font-style: italic;">
                    Thẻ quốc tế BIN nước ngoài
                </span>
                </span>
            </div>
    
            <div class="form-group col-md-4"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                <span class="input-group" style="padding-top: 5px !important;">
                <span style="font-style: italic;">
                    Thẻ nội địa
                </span>
                </span>
            </div>
            </div>
            <!-- end header -->
    
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee) ">
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="approveCardType01International"
                    pInputTextarea id="approveCardType01International" type="text"
                    [(ngModel)]="contractDetail.approveCardType01International" (input)="uncheckManual()"
                    #approveCardType01InternationalT="ngModel" maxlength="50"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    style="padding-top: 20px;">
                </textarea>
                <label class="label-custom fixPositionLabel" for="approveCardType01International">{{cardType}}</label>
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee === true && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                <span class="input-group" style="padding-top: 5px !important;">
                % giá trị giao dịch
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee === true && checkPhamViTheQuocTe() && checkDisplayInternationalFee">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                    class="form-control" name="approveCardType02International" pInputTextarea
                    id="approveCardType02International" type="text"
                    [(ngModel)]="contractDetail.approveCardType02International" (input)="uncheckManual()"
                    #approveCardType02InternationalT="ngModel" maxlength="50"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    style="padding-top: 20px;">
                </textarea>
                <label class="label-custom fixPositionLabel" for="approveCardType02International">{{cardType}}</label>
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayInternationalFee">
                <span class="input-group" style="padding-top: 5px !important;">
                % giá trị giao dịch
                </span>
            </div>
    
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="feePaymentDomesticAndApp" pInputTextarea
                    id="feePaymentDomesticAndApp" type="text" maxlength="50"
                    [(ngModel)]="contractDetail.feePaymentDomesticAndApp" (input)="uncheckManual()"
                    #approveCardType02InternationalT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                </span>
            </div>
    
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                <span class="input-group" style="padding-top: 5px !important;">
                % giá trị giao dịch
                </span>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee)">
    
            <!-- phí đặc biệt -->
            <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'
                && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                    class="form-control" name="insideDomestic" pInputTextarea id="insideDomestic" type="text"
                    [(ngModel)]="contractDetail.insideDomestic" #insideDomesticT="ngModel" (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="insideDomestic">Thẻ quốc tế BIN VN</label>
                </span>
            </div>
    
    
            <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'
                && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayInternationalFee">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                    class="form-control" name="outsideDomestic" pInputTextarea id="outsideDomestic" type="text"
                    [(ngModel)]="contractDetail.outsideDomestic" #outsideDomesticT="ngModel" (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="outsideDomestic">Thẻ quốc tế BIN nước
                    ngoài</label>
                </span>
            </div>
    
            <div class="form-group col-md-4"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'">
                <span class="input-group">
                <textarea rows="1" autoResize="true"
                    *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                    class="form-control" name="feePaymentDomesticAndApp" pInputTextarea id="feePaymentDomesticAndApp"
                    type="text" [(ngModel)]="contractDetail.feePaymentDomesticAndApp" #outsideDomesticT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="outsideDomestic">Thẻ nội địa </label>
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkInternationalFee === true
            && checkAmericanExpress === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="americanExpress01International"
                    pInputTextarea id="americanExpress01International" type="text"
                    [(ngModel)]="contractDetail.americanExpress01International" (input)="uncheckManual()"
                    #americanExpress01InternationalT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="americanExpress01International">American
                    Express</label>
                </span>
            </div>
    
            <div class="form-group col-md-2"
                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
            && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                <span class="input-group" style="padding-top: 5px !important;">
                % giá trị giao dịch
                </span>
            </div>
    
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
            && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true">
                <span class="input-group" *ngIf="checkDisplayInternationalFee">
                <textarea rows="1" autoResize="true" class="form-control" name="americanExpress02International"
                    pInputTextarea id="americanExpress02International" type="text"
                    [(ngModel)]="contractDetail.americanExpress02International" (input)="uncheckManual()"
                    #americanExpress02InternationalT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="americanExpress02International">American
                    Express</label>
                </span>
            </div>
            <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
            && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true">
                <span class="input-group" style="padding-top: 5px !important;">
                <span *ngIf="checkDisplayInternationalFee">
                    % giá trị giao dịch
                </span>
                </span>
            </div>
    
            </div>
            <!-- end phí thanh toán -->
    
            <!-- ứng dụng di động -->
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkAppFee">
    
            <div class="form-group col-md-12">
                <span style="font-style: italic">
                Ứng dụng di động
                </span>
            </div>
            <div class="row col-md-12">
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrMobile" pInputTextarea
                    id="percentQrMobile" type="text" [(ngModel)]="contractDetail.percentQrMobile"
                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">Mobile banking</label>
                </span>
                </div>
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrGrab" pInputTextarea
                    id="percentQrGrab" type="text" [(ngModel)]="contractDetail.percentQrGrab" #outsideDomesticT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">GrabPay</label>
                </span>
                </div>
    
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
    
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrShopee" pInputTextarea
                    id="percentQrShopee" type="text" [(ngModel)]="contractDetail.percentQrShopee"
                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">ShopeePay</label>
                </span>
                </div>
    
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
    
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrZalo" pInputTextarea
                    id="percentQrZalo" type="text" [(ngModel)]="contractDetail.percentQrZalo" #outsideDomesticT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">ZaloPay</label>
                </span>
                </div>
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
            </div>
    
            <div class="row col-md-12">
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrMoMo" pInputTextarea
                    id="percentQrMoMo" type="text" [(ngModel)]="contractDetail.percentQrMoMo" #outsideDomesticT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">MoMo</label>
                </span>
                </div>
    
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
    
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentQrOther" pInputTextarea
                    id="percentQrOther" type="text" [(ngModel)]="contractDetail.percentQrOther" #outsideDomesticT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                    <label class="label-custom fixPositionLabel" for="outsideDomestic">Ví và ứng dụng khác</label>
                </span>
                </div>
    
                <div class="form-group col-md-1">
                <span class="input-group" style="padding-top: 5px !important;">
                    <span>
                    %
                    </span>
                </span>
                </div>
            </div>
    
            </div>
    
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkDischargeVietQR">
    
            <div class="form-group col-md-12" *ngIf="checkDischargeVietQR === true">
                <span style="font-style: italic">
                Thanh toán VietQR
                </span>
            </div>
            <div class="row col-md-12" *ngIf="checkDischargeVietQR === true">
                <div class="form-group col-md-2">
                <span class="input-group">
                    <textarea rows="1" autoResize="true" class="form-control" name="percentVietQR" pInputTextarea
                    id="percentVietQR" type="text" [(ngModel)]="contractDetail.percentVietQR"
                    #percentVietQRT="ngModel" (input)="uncheckManual()" placeholder=""
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                    </textarea>
                </span>
                </div>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields"
            *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkInternationalFee && checkAppFee">
    
            <div class="form-group col-md-10">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="inforOther" pInputTextarea
                    id="percentQrOther" type="text" [(ngModel)]="contractDetail.inforOther" #outsideDomesticT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                <label class="label-custom fixPositionLabel" for="outsideDomestic">Thông tin khác</label>
                </span>
            </div>
            </div>
            <!-- end ứng dụng di động -->
    
            <!-- end danhnt -->
            <div id="installment-table-hd14" *ngIf="checkInstallment() === true"
            style="margin-top: 10px;margin-bottom: 10px;">
            <app-child-table-installment [feeBigMerchantList]="feeBigMerchantList" [feeNormalList]="feeNormalList"
                [label]="labelChildTable" [validatePoint]="validatePoint" [checkEditContractDetail]="checkEditContractDetail"
                [checkRefresh]="checkInstallment()" [contractDetail]="contractDetail">
            </app-child-table-installment>
            </div>
    
            <div class="row info-panel-height format-fields" style="margin-top: -15px !important;" *ngIf="checkBnplFee">
            <div class="form-group col-md-12">
                <span class="input-group first-label" style="padding-top: 15px !important;">
                Phí BNPL
                </span>
            </div>
            <div class="col-12">
                <p>Mức phí OnePay thu merchant</p>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeHomeCredit" pInputTextarea
                    id="bnplFeeHomeCredit" type="text" [(ngModel)]="contractDetail.bnplFeeHomeCredit"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                <label class="label-custom fixPositionLabel" for="bnplFee">HomeCredit</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeAmigo" pInputTextarea
                    id="bnplFeeAmigo" type="text" [(ngModel)]="contractDetail.bnplFeeAmigo"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                <label class="label-custom fixPositionLabel" for="bnplFee">Amigo</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeKredivo" pInputTextarea
                    id="bnplFeeKredivo" type="text" [(ngModel)]="contractDetail.bnplFeeKredivo"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                <label class="label-custom fixPositionLabel" for="bnplFee">Kredivo</label>
                </span>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields" style="margin-top: -10px !important;">
            <div class="form-group col-md-12">
                <span class="input-group">
                <span [ngClass]="(this.contractDetail.hinhThucThuPhi === undefined || this.contractDetail.hinhThucThuPhi === null || this.contractDetail.hinhThucThuPhi === '') && validatePoint === true
                        ? 'inValid' : ''" style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                    Hình thức thu phí *
                </span>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi"
                    value="ThuPhiCungButToanBaoCo" inputId="ThuPhiCungButToanBaoCo"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (ngModelChange)="checkOthers()">
                </p-radioButton>
                <label for="ThuPhiCungButToanBaoCo" class="card-label">Thu phí cùng bút toán báo có</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi" value="ThuTheoThang"
                    inputId="ThuTheoThang" (ngModelChange)="checkOthers()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="ThuTheoThang" class="card-label">Thu theo tháng</label>
                </span>
            </div>
            <div class="form-group col-md-1">
                <span class="input-group">
                <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi" value="Khac" inputId="Khac"
                    (ngModelChange)="checkOthers()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="Khac" class="card-label">Khác</label>
                </span>
            </div>
            <div class="form-group col-md-3">
                <span class="input-group"
                *ngIf="contractDetail.hinhThucThuPhi !== undefined && contractDetail.hinhThucThuPhi === 'Khac'"
                style="margin-top: -3px !important;">
                <textarea rows="1" autoResize="true" class="form-control" name="inputHinhThucThuPhiKhac" pInputTextarea
                    id="inputHinhThucThuPhiKhac" type="text" [(ngModel)]="contractDetail.inputHinhThucThuPhiKhac"
                    #inputHinhThucThuPhiKhacT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                </span>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields" style="margin-top: -10px !important;">
            <div class="form-group col-md-12">
                <span class="input-group">
                <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                    Hóa đơn VAT
                </span>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="hoaDonVAT" [(ngModel)]="contractDetail.hoaDonVAT" value="khongLay"
                    inputId="khongLay" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label class="card-label"for="khongLay" >ĐVCNTT là cá nhân không lấy hóa đơn</label>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="hoaDonVAT" [(ngModel)]="contractDetail.hoaDonVAT" value="coLay"
                    inputId="coLay" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label class="card-label" for="coLay" >ĐVCNTT là cá nhân có lấy hóa đơn</label>
                </span>
            </div>
            </div>
    
            <div class="row info-panel-height format-fields">
            <div class="form-group col-md-12">
                <span class="input-group">
                <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                    Khoản đảm bảo khả năng thanh toán
                </span>
                </span>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection" value="Mien"
                    inputId="Mien" (ngModelChange)="checkKyQuy()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="Mien" class="card-label">Miễn</label>
                </span>
            </div>
            <div class="form-group col-md-8">
                <span class="input-group">
                <p-radioButton name="kyQuykeep" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyKeep" inputId="kyQuyKeep"
                    (ngModelChange)="checkKyQuyType()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="kyQuyKeep" class="card-label">Giữ lại</label>
                </span>
            </div>
            <!-- <div class="form-group col-md-4">
                    <span class="input-group">
                        <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection" value="KyQuy"
                            inputId="KyQuy"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-radioButton>
                        <label for="KyQuy" class="card-label">Ký Quỹ</label>
                    </span>
                    </div> -->
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton name="kyQuyType" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyStandard"
                    inputId="kyQuyStandard" (ngModelChange)="checkKyQuyType()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="kyQuyStandard" class="card-label">Ký quỹ chuẩn</label>
                </span>
            </div>
    
            <div class="form-group col-md-1.5" style="margin-left: 12px !important;">
                <span class="input-group">
                <p-radioButton name="kyQuyType" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyKhac" inputId="kyQuyKhac"
                    (ngModelChange)="checkKyQuyType()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="kyQuyKhac" class="card-label">Ký quỹ khác</label>
                </span>
            </div>
            <div class="form-group col-md-3">
                <span class="input-group"
                *ngIf="contractDetail.hinhThucThuPhi !== undefined && contractDetail.kyQuyType === 'kyQuyKhac'"
                style="margin-top: -3px !important;">
                <textarea rows="1" autoResize="true" class="form-control" name="inputKyQuyKhac" pInputTextarea
                    id="inputKyQuyKhac" type="text" [(ngModel)]="contractDetail.inputKyQuyKhac" #inputKyQuyKhacT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                </span>
            </div>
    
            <div class="form-group col-md-3"></div>
    
            <div class="form-group col-md-4"
                *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')"
                style="padding-bottom: 10px !important;">
                <span class="input-group">
                <p-checkbox name="kyQuyAutoFill" value="kyQuyAutoFill" [(ngModel)]="contractDetail.kyQuyAutoFill"
                    inputId="kyQuyAutoFill" #kyQuyAutoFillT="ngModel" (onChange)="autoFillKyQuy()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-checkbox>
                <label for="kyQuyAutoFill" class="card-label">Tự động fill mức ký quỹ chuẩn</label>
                </span>
            </div>
            <div class="form-group col-md-8"
                *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
            </div>
            <div class="form-group col-md-2"
                *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                <span class="input-group">
                <label class="label-custom fixPositionLabel" for="khoanDamBaoInput">Số tiền</label>
                <!-- <textarea rows="1" autoResize="true" class="form-control" name="khoanDamBaoInput" pInputTextarea id="khoanDamBaoInput"
                            type="text" [(ngModel)]="contractDetail.khoanDamBaoInput" #khoanDamBaoInputT="ngModel" *ngIf="!this.checkAutoFill"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea> -->
                <p-inputNumber locale='en-US' name="khoanDamBaoInput" id="khoanDamBaoInput"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    #khoanDamBaoInputT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                    [(ngModel)]="contractDetail.khoanDamBaoInput" (onBlur)="uncheckManual()"></p-inputNumber>
                </span>
            </div>
    
            <div class="form-group col-md-1"
                *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                <span class="input-group" style="padding-top: 5px !important;">
                VND
                </span>
            </div>
            <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'"></div>
    
            <div class="form-group col-md-4" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'">
                <span class="input-group">
                <p-inputNumber name="keepPercent" id="khoanDamBaoInput"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    #keepPercentT="ngModel" [(ngModel)]="contractDetail.keepPercent" (onBlur)="uncheckManual()">
                </p-inputNumber>
                <label class="label-custom fixPositionLabel" for="keepPercent">% giữ lại mỗi giao dịch cho đến
                    khi đủ số tiền ký quỹ</label>
                </span>
            </div>
            <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'">
                <span class="input-group" style="padding-top: 5px !important;">
                %
                </span>
            </div>
    
            <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyStandard'"></div>
            <div class="form-group col-md-2"
                *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="kyHanFD" pInputTextarea id="kyHanFD"
                    type="text" [(ngModel)]="contractDetail.kyHanFD" #kyHanFDT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (input)="uncheckManual()">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="kyHanFD">Kỳ hạn FD</label>
                </span>
            </div>
            <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'"></div>
            </div>
    
            <div class="form-group col-md-2"
            *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
            </div>
    
            <div class="row info-panel-height format-fields"
            *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
            <div class="form-group col-md-2">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="stkGiaiKhoanh" pInputTextarea
                    id="stkGiaiKhoanh" type="text" [(ngModel)]="contractDetail.stkGiaiKhoanh" #stkGiaiKhoanhT="ngModel"
                    (input)="uncheckManual()"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="stkGiaiKhoanh">Số tài khoản giải
                    khoanh</label>
                </span>
            </div>
            <div class="form-group col-md-2"></div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <textarea rows="1" autoResize="true" class="form-control" name="openByBank" pInputTextarea id="openByBank"
                    type="text" [(ngModel)]="contractDetail.openByBank" #openByBankT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (input)="uncheckManual()">
                            </textarea>
                <label class="label-custom fixPositionLabel" for="openByBank">Mở tại Ngân hàng</label>
                </span>
            </div>
            </div>
            <div class="row info-panel-height format-fields"
            *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep' || contractDetail.kyQuyType === 'kyQuyKhac')">
            <div class="form-group col-md-12">
                <b class="input-group">
                STK OnePay nhận ký quỹ mở tại
                </b>
            </div>
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="vietinbank" inputId="vietinbank"
                    (ngModelChange)="checkOthersAccNumber()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="vietinbank" class="card-label">Vietinbank</label>
                </span>
            </div>
    
            <div class="form-group col-md-4">
                <span class="input-group">
                <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="vietcombank" inputId="vietcombank"
                    (ngModelChange)="checkOthersAccNumber()"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                </p-radioButton>
                <label for="vietcombank" class="card-label">Vietcombank</label>
                </span>
            </div>
    
            <div class="form-group col-md-1.5">
                <span class="input-group">
                <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="other" inputId="otherStk"
                    [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                    (ngModelChange)="checkOthersAccNumber()">
                </p-radioButton>
                <label for="otherStk" class="card-label">Khác</label>
                </span>
            </div>
            <div class="form-group col-md-3">
                <span class="input-group"
                *ngIf="contractDetail.accountNumber !== undefined && contractDetail.accountNumber === 'other'"
                style="margin-top: -3px !important;">
                <textarea rows="1" autoResize="true" class="form-control" name="accountNumberOther" pInputTextarea
                    id="accountNumberOther" type="text" [(ngModel)]="contractDetail.accountNumberOther"
                    #inputKyQuyKhacT="ngModel"
                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                </span>
            </div>
            </div>
            </div>
            <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="otherInfo" pInputTextarea id="otherInfo"
                            type="text" [(ngModel)]="contractDetail.otherInfo" #otherInfoT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                        <label class="label-custom fixPositionLabel" for="otherInfo"
                            style="color: #0089D0 !important;font-size: 12px !important;font-weight: bold !important;">Thông
                            tin khác</label>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="parentContractCode === 'HD13'">
            <div *ngIf="isActive('general_contract_information') || isCreateOrEdit">
                <div class="row info-panel-height format-fields" style="margin-top: 10px !important;">

                    <div class="form-group col-md-4" style="margin-top: 5px !important;">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="contractNumber" pInputTextarea
                                id="contractNumber" type="text" [(ngModel)]="contractOriginal.contractNumber"
                                #contractNumberT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractOriginal.contractNumber = contractOriginal.contractNumber.trim()"></textarea>
                            <label class="label-custom fixPositionLabel" for="contractNumber">Số phụ lục</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                                [(ngModel)]="contractOriginal.signatureDate" #signatureDateT="ngModel" name="signatureDate"
                                hideOnDateTimeSelect="true"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="signatureDate">Ngày ký</label>
                        </span>
                    </div>

                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-7">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="businessName" pInputTextarea
                                id="businessName" type="text" [(ngModel)]="contractOriginal.businessName"
                                #businessNameT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractOriginal.businessName = contractOriginal.businessName.trim()"></textarea>

                            <label [ngClass]="(contractOriginal.businessName === undefined || contractOriginal.businessName === '' || contractOriginal.businessName === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="businessName">Tên đăng ký kinh doanh *</label>

                        </span>
                    </div>

                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="shortName" pInputTextarea
                                id="shortName" type="text" [(ngModel)]="contractDetail.shortName" #shortNameT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.shortName = contractDetail.shortName.trim()"></textarea>

                            <label class="label-custom fixPositionLabel" for="shortName">Tên viết tắt</label>
                        </span>
                    </div>
                </div>
                <div class="row info-panel-height format-fields">


                    <div class="form-group col-md-7">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="addressBusiness" pInputTextarea
                                id="addressBusiness" type="text" [(ngModel)]="contractDetail.addressBusiness"
                                #addressBusinessT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.addressBusiness = contractDetail.addressBusiness.trim()"></textarea>

                            <label [ngClass]="(contractDetail.addressBusiness === undefined || contractDetail.addressBusiness === '' || contractDetail.addressBusiness === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="addressBusiness">Địa chỉ *</label>
                        </span>
                    </div>
                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="website" pInputTextarea id="website"
                                type="text" [(ngModel)]="contractDetail.website" #websiteT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.website = contractDetail.website.trim()"></textarea>

                            <label class="label-custom fixPositionLabel" for="website">Website</label>
                        </span>
                    </div>

                </div>
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                          <textarea rows="1" autoResize="true" class="form-control" name="numberBusiness" pInputTextarea
                            id="numberBusiness" type="text" [(ngModel)]="contractDetail.numberBusiness" #numberBusinessT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                          <label [ngClass]="(contractDetail.numberBusiness === undefined || contractDetail.numberBusiness === '' || contractDetail.numberBusiness === null) && validatePoint === true
                                      ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="numberBusiness">Số đăng ký kinh doanh *</label>
                        </span>
                      </div>
              
                      <div class="form-group col-md-3">
                        <span class="input-group" style="margin-top: -3px;">
                          <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                            [(ngModel)]="contractOriginal.rangeDate" #rangeDateT="ngModel" name="rangeDate"
                            hideOnDateTimeSelect="true" [monthNavigator]=true [yearNavigator]="true" yearRange="1900:2050"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                          </p-calendar>
                          <label class="label-custom fixPositionLabel2" for="rangeDate">Ngày cấp</label>
                        </span>
                      </div>
                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea id="phone"
                                type="text" [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.phone = contractDetail.phone.trim()"></textarea>

                            <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                        </span>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-1" style="margin-top: -3px;">
                        <span class="input-group">
                            <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="danhXungList"
                                [(ngModel)]="contractDetail.danhXung" #danhXungT="ngModel" dropdownIcon=" pi pi-sort-down"
                                name="danhXung" id="danhXung" [autoDisplayFirst]="false"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-dropdown>
                            <label [ngClass]="(contractDetail.danhXung === undefined || contractDetail.danhXung === '' || contractDetail.danhXung === null) && validatePoint === true
                                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="danhXung" style="margin-top: -4px !important;">Danh xưng *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="signaturer" pInputTextarea
                                id="signaturer" type="text" [(ngModel)]="contractDetail.signaturer" #signaturerT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.signaturer = contractDetail.signaturer.trim()"></textarea>

                            <label [ngClass]="(contractDetail.signaturer === undefined || contractDetail.signaturer === '' || contractDetail.signaturer === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="signaturer">Người ký *</label>
                        </span>
                    </div>


                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="position" pInputTextarea
                                id="position" type="text" [(ngModel)]="contractDetail.position" #positionT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="contractDetail.position = contractDetail.position.trim()"></textarea>

                            <label [ngClass]="(contractDetail.position === undefined || contractDetail.position === '' || contractDetail.position === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="position">Chức vụ *</label>
                        </span>
                    </div>

                </div>
            </div>
            <div *ngIf="isActive('fee_contract_information') || isCreateOrEdit">
                <div class="format-fields">
                    <span [ngClass]="(this.contractDetail.cardListArray === undefined || this.contractDetail.cardListArray === null || this.contractDetail.cardListArray.length < 1) && validatePoint === true
                                  ? 'inValid' : ''"
                        style="color: #0089D0; margin-top: 20px !important;font-weight: bold;font-size: 12px !important;">
                        Phạm vi hợp tác *
                    </span>
                    <div class="row info-panel-height">
                        <div class="form-group col-md-8" style="margin-top: -10px !important;margin-bottom: 0px !important;">
                            <label class="label-custom fixPositionLabel" for="cardType" style="margin-left: 0px !important;">Thẻ
                                quốc tế</label>
                            <span class="input-group">
                                <div>
                                    <p-checkbox name="Visa" value="Visa" [(ngModel)]="contractDetail.cardListArray" inputId="Visa"
                                        #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="Visa" class="card-label">Visa</label>
                                    <p-checkbox name="MasterCard" value="MasterCard" [(ngModel)]="contractDetail.cardListArray"
                                        inputId="MasterCard" #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="MasterCard" class="card-label">MasterCard</label>
                                    <p-checkbox name="JCB" value="JCB" [(ngModel)]="contractDetail.cardListArray" inputId="JCB"
                                        #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="JCB" class="card-label">JCB</label>
                                    <p-checkbox name="UnionPay" value="UnionPay" [(ngModel)]="contractDetail.cardListArray"
                                        inputId="UnionPay" #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="UnionPay" class="card-label">UnionPay</label>
                                    <p-checkbox name="AmericanExpress" value="AmericanExpress" (onChange)="checkFeeType()"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="AmericanExpress" #cardListT="ngModel"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="AmericanExpress" class="card-label">American Express</label>
                                </div>
            
                                <div style="margin-top: 5px !important;">
                                    <p-checkbox name="ApplePay" value="ApplePay" (onChange)="checkFeeType()"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApplePay" #cardListT="ngModel"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApplePay" class="card-label">Apple Pay</label>
                                    <p-checkbox name="GooglePay" value="GooglePay" (onChange)="checkFeeType()"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="GooglePay" #cardListT="ngModel"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="GooglePay" class="card-label">Google Pay</label>
                                    <p-checkbox name="SamsungPay" value="SamsungPay" (onChange)="checkFeeType()"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="SamsungPay" #cardListT="ngModel"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="SamsungPay" class="card-label">Samsung Pay</label>
                                </div>
                            </span>
                        </div>
            
                        <div class="form-group col-md-4" style="margin-top: -10px !important;margin-bottom: 0px !important;">
                            <label [ngClass]="checkInternationalFee && checkPhamViTheQuocTe() === false && validatePoint === true
                                          ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="cardType" style="margin-left: 0px !important;">Phạm vi chấp nhận của Thẻ quốc tế
                                {{checkInternationalFee ? '*' : ''}}</label>
                            <span class="input-group">
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveDomesticCard" value="ApproveDomesticCard"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveDomesticCard"
                                        #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveDomesticCard" class="card-label">Thẻ quốc tế phát hành
                                        trong
                                        nước</label>
                                </div>
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveInternationalCard" value="ApproveInternationalCard"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveInternationalCard"
                                        #cardListT="ngModel" (onChange)="changeCardType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveInternationalCard" class="card-label">Thẻ quốc tế phát hành
                                        ở
                                        nước ngoài</label>
                                </div>
                            </span>
                        </div>
            
                        <div class="form-group col-md-12" style="margin-top: -5px !important;">
                            <label class="label-custom fixPositionLabel" for="cardType"
                                style="margin-left: 0px !important; margin-top: 10px !important;">Thẻ nội địa</label>
                            <span class="input-group">
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveOnepayDomesticCard" value="ApproveOnepayDomesticCard"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveOnepayDomesticCard"
                                        #cardListT="ngModel" (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveOnepayDomesticCard" class="card-label">Thẻ nội địa được ONEPAY chấp
                                        nhận
                                        thanh toán</label>
                                </div>
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveSamsungPay" value="ApproveSamsungPay"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveSamsungPay" #cardListT="ngModel"
                                        (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveSamsungPay" class="card-label">Samsung Pay</label>
                                </div>
            
                                <br>
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveOnepayMobileApp" value="ApproveOnepayMobileApp"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveOnepayMobileApp"
                                        #cardListT="ngModel" (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveOnepayMobileApp" class="card-label">Ứng dụng di động được ONEPAY chấp
                                        nhận thanh toán</label>
                                </div>
            
                                <div class="combobox-label">
                                    <p-checkbox name="dischargeVietQR" value="ApproveDischargeVietQR"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveDischargeVietQR"
                                        #cardListT="ngModel" (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveDischargeVietQR" class="card-label">Thanh toán VietQR</label>
                                </div>
            
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveInstallment" value="ApproveInstallment"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveInstallment" #cardListT="ngModel"
                                        (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveInstallment" class="card-label">Chấp nhận triển khai dịch vụ thanh
                                        toán
                                        trả góp</label>
                                </div>
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveBNPL" value="ApproveBNPL" [(ngModel)]="contractDetail.cardListArray"
                                        inputId="ApproveBNPL" #cardListT="ngModel" (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveBNPL" class="card-label">Chấp nhận triển khai dịch vụ mua trước
                                        trả sau</label>
                                </div>
                                <div class="combobox-label">
                                    <p-checkbox name="ApproveShopify" value="ApproveShopify"
                                        [(ngModel)]="contractDetail.cardListArray" inputId="ApproveShopify" #cardListT="ngModel"
                                        (onChange)="checkFeeType()"
                                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </p-checkbox>
                                    <label for="ApproveShopify" class="card-label">Thanh toán trên nền tảng Shopify</label>
                                </div>
                            </span>
                        </div>
                    </div>
                </div>
            
                <!-- Child Table -->
            
                <div id="table-hd13" style="margin-bottom: 5px !important;margin-top: -10px !important;"
                    class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <app-child-table-merchant-id-v3 [tableData]="contractDetail.subTableMerchant"
                            [validatePoint]="validatePoint" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteMerchant()" (changeData)="autoFillKyQuy()">
                        </app-child-table-merchant-id-v3>
                    </div>
                </div>
            
                <div class="format-fields">
                    <span [ngClass]="(this.contractDetail.tgTamUngSelection === undefined || this.contractDetail.tgTamUngSelection === null || this.contractDetail.tgTamUngSelection === '') && validatePoint === true
                                  ? 'inValid' : ''" style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                        Thời gian tạm ứng *
                    </span>
                </div>
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="t1"
                                inputId="t1" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="checkOthers()">
                            </p-radioButton>
                            <label for="t1" class="card-label">T+1</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="t2"
                                inputId="t2" [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="checkOthers()">
                            </p-radioButton>
                            <label for="t2" class="card-label">T+2</label>
                        </span>
                    </div>
                    <div class="form-group col-md-1">
                        <span class="input-group">
                            <p-radioButton name="tgTamUngSelection" [(ngModel)]="contractDetail.tgTamUngSelection" value="other"
                                inputId="other" (ngModelChange)="checkOthers()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="other" class="card-label">Khác</label>
                        </span>
                    </div>
                    <div class="form-group col-md-3">
                        <span class="input-group"
                            *ngIf="contractDetail.tgTamUngSelection !== undefined && contractDetail.tgTamUngSelection === 'other'"
                            style="margin-top: -3px !important;">
                            <textarea rows="1" autoResize="true" class="form-control" name="inputTgTamUngKhac" pInputTextarea
                                id="inputTgTamUngKhac" type="text" [(ngModel)]="contractDetail.inputTgTamUngKhac"
                                #inputTgTamUngKhacT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                        </span>
                    </div>
                </div>
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                              && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true  || checkAppFee === true || checkDischargeVietQR === true)">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                                Phí dịch vụ (đã bao gồm VAT)
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="feeService" [(ngModel)]="contractDetail.feeService" value="normalFee"
                                inputId="normalFee"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (click)="changeFeeType('normalFee')">
                            </p-radioButton>
                            <label for="normalFee" class="card-label">Phí thông thường</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="feeService" [(ngModel)]="contractDetail.feeService" value="specialFee"
                                inputId="specialFee" (click)="changeFeeType('specialFee')"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="specialFee" class="card-label">Phí đặc biệt</label>
                        </span>
                    </div>
                    <div class="form-group col-md-12" style="padding-top: 0px !important;"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'">
                        <span class="input-group">
                            <p-checkbox name="contractDetail.autoFillStandardFee" value="autoFillStandardFee"
                                [(ngModel)]="contractDetail.autoFillStandardFee" inputId="autoFill" #autoFillT="ngModel"
                                (onChange)="changeFeeType('autoFill')" inputId="autoFillStandardFee"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-checkbox>
                            <label for="autoFillStandardFee" class="card-label">Tự động fill mức phí chuẩn</label>
                        </span>
                    </div>
                </div>
            
                <div class="row info-panel-height format-fields" style="margin-top: -15px !important;"
                    *ngIf="contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee')
                              && (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0)">
                    <div class="form-group col-md-4" style="margin-bottom: -5px !important;"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group first-label" style="padding-top: 15px !important;">
                            <span>
                                Phí đăng ký dịch vụ
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4" [class.col-md-8]="checkShopifyFee === false"
                        style="margin-bottom: -5px !important;"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group first-label" style="padding-top: 15px !important;">
                            <span>
                                Phí hàng tháng
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4" style="margin-bottom: -5px !important;" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                        <span class="input-group first-label" style="padding-top: 15px !important;">
                            <span>
                                Phí Shopify
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                                class="form-control" name="registerFee" pInputTextarea id="registerFee" type="text"
                                [(ngModel)]="contractDetail.registerFee" #registerFeeT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <p-inputNumber locale='en-US' name="registerFee" id="registerFee"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #registerFeeT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.registerFee" (onBlur)="uncheckManual()">
                            </p-inputNumber>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span>
                                VND
                            </span>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="monthFee" pInputTextarea id="monthFee"
                                type="text" [(ngModel)]="contractDetail.monthFee" #monthFeeT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                              </textarea>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && (checkInternationalFee === true || checkDomesticFee === true)">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <!-- <span>
                                                  VND
                                              </span> -->
                        </span>
                    </div>
            
                    <div class="form-group col-md-2" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="shopifyFee" pInputTextarea
                                id="shopifyFee" type="text" [(ngModel)]="contractDetail.shopifyFee" #shopifyFeeT="ngModel"
                                [readOnly]="true">
                                              </textarea>
                            <!-- <p-inputNumber locale='en-US' name="shopifyFee" id="shopifyFee"
                                                  *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                                  [disabled]="true"
                                                  #shopifyFeeT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                                  [(ngModel)]="contractDetail.shopifyFee">
                                              </p-inputNumber> -->
                        </span>
                    </div>
                    <div class="form-group col-md-2" *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0 && checkShopifyFee === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <!-- <span>
                                                  VND
                                              </span> -->
                        </span>
                    </div>
                </div>
            
                <!-- start danhnt -->
                <!-- phí xử lý giao dịch -->
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee === true || checkDomesticFee === true  || checkAppFee === true || checkDischargeVietQR === true)">
                    <div class="form-group col-md-12">
                        <span class="input-group first-label">
                            <span>
                                Phí xử lý giao dịch
                            </span>
                        </span>
                    </div>
                    <div [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                        *ngIf="checkInternationalFee === true">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                                class="form-control" name="feeTransInternational" pInputTextarea id="feeTransInternational"
                                type="text" maxlength="100" [(ngModel)]="contractDetail.feeTransInternational"
                                #feeTransInternationalT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <p-inputNumber locale='en-US' name="feeTransInternational" id="feeTransInternational"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #feeTransInternationalT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.feeTransInternational" (onBlur)="uncheckManual()">
                            </p-inputNumber>
                            <label class="label-custom fixPositionLabel" for="feeTransInternational">Thẻ quốc tế</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkInternationalFee === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span>
                                VND/giao dịch
                            </span>
                        </span>
                    </div>
            
                    <div [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                        *ngIf="checkDomesticFee === true">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                                class="form-control" name="feeTransDomesticAndApp" pInputTextarea id="feeTransDomesticAndApp"
                                type="text" maxlength="100" [(ngModel)]="contractDetail.feeTransDomesticAndApp"
                                #feeTransDomesticT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <p-inputNumber locale='en-US' name="feeTransDomesticAndApp" id="feeTransDomesticAndApp"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #feeTransDomesticAndAppT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.feeTransDomesticAndApp" (onBlur)="uncheckManual()">
                            </p-inputNumber>
                            <label class="label-custom fixPositionLabel" for="feeTransDomesticAndApp">Thẻ nội địa</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticFee === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span>
                                VND/giao dịch
                            </span>
                        </span>
                    </div>
            
                    <div [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                        *ngIf="checkAppFee === true">
                        <span class="input-group">
                            <!-- <textarea rows="1" autoResize="true"
                                                  *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService !== 'normalFee'"
                                                  class="form-control" maxlength="100" name="feeApp" pInputTextarea id="feeApp" type="text"
                                                  [(ngModel)]="contractDetail.feeApp" #feeAppT="ngModel"
                                                  [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                              <p-inputNumber locale='en-US' name="feeApp" id="feeApp"
                                                  *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                                  [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                                  #feeTransDomesticAndAppT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                                  [(ngModel)]="contractDetail.feeApp"></p-inputNumber>
                                              <label class="label-custom fixPositionLabel" for="feeApp">Ứng dụng di động</label> -->
                            <textarea rows="1" autoResize="true" *ngIf="contractDetail.feeService !== undefined "
                                class="form-control" maxlength="150" name="feeApp" pInputTextarea id="feeApp" type="text"
                                [(ngModel)]="contractDetail.feeApp" #feeAppT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="feeApp">Ứng dụng di động</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkAppFee === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span>
                                VND/giao dịch
                            </span>
                        </span>
                    </div>
            
                    <div [ngClass]="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' ? 'form-group col-md-2' : 'form-group col-md-4'"
                        *ngIf="checkDischargeVietQR === true">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" *ngIf="contractDetail.feeService !== undefined "
                                class="form-control" maxlength="150" name="feeVietQR" pInputTextarea id="feeVietQR" type="text"
                                [(ngModel)]="contractDetail.feeVietQR" #feeVietQRT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                </textarea>
                            <label class="label-custom fixPositionLabel" for="feeVietQR">Thanh toán VietQR</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDischargeVietQR === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span>
                                VND/giao dịch
                            </span>
                        </span>
                    </div>
            
                </div>
                <!-- end phí xử lý giao dịch -->
            
                <!-- phí thanh toán -->
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee || checkDischargeVietQR)">
            
                    <div class="form-group col-md-12">
                        <span class="input-group first-label">
                            <span>
                                Phí thanh toán
                            </span>
                        </span>
                    </div>
                </div>
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee)">
                    <!-- header -->
                    <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                  && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span style="font-style: italic">
                                Thẻ quốc tế BIN VN
                            </span>
                        </span>
                    </div>
            
                    <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                  && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayInternationalFee">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span style="font-style: italic;">
                                Thẻ quốc tế BIN nước ngoài
                            </span>
                        </span>
                    </div>
            
                    <div class="form-group col-md-4"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span style="font-style: italic;">
                                Thẻ nội địa
                            </span>
                        </span>
                    </div>
                </div>
                <!-- end header -->
            
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee) ">
            
                    <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                  && checkInternationalFee && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="approveCardType01International"
                                pInputTextarea id="approveCardType01International" type="text"
                                [(ngModel)]="contractDetail.approveCardType01International" (input)="uncheckManual()"
                                #approveCardType01InternationalT="ngModel" maxlength="50"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                style="padding-top: 20px;">
                                  </textarea>
                            <label class="label-custom fixPositionLabel" for="approveCardType01International">{{cardType}}</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                  && checkInternationalFee === true && checkPhamViTheQuocTe() && checkDisplayDomesticFee">
                        <span class="input-group" style="padding-top: 5px !important;">
                            % giá trị giao dịch
                        </span>
                    </div>
            
                    <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                  && checkInternationalFee === true && checkPhamViTheQuocTe() && checkDisplayInternationalFee">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'"
                                class="form-control" name="approveCardType02International" pInputTextarea
                                id="approveCardType02International" type="text"
                                [(ngModel)]="contractDetail.approveCardType02International" (input)="uncheckManual()"
                                #approveCardType02InternationalT="ngModel" maxlength="50"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                style="padding-top: 20px;">
                                  </textarea>
                            <label class="label-custom fixPositionLabel" for="approveCardType02International">{{cardType}}</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                                   && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayInternationalFee">
                        <span class="input-group" style="padding-top: 5px !important;">
                            % giá trị giao dịch
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="feePaymentDomesticAndApp" pInputTextarea
                                id="feePaymentDomesticAndApp" type="text" maxlength="50"
                                [(ngModel)]="contractDetail.feePaymentDomesticAndApp" (input)="uncheckManual()"
                                #approveCardType02InternationalT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkDomesticPaymentFee()">
                        <span class="input-group" style="padding-top: 5px !important;">
                            % giá trị giao dịch
                        </span>
                    </div>
                </div>
            
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && (checkInternationalFee || checkDomesticFee || checkAppFee)">
            
                    <!-- phí đặc biệt -->
                    <div class="form-group col-md-4" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'
                                  && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                                class="form-control" name="insideDomestic" pInputTextarea id="insideDomestic" type="text"
                                [(ngModel)]="contractDetail.insideDomestic" #insideDomesticT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="insideDomestic">Thẻ quốc tế BIN VN</label>
                        </span>
                    </div>
            
            
                    <div class="form-group col-md-4"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'
                                  && checkInternationalFee === true && checkPhamViTheQuocTe() === true && checkDisplayInternationalFee">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                                class="form-control" name="outsideDomestic" pInputTextarea id="outsideDomestic" type="text"
                                [(ngModel)]="contractDetail.outsideDomestic" #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="outsideDomestic">Thẻ quốc tế BIN nước
                                ngoài</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-4"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true"
                                *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'specialFee'"
                                class="form-control" name="feePaymentDomesticAndApp" pInputTextarea id="feePaymentDomesticAndApp"
                                type="text" [(ngModel)]="contractDetail.feePaymentDomesticAndApp" #outsideDomesticT="ngModel"
                                (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="outsideDomestic">Thẻ nội địa </label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2" *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee' && checkInternationalFee === true
                              && checkAmericanExpress === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="americanExpress01International"
                                pInputTextarea id="americanExpress01International" type="text"
                                [(ngModel)]="contractDetail.americanExpress01International" (input)="uncheckManual()"
                                #americanExpress01InternationalT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="americanExpress01International">American
                                Express</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                              && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true && checkDisplayDomesticFee">
                        <span class="input-group" style="padding-top: 5px !important;">
                            % giá trị giao dịch
                        </span>
                    </div>
            
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                              && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true">
                        <span class="input-group" *ngIf="checkDisplayInternationalFee">
                            <textarea rows="1" autoResize="true" class="form-control" name="americanExpress02International"
                                pInputTextarea id="americanExpress02International" type="text"
                                [(ngModel)]="contractDetail.americanExpress02International" (input)="uncheckManual()"
                                #americanExpress02InternationalT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="americanExpress02International">American
                                Express</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.feeService !== undefined && contractDetail.feeService === 'normalFee'
                              && checkInternationalFee === true && checkAmericanExpress === true && checkPhamViTheQuocTe() === true">
                        <span class="input-group" style="padding-top: 5px !important;">
                            <span *ngIf="checkDisplayInternationalFee">
                                % giá trị giao dịch
                            </span>
                        </span>
                    </div>
            
                </div>
                <!-- end phí thanh toán -->
            
                <!-- ứng dụng di động -->
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkAppFee">
            
                    <div class="form-group col-md-12">
                        <span style="font-style: italic">
                            Ứng dụng di động
                        </span>
                    </div>
                    <div class="row col-md-12">
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrMobile" pInputTextarea
                                    id="percentQrMobile" type="text" [(ngModel)]="contractDetail.percentQrMobile"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">Mobile banking</label>
                            </span>
                        </div>
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrGrab" pInputTextarea
                                    id="percentQrGrab" type="text" [(ngModel)]="contractDetail.percentQrGrab"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">GrabPay</label>
                            </span>
                        </div>
            
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
            
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrShopee" pInputTextarea
                                    id="percentQrShopee" type="text" [(ngModel)]="contractDetail.percentQrShopee"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">ShopeePay</label>
                            </span>
                        </div>
            
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
            
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrZalo" pInputTextarea
                                    id="percentQrZalo" type="text" [(ngModel)]="contractDetail.percentQrZalo"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">ZaloPay</label>
                            </span>
                        </div>
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
                    </div>
            
                    <div class="row col-md-12">
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrMoMo" pInputTextarea
                                    id="percentQrMoMo" type="text" [(ngModel)]="contractDetail.percentQrMoMo"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">MoMo</label>
                            </span>
                        </div>
            
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
            
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentQrOther" pInputTextarea
                                    id="percentQrOther" type="text" [(ngModel)]="contractDetail.percentQrOther"
                                    #outsideDomesticT="ngModel" (input)="uncheckManual()"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                                <label class="label-custom fixPositionLabel" for="outsideDomestic">Ví và ứng dụng khác</label>
                            </span>
                        </div>
            
                        <div class="form-group col-md-1">
                            <span class="input-group" style="padding-top: 5px !important;">
                                <span>
                                    %
                                </span>
                            </span>
                        </div>
                    </div>
            
                </div>
            
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkDischargeVietQR">
            
                    <div class="form-group col-md-12" *ngIf="checkDischargeVietQR === true">
                        <span style="font-style: italic">
                            Thanh toán VietQR
                        </span>
                    </div>
                    <div class="row col-md-12" *ngIf="checkDischargeVietQR === true">
                        <div class="form-group col-md-2">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="percentVietQR" pInputTextarea
                                    id="percentVietQR" type="text" [(ngModel)]="contractDetail.percentVietQR"
                                    #percentVietQRT="ngModel" (input)="uncheckManual()" placeholder=""
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </textarea>
                            </span>
                        </div>
                    </div>
                </div>
            
                <div class="row info-panel-height format-fields"
                    *ngIf="this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null
                                  && this.contractDetail.cardListArray.length > 0  && contractDetail.feeService !== undefined && (contractDetail.feeService === 'normalFee' || contractDetail.feeService === 'specialFee') && checkInternationalFee && checkAppFee">
            
                    <div class="form-group col-md-10">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="inforOther" pInputTextarea
                                id="percentQrOther" type="text" [(ngModel)]="contractDetail.inforOther" #outsideDomesticT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="outsideDomestic">Thông tin khác</label>
                        </span>
                    </div>
                </div>
                <!-- end ứng dụng di động -->
            
                <!-- end danhnt -->
                <div id="installment-table-hd13" *ngIf="checkInstallment() === true" style="margin-top: 10px;margin-bottom: 10px;">
                    <app-child-table-installment [feeBigMerchantList]="feeBigMerchantList" [feeNormalList]="feeNormalList"
                        [label]="labelChildTable" [validatePoint]="validatePoint"
                        [checkEditContractDetail]="checkEditContractDetail" [checkRefresh]="checkInstallment()"
                        [contractDetail]="contractDetail">
                    </app-child-table-installment>
                </div>
            
                <div class="row info-panel-height format-fields" style="margin-top: -15px !important;" *ngIf="checkBnplFee">
                    <div class="form-group col-md-12">
                        <span class="input-group first-label" style="padding-top: 15px !important;">
                            Phí BNPL
                        </span>
                    </div>
                    <div class="col-12">
                        <p>Mức phí OnePay thu merchant</p>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeHomeCredit" pInputTextarea
                                id="bnplFeeHomeCredit" type="text" [(ngModel)]="contractDetail.bnplFeeHomeCredit"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="bnplFee">HomeCredit</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeAmigo" pInputTextarea
                                id="bnplFeeAmigo" type="text" [(ngModel)]="contractDetail.bnplFeeAmigo"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="bnplFee">Amigo</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="bnplFeeKredivo" pInputTextarea
                                id="bnplFeeKredivo" type="text" [(ngModel)]="contractDetail.bnplFeeKredivo"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="bnplFee">Kredivo</label>
                        </span>
                    </div>
                </div>
            
            
            
                <div class="row info-panel-height format-fields" style="margin-top: -10px !important;">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <span [ngClass]="(this.contractDetail.hinhThucThuPhi === undefined || this.contractDetail.hinhThucThuPhi === null || this.contractDetail.hinhThucThuPhi === '') && validatePoint === true
                                          ? 'inValid' : ''" style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                                Hình thức thu phí *
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi"
                                value="ThuPhiCungButToanBaoCo" inputId="ThuPhiCungButToanBaoCo"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="checkOthers()">
                            </p-radioButton>
                            <label for="ThuPhiCungButToanBaoCo" class="card-label">Thu phí cùng bút toán báo có</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi" value="ThuTheoThang"
                                inputId="ThuTheoThang" (ngModelChange)="checkOthers()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="ThuTheoThang" class="card-label">Thu theo tháng</label>
                        </span>
                    </div>
                    <div class="form-group col-md-1">
                        <span class="input-group">
                            <p-radioButton name="hinhThucThuPhi" [(ngModel)]="contractDetail.hinhThucThuPhi" value="Khac"
                                inputId="Khac" (ngModelChange)="checkOthers()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="Khac" class="card-label">Khác</label>
                        </span>
                    </div>
                    <div class="form-group col-md-3">
                        <span class="input-group"
                            *ngIf="contractDetail.hinhThucThuPhi !== undefined && contractDetail.hinhThucThuPhi === 'Khac'"
                            style="margin-top: -3px !important;">
                            <textarea rows="1" autoResize="true" class="form-control" name="inputHinhThucThuPhiKhac" pInputTextarea
                                id="inputHinhThucThuPhiKhac" type="text" [(ngModel)]="contractDetail.inputHinhThucThuPhiKhac"
                                #inputHinhThucThuPhiKhacT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                        </span>
                    </div>
                </div>
            
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                                Khoản đảm bảo khả năng thanh toán
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection"
                                value="Mien" inputId="Mien" (ngModelChange)="checkKyQuy()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="Mien" class="card-label">Miễn</label>
                        </span>
                    </div>
                    <div class="form-group col-md-8">
                        <span class="input-group">
                            <p-radioButton name="kyQuykeep" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyKeep"
                                inputId="kyQuyKeep" (ngModelChange)="checkKyQuyType()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="kyQuyKeep" class="card-label">Giữ lại</label>
                        </span>
                    </div>
                    <!-- <div class="form-group col-md-4">
                                      <span class="input-group">
                                          <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection" value="KyQuy"
                                              inputId="KyQuy"
                                              [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                          </p-radioButton>
                                          <label for="KyQuy" class="card-label">Ký Quỹ</label>
                                      </span>
                                      </div> -->
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="kyQuyType" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyStandard"
                                inputId="kyQuyStandard" (ngModelChange)="checkKyQuyType()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="kyQuyStandard" class="card-label">Ký quỹ chuẩn</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-1.5" style="margin-left: 12px !important;">
                        <span class="input-group">
                            <p-radioButton name="kyQuyType" [(ngModel)]="contractDetail.kyQuyType" value="kyQuyKhac"
                                inputId="kyQuyKhac" (ngModelChange)="checkKyQuyType()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="kyQuyKhac" class="card-label">Ký quỹ khác</label>
                        </span>
                    </div>
                    <div class="form-group col-md-3">
                        <span class="input-group"
                            *ngIf="contractDetail.hinhThucThuPhi !== undefined && contractDetail.kyQuyType === 'kyQuyKhac'"
                            style="margin-top: -3px !important;">
                            <textarea rows="1" autoResize="true" class="form-control" name="inputKyQuyKhac" pInputTextarea
                                id="inputKyQuyKhac" type="text" [(ngModel)]="contractDetail.inputKyQuyKhac"
                                #inputKyQuyKhacT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                        </span>
                    </div>
            
                    <div class="form-group col-md-3"></div>
            
                    <div class="form-group col-md-4"
                        *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')"
                        style="padding-bottom: 10px !important;">
                        <span class="input-group">
                            <p-checkbox name="kyQuyAutoFill" value="kyQuyAutoFill" [(ngModel)]="contractDetail.kyQuyAutoFill"
                                inputId="kyQuyAutoFill" #kyQuyAutoFillT="ngModel" (onChange)="autoFillKyQuy()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-checkbox>
                            <label for="kyQuyAutoFill" class="card-label">Tự động fill mức ký quỹ chuẩn</label>
                        </span>
                    </div>
                    <div class="form-group col-md-8"
                        *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                    </div>
                    <div class="form-group col-md-2"
                        *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                        <span class="input-group">
                            <label class="label-custom fixPositionLabel" for="khoanDamBaoInput">Số tiền</label>
                            <!-- <textarea rows="1" autoResize="true" class="form-control" name="khoanDamBaoInput" pInputTextarea id="khoanDamBaoInput"
                                              type="text" [(ngModel)]="contractDetail.khoanDamBaoInput" #khoanDamBaoInputT="ngModel" *ngIf="!this.checkAutoFill"
                                              [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea> -->
                            <p-inputNumber locale='en-US' name="khoanDamBaoInput" id="khoanDamBaoInput"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #khoanDamBaoInputT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.khoanDamBaoInput" (onBlur)="uncheckManual()"></p-inputNumber>
                        </span>
                    </div>
            
                    <div class="form-group col-md-1"
                        *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                        <span class="input-group" style="padding-top: 5px !important;">
                            VND
                        </span>
                    </div>
                    <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'"></div>
            
                    <div class="form-group col-md-4" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'">
                        <span class="input-group">
                            <p-inputNumber name="keepPercent" id="khoanDamBaoInput"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #keepPercentT="ngModel" [(ngModel)]="contractDetail.keepPercent" (onBlur)="uncheckManual()">
                            </p-inputNumber>
                            <label class="label-custom fixPositionLabel" for="keepPercent">% giữ lại mỗi giao dịch cho đến
                                khi đủ số tiền ký quỹ</label>
                        </span>
                    </div>
                    <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'">
                        <span class="input-group" style="padding-top: 5px !important;">
                            %
                        </span>
                    </div>
            
                    <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyStandard'"></div>
                    <div class="form-group col-md-2"
                        *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="kyHanFD" pInputTextarea id="kyHanFD"
                                type="text" [(ngModel)]="contractDetail.kyHanFD" #kyHanFDT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="kyHanFD">Kỳ hạn FD</label>
                        </span>
                    </div>
                    <div class="form-group col-md-1" *ngIf="contractDetail.kyQuyType === 'kyQuyKeep'"></div>
                </div>
            
                <div class="form-group col-md-2"
                    *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                </div>
            
                <div class="row info-panel-height format-fields"
                    *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep')">
                    <div class="form-group col-md-2">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="stkGiaiKhoanh" pInputTextarea
                                id="stkGiaiKhoanh" type="text" [(ngModel)]="contractDetail.stkGiaiKhoanh" #stkGiaiKhoanhT="ngModel"
                                (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="stkGiaiKhoanh">Số tài khoản giải
                                khoanh</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"></div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="openByBank" pInputTextarea
                                id="openByBank" type="text" [(ngModel)]="contractDetail.openByBank" #openByBankT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                              </textarea>
                            <label class="label-custom fixPositionLabel" for="openByBank">Mở tại Ngân hàng</label>
                        </span>
                    </div>
                </div>
                <div class="row info-panel-height format-fields"
                    *ngIf="(contractDetail.kyQuyType === 'kyQuyStandard' || contractDetail.kyQuyType === 'kyQuyKeep' || contractDetail.kyQuyType === 'kyQuyKhac')">
                    <div class="form-group col-md-12">
                        <b class="input-group">
                            STK OnePay nhận ký quỹ mở tại
                        </b>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="vietinbank" inputId="vietinbank"
                                (ngModelChange)="checkOthersAccNumber()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="vietinbank" class="card-label">Vietinbank</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="vietcombank" inputId="vietcombank"
                                (ngModelChange)="checkOthersAccNumber()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="vietcombank" class="card-label">Vietcombank</label>
                        </span>
                    </div>
            
                    <div class="form-group col-md-1.5">
                        <span class="input-group">
                            <p-radioButton [(ngModel)]="contractDetail.accountNumber" value="other" inputId="otherStk"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (ngModelChange)="checkOthersAccNumber()">
                            </p-radioButton>
                            <label for="otherStk" class="card-label">Khác</label>
                        </span>
                    </div>
                    <div class="form-group col-md-3">
                        <span class="input-group"
                            *ngIf="contractDetail.accountNumber !== undefined && contractDetail.accountNumber === 'other'"
                            style="margin-top: -3px !important;">
                            <textarea rows="1" autoResize="true" class="form-control" name="accountNumberOther" pInputTextarea
                                id="accountNumberOther" type="text" [(ngModel)]="contractDetail.accountNumberOther"
                                #inputKyQuyKhacT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                        </span>
                    </div>
                </div>
            </div>
            <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="otherInfo" pInputTextarea id="otherInfo"
                                type="text" [(ngModel)]="contractDetail.otherInfo" #otherInfoT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="otherInfo"
                                style="color: #0089D0 !important;font-size: 12px !important;font-weight: bold !important;">Thông
                                tin khác</label>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="parentContractCode !== 'HD14' && parentContractCode !== 'HD13'">
            <div *ngIf="isActive('general_contract_information') || isCreateOrEdit">
                <div class="row info-panel-height format-fields" style="margin-top: 10px !important;">

                    <div class="form-group col-md-3" style="margin-top: 5px !important;">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="contractNumber" pInputTextarea
                                id="contractNumber" type="text" [(ngModel)]="contractOriginal.contractNumber"
                                #contractNumberT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label class="label-custom fixPositionLabel" for="contractNumber">Số phụ lục</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                                [(ngModel)]="contractOriginal.signatureDate" #signatureDateT="ngModel" name="signatureDate"
                                hideOnDateTimeSelect="true"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="signatureDate">Ngày ký</label>
                        </span>
                    </div>

                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="businessName" pInputTextarea
                                id="businessName" type="text" [(ngModel)]="contractOriginal.businessName"
                                #businessNameT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractOriginal.businessName === undefined || contractOriginal.businessName === '' || contractOriginal.businessName === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="businessName">Tên đăng ký kinh doanh *</label>

                        </span>
                    </div>

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="shortName" pInputTextarea
                                id="shortName" type="text" [(ngModel)]="contractDetail.shortName" #shortNameT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="shortName">Tên viết tắt</label>
                        </span>
                    </div>

                </div>
                <div class="row info-panel-height format-fields">


                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="addressBusiness" pInputTextarea
                                id="addressBusiness" type="text" [(ngModel)]="contractDetail.addressBusiness"
                                #addressBusinessT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.addressBusiness === undefined || contractDetail.addressBusiness === '' || contractDetail.addressBusiness === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="addressBusiness">Địa chỉ *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea id="phone"
                                type="text" [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="website" pInputTextarea id="website"
                                type="text" [(ngModel)]="contractDetail.website" #websiteT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="website">Website</label>
                        </span>
                    </div>

                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="signaturer" pInputTextarea
                                id="signaturer" type="text" [(ngModel)]="contractDetail.signaturer" #signaturerT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.signaturer === undefined || contractDetail.signaturer === '' || contractDetail.signaturer === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="signaturer">Người ký *</label>
                        </span>
                    </div>


                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="position" pInputTextarea
                                id="position" type="text" [(ngModel)]="contractDetail.position" #positionT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.position === undefined || contractDetail.position === '' || contractDetail.position === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="position">Chức vụ *</label>
                        </span>
                    </div>

                </div>

                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="cardType" pInputTextarea
                                id="cardType" type="text" [(ngModel)]="contractDetail.cardType" #cardTypeT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.cardType === undefined || contractDetail.cardType === '' || contractDetail.cardType === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="cardType">Loại
                                thẻ được chấp nhận thanh toán *</label>
                        </span>
                    </div>

                </div>
            </div>
            <div *ngIf="isActive('fee_contract_information') || isCreateOrEdit">
                <div class="format-fields">
                    <span style="color: #0089D0; margin-top: 20px !important;font-weight: bold;font-size: 12px !important;">
                        Phí dịch vụ (đã bao gồm VAT)
                    </span>
                    <div class="row info-panel-height" style="margin-top: 6px !important;">
                        <div class="form-group col-md-12">
                            <span class="input-group">
                                <textarea rows="1" autoResize="true" class="form-control" name="monthFee" pInputTextarea
                                    id="monthFee" type="text" [(ngModel)]="contractDetail.monthFee" #monthFeeT="ngModel"
                                    [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                                <label [ngClass]="(contractDetail.monthFee === undefined || contractDetail.monthFee === '' || contractDetail.monthFee === null) && validatePoint === true
                                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                    for="monthFee">Phí
                                    dịch vụ hàng tháng *</label>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="cardTransactionFee" pInputTextarea
                                id="cardTransactionFee" type="text" [(ngModel)]="contractDetail.cardTransactionFee"
                                #cardTransactionFeeT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.cardTransactionFee === undefined || contractDetail.cardTransactionFee === '' || contractDetail.cardTransactionFee === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="cardTransactionFee">Phí xử lý giao dịch thẻ
                                *</label>
                        </span>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="feeForCard" pInputTextarea
                                id="feeForCard" type="text" [(ngModel)]="contractDetail.feeForCard" #feeForCardT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.feeForCard === undefined || contractDetail.feeForCard === '' || contractDetail.feeForCard === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="feeForCard">Phí
                                thanh toán thẻ (% giá trị thanh
                                toán)
                                *</label>
                        </span>
                    </div>
                </div>
            </div>
            <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
                <!-- Child Table -->


                <div id="table-hd10" style="margin-bottom: 15px !important;margin-top: -10px !important;"
                    class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <app-child-table-merchant [tableData]="contractDetail.subTableMerchant" [typeContract]="'BAOCO'"
                            [validatePoint]="validatePoint" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteMerchant()">
                        </app-child-table-merchant>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="khoanDamBao" pInputTextarea
                                id="khoanDamBao" type="text" [(ngModel)]="contractDetail.khoanDamBao" #khoanDamBaoT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="khoanDamBao">Đảm bảo thanh toán </label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="stkGiaiKhoanh" pInputTextarea
                                id="stkGiaiKhoanh" type="text" [(ngModel)]="contractDetail.stkGiaiKhoanh"
                                #stkGiaiKhoanhT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="stkGiaiKhoanh">Tài khoản khoanh
                                giữ</label>
                        </span>
                    </div>

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="kyHanFD" pInputTextarea id="kyHanFD"
                                type="text" [(ngModel)]="contractDetail.kyHanFD" #kyHanFDT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="kyHanFD">Kỳ hạn FD</label>
                        </span>
                    </div>

                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="ptTamUng" pInputTextarea
                                id="ptTamUng" type="text" [(ngModel)]="contractDetail.ptTamUng" #ptTamUngT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.ptTamUng === undefined || contractDetail.ptTamUng === '' || contractDetail.ptTamUng === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="ptTamUng">Phương
                                thức tạm ứng *</label>
                        </span>
                    </div>


                    <div class="form-group col-md-6">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="tgTamUng" pInputTextarea
                                id="tgTamUng" type="text" [(ngModel)]="contractDetail.tgTamUng" #tgTamUngT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.tgTamUng === undefined || contractDetail.tgTamUng === '' || contractDetail.tgTamUng === null) && validatePoint === true
                                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="tgTamUng">Thời
                                gian tạm ứng *</label>
                        </span>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="otherInfo" pInputTextarea
                                id="otherInfo" type="text" [(ngModel)]="contractDetail.otherInfo" #otherInfoT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="otherInfo">Thông tin khác</label>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>