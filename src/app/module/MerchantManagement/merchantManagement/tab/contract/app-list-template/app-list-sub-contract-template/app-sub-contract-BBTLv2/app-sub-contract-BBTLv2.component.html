<div id="form-BBTL">
    <form class="contract-information-form" #contractBBTLForm="ngForm">
        <div *ngIf="parentContractCode === 'HD14'">
            <div *ngIf="isActive('general_contract_information') || isCreateOrEdit">

                <div class="row info-panel-height format-fields" style="margin-top: 10px !important;">

                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                                dateFormat="dd/mm/yy" [(ngModel)]="contractOriginal.signatureDate"
                                #signatureDateT="ngModel" name="signatureDate" hideOnDateTimeSelect="true"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel" for="signatureDate"><PERSON><PERSON><PERSON> ký</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                                dateFormat="dd/mm/yy" [(ngModel)]="contractDetail.ngayThanhLy" #ngayThanhLyT="ngModel"
                                name="ngayThanhLy" hideOnDateTimeSelect="true"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="ngayThanhLy">Ngày thanh lý</label>
                        </span>
                    </div>

                    <div class="form-group col-md-1">
                        <span class="input-group">
                            <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="danhXungList"
                                [(ngModel)]="contractDetail.danhXung" #danhXungT="ngModel"
                                dropdownIcon=" pi pi-sort-down" name="danhXung" id="danhXung" [autoDisplayFirst]="false"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-dropdown>
                            <label [ngClass]="(contractDetail.danhXung === undefined || contractDetail.danhXung === '' || contractDetail.danhXung === null) && validatePoint === true
                                  ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="danhXung" style="margin-top: -4px !important;">Danh xưng *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" id="representative"
                                name="representative" pInputTextarea type="text"
                                [(ngModel)]="contractOriginal.representative"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractOriginal.representative === undefined || contractOriginal.representative === '' || contractOriginal.representative === null) && validatePoint === true
                            ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="representative">Người đại diện *</label>
                        </span>
                    </div>

                </div>
                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="permanentAddress"
                                pInputTextarea id="permanentAddress" type="text"
                                [(ngModel)]="contractDetail.permanentAddress"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(!contractDetail.permanentAddress) && validatePoint === true
                              ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="permanentAddress">
                                Địa chỉ thường trú *</label>
                        </span>
                    </div>

                    <div class="form-group col-md-3">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea
                                id="phone" type="text" [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                        </span>
                    </div>

                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="email" pInputTextarea
                                id="email" type="text" [(ngModel)]="contractDetail.email"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.email === undefined || contractDetail.email === '' || contractDetail.email === null) && validatePoint === true
                          ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="email">Email *</label>
                        </span>
                    </div>


                </div>
                <div class="row info-panel-height format-fields">
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="peopleId" pInputTextarea
                                id="peopleId" type="text" [(ngModel)]="contractDetail.peopleId" #peopleIdT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                            <label [ngClass]="(contractDetail.peopleId === undefined || contractDetail.peopleId === '' || contractDetail.peopleId === null) && validatePoint === true
                            ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="peopleId">Số
                                CMND/CCCD *</label>
                        </span>
                    </div>
                    <div class="form-group col-md-3">
                        <span class="input-group" style="margin-top: -3px;">
                            <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                                dateFormat="dd/mm/yy" [(ngModel)]="contractOriginal.rangeDate" #rangeIdDateT="ngModel"
                                name="rangeDate" hideOnDateTimeSelect="true" [monthNavigator]=true
                                [yearNavigator]="true" yearRange="1900:2050"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel2" for="rangeDate">Ngày cấp</label>
                        </span>
                    </div>

                    <div class="form-group col-md-5">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" id="issuedBy" name="issuedBy"
                                type="text" pInputTextarea [(ngModel)]="contractDetail.issuedBy"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                            <label [ngClass]="(contractDetail.issuedBy === undefined || contractDetail.issuedBy === '' || contractDetail.issuedBy === null) && validatePoint === true
                            ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                                for="issuedBy">Nơi cấp *</label>
                        </span>
                    </div>
                </div>
            </div>
            <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
                <!-- Child Table -->
                <div id="table-hd10" style="margin-bottom: 15px !important;margin-top: -10px !important;"
                    class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <app-child-table-bbnt [tableData]="contractDetail.subTableMerchant" [validatePoint]="validatePoint"
                            (deleteMerchantById)="deleteMerchant()" [checkEditContractDetail]="checkEditContractDetail">
                        </app-child-table-bbnt>
                    </div>
                </div>


                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                                Khoản đảm bảo khả năng thanh toán
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection"
                                value="Mien" inputId="Mien" (ngModelChange)="checkKyQuy()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="Mien" class="card-label">Miễn</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection"
                                value="KyQuy" inputId="KyQuy"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="KyQuy" class="card-label">Ký Quỹ</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4"></div>

                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group">
                            <label class="label-custom fixPositionLabel" for="khoanDamBaoInput">Số tiền ký quỹ</label>
                            <p-inputNumber locale='en-US' name="khoanDamBaoInput" id="khoanDamBaoInput"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #khoanDamBaoInputT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.khoanDamBaoInput"
                                (onBlur)="uncheckManual()"></p-inputNumber>
                        </span>
                    </div>
                    <div class="form-group col-md-1"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group" style="padding-top: 5px !important;">
                            VND
                        </span>
                    </div>
                    <div class="form-group col-md-1" *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'"></div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="thoigianGiaiKhoanh" pInputTextarea
                                id="thoigianGiaiKhoanh" type="text" [(ngModel)]="contractDetail.thoigianGiaiKhoanh" #thoigianGiaiKhoanhT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                            </textarea>
                            <label class="label-custom fixPositionLabel" for="thoigianGiaiKhoanh">Thời gian giải khoanh</label>
                        </span>
                    </div>
                </div>
                <div class="form-group col-md-2"
                    *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                </div>

                <div class="row info-panel-height format-fields"
                    *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                    <div class="form-group col-md-2">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="stkGiaiKhoanh"
                                pInputTextarea id="stkGiaiKhoanh" type="text" [(ngModel)]="contractDetail.stkGiaiKhoanh"
                                #stkGiaiKhoanhT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="stkGiaiKhoanh">Số tài khoản giải
                                khoanh</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"></div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="tenTaiKhoanGiaiKhoanh" pInputTextarea
                                id="tenTaiKhoanGiaiKhoanh" type="text" [(ngModel)]="contractDetail.tenTaiKhoanGiaiKhoanh"
                                #tenTaiKhoanGiaiKhoanhT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="tenTaiKhoanGiaiKhoanh">Tên tài khoản giải khoanh</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="openByBank" pInputTextarea
                                id="openByBank" type="text" [(ngModel)]="contractDetail.openByBank"
                                #openByBankT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="openByBank">Mở tại Ngân hàng</label>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="parentContractCode !== 'HD14'">
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-3">
                    <span class="input-group">
                        <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                            dateFormat="dd/mm/yy" [(ngModel)]="contractOriginal.signatureDate"
                            #signatureDateT="ngModel" name="signatureDate" hideOnDateTimeSelect="true"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-calendar>
                        <label class="label-custom fixPositionLabel" for="signatureDate">Ngày ký</label>
                    </span>
                </div>

                <div class="form-group col-md-3">
                    <span class="input-group">
                        <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}"
                            dateFormat="dd/mm/yy" [(ngModel)]="contractDetail.ngayThanhLy" #ngayThanhLyT="ngModel"
                            name="ngayThanhLy" hideOnDateTimeSelect="true"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-calendar>
                        <label class="label-custom fixPositionLabel2" for="ngayThanhLy">Ngày thanh lý</label>
                    </span>
                </div>
            </div>
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-6" style="margin-top: 5px;">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="businessName" pInputTextarea
                            id="businessName" type="text" [(ngModel)]="contractOriginal.businessName"
                            #businessNameT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label [ngClass]="(contractOriginal.businessName === undefined || contractOriginal.businessName === '' || contractOriginal.businessName === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="businessName">Tên đăng ký kinh doanh *</label>

                    </span>
                </div>

                <div class="form-group col-md-6" style="margin-top: 5px;">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="shortName" pInputTextarea
                            id="shortName" type="text" [(ngModel)]="contractDetail.shortName" #shortNameT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label class="label-custom fixPositionLabel" for="shortName">Tên viết tắt</label>
                    </span>
                </div>
            </div>
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="addressBusiness" pInputTextarea
                            id="addressBusiness" type="text" [(ngModel)]="contractDetail.addressBusiness"
                            #addressBusinessT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label [ngClass]="(contractDetail.addressBusiness === undefined || contractDetail.addressBusiness === '' || contractDetail.addressBusiness === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="addressBusiness">Địa chỉ *</label>
                    </span>
                </div>
                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="website" pInputTextarea
                            id="website" type="text" [(ngModel)]="contractDetail.website" #websiteT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label class="label-custom fixPositionLabel" for="website">Website</label>
                    </span>
                </div>
            </div>
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-3">
                    <span class="input-group">
                      <textarea rows="1" autoResize="true" class="form-control" name="numberBusiness" pInputTextarea
                        id="numberBusiness" type="text" [(ngModel)]="contractDetail.numberBusiness" #numberBusinessT="ngModel"
                        [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                      <label [ngClass]="(contractDetail.numberBusiness === undefined || contractDetail.numberBusiness === '' || contractDetail.numberBusiness === null) && validatePoint === true
                                  ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                        for="numberBusiness">Số đăng ký kinh doanh *</label>
                    </span>
                </div>
          
                  <div class="form-group col-md-3">
                    <span class="input-group" style="margin-top: -3px;">
                      <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                        [(ngModel)]="contractOriginal.rangeDate" #rangeDateT="ngModel" name="rangeDate"
                        hideOnDateTimeSelect="true" [monthNavigator]=true [yearNavigator]="true" yearRange="1900:2050"
                        [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                      </p-calendar>
                      <label class="label-custom fixPositionLabel2" for="rangeDate">Ngày cấp</label>
                    </span>
                </div>

                  <div class="form-group col-md-3">
                    <span class="input-group">
                      <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea id="phone" type="text"
                        [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                        [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>
                      <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                    </span>
                </div>
            </div>
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-1">
                    <span class="input-group">
                        <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="danhXungList"
                            [(ngModel)]="contractDetail.danhXung" #danhXungT="ngModel"
                            dropdownIcon=" pi pi-sort-down" name="danhXung" id="danhXung" [autoDisplayFirst]="false"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-dropdown>
                        <label [ngClass]="(contractDetail.danhXung === undefined || contractDetail.danhXung === '' || contractDetail.danhXung === null) && validatePoint === true
                              ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="danhXung" style="margin-top: -4px !important;">Danh xưng *</label>
                    </span>
                </div>

                <div class="form-group col-md-5">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="signaturer" pInputTextarea
                            id="signaturer" type="text" [(ngModel)]="contractDetail.signaturer" #signaturerT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label [ngClass]="(contractDetail.signaturer === undefined || contractDetail.signaturer === '' || contractDetail.signaturer === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="signaturer">Người ký *</label>
                    </span>
                </div>


                <div class="form-group col-md-3">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="position" pInputTextarea
                            id="position" type="text" [(ngModel)]="contractDetail.position" #positionT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"></textarea>

                        <label [ngClass]="(contractDetail.position === undefined || contractDetail.position === '' || contractDetail.position === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="position">Chức vụ *</label>
                    </span>
                </div>
            </div>
            <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
                <!-- Child Table -->
                <div id="table-hd10" style="margin-bottom: 15px !important;margin-top: -10px !important;"
                    class="row info-panel-height format-fields">
                    <div class="form-group col-md-12">
                        <app-child-table-bbnt [tableData]="contractDetail.subTableMerchant" [validatePoint]="validatePoint"
                            (deleteMerchantById)="deleteMerchant()" [checkEditContractDetail]="checkEditContractDetail">
                        </app-child-table-bbnt>
                    </div>
                </div>

                <div class="row info-panel-height format-fields">

                    <div class="form-group col-md-12">
                        <span class="input-group">
                            <span style="color: #0089D0;font-weight: bold;font-size: 12px !important;">
                                Khoản đảm bảo khả năng thanh toán
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection"
                                value="Mien" inputId="Mien" (ngModelChange)="checkKyQuy()"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="Mien" class="card-label">Miễn</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <p-radioButton name="khoanDamBaoSelection" [(ngModel)]="contractDetail.khoanDamBaoSelection"
                                value="KyQuy" inputId="KyQuy"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="KyQuy" class="card-label">Ký Quỹ</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4"></div>

                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group">
                            <label class="label-custom fixPositionLabel" for="khoanDamBaoInput">Số tiền ký quỹ</label>
                            <p-inputNumber locale='en-US' name="khoanDamBaoInput" id="khoanDamBaoInput"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                #khoanDamBaoInputT="ngModel" mode="decimal" currency="USD" currencyDisplay="code"
                                [(ngModel)]="contractDetail.khoanDamBaoInput"
                                (onBlur)="uncheckManual()"></p-inputNumber>
                        </span>
                    </div>
                    <div class="form-group col-md-1"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group" style="padding-top: 5px !important;">
                            VND
                        </span>
                    </div>
                    <div class="form-group col-md-1" *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'"></div>
                    <div class="form-group col-md-2"
                        *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="thoigianGiaiKhoanh" pInputTextarea
                                id="thoigianGiaiKhoanh" type="text" [(ngModel)]="contractDetail.thoigianGiaiKhoanh" #thoigianGiaiKhoanhT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                            </textarea>
                            <label class="label-custom fixPositionLabel" for="thoigianGiaiKhoanh">Thời gian giải khoanh</label>
                        </span>
                    </div>
                </div>
                <div class="form-group col-md-2"
                    *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                </div>

                <div class="row info-panel-height format-fields"
                    *ngIf="contractDetail.khoanDamBaoSelection === 'KyQuy'">
                    <div class="form-group col-md-2">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="stkGiaiKhoanh"
                                pInputTextarea id="stkGiaiKhoanh" type="text" [(ngModel)]="contractDetail.stkGiaiKhoanh"
                                #stkGiaiKhoanhT="ngModel" (input)="uncheckManual()"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="stkGiaiKhoanh">Số tài khoản giải
                                khoanh</label>
                        </span>
                    </div>
                    <div class="form-group col-md-2"></div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="tenTaiKhoanGiaiKhoanh" pInputTextarea
                                id="tenTaiKhoanGiaiKhoanh" type="text" [(ngModel)]="contractDetail.tenTaiKhoanGiaiKhoanh"
                                #tenTaiKhoanGiaiKhoanhT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="tenTaiKhoanGiaiKhoanh">Tên tài khoản giải khoanh</label>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <span class="input-group">
                            <textarea rows="1" autoResize="true" class="form-control" name="openByBank" pInputTextarea
                                id="openByBank" type="text" [(ngModel)]="contractDetail.openByBank"
                                #openByBankT="ngModel"
                                [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                                (input)="uncheckManual()">
                                    </textarea>
                            <label class="label-custom fixPositionLabel" for="openByBank">Mở tại Ngân hàng</label>
                        </span>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>