#form-pl02v2 input {
    height: 30px !important;
    border-radius: 0px !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #ced4da;
    /* padding-top: 13px !important; */
  }
  
  #form-pl02v2 input:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-pl02v2 textarea:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-pl02v2 .form-group {
    padding-top: 10px !important;
  }
  
  #form-pl02v2 .fixPositionLabel {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -6px !important;
  }
  
  #form-pl02v2 .fixPositionLabel2 {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -3px !important;
  }
  
  #form-pl02v2 .format-fields {
    margin-bottom: 5px !important;
  }
  
  #form-pl02v2 .btn-common {
    font-size: 10px !important;
    height: 25px !important;
    /* margin-top: 10px !important; */
  }
  
  #form-pl02v2 .btn-common span{
    font-size: 10px !important;
  }
  
  #form-pl02v2 .header-table {
      background-color: rgb(249, 252, 255) !important;
  }
  
  #form-pl02v2 .tr_body td {
    background-color: white !important;
  }
  
  #form-pl02v2 .card-label {
    margin-left: 3px !important;
    font-size: 12px !important;
    margin-right: 20px !important;
    margin-bottom: 1px !important;
    cursor: pointer;
  }
  
  #form-pl02v2 .combobox-label {
    margin-bottom: 3px !important;
  }
  
  #form-pl02v2 .first-label {
    color: #0089D0;
    font-weight: bold;
    font-size: 12px !important;
    padding-top: 5px !important;
  }
  
  #form-pl02v2 input[type="text"]:disabled {
    background: #bcbdbf !important;
    color: black !important;
  }
  
  #form-pl02v2 .label-style {
    margin-right: 60px !important;
  }
  
  ::ng-deep #form-pl02v2 .p-dropdown .p-dropdown-label{
    height: 38px !important;
  }
  
  ::ng-deep #form-pl02v2 #carrer .p-dropdown .p-dropdown-label{
    height: 28px !important;
  }
  
  ::ng-deep #form-pl02v2 #danhXung .p-dropdown .p-dropdown-label{
    height: 28px !important;
  }
  
  ::ng-deep #form-pl02v2 #carrer .p-disabled, ::ng-deep #form-pl02v2 #danhXung .p-disabled{
    background-color: aliceblue !important;
  }
  
  ::ng-deep #form-pl02v2 #danhXung .p-disabled .p-dropdown-label, ::ng-deep #form-pl02v2 #carrer .p-disabled .p-dropdown-label{
    font-weight: bold !important;
    color: black !important;
  }
  
  ::ng-deep #form-pl02v2 .inValid {
    color: rgb(218, 0, 0) !important;
  }
  
  ::ng-deep #form-pl02v2 #installment-table-hd13 .p-dropdown {
    margin-left: 10px !important;
  }
  
  ::ng-deep #form-pl02v2 .p-calendar {
    border: none !important;
  }
  
  ::ng-deep #form-pl02v2 .p-inputnumber-input {
    height: 26px !important;
  }
  
  @media(max-width:1800px) {
    #form-pl02v2 .label-style {
      margin-right: 50px !important;
    }
  }
  
  @media(max-width:1700px) {
    #form-pl02v2 .label-style {
      margin-right: 40px !important;
    }
  }
  
  @media(max-width:1600px) {
    #form-pl02v2 .label-style {
      margin-right: 30px !important;
    }
  }
  
  @media(max-width:1500px) {
    #form-pl02v2 .label-style {
      margin-right: 20px !important;
    }
  }
  
  @media(max-width:1400px) {
    #form-pl02v2 .label-style {
      margin-right: 10px !important;
    }
  }
  