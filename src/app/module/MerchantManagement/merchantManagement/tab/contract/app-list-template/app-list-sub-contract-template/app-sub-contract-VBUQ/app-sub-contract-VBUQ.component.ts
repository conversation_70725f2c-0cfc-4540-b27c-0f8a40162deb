import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

@Component({
  selector: 'app-sub-contract-VBUQ',
  templateUrl: './app-sub-contract-VBUQ.component.html',
  styleUrls: ['./app-sub-contract-VBUQ.component.css']
})
export class AppSubContractVBUQComponent implements OnInit, OnChanges {

  @Input() currentPartner: Partner
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    if (this.contractOriginal) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;
      
      if (this.contractOriginal.signatureDate && this.contractOriginal.id && this.contractOriginal.id !== '0') {
        this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
      } else {
        this.contractOriginal.signatureDate = null;
      }
      if (this.contractOriginal.rangeDate && this.contractOriginal.id && this.contractOriginal.id !== '0') {
        this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
      } else {
        this.contractOriginal.rangeDate = null;
      }
      if (this.contractDetail.authorizationPeriodFrom) {
        this.contractDetail.authorizationPeriodFrom = new Date(this.contractDetail.authorizationPeriodFrom);
      }
      else {
        this.contractDetail.authorizationPeriodFrom = undefined;
      }
      // if (this.contractDetail.authorizationPeriodTo) {
      //   this.contractDetail.authorizationPeriodTo = new Date(this.contractDetail.authorizationPeriodTo);
      // }
      // else {
      //   this.contractDetail.authorizationPeriodTo = undefined;
      // }
      if (this.contractDetail.authorizedBirthDate) {
        this.contractDetail.authorizedBirthDate = new Date(this.contractDetail.authorizedBirthDate);
      }
      else {
        this.contractDetail.authorizedBirthDate = undefined;
      }
      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
      if (!this.contractOriginal.id || this.contractOriginal.id === '0') {
        // this.contractDetail.signaturer = undefined;
        // this.contractDetail.position = undefined;
        this.contractDetail.accountBank = undefined;
        this.contractDetail.authorizationNumber = undefined;
        this.contractDetail.accountNumber = undefined;
      }
      
    }

  }

  validateForm(): boolean {
    if (!this.contractOriginal.businessName || !this.contractDetail.addressBusiness
      || !this.contractDetail.numberBusiness || !this.contractDetail.signaturer || !this.contractDetail.position 
      || !this.contractDetail.authorizedPersonName || !this.contractDetail.authorizedPersonId || !this.contractDetail.authorizedBirthDate 
      || !this.contractDetail.authorizedAddress || !this.contractOriginal.rangeDate || !this.contractDetail.authorizedIssuedBy 
      || !this.contractOriginal.contractNumber || !this.contractDetail.accountName || !this.contractDetail.accountNumber 
      || !this.contractDetail.accountBank || !this.contractDetail.authorizationPeriodFrom || !this.contractDetail.authorizationPeriodTo) {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = false;
      return true;
    }
  }
}