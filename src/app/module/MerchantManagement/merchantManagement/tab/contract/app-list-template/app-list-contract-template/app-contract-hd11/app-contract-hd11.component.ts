import { Component, OnInit, Input, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

interface Carrer {
  value: string;
  label: string;
}


@Component({
  selector: 'app-contract-hd11',
  templateUrl: './app-contract-hd11.component.html',
  styleUrls: ['./app-contract-hd11.component.css']
})

export class AppContractHD11Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner;
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() checkEditContractDetail: boolean;
  @Input() carrerList: Carrer[];
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() feeNormalList;
  @Input() feeBigMerchantList;

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    this.convertCarrerList();
    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;
    }
    if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
      this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
    }

    if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
      this.state = this.contractOriginal.state;
    }

  }

  convertCarrerList() {
    if (this.carrerList) {
      this.carrerList = this.carrerList.map(item => ({
        value: item.label,
        label: item.label
      }));
    }
  }


  validateForm(): boolean {
    if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
      || this.contractDetail.carrer === undefined || this.contractDetail.carrer === null || this.contractDetail.carrer === ''
      || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
      || this.contractDetail.addressOffice === undefined || this.contractDetail.addressOffice === null || this.contractDetail.addressOffice === ''
      || this.contractDetail.numberBusiness === undefined || this.contractDetail.numberBusiness === null || this.contractDetail.numberBusiness === ''
      || this.contractDetail.accountBank === undefined || this.contractDetail.accountBank === null || this.contractDetail.accountBank === ''
      || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
      || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
    ) {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = false;
      return true;
    }
  }

}
