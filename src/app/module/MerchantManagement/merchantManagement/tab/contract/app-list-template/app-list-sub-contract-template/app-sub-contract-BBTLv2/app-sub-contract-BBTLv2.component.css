#form-<PERSON><PERSON><PERSON> input {
    height: 30px !important;
    border-radius: 0px !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #ced4da;
    /* padding-top: 13px !important; */
}

#form-BB<PERSON> input:focus {
  border-bottom: 1px solid #0082f3 !important;
}

#form-BBTL textarea:focus {
  border-bottom: 1px solid #0082f3 !important;
}

#form-BBTL .form-group {
    padding-top: 10px !important;
}

#form-BBTL .fixPositionLabel {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -6px !important;
  }

  #form-BBTL .fixPositionLabel2 {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -3px !important;
  }
  
  #form-BBTL .btn-common {
    font-size: 10px !important;
    height: 25px !important;
    /* margin-top: 10px !important; */
  }
  
  #form-BBTL .btn-common span{
    font-size: 10px !important;
  }

  #form-BBTL .header-table {
      background-color: rgb(249, 252, 255) !important;
  }

  #form-BBTL .tr_body td {
    background-color: white !important;
  }

  #form-BBTL .label-style {
      margin-right: 60px !important;
  }

  #form-BBTL .format-fields {
    margin-bottom: 5px !important;
  }

  ::ng-deep #form-BBTL .inValid {
    color: rgb(218, 0, 0) !important;
  }

  @media(max-width:1800px) {
    #form-BBTL .label-style {
      margin-right: 50px !important;
    }
  }

  @media(max-width:1700px) {
    #form-BBTL .label-style {
      margin-right: 40px !important;
    }
  }

  @media(max-width:1600px) {
    #form-BBTL .label-style {
      margin-right: 30px !important;
    }
  }

  @media(max-width:1500px) {
    #form-BBTL .label-style {
      margin-right: 20px !important;
    }
  }

  @media(max-width:1400px) {
    #form-BBTL .label-style {
      margin-right: 10px !important;
    }
  }