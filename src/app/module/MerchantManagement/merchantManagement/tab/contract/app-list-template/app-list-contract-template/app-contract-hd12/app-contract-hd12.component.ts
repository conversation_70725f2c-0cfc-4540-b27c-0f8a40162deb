import { Component, OnInit, Input, Output, EventEmitter, ViewChild, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';
import { AppChildTableMerchantComponent } from '../../app-child-table/app-child-table-merchant/app-child-table-merchant.component';

interface Carrer {
  value: string;
  label: string;
}

@Component({
  selector: 'app-contract-hd12',
  templateUrl: './app-contract-hd12.component.html',
  styleUrls: ['./app-contract-hd12.component.css']
})

export class AppContractHD12Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner;
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() checkEditContractDetail: boolean;
  @Input() carrerList: Carrer[];
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() feeNormalList;
  @Input() feeBigMerchantList;
  @Output()
  private deleteMerchantById = new EventEmitter<any>();
  @ViewChild(AppChildTableMerchantComponent) childTable: AppChildTableMerchantComponent;

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.contractDetail && this.contractDetail.carrer && this.carrerList) {
      let studentObj = this.carrerList.find(t => t.value === this.contractDetail.carrer);
      if (studentObj) {
        this.contractDetail.carrer = studentObj.label;
      }
    }

    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    this.convertCarrerList();

    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;
    }
    if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
      this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
    }

    if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
      this.state = this.contractOriginal.state;
    }

  }

  convertCarrerList() {
    if (this.carrerList) {
      this.carrerList = this.carrerList.map(item => ({
        value: item.label,
        label: item.label
      }));
    }
  }

  validateForm(): boolean {
    if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
      || this.contractDetail.carrer === undefined || this.contractDetail.carrer === null || this.contractDetail.carrer === ''
      || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
      || this.contractDetail.addressOffice === undefined || this.contractDetail.addressOffice === null || this.contractDetail.addressOffice === ''
      || this.contractDetail.numberBusiness === undefined || this.contractDetail.numberBusiness === null || this.contractDetail.numberBusiness === ''
      || this.contractDetail.accountBank === undefined || this.contractDetail.accountBank === null || this.contractDetail.accountBank === ''
      || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
      || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
      || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant === [] || this.contractDetail.subTableMerchant.length === 0
      || this.contractDetail.tgTamUng === undefined || this.contractDetail.tgTamUng === null || this.contractDetail.tgTamUng === ''
      || this.contractDetail.ptTamUng === undefined || this.contractDetail.ptTamUng === null || this.contractDetail.ptTamUng === ''
      || this.contractDetail.feeForCard === undefined || this.contractDetail.feeForCard === null || this.contractDetail.feeForCard === ''
      || this.contractDetail.ptTamUngMoi === undefined || this.contractDetail.ptTamUngMoi === null || this.contractDetail.ptTamUngMoi === ''
    ) {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = false;
      return true;
    }
  }

  deleteMerchant() {
    this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }

}
