import {Component, OnInit, Input, EventEmitter, Output, ViewChild, OnChanges, SimpleChanges} from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

import { AppChildTablePauseComponent } from '../../app-child-table/app-child-table-pause/app-child-table-pause.component';

@Component({
  selector: 'app-sub-contract-pl15v2',
  templateUrl: './app-sub-contract-pl15v2.component.html',
  styleUrls: ['./app-sub-contract-pl15v2.component.css']
})

export class AppSubContractPL15v2Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() carrerList: [];
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() parentContractCode: string;
  @Input() parentContractDetail: any;
  @Input() parentContractNumber: string;
  @Input() parentSignatureDate: string;
  @Input() danhXungList: any;
  @ViewChild(AppChildTablePauseComponent) childTable: AppChildTablePauseComponent;

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  public state = '';
  public validatePoint = false;
  public isCreateOrEdit = false;
  public showParentContractInfo = true;
  public checkAutoFill = false;
  public isValidParentContract = false;
  constructor(public global: Globals) {
  }

  ngOnInit() {
    this.checkParentContract();
    if (this.contractOriginal !== undefined) {
        // Nếu chưa có id thì tức là đang tạo 1 hđ mới
        if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
        else this.isCreateOrEdit = false;
  
        if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
          this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
        }
        if (this.contractOriginal.rangeDate !== undefined && this.contractOriginal.rangeDate !== '') {
          this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
        }
        if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
          this.state = this.contractOriginal.state;
        }
      }
  
      if (this.contractDetail !== undefined) {
        if (this.contractDetail.thoiGianApDung !== undefined && this.contractDetail.thoiGianApDung !== '') {
          this.contractDetail.thoiGianApDung = new Date(this.contractDetail.thoiGianApDung);
        } else {
          this.contractDetail.thoiGianApDung = undefined;
        }
      }
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
        this.contractOriginal.contractNumber = undefined;
        this.contractOriginal.signatureDate = null;
        this.contractDetail.thoiGianApDung = undefined;
        this.contractDetail.stkGiaiKhoanh = undefined;
        this.contractDetail.openByBank = undefined;
      }

      console.log('Parent Contract Code:', this.getParentContractCode());
  }

  getParentContractCode(): string {
    return this.parentContractCode || '';
  }

  getParentContractNumber(): string {
    return this.parentContractNumber || '';
  }

  getParentSignatureDate(): string {
    return this.parentSignatureDate || '';
  }

  hasParentContract(): boolean {
    return !!this.parentContractCode && !!this.parentContractNumber;
  }

  toggleParentContractInfo(): void {
    this.showParentContractInfo = !this.showParentContractInfo;
  }

  validateForm(): boolean {
    if (this.parentContractCode === 'HD14') {
      if (
        this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === '' ||
        this.contractOriginal.representative === undefined || this.contractOriginal.representative === null || this.contractOriginal.representative === '' ||
        this.contractDetail.permanentAddress === undefined || this.contractDetail.permanentAddress === null || this.contractDetail.permanentAddress === '' ||
        this.contractDetail.tgTamUng === undefined || this.contractDetail.tgTamUng === null || this.contractDetail.tgTamUng === '' ||
        this.contractDetail.subTablePause === undefined || this.contractDetail.subTablePause === null || this.contractDetail.subTablePause.length === 0 ||
        this.contractDetail.email === undefined || this.contractDetail.email === null || this.contractDetail.email === '' ||
        this.contractDetail.peopleId === undefined || this.contractDetail.peopleId === null || this.contractDetail.peopleId === '' ||
        this.contractDetail.issuedBy === undefined || this.contractDetail.issuedBy === null || this.contractDetail.issuedBy === ''
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = true;
        return true;
      }
    } else {
      if (
        this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === '' ||
        this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === '' ||
        this.contractDetail.numberBusiness === undefined || this.contractDetail.numberBusiness === null || this.contractDetail.numberBusiness === '' ||
        this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === '' ||
        this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === '' ||
        this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === '' ||
        this.contractDetail.subTablePause === undefined || this.contractDetail.subTablePause === null || this.contractDetail.subTablePause.length === 0 ||
        this.contractDetail.tgTamUng === undefined || this.contractDetail.tgTamUng === null || this.contractDetail.tgTamUng === ''
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = true;
        return true;
      }
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
    if (changes.parentContractCode) {
      this.checkParentContract();
    }
  }

  checkParentContract() {
    this.isValidParentContract = this.parentContractCode === 'HD14';
  }

  deleteMerchant() {
    this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }

  autoFillKyQuy() {
    if (this.contractDetail.kyQuyAutoFill && this.contractDetail.kyQuyAutoFill !== null && this.contractDetail.kyQuyAutoFill.length > 0) {
      this.contractDetail.khoanDamBaoInput = ********;
      this.contractDetail.kyHanFD = '6 tháng';
      if (this.contractDetail.subTableMerchant && this.contractDetail.subTableMerchant.length > 0) {
        this.contractDetail.stkGiaiKhoanh = this.contractDetail.subTableMerchant[0].accountNumber;
        this.contractDetail.openByBank = this.contractDetail.subTableMerchant[0].bank;
      }

      if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKeep') {
        this.contractDetail.keepPercent = 100;
      } else {
        this.contractDetail.keepPercent = undefined;
      }
    }
  }

  checkKyQuy() {
    if (this.contractDetail.khoanDamBaoSelection && this.contractDetail.khoanDamBaoSelection === 'Mien') {
      this.contractDetail.kyQuyType = undefined;
      this.contractDetail.inputKyQuyKhac = undefined;
      this.cancelAutoFillKyQuy();
    }
  }

  cancelAutoFillKyQuy() {
    this.contractDetail.kyQuyAutoFill = undefined;
    this.contractDetail.kyHanFD = undefined;
    this.contractDetail.khoanDamBaoInput = undefined;
    this.contractDetail.stkGiaiKhoanh = undefined;
    this.contractDetail.openByBank = undefined;
    this.checkAutoFill = false;
  }
} 