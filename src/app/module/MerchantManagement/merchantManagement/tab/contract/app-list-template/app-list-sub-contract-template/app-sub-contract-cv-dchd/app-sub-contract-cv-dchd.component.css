#form-cv-dchd input {
    height: 30px !important;
    border-radius: 0px !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #ced4da;
    /* padding-top: 13px !important; */
  }
  
  #form-cv-dchd input:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-cv-dchd textarea:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-cv-dchd .form-group {
    padding-top: 10px !important;
  }
  
  #form-cv-dchd .fixPositionLabel {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -6px !important;
  }
  
  #form-cv-dchd .fixPositionLabel2 {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -3px !important;
  }
  
  #form-cv-dchd .btn-common {
    font-size: 10px !important;
    height: 25px !important;
    /* margin-top: 10px !important; */
  }
  
  #form-cv-dchd .format-fields {
    margin-bottom: 5px !important;
  }
  
  #form-cv-dchd .btn-common span{
    font-size: 10px !important;
  }
  
  #form-cv-dchd .header-table {
      background-color: rgb(249, 252, 255) !important;
  }
  
  #form-cv-dchd .tr_body td {
    background-color: white !important;
  }
  

  #form-cv-dchd .card-label {
    margin-left: 3px !important;
    font-size: 12px !important;
    margin-right: 20px !important;
    margin-bottom: 1px !important;
    cursor: pointer;
  }
  
  #form-cv-dchd .combobox-label {
    margin-bottom: 3px !important;
  }
  
  #form-cv-dchd .first-label {
    color: #0089D0;
    font-weight: bold;
    font-size: 12px !important;
    padding-top: 5px !important;
  }
  
  #form-cv-dchd input[type="text"]:disabled {
    background: #bcbdbf !important;
    color: black !important;
  }
  
  #form-cv-dchd .label-style {
    margin-right: 60px !important;
  }
  
  ::ng-deep #form-cv-dchd .p-dropdown .p-dropdown-label{
    height: 38px !important;
  }
  
  ::ng-deep #form-cv-dchd #carrer .p-dropdown .p-dropdown-label{
    height: 28px !important;
  }
  
  ::ng-deep #form-cv-dchd #danhXung .p-dropdown .p-dropdown-label{
    height: 28px !important;
  }
  
  ::ng-deep #form-cv-dchd #carrer .p-disabled, ::ng-deep #form-cv-dchd #danhXung .p-disabled{
    background-color: aliceblue !important;
  }
  
  ::ng-deep #form-cv-dchd #danhXung .p-disabled .p-dropdown-label, ::ng-deep #form-cv-dchd #carrer .p-disabled .p-dropdown-label{
    font-weight: bold !important;
    color: black !important;
  }

  ::ng-deep #form-cv-dchd .inValid {
    color: rgb(218, 0, 0) !important;
  }

  
::ng-deep #form-cv-dchd #installment-table-hd13 .p-dropdown {
  margin-left: 10px !important;
}

::ng-deep #form-cv-dchd .p-calendar {
  border: none !important;
}

::ng-deep #form-cv-dchd .p-inputnumber-input {
  height: 26px !important;
}

@media(max-width:1800px) {
  #form-cv-dchd .label-style {
    margin-right: 50px !important;
  }
}

@media(max-width:1700px) {
  #form-cv-dchd .label-style {
    margin-right: 40px !important;
  }
}

@media(max-width:1600px) {
  #form-cv-dchd .label-style {
    margin-right: 30px !important;
  }
}

@media(max-width:1500px) {
  #form-cv-dchd .label-style {
    margin-right: 20px !important;
  }
}

@media(max-width:1400px) {
  #form-cv-dchd .label-style {
    margin-right: 10px !important;
  }
}
