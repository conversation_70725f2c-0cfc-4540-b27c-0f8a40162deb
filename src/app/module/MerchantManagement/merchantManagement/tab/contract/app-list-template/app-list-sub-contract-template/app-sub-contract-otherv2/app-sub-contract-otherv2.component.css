#form-pl-otherv2 input {
    height: 30px !important;
    border-radius: 0px !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #ced4da;
    /* padding-top: 13px !important; */
  }
  
  #form-pl-otherv2 input:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-pl-otherv2 textarea:focus {
  border-bottom: 1px solid #0082f3 !important;
  }
  
  #form-pl-otherv2 .form-group {
    padding-top: 10px !important;
  }
  
  #form-pl-otherv2 .fixPositionLabel {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -6px !important;
  }
  
  #form-pl-otherv2 .fixPositionLabel2 {
    top: 0% !important;
    color: #888888;
    font-size: 10px !important;
    padding-left: -10px !important;
    margin-left: -10px !important;
    margin-top: -3px !important;
  }
  
  #form-pl-otherv2 .btn-common {
    font-size: 10px !important;
    height: 25px !important;
    /* margin-top: 10px !important; */
  }
  
  #form-pl-otherv2 .format-fields {
    margin-bottom: 5px !important;
  }
  
  #form-pl-otherv2 .btn-common span{
    font-size: 10px !important;
  }
  
  #form-pl-otherv2 .header-table {
      background-color: rgb(249, 252, 255) !important;
  }
  
  #form-pl-otherv2 .tr_body td {
    background-color: white !important;
  }
  
  ::ng-deep #form-pl-otherv2 .inValid {
    color: rgb(218, 0, 0) !important;
  }