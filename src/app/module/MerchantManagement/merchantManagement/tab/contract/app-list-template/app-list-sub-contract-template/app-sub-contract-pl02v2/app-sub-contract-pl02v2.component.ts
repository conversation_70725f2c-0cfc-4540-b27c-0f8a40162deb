import { Component, OnInit, Input, EventEmitter, Output, ViewChild, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

import { AppChildTableMerchantComponent } from '../../app-child-table/app-child-table-merchant/app-child-table-merchant.component';

@Component({
  selector: 'app-sub-contract-pl02v2',
  templateUrl: './app-sub-contract-pl02v2.component.html',
  styleUrls: ['./app-sub-contract-pl02v2.component.css']
})
export class AppSubContractPl02v2Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() carrerList: [any];
  @Input() feeNormalList;
  @Input() feeBigMerchantList;
  @Input() danhXungList;
  @Input() feeInstallmentList;
  @Input() parentContractDetail;
  @Input() parentContractCode: string;

  @ViewChild(AppChildTableMerchantComponent) childTable: AppChildTableMerchantComponent;

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  public state = '';
  public cardType: string;
  public labelChildTable = 'Phí dịch vụ trả góp';
  public validatePoint = false;
  public checkInternationalFee = false;
  public checkDomesticFee = false;
  public checkAppFee = false;
  public checkAmericanExpress = false;
  public checkDisplayInternationalFee = false;
  public checkDisplayDomesticFee = false;
  public checkBnplFee = false;
  public checkShopifyFee = false;
  public checkAutoFill = false;
  public checkDischargeVietQR = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.contractDetail && this.contractDetail.carrer && this.carrerList) {
      let studentObj = this.carrerList.find(t => t.value === this.contractDetail.carrer);
      if (studentObj) {
        this.contractDetail.carrer = studentObj.label;
      }
    }

    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    if(this.parentContractCode === 'HD14'){
      console.log('parent HD14')
      if (this.contractDetail.feeService === 'normalFee' && this.contractDetail.feeTransInternational) {
        //phi thong thuong loai bo cac ky tu dac biet
        const IdRegexp: RegExp = /[,]/;
        if (IdRegexp.test(this.contractDetail.feeTransInternational)) {
          this.contractDetail.feeTransInternational = this.contractDetail.feeTransInternational.replace(',', '');
        }
      }

      if (this.contractOriginal !== undefined) {
        // Nếu chưa có id thì tức là đang tạo 1 hđ mới
        if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
        else this.isCreateOrEdit = false;
      }

      if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
        this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
      }

      if (this.contractOriginal.rangeDate !== undefined && this.contractOriginal.rangeDate !== '') {
        this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
      }

      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
    } else if(this.parentContractCode === 'HD13'){
      console.log('parent HD13')
      if (this.contractDetail.feeService === 'normalFee' && this.contractDetail.feeTransInternational) {
        //phi thong thuong loai bo cac ky tu dac biet
        const IdRegexp: RegExp = /[,]/;
        if (IdRegexp.test(this.contractDetail.feeTransInternational)) {
          this.contractDetail.feeTransInternational = this.contractDetail.feeTransInternational.replace(',', '');
        }
      }
  
      if (this.contractOriginal !== undefined) {
        // Nếu chưa có id thì tức là đang tạo 1 hđ mới
        if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
        else this.isCreateOrEdit = false;
      }
  
      if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
        this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
      }
  
      if (this.contractOriginal.rangeDate !== undefined && this.contractOriginal.rangeDate !== '') {
        this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
      }
  
      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
    } else {
      console.log('parent other')
      if (this.contractOriginal !== undefined) {
        // Nếu chưa có id thì tức là đang tạo 1 hđ mới
        if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
        else this.isCreateOrEdit = false;
  
        if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
          this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
        }
        if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
          this.state = this.contractOriginal.state;
        }
      }
  
      if (this.contractDetail !== undefined) {
        if (this.contractDetail.cardList !== undefined && this.contractDetail.cardList !== '') {
          this.contractDetail.cardListArray = this.contractDetail.cardList.split(',');
        }
  
      }
  
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
        this.contractOriginal.contractNumber = undefined;
        this.contractOriginal.signatureDate = null;
        this.contractDetail.rangeDate = null;
        this.contractDetail.subTableMerchant = [];
        this.contractDetail.khoanDamBao = undefined;
        this.contractDetail.stkGiaiKhoanh = undefined;
        this.contractDetail.kyHanFD = undefined;
      }
    }

    this.changeCardType();

  }

  validateForm(): boolean {
    if(this.parentContractCode === 'HD14'){
      if (this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === ''
        || this.contractOriginal.representative === undefined || this.contractOriginal.representative === null || this.contractOriginal.representative === ''
        || this.contractDetail.permanentAddress === undefined || this.contractDetail.permanentAddress === null || this.contractDetail.permanentAddress === ''
        || this.contractDetail.email === undefined || this.contractDetail.email === null || this.contractDetail.email === ''
        || this.contractDetail.peopleId === undefined || this.contractDetail.peopleId === null || this.contractDetail.peopleId === ''
        || this.contractDetail.issuedBy === undefined || this.contractDetail.issuedBy === null || this.contractDetail.issuedBy === ''
        || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant.length === 0
        || this.contractDetail.tgTamUngSelection === undefined || this.contractDetail.tgTamUngSelection === null || this.contractDetail.tgTamUngSelection === ''
        || this.contractDetail.cardListArray === undefined || this.contractDetail.cardListArray === null || this.contractDetail.cardListArray.length < 1
        || (this.checkPhamViTheQuocTe() === false && this.checkInternationalFee)
        || this.contractDetail.hinhThucThuPhi === undefined || this.contractDetail.hinhThucThuPhi === null || this.contractDetail.hinhThucThuPhi === ''
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = false;
        return true;
      }
    } else if(this.parentContractCode === 'HD13'){
      if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
        || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
        || this.contractDetail.numberBusiness === undefined || this.contractDetail.numberBusiness === null || this.contractDetail.numberBusiness === ''
        || this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === ''
        || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
        || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
        || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant.length === 0
        || this.contractDetail.tgTamUngSelection === undefined || this.contractDetail.tgTamUngSelection === null || this.contractDetail.tgTamUngSelection === ''
        || this.contractDetail.cardListArray === undefined || this.contractDetail.cardListArray === null || this.contractDetail.cardListArray.length < 1
        || (this.checkPhamViTheQuocTe() === false && this.checkInternationalFee)
        || this.contractDetail.hinhThucThuPhi === undefined || this.contractDetail.hinhThucThuPhi === null || this.contractDetail.hinhThucThuPhi === ''
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = false;
        return true;
      }
    } else {
      if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
        || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
        || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
        || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
  
        || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant.length === 0
        || this.contractDetail.tgTamUng === undefined || this.contractDetail.tgTamUng === null || this.contractDetail.tgTamUng === ''
        || this.contractDetail.ptTamUng === undefined || this.contractDetail.ptTamUng === null || this.contractDetail.ptTamUng === ''
        || this.contractDetail.monthFee === undefined || this.contractDetail.monthFee === null || this.contractDetail.monthFee === ''
        || this.contractDetail.feeForCard === undefined || this.contractDetail.feeForCard === null || this.contractDetail.feeForCard === ''
        // || this.contractDetail.khoanDamBao === undefined || this.contractDetail.khoanDamBao === null || this.contractDetail.khoanDamBao === ''
        || this.contractDetail.cardType === undefined || this.contractDetail.cardType === null || this.contractDetail.cardType === ''
        || this.contractDetail.cardTransactionFee === undefined || this.contractDetail.cardTransactionFee === null || this.contractDetail.cardTransactionFee === '') {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = false;
        return true;
      }
    }
  }

  changeCardType() {
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.cardType = undefined;
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'ApplePay' || element === 'GooglePay' || element === 'SamsungPay') {
          if (this.cardType !== undefined && this.cardType !== '') {
            this.cardType = this.cardType + ', ' + element;
          } else {
            this.cardType = element;
          }
        }
      });
    }
    this.checkFeeType();
  }

  checkFeeType() {
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.checkInternationalFee = false;
      this.checkDomesticFee = false;
      this.checkAppFee = false;
      this.checkAmericanExpress = false;
      this.checkDisplayInternationalFee = false;
      this.checkDisplayDomesticFee = false;
      this.checkBnplFee = false;
      this.checkShopifyFee = false;
      this.checkDischargeVietQR = false;
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'AmericanExpress' || element === 'ApplePay' || element === 'GooglePay' || element === 'SamsungPay') {
          this.checkInternationalFee = true;
          if (element === 'AmericanExpress') {
            this.checkAmericanExpress = true;
          }
        } else if (element === 'ApproveOnepayDomesticCard') {
          this.checkDomesticFee = true;
        } else if (element === 'ApproveOnepayMobileApp') {
          this.checkAppFee = true;
        } else if (element === 'ApproveInternationalCard') {
          this.checkDisplayInternationalFee = true;
        } else if (element === 'ApproveDomesticCard') {
          this.checkDisplayDomesticFee = true;
        } else if (element === 'ApproveBNPL') {
          this.checkBnplFee = true;
        } else if (element === 'ApproveShopify') {
          this.checkShopifyFee = true;
          this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
        } else if (element === 'ApproveDischargeVietQR') {
          this.checkDischargeVietQR = true;
        } else {
          this.contractDetail.shopifyFee = undefined;
        }
      });
      if (!this.checkInternationalFee && this.checkEditContractDetail) {
        this.contractDetail.feeTransInternational = undefined;
      } else if (this.checkInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeTransInternational = 7150;
      }
      if (!this.checkInternationalFee && !this.checkDomesticFee && !this.checkAppFee && !this.checkDischargeVietQR && this.checkEditContractDetail) {
        this.contractDetail.feeService = undefined;
        this.handleFee('REMOVE');
      }
      if (!this.checkDisplayDomesticFee && this.checkEditContractDetail) {
        this.contractDetail.approveCardType01International = undefined;
        this.contractDetail.insideDomestic = undefined;
      } else if (this.checkInternationalFee && this.checkDisplayDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.approveCardType01International = '2.75';
      }
      if (!this.checkAmericanExpress && this.checkEditContractDetail) {
        this.contractDetail.americanExpress01International = undefined;
      } else if (this.checkAmericanExpress && this.checkDisplayDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.americanExpress01International = '3.30';
      }
      if (!this.checkDisplayInternationalFee && this.checkEditContractDetail) {
        this.contractDetail.approveCardType02International = undefined;
        this.contractDetail.outsideDomestic = undefined;
      } else if (this.checkInternationalFee && this.checkDisplayInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.approveCardType02International = '3.30'
      }

      if (!this.checkAppFee && this.checkEditContractDetail) {
        this.contractDetail.percentQrMobile = undefined;
        this.contractDetail.percentQrGrab = undefined;
        this.contractDetail.percentQrShopee = undefined;
        this.contractDetail.percentQrZalo = undefined;
        this.contractDetail.percentQrOther = undefined;
        this.contractDetail.percentQrMoMo = undefined;
      } else if (this.checkAppFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.percentQrMobile = '1.10';
        this.contractDetail.percentQrGrab = '1.10';
        this.contractDetail.percentQrShopee = '1.10';
        this.contractDetail.percentQrZalo = '1.10';
        this.contractDetail.percentQrMoMo = '1.75';
        this.contractDetail.percentQrOther = '1.10';
      }

      if (!this.checkAmericanExpress && this.checkEditContractDetail) {
        this.contractDetail.americanExpress02International = undefined;
      } else if (this.checkAmericanExpress && this.checkDisplayInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.americanExpress02International = '3.50';
      }
      if (!this.checkDomesticFee && this.checkEditContractDetail) {
        this.contractDetail.feeTransDomesticAndApp = undefined;
        this.contractDetail.feePaymentDomesticAndApp = undefined;
      } else if (this.checkDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeTransDomesticAndApp = 1760;
        this.contractDetail.feePaymentDomesticAndApp = '1.1';
      }

      if (!this.checkAppFee && this.checkEditContractDetail) {
        this.contractDetail.feeApp = undefined;
        this.contractDetail.feePaymentApp = undefined;
      } else if (this.checkAppFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeApp = '1,760';
        this.contractDetail.feePaymentApp = '1.1';
      }

      if (!this.checkDischargeVietQR && this.checkEditContractDetail) {
        this.contractDetail.feeVietQR = undefined;
        this.contractDetail.percentVietQR = undefined;
      } else if (this.checkDischargeVietQR && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeVietQR = '2,200';
        this.contractDetail.percentVietQR = 'Không áp dụng phí thanh toán';
      }
    } else {
      this.handleFee('REMOVE');
    }
    // this.checkPhamViTheQuocTe();
  }

  checkInstallment(): boolean {
    var checkInstallment = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'ApproveInstallment') {
          checkInstallment = true;
        }
      });
      if (this.contractDetail.cardListArray.length === 1 && checkInstallment !== false) {
        this.contractDetail.feeService = undefined;
        this.checkInternationalFee = false;
        this.checkDomesticFee = false;
        this.checkAppFee = false;
      }
    }
    return checkInstallment;
  }

  changeFeeType(inputType: string) {

    if (this.checkEditContractDetail === true) {
      if (inputType === 'normalFee') {
        this.handleFee('REMOVE');
        this.contractDetail.autoFillStandardFee = undefined;
        this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
      } else if (inputType === 'specialFee') {
        this.handleFee('REMOVE');
        this.contractDetail.autoFillStandardFee = undefined;
        this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
      } else {
        if (this.contractDetail.autoFillStandardFee !== undefined && this.contractDetail.autoFillStandardFee !== null && this.contractDetail.autoFillStandardFee.length > 0) {
          // console.log('autoFillStandardFee'  + this.contractDetail.autoFillStandardFee);
          this.handleFee('FILL');
        } else {
          this.handleFee('REMOVE');
        }
      }
    }
  }

  handleFee(inputType: string) {
    if (inputType === 'FILL') {
      this.contractDetail.registerFee = '2400000';
      this.contractDetail.monthFee = 'Miễn phí';
      this.checkFeeType();
    } else {
      this.contractDetail.registerFee = undefined;
      this.contractDetail.monthFee = undefined;
      this.contractDetail.feeTransDomesticAndApp = undefined;
      this.contractDetail.feeApp = undefined;
      this.contractDetail.feeVietQR = undefined;
      this.contractDetail.feePaymentDomesticAndApp = undefined;
      this.contractDetail.feeTransInternational = undefined;
      this.contractDetail.approveCardType01International = undefined;
      this.contractDetail.americanExpress01International = undefined;
      this.contractDetail.approveCardType02International = undefined;
      this.contractDetail.americanExpress02International = undefined;
      this.contractDetail.autoFillStandardFee = undefined;

      this.contractDetail.percentQrMobile = undefined;
      this.contractDetail.percentQrGrab = undefined;
      this.contractDetail.percentQrShopee = undefined;
      this.contractDetail.percentQrZalo = undefined;
      this.contractDetail.percentQrOther = undefined;
      this.contractDetail.percentQrMoMo = undefined;
      this.contractDetail.inforOther = undefined;
      this.contractDetail.percentVietQR = undefined;

      this.contractDetail.bnplFee = undefined;
      if(this.checkShopifyFee !== true){
        this.contractDetail.shopifyFee = undefined;
      }
    }
  }

  checkPhamViTheQuocTe(): boolean {
    var checkPhamVi = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        // if(element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'AmericanExpress'){
        if (element === 'ApproveDomesticCard' || element === 'ApproveInternationalCard') {
          checkPhamVi = true;
        }
        // }
      });
    }
    return checkPhamVi;
  }

  checkOthers() {
    if (this.contractDetail.hinhThucThuPhi && this.contractDetail.hinhThucThuPhi !== 'Khac') {
      this.contractDetail.inputHinhThucThuPhiKhac = undefined;
    } else if (this.contractDetail.tgTamUngSelection && this.contractDetail.tgTamUngSelection !== 'other') {
      this.contractDetail.inputTgTamUngKhac = undefined;
    }
  }

  checkOthersAccNumber() {
    if (this.contractDetail.accountNumber && (this.contractDetail.accountNumber !== 'Khac' || this.contractDetail.accountNumber !== 'other')) {
      this.contractDetail.accountNumberOther = undefined;
    }
  }

  checkKyQuyType() {
    this.contractDetail.khoanDamBaoSelection = undefined;
    if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKhac') {
      this.cancelAutoFillKyQuy();
      this.contractDetail.accountNumber = 'vietinbank';
    } else if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyStandard') {
      this.contractDetail.inputKyQuyKhac = undefined;
      this.contractDetail.keepPercent = undefined;
      this.contractDetail.khoanDamBaoInput = undefined;
      this.contractDetail.kyHanFD = undefined;
      this.contractDetail.kyQuyAutoFill = undefined;
      this.checkAutoFill = true;
      this.contractDetail.accountNumber = 'vietinbank';
    } else if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKeep') {
      this.contractDetail.inputKyQuyKhac = undefined;
      this.contractDetail.khoanDamBaoInput = undefined;
      this.contractDetail.kyHanFD = undefined;
      this.contractDetail.kyQuyAutoFill = undefined;
      this.checkAutoFill = true;
      this.contractDetail.accountNumber = 'vietinbank';
    } else {
      this.cancelAutoFillKyQuy();
    }
  }

  cancelAutoFillKyQuy() {
    this.contractDetail.kyQuyAutoFill = undefined;
    this.contractDetail.kyHanFD = undefined;
    this.contractDetail.khoanDamBaoInput = undefined;
    this.contractDetail.stkGiaiKhoanh = undefined;
    this.contractDetail.openByBank = undefined;
    this.checkAutoFill = false;
  }

  autoFillKyQuy() {
    if (this.contractDetail.kyQuyAutoFill && this.contractDetail.kyQuyAutoFill !== null && this.contractDetail.kyQuyAutoFill.length > 0) {
      this.contractDetail.khoanDamBaoInput = ********;
      this.contractDetail.kyHanFD = '6 tháng';
      if (this.contractDetail.subTableMerchant && this.contractDetail.subTableMerchant.length > 0) {
        this.contractDetail.stkGiaiKhoanh = this.contractDetail.subTableMerchant[0].accountNumber;
        this.contractDetail.openByBank = this.contractDetail.subTableMerchant[0].bank;
      }

      if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKeep') {
        this.contractDetail.keepPercent = 100;
      } else {
        this.contractDetail.keepPercent = undefined;
      }
    }
  }

  checkKyQuy() {
    if (this.contractDetail.khoanDamBaoSelection && this.contractDetail.khoanDamBaoSelection === 'Mien') {
      this.contractDetail.kyQuyType = undefined;
      this.contractDetail.inputKyQuyKhac = undefined;
      this.cancelAutoFillKyQuy();
    }
  }

  deleteMerchant() {
    this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }

  uncheckManual() {
    if (this.contractDetail.autoFillStandardFee == 'autoFillStandardFee') {
      this.contractDetail.autoFillStandardFee = undefined;
    }
    if (this.contractDetail.kyQuyAutoFill == 'kyQuyAutoFill') {
      this.contractDetail.kyQuyAutoFill = undefined;
    }
  }

  checkDomesticPaymentFee() {
    var checkDomesticCard = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'ApproveOnepayDomesticCard') {
          checkDomesticCard = true;
        }
      });
    }
    return checkDomesticCard;
  }

}
