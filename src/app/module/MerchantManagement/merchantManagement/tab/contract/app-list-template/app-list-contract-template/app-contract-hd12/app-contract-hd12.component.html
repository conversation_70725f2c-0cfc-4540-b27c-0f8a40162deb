<div id="form-hd12">
    <form class="contract-information-form" #contractHD12Form="ngForm">
        <div *ngIf="isActive('general_contract_information') || isCreateOrEdit">
            <div class="row info-panel-height format-fields" style="margin-top: 10px !important;">
                <div class="form-group col-md-6" style="margin-top: 5px !important;">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="contractNumber" pInputTextarea
                            id="contractNumber" type="text" [(ngModel)]="contractOriginal.contractNumber"
                            #contractNumberT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractOriginal.contractNumber = contractOriginal.contractNumber.trim()"></textarea>
                        <label class="label-custom fixPositionLabel" for="contractNumber"><PERSON><PERSON>ồng</label>
                    </span>
                </div>

                <div class="form-group col-md-3">
                    <span class="input-group">
                        <p-calendar appendTo="body" [showIcon]="true" [style]="{'width':'100%'}" dateFormat="dd/mm/yy"
                            [(ngModel)]="contractOriginal.signatureDate" #signatureDateT="ngModel" name="signatureDate"
                            hideOnDateTimeSelect="true"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-calendar>
                        <label class="label-custom fixPositionLabel" for="signatureDate">Ngày ký</label>
                    </span>
                </div>

                <div class="form-group col-md-3">
                    <span class="input-group">
                        <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="carrerList"
                            [(ngModel)]="contractDetail.carrer" #carrerT="ngModel" dropdownIcon=" pi pi-sort-down"
                            name="carrer" id="carrer" [autoDisplayFirst]="false"
                            [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                        </p-dropdown>
                        <label [ngClass]="(contractDetail.carrer === undefined || contractDetail.carrer === '' || contractDetail.carrer === null) && validatePoint === true
                    ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'" for="carrer">Ngành
                            nghề *</label>
                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="businessName" pInputTextarea
                            id="businessName" type="text" [(ngModel)]="contractOriginal.businessName"
                            #businessNameT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractOriginal.businessName = contractOriginal.businessName.trim()"></textarea>
                        <label [ngClass]="(contractOriginal.businessName === undefined || contractOriginal.businessName === '' || contractOriginal.businessName === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="businessName">Tên đăng ký kinh doanh *</label>
                    </span>
                </div>

                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="shortName" pInputTextarea
                            id="shortName" type="text" [(ngModel)]="contractDetail.shortName" #shortNameT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.shortName = contractDetail.shortName.trim()"></textarea>
                        <label class="label-custom fixPositionLabel" for="shortName">Tên viết tắt</label>
                    </span>
                </div>

            </div>
            <div class="row info-panel-height format-fields">

                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="addressBusiness" pInputTextarea
                            id="addressBusiness" type="text" [(ngModel)]="contractDetail.addressBusiness"
                            #addressBusinessT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.addressBusiness = contractDetail.addressBusiness.trim()"></textarea>
                        <label [ngClass]="(contractDetail.addressBusiness === undefined || contractDetail.addressBusiness === '' || contractDetail.addressBusiness === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="addressBusiness">Địa chỉ đăng ký kinh doanh
                            *</label>
                    </span>
                </div>

                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="addressOffice" pInputTextarea
                            id="addressOffice" type="text" [(ngModel)]="contractDetail.addressOffice"
                            #addressOfficeT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.addressOffice = contractDetail.addressOffice.trim()"></textarea>
                        <label [ngClass]="(contractDetail.addressOffice === undefined || contractDetail.addressOffice === '' || contractDetail.addressOffice === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="addressOffice">Địa chỉ VPGD *</label>
                    </span>
                </div>

            </div>
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-3">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="website" pInputTextarea
                            id="website" type="text" [(ngModel)]="contractDetail.website" #websiteT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.website = contractDetail.website.trim()"></textarea>
                        <label class="label-custom fixPositionLabel" for="website">Website</label>
                    </span>
                </div>


                <div class="form-group col-md-3">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="phone" pInputTextarea id="phone"
                            type="text" [(ngModel)]="contractDetail.phone" #phoneT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.phone = contractDetail.phone.trim()"></textarea>
                        <label class="label-custom fixPositionLabel" for="phone">Số điện thoại</label>
                    </span>
                </div>

                <div class="form-group col-md-3">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="numberBusiness" pInputTextarea
                            id="numberBusiness" type="text" [(ngModel)]="contractDetail.numberBusiness"
                            #numberBusinessT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.numberBusiness = contractDetail.numberBusiness.trim()"></textarea>
                        <label [ngClass]="(contractDetail.numberBusiness === undefined || contractDetail.numberBusiness === '' || contractDetail.numberBusiness === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="numberBusiness">Số đăng ký kinh doanh *</label>
                    </span>
                </div>

                <div class="form-group col-md-3">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="accountBank" pInputTextarea
                            id="accountBank" type="text" [(ngModel)]="contractDetail.accountBank"
                            #accountBankT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.accountBank = contractDetail.accountBank.trim()"></textarea>
                        <label [ngClass]="(contractDetail.accountBank === undefined || contractDetail.accountBank === '' || contractDetail.accountBank === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="accountBank">Tài khoản ngân hàng *</label>
                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">

                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="signaturer" pInputTextarea
                            id="signaturer" type="text" [(ngModel)]="contractDetail.signaturer" #signaturerT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.signaturer = contractDetail.signaturer.trim()"></textarea>
                        <label [ngClass]="(contractDetail.signaturer === undefined || contractDetail.signaturer === '' || contractDetail.signaturer === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="signaturer">Người ký *</label>
                    </span>
                </div>


                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="position" pInputTextarea
                            id="position" type="text" [(ngModel)]="contractDetail.position" #positionT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.position = contractDetail.position.trim()"></textarea>
                        <label [ngClass]="(contractDetail.position === undefined || contractDetail.position === '' || contractDetail.position === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="position">Chức vụ *</label>
                    </span>
                </div>

            </div>
        </div>
        <div *ngIf="isActive('fee_contract_information') || isCreateOrEdit">
            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-12" style="margin-top: -10px !important;">
                    <span
                        style="color: #0089D0; margin-top: 20px !important;font-weight: bold;font-size: 12px !important;">
                        Phí dịch vụ thu hộ
                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-9" style="margin-top: -20px !important;">
                    <label [ngClass]="(contractDetail.cardType === undefined || contractDetail.cardType === '' || contractDetail.cardType === null) && validatePoint === true
                ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'" for="cardType"
                        style="margin-left: 0px !important;">Loại phí *</label>
                    <span class="input-group" style="margin-top: -3px !important;">

                        <div>
                            <p-radioButton name="cardType" [(ngModel)]="contractDetail.cardType" value="monthFee"
                                inputId="monthFee"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="monthFee" style="margin-left: 5px !important;font-size: 12px !important;"
                                class="label-style">Phí tháng</label>

                            <p-radioButton name="cardType" [(ngModel)]="contractDetail.cardType" value="transactionFee"
                                inputId="transactionFee"
                                [disabled]="contractOriginal.state === 'approved' || checkEditContractDetail === false">
                            </p-radioButton>
                            <label for="transactionFee"
                                style="margin-left: 5px !important;font-size: 12px !important;">Phí
                                giao dịch</label>
                        </div>

                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-12">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="feeForCard" pInputTextarea
                            id="feeForCard" type="text" [(ngModel)]="contractDetail.feeForCard" #feeForCardT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.feeForCard = contractDetail.feeForCard.trim()"></textarea>
                        <label [ngClass]="(contractDetail.feeForCard === undefined || contractDetail.feeForCard === '' || contractDetail.feeForCard === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="feeForCard">Mức phí *</label>
                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">
                <div class="form-group col-md-12">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="ptTamUngMoi" pInputTextarea
                            id="ptTamUngMoi" type="text" [(ngModel)]="contractDetail.ptTamUngMoi"
                            #ptTamUngMoiT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.ptTamUngMoi = contractDetail.ptTamUngMoi.trim()"></textarea>
                        <label [ngClass]="(contractDetail.ptTamUngMoi === undefined || contractDetail.ptTamUngMoi === '' || contractDetail.ptTamUngMoi === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="ptTamUngMoi">Phương thức thu phí *</label>
                    </span>
                </div>
            </div>
        </div>
        <div *ngIf="isActive('other_contract_information') || isCreateOrEdit">
            <!-- Child Table -->

            <div id="table-hd04" style="margin-bottom: 15px !important;margin-top: -10px !important;"
                class="row info-panel-height format-fields">
                <div class="form-group col-md-12">
                    <app-child-table-merchant [tableData]="contractDetail.subTableMerchant"
                        [validatePoint]="validatePoint" [typeContract]="'GHICO'"
                        [checkEditContractDetail]="checkEditContractDetail" (deleteMerchantById)="deleteMerchant()">
                    </app-child-table-merchant>
                </div>
            </div>

            <div class="row info-panel-height format-fields">

                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="ptTamUng" pInputTextarea
                            id="ptTamUng" type="text" [(ngModel)]="contractDetail.ptTamUng" #ptTamUngT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.ptTamUng = contractDetail.ptTamUng.trim()"></textarea>
                        <label [ngClass]="(contractDetail.ptTamUng === undefined || contractDetail.ptTamUng === '' || contractDetail.ptTamUng === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="ptTamUng">Phương thức ghi có *</label>
                    </span>
                </div>


                <div class="form-group col-md-6">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="tgTamUng" pInputTextarea
                            id="tgTamUng" type="text" [(ngModel)]="contractDetail.tgTamUng" #tgTamUngT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.tgTamUng = contractDetail.tgTamUng.trim()"></textarea>
                        <label [ngClass]="(contractDetail.tgTamUng === undefined || contractDetail.tgTamUng === '' || contractDetail.tgTamUng === null) && validatePoint === true
                        ? 'label-custom fixPositionLabel inValid' : 'label-custom fixPositionLabel'"
                            for="tgTamUng">Thời gian ghi có *</label>
                    </span>
                </div>
            </div>

            <div class="row info-panel-height format-fields">

                <div class="form-group col-md-12">
                    <span class="input-group">
                        <textarea rows="1" autoResize="true" class="form-control" name="otherInfo" pInputTextarea
                            id="otherInfo" type="text" [(ngModel)]="contractDetail.otherInfo" #otherInfoT="ngModel"
                            [readOnly]="contractOriginal.state === 'approved' || checkEditContractDetail === false"
                            (ngModelChange)="contractDetail.otherInfo = contractDetail.otherInfo.trim()"></textarea>
                        <label class="label-custom fixPositionLabel" for="otherInfo">Thông tin khác</label>
                    </span>
                </div>
            </div>
        </div>
    </form>
</div>