import { Component, OnInit, Input, EventEmitter, Output, ViewChild, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

import { AppChildTablePauseComponent } from '../../app-child-table/app-child-table-pause/app-child-table-pause.component';

@Component({
  selector: 'app-sub-contract-BBTL',
  templateUrl: './app-sub-contract-BBTL.component.html',
  styleUrls: ['./app-sub-contract-BBTL.component.css']
})

export class AppSubContractBBTLComponent implements OnInit, OnChanges {

  @Input() currentPartner: Partner;
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() carrerList: [];
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;

  @ViewChild(AppChildTablePauseComponent) childTable: AppChildTablePauseComponent;

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;

      if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
        this.contractOriginal.signatureDate = null;
        this.contractOriginal.rangeDate = null;
      } else {
        if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
          this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
        }
        if (this.contractOriginal.rangeDate !== undefined && this.contractOriginal.rangeDate !== '') {
          this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
        }
      }

      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
    }

    if (this.contractDetail !== undefined) {
      if (this.contractDetail.cardList !== undefined && this.contractDetail.cardList !== '') {
        this.contractDetail.cardListArray = this.contractDetail.cardList.split(',');
      }
    }
    if (this.contractDetail.subTablePause == undefined ){
      this.contractDetail.subTablePause = [];
    }
  }

  validateForm(): boolean {
    if (this.contractDetail.changeContent === undefined || this.contractDetail.changeContent === null || this.contractDetail.changeContent === ''
      || this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
      || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
      || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
      || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
      || this.contractDetail.peopleId === undefined || this.contractDetail.peopleId === null || this.contractDetail.peopleId === ''
      || this.contractDetail.subTablePause === undefined || this.contractDetail.subTablePause === null || this.contractDetail.subTablePause.length === 0) {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = true;
      return true;
    }
  }

  deleteMerchant() {
    if (this.childTable && this.childTable.idDelete) {
      this.deleteMerchantById.emit({ id: this.childTable.idDelete });
    } else {
      console.log('Khong the xoa: childTable hoac idDelete khong ton tai');
    }
  }

}
