import { Component, OnInit, Input, EventEmitter, Output, ViewChild, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

import { AppChildTableMerchantComponent } from '../../app-child-table/app-child-table-merchant/app-child-table-merchant.component';
@Component({
  selector: 'app-sub-contract-pl02',
  templateUrl: './app-sub-contract-pl02.component.html',
  styleUrls: ['./app-sub-contract-pl02.component.css']
})

export class AppSubContractPL02Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() carrerList: [];
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;

  @ViewChild(AppChildTableMerchantComponent) childTable: AppChildTableMerchantComponent;

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;

      if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
        this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
      }
      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
    }

    if (this.contractDetail !== undefined) {
      if (this.contractDetail.cardList !== undefined && this.contractDetail.cardList !== '') {
        this.contractDetail.cardListArray = this.contractDetail.cardList.split(',');
      }

    }

    if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
      this.contractOriginal.contractNumber = undefined;
      this.contractOriginal.signatureDate = null;
      this.contractDetail.rangeDate = null;
      this.contractDetail.subTableMerchant = [];
      this.contractDetail.khoanDamBao = undefined;
      this.contractDetail.stkGiaiKhoanh = undefined;
      this.contractDetail.kyHanFD = undefined;
    }

  }

  validateForm(): boolean {
    if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
      || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
      || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
      || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''

      || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant.length === 0
      || this.contractDetail.tgTamUng === undefined || this.contractDetail.tgTamUng === null || this.contractDetail.tgTamUng === ''
      || this.contractDetail.ptTamUng === undefined || this.contractDetail.ptTamUng === null || this.contractDetail.ptTamUng === ''
      || this.contractDetail.monthFee === undefined || this.contractDetail.monthFee === null || this.contractDetail.monthFee === ''
      || this.contractDetail.feeForCard === undefined || this.contractDetail.feeForCard === null || this.contractDetail.feeForCard === ''
      // || this.contractDetail.khoanDamBao === undefined || this.contractDetail.khoanDamBao === null || this.contractDetail.khoanDamBao === ''
      || this.contractDetail.cardType === undefined || this.contractDetail.cardType === null || this.contractDetail.cardType === ''
      || this.contractDetail.cardTransactionFee === undefined || this.contractDetail.cardTransactionFee === null || this.contractDetail.cardTransactionFee === '') {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = false;
      return true;
    }
  }

  deleteMerchant() {
    this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }

}
