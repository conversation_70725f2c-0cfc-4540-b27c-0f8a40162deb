import { Component, OnInit, Input, EventEmitter, Output, ViewChild, SimpleChanges, OnChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';

import { AppChildTableBbntComponent } from '../../app-child-table/app-child-table-bbnt/app-child-table-bbnt.component';

@Component({
  selector: 'app-sub-contract-BBTLv2',
  templateUrl: './app-sub-contract-BBTLv2.component.html',
  styleUrls: ['./app-sub-contract-BBTLv2.component.css']
})

export class AppSubContractBBTLv2Component implements OnInit, OnChanges {

  @Input() currentPartner: Partner;
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() carrerList: [];
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() parentContractCode: string;
  @Input() parentContractDetail: any;
  @Input() parentContractNumber: string;
  @Input() parentSignatureDate: string;
  @Input() danhXungList: any;
  @ViewChild(AppChildTableBbntComponent) childTable: AppChildTableBbntComponent;

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  public state = '';
  public validatePoint = false;

  public isCreateOrEdit = false;
  public showParentContractInfo = true;

  public checkAutoFill = false;

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id === 0) this.isCreateOrEdit = true;
      else this.isCreateOrEdit = false;

      if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
        this.contractOriginal.signatureDate = null;
      } else {
        if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
          this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
        }
      }

      if (this.contractOriginal.rangeDate !== undefined && this.contractOriginal.rangeDate !== '') {
        this.contractOriginal.rangeDate = new Date(this.contractOriginal.rangeDate);
      }

      if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
        this.state = this.contractOriginal.state;
      }
    }

    if (this.contractDetail !== undefined) {
      if (this.contractDetail.cardList !== undefined && this.contractDetail.cardList !== '') {
        this.contractDetail.cardListArray = this.contractDetail.cardList.split(',');
      }

      if (this.contractDetail.ngayThanhLy) {
        this.contractDetail.ngayThanhLy = new Date(this.contractDetail.ngayThanhLy);
      }
      else {
        this.contractDetail.ngayThanhLy = undefined;
      }

      if (this.contractDetail.subTablePause === undefined) {
        this.contractDetail.subTablePause = [];
      }
    }
    


    

    console.log('Parent Contract Code:', this.getParentContractCode());
  }

  /**
   * Lấy mã hợp đồng (contractCode) của contract chính (parent contract)
   * @returns mã hợp đồng của contract chính
   */
  getParentContractCode(): string {
    return this.parentContractCode || '';
  }

  /**
   * Lấy số hợp đồng của contract chính (parent contract)
   * @returns số hợp đồng của contract chính
   */
  getParentContractNumber(): string {
    return this.parentContractNumber || '';
  }

  /**
   * Lấy ngày ký hợp đồng của contract chính (parent contract)
   * @returns ngày ký hợp đồng của contract chính
   */
  getParentSignatureDate(): string {
    return this.parentSignatureDate || '';
  }

  /**
   * Kiểm tra xem có thông tin hợp đồng chính hay không
   * @returns true nếu có thông tin hợp đồng chính
   */
  hasParentContract(): boolean {
    return !!this.parentContractCode && !!this.parentContractNumber;
  }

  /**
   * Chuyển đổi trạng thái hiển thị thông tin hợp đồng chính
   */
  toggleParentContractInfo(): void {
    this.showParentContractInfo = !this.showParentContractInfo;
  }

  validateForm(): boolean {
    if (this.parentContractCode === 'HD14') {
      if (this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === ''
        || this.contractOriginal.representative === undefined || this.contractOriginal.representative === null || this.contractOriginal.representative === ''
        || this.contractDetail.permanentAddress === undefined || this.contractDetail.permanentAddress === null || this.contractDetail.permanentAddress === ''
        || this.contractDetail.email === undefined || this.contractDetail.email === null || this.contractDetail.email === ''
        || this.contractDetail.peopleId === undefined || this.contractDetail.peopleId === null || this.contractDetail.peopleId === ''
        || this.contractDetail.issuedBy === undefined || this.contractDetail.issuedBy === null || this.contractDetail.issuedBy === ''
        || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant === null || this.contractDetail.subTableMerchant.length === 0
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = true;
        return true;
      }
    } else {
        if (this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
          || this.contractDetail.addressBusiness === undefined || this.contractDetail.addressBusiness === null || this.contractDetail.addressBusiness === ''
        || this.contractDetail.numberBusiness === undefined || this.contractDetail.numberBusiness === null || this.contractDetail.numberBusiness === ''
        || this.contractDetail.danhXung === undefined || this.contractDetail.danhXung === null || this.contractDetail.danhXung === ''
        || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
        || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
        || this.contractDetail.subTableMerchant === undefined || this.contractDetail.subTableMerchant === null || this.contractDetail.subTableMerchant.length === 0
      ) {
        this.validatePoint = true;
        return false;
      } else {
        this.validatePoint = true;
        return true;
      }
    }
  }

  deleteMerchant() {
      this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }


  uncheckManual() {
    if (this.contractDetail.autoFillStandardFee == 'autoFillStandardFee') {
      this.contractDetail.autoFillStandardFee = undefined;
    }
    if (this.contractDetail.kyQuyAutoFill == 'kyQuyAutoFill') {
      this.contractDetail.kyQuyAutoFill = undefined;
    }
  }

  checkKyQuy() {
    if (this.contractDetail.khoanDamBaoSelection && this.contractDetail.khoanDamBaoSelection === 'Mien') {
      this.contractDetail.kyQuyType = undefined;
      this.contractDetail.inputKyQuyKhac = undefined;
      this.cancelAutoFillKyQuy();
    }
  }

  cancelAutoFillKyQuy() {
    this.contractDetail.kyQuyAutoFill = undefined;
    this.contractDetail.kyHanFD = undefined;
    this.contractDetail.khoanDamBaoInput = undefined;
    this.contractDetail.stkGiaiKhoanh = undefined;
    this.contractDetail.openByBank = undefined;
    this.checkAutoFill = false;
  }
} 