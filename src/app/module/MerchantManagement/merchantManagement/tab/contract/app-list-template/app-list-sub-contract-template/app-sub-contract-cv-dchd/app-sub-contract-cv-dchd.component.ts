import { Component, OnInit, Input, Output, EventEmitter, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { Globals } from '@core/global';
import { Partner } from 'app/model/partner';
import { AppChildTableMerchantIdV3Component } from '../../app-child-table/app-child-table-merchant-id-v3/app-child-table-merchant-id-v3.component';

@Component({
  selector: 'app-sub-contract-cv-dchd',
  templateUrl: './app-sub-contract-cv-dchd.component.html',
  styleUrls: ['./app-sub-contract-cv-dchd.component.css']
})
export class AppSubContractCvDchdComponent implements OnInit, OnChanges {

  @Input() currentPartner: Partner;
  @Input() isActive: (functionName: string) => Function;
  @Input() contractId: string;
  @Input() checkEditContractDetail: boolean;
  @Input() contractDetail;
  @Input() contractOriginal;
  @Input() feeInstallmentList;
  @Input() parentContractDetail;

  public state = '';
  public cardType: string;
  public validatePoint = false;

  public isCreateOrEdit = false;
  adjustmentOptions: any[] = [];
  contractContentBasisOptions: any[] = [
    { label: 'Hợp đồng', value: 'Hợp đồng' },
    { label: 'Phụ lục', value: 'Phụ lục' }
  ];

  guaranteeHoldingTypeFilterList: any[] = [
    { label: 'Sending', value: 'sending' },
    { label: 'Wait for approve', value: 'wait' },
    { label: 'Sent', value: 'sent' }
  ];

  constructor(public global: Globals) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.checkEditContractDetail && changes.checkEditContractDetail.currentValue) {
      this.isCreateOrEdit = true;
    } else if (changes.checkEditContractDetail) {
      this.isCreateOrEdit = false;
    }
  }

  ngOnInit() {
    this.adjustmentOptions = [
      { label: 'Điều chỉnh phí dịch vụ', value: 'Điều chỉnh phí dịch vụ' },
      { label: 'Điều chỉnh khoản đảm bảo thanh toán', value: 'Điều chỉnh khoản đảm bảo thanh toán' },
      { label: 'Điều chỉnh thời gian tạm ứng', value: 'Điều chỉnh thời gian tạm ứng' },
      { label: 'Điều chỉnh hình thức thu phí', value: 'Điều chỉnh hình thức thu phí' },
      { label: 'Điều chỉnh tài khoản tạm ứng', value: 'Điều chỉnh tài khoản tạm ứng' },
      { label: 'Nội dung khác', value: 'Nội dung khác' }
    ];


    if (this.contractOriginal !== undefined) {
      // Nếu chưa có id thì tức là đang tạo 1 hđ mới
      if (this.contractOriginal.id === undefined || this.contractOriginal.id === '' || this.contractOriginal.id == 0) {
        this.isCreateOrEdit = true;


        console.log('this.parentContractDetail:', this.parentContractDetail);
        console.log('this.contractOriginal:', this.contractOriginal);
        console.log('this.contractDetail:', this.contractDetail);
        console.log('contractOriginal.businessName:', this.contractOriginal.businessName);
        // // Kiểm tra và khởi tạo contractOriginal nếu cần
        // if (!this.contractOriginal) {
        //   this.contractOriginal = {};
        // }
        // // Kiểm tra và khởi tạo parentContractDetail nếu cần
        // if (this.parentContractDetail) {
        //   // Fill từ hợp đồng cha khi tạo mới
        //   this.contractOriginal.businessName = this.parentContractDetail.businessName || ''; // Fill businessName
        //   this.contractDetail.cardListArray = this.parentContractDetail.cardListArray || []; // Fill cardListArray
        // }

        // //fill từ hợp đồng cha khi tạo mới
        // this.contractOriginal.businessName = this.parentContractDetail.businessName; //fill businessName 
        // this.contractDetail.cardListArray = this.parentContractDetail.cardListArray; //fill cardListArray
      }
      else this.isCreateOrEdit = false;
    }
    if (this.contractOriginal.signatureDate !== undefined && this.contractOriginal.signatureDate !== '') {
      this.contractOriginal.signatureDate = new Date(this.contractOriginal.signatureDate);
    }

    if (this.contractOriginal.state !== undefined && this.contractOriginal.state !== '') {
      this.state = this.contractOriginal.state;
    }
    if (this.contractOriginal.id === undefined || this.contractOriginal.id === null || this.contractOriginal.id === '' || this.contractOriginal.id === '0') {
      this.contractOriginal.contractNumber = undefined;
      this.contractOriginal.signatureDate = null;
      this.contractDetail.rangeDate = null;
    }

    this.changeCardType() ;
    this.checkShowDetail();
  }

  validateForm(): boolean {
    if (this.contractOriginal.contractNumber === undefined || this.contractOriginal.contractNumber === null || this.contractOriginal.contractNumber === ''
      || this.contractOriginal.businessName === undefined || this.contractOriginal.businessName === null || this.contractOriginal.businessName === ''
      || this.contractDetail.signaturer === undefined || this.contractDetail.signaturer === null || this.contractDetail.signaturer === ''
      || this.contractDetail.position === undefined || this.contractDetail.position === null || this.contractDetail.position === ''
      || this.contractDetail.adjustment === undefined || this.contractDetail.adjustment === null || this.contractDetail.adjustment === ''
      || this.contractDetail.contractContentBasis === undefined || this.contractDetail.contractContentBasis === null || this.contractDetail.contractContentBasis === ''
    ) {
      this.validatePoint = true;
      return false;
    } else {
      this.validatePoint = false;
      return true;
    }
  }

  public showFee = false;
  public showGuaranteeHolding = false;
  public showAdvanceTime = false;
  public showFeeType = false;
  public showAdvanceAccount = false;
  public showOtherContent = false;
  onAdjustmentChange(event: any) {
    this.changeCardType() ;
    if (Array.isArray(event.value)) {
      this.showFee = event.value.includes('Điều chỉnh phí dịch vụ');
      this.showGuaranteeHolding = event.value.includes('Điều chỉnh khoản đảm bảo thanh toán');
      this.showAdvanceTime = event.value.includes('Điều chỉnh thời gian tạm ứng');
      this.showFeeType = event.value.includes('Điều chỉnh hình thức thu phí');
      this.showAdvanceAccount = event.value.includes('Điều chỉnh tài khoản tạm ứng');
      this.showOtherContent = event.value.includes('Nội dung khác');
    } else {
      console.error('Event value is not an array:', event.value);
    }
  }
/**
 * Kiểm tra và hiển thị các mục điều chỉnh trên chi tiết của phụ lục hợp đồng
 */
  checkShowDetail(){
    if(this.contractDetail?.adjustment){
      this.contractDetail.adjustment.includes('Điều chỉnh phí dịch vụ') ? this.showFee = true : this.showFee = false;
      this.contractDetail.adjustment.includes('Điều chỉnh khoản đảm bảo thanh toán') ? this.showGuaranteeHolding = true : this.showGuaranteeHolding = false;
      this.contractDetail.adjustment.includes('Điều chỉnh thời gian tạm ứng') ? this.showAdvanceTime = true : this.showAdvanceTime = false;
      this.contractDetail.adjustment.includes('Điều chỉnh hình thức thu phí') ? this.showFeeType = true : this.showFeeType = false;
      this.contractDetail.adjustment.includes('Điều chỉnh tài khoản tạm ứng') ? this.showAdvanceAccount = true : this.showAdvanceAccount = false;
      this.contractDetail.adjustment.includes('Nội dung khác') ? this.showOtherContent = true : this.showOtherContent = false;
    }
  }

  changeCardType() {
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.cardType = undefined;
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'ApplePay' || element === 'GooglePay' || element === 'SamsungPay') {
          if (this.cardType !== undefined && this.cardType !== '') {
            this.cardType = this.cardType + ', ' + element;
          } else {
            this.cardType = element;
          }
        }
      });
    }
    this.checkFeeType();
  }

  public labelChildTable = 'Phí dịch vụ trả góp';
  // public validatePoint = false;
  public checkInternationalFee = false;
  public checkDomesticFee = false;
  public checkAppFee = false;
  public checkAmericanExpress = false;
  public checkDisplayInternationalFee = false;
  public checkDisplayDomesticFee = false;
  public checkBnplFee = false;
  public checkShopifyFee = false;
  public checkAutoFill = false;
  public checkDischargeVietQR = false;

  /**
     * kiểm tra và cập nhật các loại phí dựa trên danh sách các loại thẻ
     * @param inputType 
     */
  checkFeeType() {
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.checkInternationalFee = false;
      this.checkDomesticFee = false;
      this.checkAppFee = false;
      this.checkAmericanExpress = false;
      this.checkDisplayInternationalFee = false;
      this.checkDisplayDomesticFee = false;
      this.checkBnplFee = false;
      this.checkShopifyFee = false;
      this.checkDischargeVietQR = false;
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'AmericanExpress' || element === 'ApplePay' || element === 'GooglePay' || element === 'SamsungPay') {
          this.checkInternationalFee = true;
          if (element === 'AmericanExpress') {
            this.checkAmericanExpress = true;
          }
        } else if (element === 'ApproveOnepayDomesticCard') {
          this.checkDomesticFee = true;
        } else if (element === 'ApproveOnepayMobileApp') {
          this.checkAppFee = true;
        } else if (element === 'ApproveInternationalCard') {
          this.checkDisplayInternationalFee = true;
        } else if (element === 'ApproveDomesticCard') {
          this.checkDisplayDomesticFee = true;
        } else if (element === 'ApproveBNPL') {
          this.checkBnplFee = true;
        } else if (element === 'ApproveShopify') {
          this.checkShopifyFee = true;
          this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
        } else if (element === 'ApproveDischargeVietQR') {
          this.checkDischargeVietQR = true;
        } else {
          this.contractDetail.shopifyFee = undefined;
        }
      });
      // Thêm console.log để in ra các tham số khi điều kiện được thỏa mãn
    // if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0 && (this.checkInternationalFee === true || this.checkDomesticFee === true || this.checkAppFee === true || this.checkDischargeVietQR === true)) {
      console.log('cardListArray:', this.contractDetail.cardListArray);
      console.log('checkInternationalFee:', this.checkInternationalFee);
      console.log('checkDomesticFee:', this.checkDomesticFee);
      console.log('checkAppFee:', this.checkAppFee);
      console.log('checkDischargeVietQR:', this.checkDischargeVietQR);
    // }
      if (!this.checkInternationalFee && this.checkEditContractDetail) {
        this.contractDetail.feeTransInternational = undefined;
      } else if (this.checkInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeTransInternational = 7150;
      }
      if (!this.checkInternationalFee && !this.checkDomesticFee && !this.checkAppFee && !this.checkDischargeVietQR && this.checkEditContractDetail) {
        this.contractDetail.feeService = undefined;
        this.handleFee('REMOVE');
      }
      if (!this.checkDisplayDomesticFee && this.checkEditContractDetail) {
        this.contractDetail.approveCardType01International = undefined;
        this.contractDetail.insideDomestic = undefined;
      } else if (this.checkInternationalFee && this.checkDisplayDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.approveCardType01International = '2.75';
      }
      if (!this.checkAmericanExpress && this.checkEditContractDetail) {
        this.contractDetail.americanExpress01International = undefined;
      } else if (this.checkAmericanExpress && this.checkDisplayDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.americanExpress01International = '3.30';
      }
      if (!this.checkDisplayInternationalFee && this.checkEditContractDetail) {
        this.contractDetail.approveCardType02International = undefined;
        this.contractDetail.outsideDomestic = undefined;
      } else if (this.checkInternationalFee && this.checkDisplayInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.approveCardType02International = '3.30'
      }

      if (!this.checkAppFee && this.checkEditContractDetail) {
        this.contractDetail.percentQrMobile = undefined;
        this.contractDetail.percentQrGrab = undefined;
        this.contractDetail.percentQrShopee = undefined;
        this.contractDetail.percentQrZalo = undefined;
        this.contractDetail.percentQrOther = undefined;
        this.contractDetail.percentQrMoMo = undefined;
      } else if (this.checkAppFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.percentQrMobile = '1.10';
        this.contractDetail.percentQrGrab = '1.10';
        this.contractDetail.percentQrShopee = '1.10';
        this.contractDetail.percentQrZalo = '1.10';
        this.contractDetail.percentQrMoMo = '1.75';
        this.contractDetail.percentQrOther = '1.10';
      }

      if (!this.checkAmericanExpress && this.checkEditContractDetail) {
        this.contractDetail.americanExpress02International = undefined;
      } else if (this.checkAmericanExpress && this.checkDisplayInternationalFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.americanExpress02International = '3.50';
      }
      if (!this.checkDomesticFee && this.checkEditContractDetail) {
        this.contractDetail.feeTransDomesticAndApp = undefined;
        this.contractDetail.feePaymentDomesticAndApp = undefined;
      } else if (this.checkDomesticFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeTransDomesticAndApp = 1760;
        this.contractDetail.feePaymentDomesticAndApp = '1.1';
      }

      if (!this.checkAppFee && this.checkEditContractDetail) {
        this.contractDetail.feeApp = undefined;
        this.contractDetail.feePaymentApp = undefined;
      } else if (this.checkAppFee && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeApp = '1,760';
        this.contractDetail.feePaymentApp = '1.1';
      }

      if (!this.checkDischargeVietQR && this.checkEditContractDetail) {
        this.contractDetail.feeVietQR = undefined;
        this.contractDetail.percentVietQR = undefined;
      } else if (this.checkDischargeVietQR && this.contractDetail.autoFillStandardFee && this.contractDetail.autoFillStandardFee !== '' && this.checkEditContractDetail) {
        this.contractDetail.feeVietQR = '2,200';
        this.contractDetail.percentVietQR = 'Không áp dụng phí thanh toán';
      }
    } else {
      this.handleFee('REMOVE');
    }
    // this.checkPhamViTheQuocTe();
  }

  changeFeeType(inputType: string) {

    if (this.checkEditContractDetail === true) {
      if (inputType === 'normalFee') {
        this.handleFee('REMOVE');
        this.contractDetail.autoFillStandardFee = undefined;
        this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
      } else if (inputType === 'specialFee') {
        this.handleFee('REMOVE');
        this.contractDetail.autoFillStandardFee = undefined;
        this.contractDetail.shopifyFee = '0.35% giá trị giao dịch';
      } else {
        if (this.contractDetail.autoFillStandardFee !== undefined && this.contractDetail.autoFillStandardFee !== null && this.contractDetail.autoFillStandardFee.length > 0) {
          // console.log('autoFillStandardFee'  + this.contractDetail.autoFillStandardFee);
          this.handleFee('FILL');
        } else {
          this.handleFee('REMOVE');
        }
      }
    }
  }

  /**
   * thiết lập hoặc xóa các loại phí liên quan đến hợp đồng dựa trên tham số đầu vào
   * @param inputType 
   */
  handleFee(inputType: string) {
    if (inputType === 'FILL') {
      this.contractDetail.registerFee = '2400000';
      this.contractDetail.monthFee = 'Miễn phí';
      this.checkFeeType();
    } else {
      this.contractDetail.registerFee = undefined;
      this.contractDetail.monthFee = undefined;
      this.contractDetail.feeTransDomesticAndApp = undefined;
      this.contractDetail.feeApp = undefined;
      this.contractDetail.feeVietQR = undefined;
      this.contractDetail.feePaymentDomesticAndApp = undefined;
      this.contractDetail.feeTransInternational = undefined;
      this.contractDetail.approveCardType01International = undefined;
      this.contractDetail.americanExpress01International = undefined;
      this.contractDetail.approveCardType02International = undefined;
      this.contractDetail.americanExpress02International = undefined;
      this.contractDetail.autoFillStandardFee = undefined;

      this.contractDetail.percentQrMobile = undefined;
      this.contractDetail.percentQrGrab = undefined;
      this.contractDetail.percentQrShopee = undefined;
      this.contractDetail.percentQrZalo = undefined;
      this.contractDetail.percentQrOther = undefined;
      this.contractDetail.percentQrMoMo = undefined;
      this.contractDetail.inforOther = undefined;
      this.contractDetail.percentVietQR = undefined;

      this.contractDetail.bnplFee = undefined;
      if (this.checkShopifyFee !== true) {
        this.contractDetail.shopifyFee = undefined;
      }
    }
  }

  /**
   * kiểm tra xem danh sách các loại thẻ (cardListArray) trong contractDetail có chứa các thẻ thuộc phạm vi quốc tế hay không. 
   * @returns trả về một giá trị boolean (true hoặc false)
   */
  checkPhamViTheQuocTe(): boolean {
    var checkPhamVi = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        // if(element === 'Visa' || element === 'MasterCard' || element === 'JCB' || element === 'UnionPay' || element === 'AmericanExpress'){
        if (element === 'ApproveDomesticCard' || element === 'ApproveInternationalCard') {
          checkPhamVi = true;
        }
        // }
      });
    }
    return checkPhamVi;
  }

  checkOthers() {
    if (this.contractDetail.hinhThucThuPhi && this.contractDetail.hinhThucThuPhi !== 'Khac') {
      this.contractDetail.inputHinhThucThuPhiKhac = undefined;
    } else if (this.contractDetail.tgTamUngSelection && this.contractDetail.tgTamUngSelection !== 'other') {
      this.contractDetail.inputTgTamUngKhac = undefined;
    }
  }

  @Output()
  private deleteMerchantById = new EventEmitter<any>();

  @ViewChild(AppChildTableMerchantIdV3Component) childTable: AppChildTableMerchantIdV3Component;
  deleteMerchant() {
    this.deleteMerchantById.emit({ id: this.childTable.idDelete });
  }

  autoFillKyQuy() {
    if (this.contractDetail.kyQuyAutoFill && this.contractDetail.kyQuyAutoFill !== null && this.contractDetail.kyQuyAutoFill.length > 0) {
      this.contractDetail.khoanDamBaoInput = ********;
      this.contractDetail.kyHanFD = '6 tháng';
      if (this.contractDetail.subTableMerchant && this.contractDetail.subTableMerchant.length > 0) {
        this.contractDetail.stkGiaiKhoanh = this.contractDetail.subTableMerchant[0].accountNumber;
        this.contractDetail.openByBank = this.contractDetail.subTableMerchant[0].bank;
      }

      if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKeep') {
        this.contractDetail.keepPercent = 100;
      } else {
        this.contractDetail.keepPercent = undefined;
      }
    }
  }

  cancelAutoFillKyQuy() {
    this.contractDetail.kyQuyAutoFill = undefined;
    this.contractDetail.kyHanFD = undefined;
    this.contractDetail.khoanDamBaoInput = undefined;
    this.contractDetail.stkGiaiKhoanh = undefined;
    this.contractDetail.openByBank = undefined;
    this.checkAutoFill = false;
  }

  checkInstallment(): boolean {
    var checkInstallment = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'ApproveInstallment') {
          checkInstallment = true;
        }
      });
      if (this.contractDetail.cardListArray.length === 1 && checkInstallment !== false) {
        this.contractDetail.feeService = undefined;
        this.checkInternationalFee = false;
        this.checkDomesticFee = false;
        this.checkAppFee = false;
      }
    }
    return checkInstallment;
  }

  checkKyQuy() {
    if (this.contractDetail.khoanDamBaoSelection && this.contractDetail.khoanDamBaoSelection === 'Mien') {
      this.contractDetail.kyQuyType = undefined;
      this.contractDetail.inputKyQuyKhac = undefined;
      this.cancelAutoFillKyQuy();
    }
  }

  checkKyQuyType() {
    this.contractDetail.khoanDamBaoSelection = undefined;
    if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKhac') {
      this.cancelAutoFillKyQuy();
      this.contractDetail.accountNumber = 'vietinbank';
    } else if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyStandard') {
      this.contractDetail.inputKyQuyKhac = undefined;
      this.contractDetail.keepPercent = undefined;
      this.contractDetail.khoanDamBaoInput = undefined;
      this.contractDetail.kyHanFD = undefined;
      this.contractDetail.kyQuyAutoFill = undefined;
      this.checkAutoFill = true;
      this.contractDetail.accountNumber = 'vietinbank';
    } else if (this.contractDetail.kyQuyType && this.contractDetail.kyQuyType === 'kyQuyKeep') {
      this.contractDetail.inputKyQuyKhac = undefined;
      this.contractDetail.khoanDamBaoInput = undefined;
      this.contractDetail.kyHanFD = undefined;
      this.contractDetail.kyQuyAutoFill = undefined;
      this.checkAutoFill = true;
      this.contractDetail.accountNumber = 'vietinbank';
    } else {
      this.cancelAutoFillKyQuy();
    }
  }

  uncheckManual() {
    if (this.contractDetail.autoFillStandardFee == 'autoFillStandardFee') {
      this.contractDetail.autoFillStandardFee = undefined;
    }
    if (this.contractDetail.kyQuyAutoFill == 'kyQuyAutoFill') {
      this.contractDetail.kyQuyAutoFill = undefined;
    }
  }

  checkDomesticPaymentFee() {
    var checkDomesticCard = false;
    if (this.contractDetail.cardListArray !== undefined && this.contractDetail.cardListArray !== null && this.contractDetail.cardListArray.length > 0) {
      this.contractDetail.cardListArray.forEach(element => {
        if (element === 'ApproveOnepayDomesticCard') {
          checkDomesticCard = true;
        }
      });
    }
    return checkDomesticCard;
  }

}
