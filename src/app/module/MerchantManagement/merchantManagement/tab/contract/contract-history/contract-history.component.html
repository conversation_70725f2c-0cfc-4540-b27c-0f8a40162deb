<div class="contract-history">
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Action</th>
                <th>Comment</th>
                <th>Operator</th>
                <th>Template version</th>
                <th>Scanned file</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let history of contractHistories">
                <td style="text-align:center">{{ history.D_CREATE }}</td>
                <td style="text-align:center">{{ history.S_ACTION }}</td>
                <td>{{ history.S_COMMENT }}</td>
                <td style="text-align:center">{{ history.S_CREATE }}</td>
                <td style="text-align:center">{{ history.S_VERSION }}</td>
                <td style="text-align:center">
                    <a href="/iportal/api/v1/contract-history/download-file?id={{ history.N_ID }}" target="_blank" *ngIf="history.S_ACTION === 'approve'">
                        <i class="pi pi-download" style="font-size:1.3rem; cursor: pointer;"></i>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>