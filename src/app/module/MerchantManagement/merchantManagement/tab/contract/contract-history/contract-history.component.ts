import { Component, OnInit } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ContractService } from '../../contract.service';

interface ContractHistory {
  date: string;
  action: string;
  user: string;
}

@Component({
  selector: 'app-contract-history',
  templateUrl: './contract-history.component.html',
  styleUrls: ['./contract-history.component.css']
})
export class ContractHistoryComponent implements OnInit {
  contractId: any;

  contractHistories: ContractHistory[] = [
    { date: '2023-01-01', action: 'Created', user: 'Admin' },
    { date: '2023-02-01', action: 'Updated', user: 'User1' },
    // Add more history records as needed
];
  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig
    ,public contractService: ContractService
  ) { }

  ngOnInit(): void {
    this.contractId = this.config.data.contractId;
    this.contractService.loadContractHistory(this.contractId).subscribe((response) => {
      if (response) {
        this.contractHistories = response.list;
      }
    });
  }

}
