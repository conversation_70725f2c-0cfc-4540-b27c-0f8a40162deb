<div id="contract-new">
    <div class="menu-btn" [ngClass]="viewListContractDetail === true ? 'menu-btn menu-detail' : 'menu-btn menu-list'">
        <div class="menu-btn-left-contract" *ngIf="viewListContractDetail === true">

            <div class="ui-helper-clearfix clearfix  menu-btn-left-item row" style="text-align: left;color:#00529c;">
                <div class="col-sm-7" style="margin-left: -10px !important;">
                    <button type="button" pTooltip="Quay lại" tooltipPosition="top" pButton label="Quay lại"
                        icon="pi pi-chevron-left" iconPos="left" id="back-button" (click)="backContractManagement()"
                        class="back-button p-button-success btn-common"></button>
                    <span style="color: #333333 !important;font-size: 12px !important;">
                        <span style="margin-left: 5px !important;">|</span>
                        <span style="margin-left: 5px !important;">
                            {{checkAddNewContract === true ? 'Thêm mới hợp đồng' : checkAddNewSubContract === true ?
                            'Thêm mới phụ lục'
                            : checkUpdateContract === true ? 'Cập nhật hợp đồng' : 'Cập nhật phụ lục'}}
                        </span>
                    </span>
                </div>
                <div class="col-sm-5" style="text-align: right;">
                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true)
           && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Lịch sử" tooltipPosition="top" pButton label=""
                        icon="pi pi-history" iconPos="left" id="edit-button" (click)="openDialogHistory(contractId)"
                        class="back-button ui-button btn-common"></button>
                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && contractOriginal.state !== 'approved' && isActive('approve_contract')
          && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Review" tooltipPosition="top" pButton label="Review"
                        icon="pi pi-check-circle" iconPos="left" id="approve-button" (click)="reviewPdf()"
                        class="back-button ui-button btn-common"></button>

                    <button
                        *ngIf="(checkAddNewContract === true || checkAddNewSubContract === true) && contractOriginal.state !== 'approved'
            && checkUpdateContract === false && checkUpdateSubContract === false || checkEditContractDetail === true && (isActive('new_contract') || isActive('edit_contract'))"
                        type="button" pTooltip="Làm mới" tooltipPosition="top" pButton label="Làm mới"
                        icon="pi pi-refresh" iconPos="left" id="refresh-button" class="back-button ui-button btn-common"
                        (click)="refreshFormContract()"></button>

                    <button
                        *ngIf="(checkAddNewContract === true || checkAddNewSubContract === true) && contractOriginal.state !== 'approved'
           && checkUpdateContract === false && checkUpdateSubContract === false || checkEditContractDetail === true && (isActive('new_contract') || isActive('edit_contract'))"
                        type="button" pTooltip="Lưu" tooltipPosition="top" pButton label="Lưu" icon="pi pi-save"
                        iconPos="left" id="save-button" class="back-button ui-button btn-common"
                        (click)="submitFormContract()"></button>

                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && contractOriginal.state !== 'approved' && isActive('edit_contract')
           && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Chỉnh sửa" tooltipPosition="top" pButton label="Chỉnh sửa"
                        icon="pi pi-pencil" iconPos="left" id="edit-button" (click)="checkEditContract()"
                        class="back-button ui-button btn-common"></button>

                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && contractOriginal.state !== 'approved' && isActive('approve_contract')
          && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Duyệt" tooltipPosition="top" pButton label="Duyệt"
                        icon="pi pi-check-circle" iconPos="left" id="approve-button" (click)="approveContract()"
                        class="back-button ui-button btn-common"></button>
                        <!--merge sau reviewPdf() -->
                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && contractOriginal.state === 'approved' && isActive('un_approve_contract')
          && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Hủy duyệt" tooltipPosition="top" pButton label="Hủy duyệt"
                        icon="pi pi-ban" iconPos="left" id="remove-approve-button" (click)="removeApproveContract()"
                        class="back-button ui-button btn-common"></button>

                    <button
                        *ngIf="(checkAddNewContract === true || checkAddNewSubContract === true) && contractOriginal.state !== 'approved'
           && checkUpdateContract === false && checkUpdateSubContract === false || checkEditContractDetail === true && (isActive('new_contract') || isActive('edit_contract'))"
                        type="button" pTooltip="Hủy" tooltipPosition="top" pButton label="Hủy" icon="pi pi-times-circle"
                        iconPos="left" id="cancel-button" (click)="cancelEditContract()"
                        class="back-button ui-button btn-common"></button>

                    <button
                        *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && contractOriginal.state !== 'approved' && isActive('remove_contract')
          && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'"
                        type="button" pTooltip="Xóa" tooltipPosition="top" pButton label="Xóa" icon="pi pi-trash"
                        iconPos="left" id="delete-button" (click)="deleteContract()"
                        class="back-button ui-button btn-common"></button>

                    <button *ngIf="contractOriginal.state === 'approved'" type="button" pTooltip="Đã duyệt"
                        tooltipPosition="top" pButton disabled="true" label="Đã duyệt" icon="pi pi-check-circle"
                        iconPos="left" id="approved-button" class="ui-button btn-common"></button>
                </div>
            </div>
            <div style="margin-top: 10px;margin-left: 5px;margin-bottom: 5px !important;">
                <span style="text-align: left;color:#00529c;font-size: 14px;font-weight: bold;margin-top: 10px;">
                    {{contractName}}
                </span>
                <a data-toggle="modal" data-target="#menuContractModal" style="border: 1px solid;margin-left: 8px"
                    *ngIf="contractId === null || contractId === '' || contractId === undefined || contractId === '0'">
                    <em class="pi pi-sort-down" style="font-size: 1rem;margin-top: -10px !important;"></em>
                </a>

                <button style="margin-left: 5px"
                    *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true) && checkExportContract === true && isActive('export_contract_information')
                    && checkAddNewContract === false && checkAddNewSubContract === false && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'
                    && !((contractCode == 'HD13' && (contractDetail?.version == 'v3' || contractDetail?.version == 'v4' || contractDetail?.version == 'v5'))
                    || contractCode == 'HD14' || contractCode == 'CV-DCHD' || contractCode == 'HD11' || contractCode == 'HD12' || contractCode == 'PL02_v2' || contractCode == 'PL-K_v2' || contractCode == 'VBUQ' || contractCode == 'BBTLv2' || contractCode == 'BBTTv2' || contractCode == 'PL15v2')"
                    type="button" pTooltip="Xuất file" tooltipPosition="top" pButton label="Xuất file"
                    icon="pi pi-download" (click)="exportContract()" iconPos="left" id="export-button"
                    class="back-button ui-button btn-common"></button>
                <button style="margin-left: 5px"
                    *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true)
                    && checkExportContract === true && isActive('export_contract_information')
                    && checkAddNewContract === false && checkAddNewSubContract === false
                    && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'
                    && ((contractCode == 'HD13' && (contractDetail?.version == 'v3' || contractDetail?.version == 'v4' || contractDetail?.version == 'v5'))
                    || contractCode == 'HD14' || contractCode == 'CV-DCHD' || contractCode == 'HD11' || contractCode == 'HD12' || contractCode == 'PL02_v2' || contractCode == 'PL-K_v2' || contractCode == 'VBUQ' || contractCode == 'BBTLv2' || contractCode == 'BBTTv2' || contractCode == 'PL15v2')"
                    type="button" pTooltip="Xuất file" tooltipPosition="top" pButton label="Xuất file"
                    icon="pi pi-download" (click)="exportContractHtml()" iconPos="left" id="export-button-2"
                    class="back-button ui-button btn-common"></button>
                <button style="margin-left: 5px"
                    *ngIf="(checkUpdateContract === true || checkUpdateSubContract === true)
                    && checkExportContract === true && isActive('export_contract_information')
                    && checkAddNewContract === false && checkAddNewSubContract === false
                    && checkEditContractDetail === false && contractId !== '' && contractId !== undefined && contractId !== '0'
                    && (contractCode == 'HD13' && (contractDetail?.version == 'v3' || contractDetail?.version == 'v4' || contractDetail?.version == 'v5'))"
                    type="button" pTooltip="Xuất file song ngữ" tooltipPosition="top" pButton label="Xuất file song ngữ"
                    icon="pi pi-download" (click)="exportContractHtml(true)" iconPos="left" id="export-button-3"
                    class="back-button ui-button btn-common"></button>

                <button style="margin-left: 5px" *ngIf="contractOriginal.contractCode=='PL-K' && !checkEditContractDetail"
                pTooltip="Edit Template" tooltipPosition="top" pButton label="Edit Template"
                icon="pi pi-pencil" (click)="openTemplate('1')" iconPos="left" id="export-button"
                class="back-button ui-button btn-common">
                </button>
                <button style="margin-left: 5px" *ngIf="contractOriginal.contractCode=='PL-K' && !checkEditContractDetail"
                    type="button" pTooltip="Download" tooltipPosition="top" pButton label="Download"
                    icon="pi pi-download" (click)="exportSubContract()" iconPos="left" id="export-button"
                    class="back-button ui-button btn-common"></button>
            </div>

        </div>

        <div class="menu-btn-right-contract">

            <a *ngIf="(isActive('new_contract')) && viewListContractDetail === false" (click)="checkTypeContract()"
                id="new-contract-btn" class="menu-btn-right-item" data-toggle="modal" data-target="#menuContractModal">
                Thêm Mới Hợp Đồng
            </a>

        </div>

    </div>
    <div class="overflow"
        [ngClass]="viewListContractDetail === true ? 'overflow detail-contract' : 'overflow no-detail-contract'">
        <div id="contract-list" *ngIf="viewListContractDetail === false && isActive('list_contract')">
            <p-table [value]="contractList" [scrollable]="true">
                <ng-template pTemplate="emptymessage">
                    <div class="text-center empty_results">
                        Không Tìm Thấy Bản Ghi Nào!
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr class="tr_header">

                        <th scope="col" class="text-center mat-column-type">Phân Loại</th>
                        <th scope="col" class="text-center mat-column-name">Tên Đơn vị chấp nhận thanh toán</th>
                        <th scope="col" class="text-center mat-column-contract_number">Số HĐ/PL</th>
                        <th scope="col" class="text-center mat-column-contract_date">Ngày Ký</th>
                        <th scope="col" class="text-center mat-column-status">Duyệt VB</th>
                        <th scope="col" class="text-center mat-column-contract_information"
                            *ngIf="isActive('new_contract')">Chức
                            năng</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-i="rowIndex" let-contractList let-expanded="expanded">


                    <tr [ngClass]="i % 2 ? 'tr_body child' : 'tr_body table-row' ">

                        <td class="text-left mat-column-type" style="color: #00529A !important;"
                            (click)="goContractDetail(contractList)">
                            <span style="font-size: 12px !important;">
                                {{contractList.contractName ? contractList.contractName : ''}}
                            </span>
                        </td>

                        <td class="text-left mat-column-name" style="color: #00529A !important;"
                            (click)="goContractDetail(contractList)">
                            <span style="font-size: 12px !important;">
                                {{contractList.contractCode === 'HD14' ? contractList.representative : (contractList.businessName ? contractList.businessName : '')}}
                            </span>
                        </td>

                        <td class="text-left mat-column-contract_number" style="color: #00529A !important;"
                            (click)="goContractDetail(contractList)">
                            <span style="font-size: 12px !important;">
                                {{contractList.contractNumber ? contractList.contractNumber : ''}}
                            </span>
                        </td>

                        <td class="text-center mat-column-contract_date" style="color: #00529A !important;"
                            (click)="goContractDetail(contractList)">
                            <span style="font-size: 12px !important;">
                                {{contractList.signatureDate | date: 'dd/MM/yyyy'}}
                            </span>
                        </td>

                        <td class="text-center mat-column-status" (click)="goContractDetail(contractList)">
                            <span pTooltip="Đã duyệt" tooltipPosition="top" label="Đã duyệt">
                                <em *ngIf="contractList.stateContract === 'approved'" class="pi pi-check-circle"
                                    style="font-size: 1.6em;color: #02A554;">
                                </em>
                            </span>
                        </td>

                        <td class="text-center mat-column-contract_information" *ngIf="isActive('new_contract')">
                            <a class="pi pi-plus-circle" pTooltip="Thêm văn bản" tooltipPosition="left"
                                label="Thêm văn bản" (click)="checkTypeSubContract(contractList)" data-toggle="modal"
                                data-target="#menuContractModal"
                                style="font-size: 1.5em;color: #00529A;margin-right: 5px !important;"></a>
                            <a class="pi pi-copy text-primary" style="font-size: 1.5em;color: #2d9fc1 !important;"
                                (click)="cloneCopy(contractList, 'HD')" pTooltip="Thêm bản sao" tooltipPosition="left"
                                label="Thêm bản sao"></a>
                        </td>

                    </tr>


                    <!-- LIST SUB CONTRACT  -->

                    <tr [ngClass]="i % 2 ? 'tr_body child' : 'tr_body table-row' "
                        *ngFor="let subContract of contractList.subContractList" class="sub-contract">

                        <td class="text-left mat-column-type" (click)="goContractDetail(subContract)">
                            <span style="margin-left: 15px !important;">
                                {{subContract.contractName ? subContract.contractName : ''}}
                            </span>
                        </td>

                        <td class="text-left mat-column-name" (click)="goContractDetail(subContract)">
                            <span>
                                {{contractList.contractCode === 'HD14' ? subContract.representative : (subContract.businessName ? subContract.businessName : '')}}
                            </span>
                        </td>

                        <td class="text-left mat-column-contract_number" (click)="goContractDetail(subContract)">
                            <span *ngIf="subContract.contractCode != 'VBUQ'">
                                {{subContract.contractCode === 'BBTLv2' ? '' : subContract.contractNumber ? subContract.contractNumber : ''}}
                            </span>
                            <span *ngIf="subContract.contractCode === 'VBUQ'">
                                {{subContract.authorizationNumber ? subContract.authorizationNumber : ''}}
                            </span>
                        </td>

                        <td class="text-center mat-column-contract_date" (click)="goContractDetail(subContract)">
                            <span>
                                {{subContract.signatureDate | date: 'dd/MM/yyyy'}}
                            </span>
                        </td>

                        <td class="text-center mat-column-status" (click)="goContractDetail(subContract)">
                            <span>
                                <em *ngIf="subContract.stateContract === 'approved'" class="pi pi-check-circle"
                                    style="font-size: 1.6em;color: #02A554;">
                                </em>
                            </span>
                        </td>

                        <td class="text-center mat-column-contract_information" *ngIf="isActive('new_contract')">
                            <a class="pi pi-copy text-primary"
                                style="font-size: 1.5em;color: #2d9fc1 !important;margin-left: 21px;"
                                (click)="cloneCopy(subContract, 'PL')" pTooltip="Thêm bản sao" tooltipPosition="left"
                                label="Thêm bản sao"></a>
                        </td>

                    </tr>

                </ng-template>
            </p-table>

        </div>


        <div *ngIf="viewListContractDetail === true && (isActive('new_contract') || isActive('approve_contract') || isActive('edit_contract') || isActive('remove_contract')
    || isActive('detail_contract'))">

            <div id="contract-information" class="contract-information" style="margin-left: 5px;margin-right: 5px;">

                <div class="list-contract" id="list-contract-style">

                    <!-- LIST CONTRACTS -->

                    <div *ngIf="contractCode === 'HD01'">
                        <app-contract-hd01 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [branchList]="branchVCBList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd01>
                    </div>


                    <div *ngIf="contractCode === 'HD02'">
                        <app-contract-hd02 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [branchList]="branchVCBList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd02>
                    </div>


                    <div *ngIf="contractCode === 'HD03-01'">
                        <app-contract-hd03 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [branchList]="branchVCBList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteTableData01)="deleteTableData01($event)"
                            (deleteTableData001)="deleteTableData001($event)"
                            (deleteTableData02)="deleteTableData02($event)"
                            (deleteTableData002)="deleteTableData002($event)"
                            (deleteTableData03)="deleteTableData03($event)"
                            (deleteTableData003)="deleteTableData003($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd03>
                    </div>


                    <div *ngIf="contractCode === 'HD04'">
                        <app-contract-hd04 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd04>
                    </div>


                    <div *ngIf="contractCode === 'HD05'">
                        <app-contract-hd05 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [branchList]="branchBIDVList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteTableData01)="deleteTableData01($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd05>
                    </div>


                    <div *ngIf="contractCode === 'HD06'">
                        <app-contract-hd06 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteTableData01)="deleteTableData01($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd06>
                    </div>


                    <div *ngIf="contractCode === 'HD07'">
                        <app-contract-hd07 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd07>
                    </div>


                    <div *ngIf="contractCode === 'HD08'">
                        <app-contract-hd08 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd08>
                    </div>


                    <div *ngIf="contractCode === 'HD09'">
                        <app-contract-hd09 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [checkEditContractDetail]="checkEditContractDetail"
                            [carrerList]="carrerList" [feeBigMerchantList]="feeBigMerchantList" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd09>
                    </div>


                    <div *ngIf="contractCode === 'HD10'">
                        <app-contract-hd10 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd10>
                    </div>

                    <div *ngIf="contractCode === 'HD11'">
                        <app-contract-hd11 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd11>
                    </div>

                    <div *ngIf="contractCode === 'HD12'">
                        <app-contract-hd12 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-contract-hd12>
                    </div>

                    <div *ngIf="contractCode === 'HD13' && version != 'v2' && version != 'v3' && version != 'v4' && version != 'v5'">
                        <app-contract-hd13 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner">
                        </app-contract-hd13>
                    </div>

                    <div *ngIf="contractCode === 'HD13' && version === 'v2'">
                        <app-contract-hd13-v2 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner">
                        </app-contract-hd13-v2>
                    </div>

                    <div *ngIf="contractCode === 'HD13' && version === 'v3'">
                        <app-contract-hd13-v3 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner">
                        </app-contract-hd13-v3>
                    </div>

                    <div *ngIf="contractCode === 'HD13' && version === 'v4'">
                        <app-contract-hd13-v4 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner">
                        </app-contract-hd13-v4>
                    </div>

                    <div *ngIf="contractCode === 'HD13' && version === 'v5'">
                      <app-contract-hd13-v5 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                          [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                          [checkEditContractDetail]="checkEditContractDetail"
                          (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                          [isActive]="isActive" [currentPartner]="currentPartner">
                      </app-contract-hd13-v5>
                    </div>

                    <div *ngIf="contractCode === 'HD14'">
                      <app-contract-hd14 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                          [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                          [checkEditContractDetail]="checkEditContractDetail"
                          (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                          [isActive]="isActive" [currentPartner]="currentPartner">
                      </app-contract-hd14>
                    </div>

                    <!--========= LIST SUB CONTRACT =========-->

                    <div *ngIf="contractCode === 'PL01'">
                        <app-sub-contract-pl01 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [branchList]="branchVCBList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteTableData01)="deleteTableData01($event)"
                            (deleteTableData001)="deleteTableData001($event)"
                            (deleteTableData02)="deleteTableData02($event)"
                            (deleteTableData002)="deleteTableData002($event)"
                            (deleteTableData03)="deleteTableData03($event)"
                            (deleteTableData003)="deleteTableData003($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl01>
                    </div>


                    <div *ngIf="contractCode === 'PL02'">
                        <app-sub-contract-pl02 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl02>
                    </div>
                    <div *ngIf="contractCode === 'PL02_v2'">
                        <app-sub-contract-pl02v2 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                        [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList" 
                        [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner" [parentContractCode]="parentContractCodePL">
                        </app-sub-contract-pl02v2>
                    </div>

                    <div *ngIf="contractCode === 'PL03'">
                        <app-sub-contract-pl03 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl03>
                    </div>


                    <div *ngIf="contractCode === 'PL04'">
                        <app-sub-contract-pl04 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl04>
                    </div>


                    <div *ngIf="contractCode === 'PL05'">
                        <app-sub-contract-pl05 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl05>
                    </div>


                    <div *ngIf="contractCode === 'PL06'">
                        <app-sub-contract-pl06 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteTableData01)="deleteTableData01($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl06>
                    </div>


                    <div *ngIf="contractCode === 'PL07'">
                        <app-sub-contract-pl07 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl07>
                    </div>


                    <div *ngIf="contractCode === 'PL08'">
                        <app-sub-contract-pl08 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl08>
                    </div>


                    <div *ngIf="contractCode === 'PL09'">
                        <app-sub-contract-pl09 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [checkEditContractDetail]="checkEditContractDetail"
                            [feeBigMerchantList]="feeBigMerchantList" [feeNormalList]="feeNormalList"
                            [isActive]="isActive" [currentPartner]="currentPartner">
                        </app-sub-contract-pl09>
                    </div>


                    <div *ngIf="contractCode === 'PL10'">
                        <app-sub-contract-pl10 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl10>
                    </div>


                    <div *ngIf="contractCode === 'PL11'">
                        <app-sub-contract-pl11 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl11>
                    </div>


                    <div *ngIf="contractCode === 'PL12'">
                        <app-sub-contract-pl12 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl12>
                    </div>


                    <div *ngIf="contractCode === 'PL13'">
                        <app-sub-contract-pl13 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl13>
                    </div>


                    <div *ngIf="contractCode === 'PL14'">
                        <app-sub-contract-pl14 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl14>
                    </div>


                    <div *ngIf="contractCode === 'PL15'">
                        <app-sub-contract-pl15 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-pl15>
                    </div>

                    <div *ngIf="contractCode === 'PL15v2'">
                        <app-sub-contract-pl15v2 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner" [parentContractCode]="parentContractCodePL" [danhXungList]="danhXungList">
                        </app-sub-contract-pl15v2>
                    </div>

                    <div *ngIf="contractCode === 'PLBNPL'">
                      <app-sub-contract-plBNPL [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                          [checkEditContractDetail]="checkEditContractDetail"
                          [feeBigMerchantList]="feeBigMerchantList" [feeNormalList]="feeNormalList"
                          [isActive]="isActive" [currentPartner]="currentPartner">
                      </app-sub-contract-plBNPL>
                    </div>

                    <div *ngIf="contractCode === 'PL16'">
                      <app-sub-contract-pl16 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                          [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                          (deleteMerchantNonShopifyById)="deleteSubmerchantNonShopify($event)" [isActive]="isActive"
                          (deleteMerchantShopifyById)="deleteSubmerchantShopify($event)"
                          [currentPartner]="currentPartner">
                      </app-sub-contract-pl16>
                    </div>

                    <div *ngIf="contractCode === 'PL-K'">
                        <app-sub-contract-other [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner" [parentContractDetail]="parentContractDetail">
                        </app-sub-contract-other>
                    </div>

                    <div *ngIf="contractCode === 'PL-K_v2'">
                        <app-sub-contract-otherv2 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [feeNormalList]="feeNormalList" [carrerList]="carrerList" [danhXungList]="danhXungList"
                            [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [feeBigMerchantList]="feeBigMerchantList"
                            [isActive]="isActive" [currentPartner]="currentPartner" [parentContractDetail]="parentContractDetail">
                        </app-sub-contract-otherv2>
                    </div>

                    <div *ngIf="contractCode === 'CV-Den'">
                        <app-sub-contract-cv-den [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [checkEditContractDetail]="checkEditContractDetail"
                            [feeInstallmentList]="feeInstallmentList" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-cv-den>
                    </div>


                    <div *ngIf="contractCode === 'CV-Di'">
                        <app-sub-contract-cv-di [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [checkEditContractDetail]="checkEditContractDetail"
                            [feeInstallmentList]="feeInstallmentList" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-cv-di>
                    </div>

                    <div *ngIf="contractCode === 'CV-DCHD'">
                        <app-sub-contract-cv-dchd [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [checkEditContractDetail]="checkEditContractDetail"
                            [feeInstallmentList]="feeInstallmentList" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-cv-dchd>
                    </div>


                    <div *ngIf="contractCode === 'BBNT'">
                        <app-sub-contract-BBNT [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)"
                            [parentContractCodeTempl]="parentContractCodeTempl" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-BBNT>
                    </div>


                    <div *ngIf="contractCode === 'BBTL'">
                        <app-sub-contract-BBTL [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-BBTL>
                    </div>

                    <div *ngIf="contractCode === 'BBTLv2'">
                        <app-sub-contract-BBTLv2 [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteSubmerchant($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner" [parentContractCode]="parentContractCodePL" [danhXungList]="danhXungList">
                        </app-sub-contract-BBTLv2>
                    </div>


                    <div *ngIf="contractCode === 'BBTT'">
                        <app-sub-contract-BBTT [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-BBTT>
                    </div>


                    <div *ngIf="contractCode === 'VBUQ'">
                        <app-sub-contract-VBUQ [contractDetail]="contractDetail" [contractOriginal]="contractOriginal"
                            [carrerList]="carrerList" [checkEditContractDetail]="checkEditContractDetail"
                            (deleteMerchantById)="deleteDataPause($event)" [isActive]="isActive"
                            [currentPartner]="currentPartner">
                        </app-sub-contract-VBUQ>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <!-- Modal Contract-->
    <div class="modal fade" id="menuContractModal" tabindex="-1" role="dialog" aria-labelledby="menuContractModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <div class="close-sub">
                            <span aria-hidden="true" class="close-btn" #closeModal>&times;</span>
                        </div>
                    </button>
                </div>
                <div class="contract-title">
                    <span class="modal-title" id="menuContractModalLabel">
                        Chọn loại {{typeContract === true && typeSubContract === false ? 'hợp đồng' : 'phụ lục'}}
                    </span>
                </div>
                <div class="modal-body">
                    <div class="type-contact">
                        <span>
                            Loại {{typeContract === true && typeSubContract === false ? 'hợp đồng' : 'phụ lục'}}
                        </span>
                    </div>
                    <p-dropdown *ngIf="typeContract === true && typeSubContract === false"
                        [options]="contractTemplateList" [(ngModel)]="contractTemplate" placeholder="Chọn loại hợp đồng"
                        optionLabel="contractName" [showClear]="true" [filter]="true">

                    </p-dropdown>
                    <p-dropdown *ngIf="typeContract === false && typeSubContract === true"
                        [options]="subContractTemplateList" [(ngModel)]="subContractTemplate"
                        placeholder="Chọn loại phụ lục" optionLabel="subContractName" [showClear]="true"
                        [filter]="true"></p-dropdown>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-my-confirnm" (click)="actionAddNewContract()"
                        [disabled]="contractTemplate === undefined && subContractTemplate === undefined">
                        Xác nhận
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
