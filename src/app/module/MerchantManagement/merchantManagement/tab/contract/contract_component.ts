import { Component, Input, OnChang<PERSON>, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { Globals } from '@core/global';
import { MerchantService } from '@service/merchant.service';
import { HttpParams } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';

import { AppContractHD01Component } from './app-list-template/app-list-contract-template/app-contract-hd01/app-contract-hd01.component';
import { AppContractHD02Component } from './app-list-template/app-list-contract-template/app-contract-hd02/app-contract-hd02.component';
import { AppContractHD03Component } from './app-list-template/app-list-contract-template/app-contract-hd03/app-contract-hd03.component';
import { AppContractHD04Component } from './app-list-template/app-list-contract-template/app-contract-hd04/app-contract-hd04.component';
import { AppContractHD05Component } from './app-list-template/app-list-contract-template/app-contract-hd05/app-contract-hd05.component';
import { AppContractHD06Component } from './app-list-template/app-list-contract-template/app-contract-hd06/app-contract-hd06.component';
import { AppContractHD07Component } from './app-list-template/app-list-contract-template/app-contract-hd07/app-contract-hd07.component';
import { AppContractHD08Component } from './app-list-template/app-list-contract-template/app-contract-hd08/app-contract-hd08.component';
import { AppContractHD09Component } from './app-list-template/app-list-contract-template/app-contract-hd09/app-contract-hd09.component';
import { AppContractHD10Component } from './app-list-template/app-list-contract-template/app-contract-hd10/app-contract-hd10.component';
import { AppContractHD11Component } from './app-list-template/app-list-contract-template/app-contract-hd11/app-contract-hd11.component';
import { AppContractHD12Component } from './app-list-template/app-list-contract-template/app-contract-hd12/app-contract-hd12.component';
import { AppContractHD13Component } from './app-list-template/app-list-contract-template/app-contract-hd13/app-contract-hd13.component';
import { AppContractHD13V2Component } from './app-list-template/app-list-contract-template/app-contract-hd13-v2/app-contract-hd13-v2.component';
import { AppContractHD13V3Component } from './app-list-template/app-list-contract-template/app-contract-hd13-v3/app-contract-hd13-v3.component';
import { AppContractHD13V4Component } from './app-list-template/app-list-contract-template/app-contract-hd13-v4/app-contract-hd13-v4.component';
import { AppContractHD13V5Component } from './app-list-template/app-list-contract-template/app-contract-hd13-v5/app-contract-hd13-v5.component';
import { AppContractHD14Component } from './app-list-template/app-list-contract-template/app-contract-hd14/app-contract-hd14.component';

import { AppSubContractPL01Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl01/app-sub-contract-pl01.component';
import { AppSubContractPL02Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl02/app-sub-contract-pl02.component';
import { AppSubContractPl02v2Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl02v2/app-sub-contract-pl02v2.component';
import { AppSubContractPL03Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl03/app-sub-contract-pl03.component';
import { AppSubContractPL04Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl04/app-sub-contract-pl04.component';
import { AppSubContractPL05Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl05/app-sub-contract-pl05.component';
import { AppSubContractPL06Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl06/app-sub-contract-pl06.component';
import { AppSubContractPL07Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl07/app-sub-contract-pl07.component';
import { AppSubContractPL08Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl08/app-sub-contract-pl08.component';
import { AppSubContractPL09Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl09/app-sub-contract-pl09.component';
import { AppSubContractPL10Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl10/app-sub-contract-pl10.component';
import { AppSubContractPL11Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl11/app-sub-contract-pl11.component';
import { AppSubContractPL12Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl12/app-sub-contract-pl12.component';
import { AppSubContractPL13Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl13/app-sub-contract-pl13.component';
import { AppSubContractPL14Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl14/app-sub-contract-pl14.component';
import { AppSubContractPL15Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl15/app-sub-contract-pl15.component';
import { AppSubContractPL15v2Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl15v2/app-sub-contract-pl15v2.component';
import { AppSubContractPLBNPLComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-plBNPL/app-sub-contract-plBNPL.component';
import { AppSubContractBBNTComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-BBNT/app-sub-contract-BBNT.component';
import { AppSubContractBBTLComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-BBTL/app-sub-contract-BBTL.component';
import { AppSubContractBBTLv2Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-BBTLv2/app-sub-contract-BBTLv2.component';
import { AppSubContractBBTTComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-BBTT/app-sub-contract-BBTT.component';
import { AppSubContractCVDenComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-cv-den/app-sub-contract-cv-den.component';
import { AppSubContractCVDiComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-cv-di/app-sub-contract-cv-di.component';
import { AppSubContractCvDchdComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-cv-dchd/app-sub-contract-cv-dchd.component';
import { AppSubContractVBUQComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-VBUQ/app-sub-contract-VBUQ.component';
import { AppSubContractPL16Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-pl16/app-sub-contract-pl16.component';
import { AppSubContractOtherComponent } from './app-list-template/app-list-sub-contract-template/app-sub-contract-other/app-sub-contract-other.component';
import { AppSubContractOtherv2Component } from './app-list-template/app-list-sub-contract-template/app-sub-contract-otherv2/app-sub-contract-otherv2.component';

import { AppChildTableMerchantComponent } from './app-list-template/app-child-table/app-child-table-merchant/app-child-table-merchant.component';
import { AppChildTableBbntComponent } from './app-list-template/app-child-table/app-child-table-bbnt/app-child-table-bbnt.component';
import {
  ContractDetailModel,
  ContractOriginal,
  ContractTemplate,
  SubContractTemplate,
  SubTableFee,
  SubTableMerchant
} from 'app/model/contract.model';
import { ToastrService } from 'ngx-toastr';
import { DatePipe } from '@angular/common';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { Partner } from 'app/model/partner';
import { EditTemplateComponent } from './edit-gd-hd/edit-gd-hd.component';
import { DialogService,DynamicDialogRef } from 'primeng/dynamicdialog';

import { asBlob } from 'html-docx-js-typescript-papersize-thenn';
// if you want to save the docx file, you need import 'file-saver'
import { saveAs } from 'file-saver'
import { ContractService } from '../contract.service';
import { ContractReviewComponent } from './contract-review/contract-review.component';
import { ContractHistoryComponent } from './contract-history/contract-history.component';

@Component({
  selector: 'contract-management',
  templateUrl: './contract_component.html',
  styleUrls: ['./contract_component.css'],
  providers: [MerchantService]
})

export class ContractManagementComponent implements OnInit, OnChanges {

  @Input() currentPartnerId: string;
  @Input() sale: string;
  @Input() currentPartnerInput: any;
  @Input() merchantId: any;
  @ViewChild('closeModal', { static: true }) closeModal;

  @ViewChild(AppContractHD01Component) contractHD01: AppContractHD01Component;
  @ViewChild(AppContractHD02Component) contractHD02: AppContractHD02Component;
  @ViewChild(AppContractHD03Component) contractHD03: AppContractHD03Component;
  @ViewChild(AppContractHD04Component) contractHD04: AppContractHD04Component;
  @ViewChild(AppContractHD05Component) contractHD05: AppContractHD05Component;
  @ViewChild(AppContractHD06Component) contractHD06: AppContractHD06Component;
  @ViewChild(AppContractHD07Component) contractHD07: AppContractHD07Component;
  @ViewChild(AppContractHD08Component) contractHD08: AppContractHD08Component;
  @ViewChild(AppContractHD09Component) contractHD09: AppContractHD09Component;
  @ViewChild(AppContractHD10Component) contractHD10: AppContractHD10Component;
  @ViewChild(AppContractHD11Component) contractHD11: AppContractHD11Component;
  @ViewChild(AppContractHD12Component) contractHD12: AppContractHD12Component;
  @ViewChild(AppContractHD13Component) contractHD13: AppContractHD13Component;
  @ViewChild(AppContractHD13V2Component) contractHD13V2: AppContractHD13V2Component;
  @ViewChild(AppContractHD13V3Component) contractHD13V3: AppContractHD13V3Component;
  @ViewChild(AppContractHD13V4Component) contractHD13V4: AppContractHD13V4Component;
  @ViewChild(AppContractHD13V5Component) contractHD13V5: AppContractHD13V5Component;
  @ViewChild(AppContractHD14Component) contractHD14: AppContractHD14Component;

  @ViewChild(AppSubContractPL01Component) subContractPL01: AppSubContractPL01Component;
  @ViewChild(AppSubContractPL02Component) subContractPL02: AppSubContractPL02Component;
  @ViewChild(AppSubContractPl02v2Component) subContractPl02v2: AppSubContractPl02v2Component;
  @ViewChild(AppSubContractPL03Component) subContractPL03: AppSubContractPL03Component;
  @ViewChild(AppSubContractPL04Component) subContractPL04: AppSubContractPL04Component;
  @ViewChild(AppSubContractPL05Component) subContractPL05: AppSubContractPL05Component;
  @ViewChild(AppSubContractPL06Component) subContractPL06: AppSubContractPL06Component;
  @ViewChild(AppSubContractPL07Component) subContractPL07: AppSubContractPL07Component;
  @ViewChild(AppSubContractPL08Component) subContractPL08: AppSubContractPL08Component;
  @ViewChild(AppSubContractPL09Component) subContractPL09: AppSubContractPL09Component;
  @ViewChild(AppSubContractPL10Component) subContractPL10: AppSubContractPL10Component;
  @ViewChild(AppSubContractPL11Component) subContractPL11: AppSubContractPL11Component;
  @ViewChild(AppSubContractPL12Component) subContractPL12: AppSubContractPL12Component;
  @ViewChild(AppSubContractPL13Component) subContractPL13: AppSubContractPL13Component;
  @ViewChild(AppSubContractPL14Component) subContractPL14: AppSubContractPL14Component;
  @ViewChild(AppSubContractPL15Component) subContractPL15: AppSubContractPL15Component;
  @ViewChild(AppSubContractPLBNPLComponent) subContractPLBNPL: AppSubContractPLBNPLComponent;
  @ViewChild(AppSubContractPL15v2Component) subContractPL15v2: AppSubContractPL15v2Component;
  @ViewChild(AppSubContractBBNTComponent) subContractBBNT: AppSubContractBBNTComponent;
  @ViewChild(AppSubContractBBTLComponent) subContractBBTL: AppSubContractBBTLComponent;
  @ViewChild(AppSubContractBBTLv2Component) subContractBBTLv2: AppSubContractBBTLv2Component;
  @ViewChild(AppSubContractBBTTComponent) subContractBBTT: AppSubContractBBTTComponent;
  @ViewChild(AppSubContractCVDenComponent) subContractCVDen: AppSubContractCVDenComponent;
  @ViewChild(AppSubContractCVDiComponent) subContractCVDi: AppSubContractCVDiComponent;
  @ViewChild(AppSubContractCvDchdComponent) subContractCVDCHD : AppSubContractCvDchdComponent;
  @ViewChild(AppSubContractVBUQComponent) subContractVBUQ: AppSubContractVBUQComponent;
  @ViewChild(AppSubContractPL16Component) subContractPL16: AppSubContractPL16Component;
  @ViewChild(AppSubContractOtherComponent) subContractOther: AppSubContractOtherComponent;
  @ViewChild(AppSubContractOtherv2Component) subContractOtherv2: AppSubContractOtherv2Component;
  @ViewChild(AppChildTableMerchantComponent) subMerchantTable: AppChildTableMerchantComponent;
  @ViewChild(AppChildTableBbntComponent) subBbntTable: AppChildTableBbntComponent;


  contractTemplateDB: Array<any> = [];
  contractTemplateDBFull: Array<any> = [];
  S_VERSION: string;
  N_ID_VERSION: string;
  N_ID_TEMPLATE: string;

  public textHD = '';
  public contractList = [];
  public exportContractList = ['HD03-01', 'HD04', 'HD05', 'HD06', 'HD08', 'HD09', 'HD11', 'HD12', 'HD13', 'HD14', 'BBTL', 'BBNT', 'PL01', 'PL02_v2', 'PL03', 'PL04', 'PL05', 'PL06', 'PL07', 'PL08', 'PL09', 'PL12', 'PL11', 'PL14', 'PL15', 'PL16', 'PLBNPL', 'VBUQ','CV-DCHD','PL-K_v2','PL02_v2','PL15v2','BBTLv2','BBTTv2'];

  // contractTemplateList: [];
  contractTemplateList: ContractTemplate[] = [
    // { contractCode: 'HD13', contractName: 'Hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    // { contractCode: 'HD14', contractName: 'Hợp đồng OnePay Ecom (Cá nhân)' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD04', contractName: 'Hợp đồng OneSTART' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD08', contractName: 'Hợp đồng Nội địa 2 bên' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD03-01', contractName: 'Hợp đồng VCB Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD09', contractName: 'Hợp đồng trả góp' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD05', contractName: 'Hợp đồng BIDV Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD06', contractName: 'Hợp đồng Vietinbank Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD01', contractName: 'Hợp đồng VCB Quốc tế' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD02', contractName: 'Hợp đồng VCB Nội địa' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD07', contractName: 'Hợp đồng CUP' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD10', contractName: 'Hợp đồng thanh toán di động' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD11', contractName: 'Hợp đồng PayOut' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '4'},
    { contractCode: 'HD12', contractName: 'Hợp đồng PayCollect' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '5'}
  ];

  contractFullName: ContractTemplate[] = [
    { contractCode: 'HD13', contractName: 'Hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD14', contractName: 'Hợp đồng OnePay Ecom (Cá nhân)' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD04', contractName: 'Hợp đồng cung cấp dịch vụ cổng thanh toán điện tử' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { contractCode: 'HD08', contractName: 'Hợp đồng sử dụng dịch vụ thanh toán trực tuyến của Ngân hàng và ứng dụng thanh toán di động', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD03-01', contractName: 'Giấy đề nghị sử dụng dịch vụ kiêm hợp đồng chấp nhận thanh toán trực tuyến', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD09', contractName: 'Hợp đồng sử dụng dịch vụ trả góp', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD05', contractName: 'Hợp đồng thanh toán trực tuyến', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD06', contractName: 'Hợp đồng chấp nhận thanh toán trực tuyến thẻ Quốc tế', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD01', contractName: 'Hợp đồng VCB Quốc tế', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD02', contractName: 'Hợp đồng VCB Nội địa', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD07', contractName: 'Hợp đồng CUP', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD10', contractName: 'Hợp đồng thanh toán di động', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD11', contractName: 'Hợp đồng PayOut', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { contractCode: 'HD12', contractName: 'Hợp đồng PayCollect', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' }
  ];

  subContractTemplateList: SubContractTemplate[] 
  = [
    { subContractCode: 'PL-K', subContractName: 'Phụ lục điều chỉnh thông tin hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL-K_v2', subContractName: '2 Phụ lục điều chỉnh thông tin hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '7' },
    { subContractCode: 'CV-DCHD', subContractName: 'Công văn điều chỉnh hợp đồng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '3' },
    { subContractCode: 'VBUQ', subContractName: 'Văn bản ủy quyền' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '8' },
    // { subContractCode: 'BBTL', subContractName: 'Biên bản thanh lý' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'BBTLv2', subContractName: 'Biên bản thanh lý' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '9' },
    { subContractCode: 'BBTT', subContractName: 'Biên bản thỏa thuận hợp tác' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '10' },
    // { subContractCode: 'PL02', subContractName: 'Phụ lục thêm Merchant Account' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '6' },
    { subContractCode: 'PL02_v2', subContractName: 'Phụ lục thêm Merchant Account' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '6' },
    { subContractCode: 'PL15', subContractName: 'Phụ lục 3D Secure' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL15v2', subContractName: '2 Phụ lục 3D Secure' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '11' },
    { subContractCode: 'CV-Den', subContractName: 'Công văn đến' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'CV-Di', subContractName: 'Công văn đi' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL09', subContractName: 'Phụ lục trả góp' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL04', subContractName: 'Phụ lục điều chỉnh tài khoản tạm ứng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL05', subContractName: 'Phụ lục điều chỉnh phương thức tạm ứng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL01', subContractName: 'Phụ lục thay đổi thông tin đơn vị' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL14', subContractName: 'Phụ lục bổ sung phạm vi và đối tượng hợp tác' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL03', subContractName: 'Phụ lục thay đổi phí dịch vụ' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL07', subContractName: 'Phụ lục điều chỉnh khoản đảm bảo thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL08', subContractName: 'Phụ lục chấp nhận thêm loại thẻ' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL06', subContractName: 'Phụ lục chấp nhận thanh toán ứng dụng di động' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL11', subContractName: 'Phụ lục tạm ngưng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL12', subContractName: 'Phụ lục kết nối lại dịch vụ' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PLBNPL', subContractName: 'Phụ lục BNPL' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL10', subContractName: 'Phụ lục chiết khấu phí' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL13', subContractName: 'Phụ lục sửa đổi, bổ sung điều khoản hợp đồng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL16', subContractName: 'Phụ lục phí (dành cho Merchant dùng shopify)' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'BBNT', subContractName: 'Biên bản nghiệm thu' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' }
  ];

  subContractFullName: SubContractTemplate[] = [
    { subContractCode: 'PL-K', subContractName: 'Phụ lục điều chỉnh thông tin hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''}, // Phu luc khac
    { subContractCode: 'PL-K_v2', subContractName: '2 Phụ lục điều chỉnh thông tin hợp đồng OnePay Ecom' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '7'}, // Phu luc khac
    { subContractCode: 'CV-DCHD', subContractName: 'Công văn điều chỉnh hợp đồng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '3' },
    { subContractCode: 'VBUQ', subContractName: 'Văn bản ủy quyền' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '8'},
    // { subContractCode: 'BBTL', subContractName: 'Biên bản thanh lý' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'BBTLv2', subContractName: 'Biên bản thanh lý' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '9'},
    { subContractCode: 'BBTT', subContractName: 'Biên bản thỏa thuận hợp tác', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '10' },
    // { subContractCode: 'PL02', subContractName: 'Phụ lục mở thêm tài khoản hệ thống cổng thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '6'},
    { subContractCode: 'PL02_v2', subContractName: 'Phụ lục mở thêm tài khoản hệ thống cổng thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '6'},
    { subContractCode: 'PL15', subContractName: 'Phụ lục ngừng tham gia chương trình 3D-Secure' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL15v2', subContractName: '2 Phụ lục ngừng tham gia chương trình 3D-Secure' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '11'},
    { subContractCode: 'CV-Den', subContractName: 'Công văn đến' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'CV-Di', subContractName: 'Công văn đi' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL09', subContractName: 'Phụ lục trả góp' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL04', subContractName: 'Phụ lục thay đổi tài khoản ghi có' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL05', subContractName: 'Phụ lục thay đổi thời gian/phương thức tạm ứng' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL01', subContractName: 'Phụ lục thay đổi thông tin đơn vị' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL14', subContractName: 'Phụ lục bổ sung phạm vi và đối tượng hợp tác' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL03', subContractName: 'Phụ lục áp dụng ưu đãi phí' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL07', subContractName: 'Phụ lục khoản đảm bảo thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL08', subContractName: 'Phụ lục chấp nhận thanh toán thêm thẻ', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL06', subContractName: 'Phụ lục chấp nhận thanh toán ứng dụng di động' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL11', subContractName: 'Phụ lục tạm ngưng tài khoản hệ thống cổng thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL12', subContractName: 'Phụ lục kết nối lại tài khoản hệ thống cổng thanh toán' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PLBNPL', subContractName: 'Phụ lục BNPL' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL10', subContractName: 'Phụ lục chiết khấu phí' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''},
    { subContractCode: 'PL13', subContractName: 'Phụ lục sửa đổi, bổ sung điều khoản hợp đồng', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'PL16', subContractName: 'Phụ lục phí (dành cho Merchant dùng shopify)', S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: '' },
    { subContractCode: 'BBNT', subContractName: 'Biên bản nghiệm thu' , S_VERSION: '', N_ID_VERSION: '', N_ID_TEMPLATE: ''}    
  ];

  public contractDetail: ContractDetailModel;
  public parentContractDetail: ContractDetailModel;
  public contractOriginal: ContractOriginal;

  public contractDetailTemp: ContractDetailModel;
  public contractOriginalTemp: ContractOriginal;

  contractTemplate: ContractTemplate;
  subContractTemplate: SubContractTemplate;

  public carrerList = [];
  public danhXungList = [];
  public provinceList = [];
  public branchList = [];
  public branchVCBList = [];
  public branchBIDVList = [];
  public branchVietinbankList = [];
  public feeInstallmentList: Array<SubTableFee>;
  public feeBigMerchantList: Array<SubTableFee> = [];
  public feeNormalList: Array<SubTableFee> = [];
  public submitForm: string;
  public abc = '';

  public currentPartner: Partner;

  contractType: string;
  contractId: string;
  parentId: string;
  contractCode: string;
  contractName: string;
  stateContract: string;
  idForm: string;
  idTemplate: string;

  parentContractFullName: string;
  parentContractCode: string;
  parentContractNumber: string;
  parentSignatureDate: string;
  parentPtTamUng: string;
  parentTgTamUng: string;
  parentCardTransactionFee: string;
  parentFeeForCard: string;
  parentStkGiaiKhoanh: string;
  parentAccountBank: string;
  parentContractCodeTempl: string;
  parentTableMerchant: SubTableMerchant[] = [];

  checkEditContractDetail = false;
  checkExportContract = false;
  typeContract = false;
  typeSubContract = false;
  checkModal = false;
  checkMenu = false;
  checkBtnAddNew = true;

  checkAddNewContract = false;
  checkUpdateContract = false;

  checkAddNewSubContract = false;
  checkUpdateSubContract = false;
  viewListContractDetail = false;

  subscription: any;

  version: string;
  numberBusiness: string;

  parentContractCodePL: string;

  constructor(private global: Globals,
    private confirmService: ConfirmService,
    public datePipe: DatePipe,
    private merchantService: MerchantService,
    private toastr: ToastrService,
    public dialogService: DialogService,
    public contractService: ContractService,private route: ActivatedRoute,) {
  }

  openTemplate(value: string){
    this.onSubmitExportTemplateContract(value);

  }

  /**
   * thực hiện các công việc như tải danh sách ngành nghề, tỉnh thành, chi nhánh ngân hàng, và thông tin phí trả góp.
   */
  ngOnInit() {
    this.merchantService.getListCarrrer().subscribe(data => {
      this.carrerList = data.list.map(m => {
        return { label: `${m.tenNganhNghe}`, value: m.id };
      });

    });
    this.merchantService.getListProvince().subscribe(response => {
      this.provinceList = response.list;
    });

    var VCBList = [];
    var VietinbankList = [];
    var BIDVList = [];
    this.merchantService.getContractBranch().subscribe(data => {
      this.branchList = data.list;
      this.branchList.forEach(branch => {
        if (branch.bank === 'all') {
          VCBList.unshift(branch);
          VietinbankList.unshift(branch);
          BIDVList.unshift(branch);
        } else if (branch.bank === 'VCB') {
          VCBList.push(branch);
        } else if (branch.bank === 'VIETIN') {
          VietinbankList.push(branch);
        } else if (branch.bank === 'BIDV') {
          BIDVList.push(branch);
        }
      });
      this.branchVCBList = VCBList.map(m => {
        return { label: `${m.name}`, value: m.id };
      });
      this.branchVietinbankList = VietinbankList.map(m => {
        return { label: `${m.name}`, value: m.id };
      });
      this.branchBIDVList = BIDVList.map(m => {
        return { label: `${m.name}`, value: m.id };
      });
    });

    this.merchantService.getInfoFeeInstallment().subscribe(data => {
      this.feeInstallmentList = data.list;
      this.feeInstallmentList.forEach(element => {
        if (element.feeType === 1) {
          this.feeBigMerchantList.push(element);
        } else {
          this.feeNormalList.push(element);
        }
      });
    });

    this.danhXungList = [
      { value: 'Ông', label: 'Ông' },
      { value: 'Bà', label: 'Bà' },
      { value: 'Mr', label: 'Mr' },
      { value: 'Mrs', label: 'Mrs' }
    ];

    this.contractTemplateDB= [];
    this.contractTemplateDBFull = [];
    this.loadContractTemplateDB();
  }

  loadContractTemplateDB() {
    //custome tạm để chỉ lấy HD13 và HD14
    this.contractService.loadContractTemplate("HD", "").subscribe(response => {
      const data = response?.list || [];
      const filteredContracts = data?.filter(contract => contract.S_CONTRACT_CODE === 'HD13' || contract.S_CONTRACT_CODE === 'HD14').map((conctract) => {
        return { contractName: conctract.S_NAME, contractCode: conctract.S_CONTRACT_CODE ,N_ID_TEMPLATE: conctract.N_ID_TEMPLATE,N_ID_VERSION: conctract.N_ID_VERSION, S_VERSION: conctract.S_VERSION};
      });
      this.contractTemplateList = [...filteredContracts, ...this.contractTemplateList];
    });
    // this.contractService.loadContractTemplate("HD","").subscribe(data => {
    //   this.contractTemplateList = data?.list?.map((conctract) => {
    //     return { contractName: conctract.S_NAME, contractCode: conctract.S_CONTRACT_CODE ,N_ID_TEMPLATE: conctract.N_ID_TEMPLATE,N_ID_VERSION: conctract.N_ID_VERSION, S_VERSION: conctract.S_VERSION};
    //   });
    //   this.contractTemplateDBFull = data?.list;
    // });
    // this.contractService.loadContractTemplate("PL","").subscribe(data => {
    //   this.subContractTemplateList = data?.list?.map((conctract) => {
    //     return { subContractName: conctract.S_NAME, subContractCode: conctract.S_CONTRACT_CODE ,N_ID_TEMPLATE: conctract.N_ID_TEMPLATE,N_ID_VERSION: conctract.N_ID_VERSION, S_VERSION: conctract.S_VERSION};
    //   });
    //   this.contractTemplateDBFull = data?.list;
    // });
  }

  ngOnChanges(changes: SimpleChanges): void {
    //When change curent partner => this.contractService.isEditing() == false
    //Then back to List hợp đồng, avoid save contract id of last partner to current partner
    if (this.contractService.isEditing() == false){
      this.checkEditContractDetail = false;
      this.viewListContractDetail = false;
      this.subResetData();
    }

    if (changes.currentPartnerInput && changes.currentPartnerInput.currentValue) {
      const id = changes.currentPartnerInput.currentValue.id;
      if (id !== undefined) {
        if (this.subscription) {
          this.subscription.unsubscribe();
        }

        //open contract detail if url has query params
        // this.subscription = this.route
        //   .queryParams
        //   .subscribe(params => {
            // let contractId = params.contractId === undefined ? '' : params.contractId;
            this.merchantService.getContractList(id).subscribe(foundedContract => {
              this.contractList = foundedContract.list;
              // if(contractId !== '' && this.contractList.length > 0){
              //   for(let i = 0; i < this.contractList.length; i++){
              //     if(this.contractList[i].contractId == contractId){
              //       this.goContractDetail(this.contractList[i]);
              //       break;
              //     }
              //   }
              // }
            });
            this.currentPartner = this.currentPartnerInput;
            this.refreshAddContract();
          // });
      }
    }
  }

  /**
   * được gọi khi muốn thêm mới một hợp đồng. Hàm thiết lập các thông tin cần thiết cho hợp đồng mới và hiển thị giao diện thêm mới hợp đồng.
   */
  actionAddNewContract() {
    //todo: load data form and template, rồi gán vào 1 tham số , sử dụng khi save contract onSubmitContract
    if (this.viewListContractDetail === false) {  //  Chọn loại hợp đồng từ màn danh sách
      if (this.contractTemplate !== undefined) {
        this.contractDetail = new ContractDetailModel;
        this.contractOriginal = new ContractOriginal;
        this.refreshAddContract();
        this.parentId = undefined;
        this.contractType = 'HD';
        this.contractCode = this.contractTemplate.contractCode;
        this.contractName = this.contractTemplate.contractName;
        this.S_VERSION = this.contractTemplate.S_VERSION;
        this.N_ID_VERSION = this.contractTemplate.N_ID_VERSION;
        this.N_ID_TEMPLATE = this.contractTemplate.N_ID_TEMPLATE;
        // this.version = 'v2';
        this.checkAddNewContract = true;
        if (this.contractTemplate.contractCode === 'HD13') {
          this.contractDetail.carrer = '';
          // this.version = 'v4';
          this.version = 'v5';
        }
        this.closeModal.nativeElement.click();

      } else if (this.subContractTemplate !== undefined) {
        this.contractType = 'PL';
        this.contractCode = this.subContractTemplate.subContractCode;
        this.contractName = this.subContractTemplate.subContractName;
        this.S_VERSION = this.subContractTemplate.S_VERSION;
        this.N_ID_VERSION = this.subContractTemplate.N_ID_VERSION;
        this.N_ID_TEMPLATE = this.subContractTemplate.N_ID_TEMPLATE;
        this.merchantService.getContractDetail(this.parentId).subscribe(response => {
          if (response) {
            this.contractFullName.forEach(element => {
              if (element.contractCode === response.contract.contractCode) {
                this.parentContractDetail = JSON.parse(response.contract.dataContractDetail);
              }
            });
          }
        });

        this.checkAddNewSubContract = true;
        this.closeModal.nativeElement.click();

      }
    } else {  //  Chọn loại hợp đồng từ màn thêm mới hợp đồng
      if (this.contractTemplate !== undefined) {
        this.contractOriginalTemp = this.contractOriginal;
        this.contractOriginal = new ContractOriginal;
        this.contractOriginal.businessName = this.contractOriginalTemp.businessName;
        this.contractOriginal.signatureDate = this.contractOriginalTemp.signatureDate;
        this.contractOriginal.rangeDate = this.contractOriginalTemp.rangeDate;
        this.contractOriginal.contractNumber = this.contractOriginalTemp.contractNumber;
        this.contractOriginal.contractCode = this.contractTemplate.contractCode;
        this.contractOriginal.contractName = this.contractTemplate.contractName;
        this.parentId = undefined;
        this.contractType = 'HD';
        this.contractCode = this.contractTemplate.contractCode;
        this.contractName = this.contractTemplate.contractName;

        this.checkAddNewContract = true;
        this.closeModal.nativeElement.click();

      } else {
        this.contractType = 'PL';
        this.contractCode = this.subContractTemplate.subContractCode;
        this.contractName = this.subContractTemplate.subContractName;

        this.checkAddNewSubContract = true;
        this.closeModal.nativeElement.click();

      }
    }

    this.checkEditContractDetail = true;
    this.contractService.setEditing(true);
    this.viewListContractDetail = true;
  }

  /**
   * làm mới (clear) thông tin hợp đồng khi thêm mới.
   */
  refreshAddContract() {
    if (this.contractOriginal == undefined) {
      this.contractOriginal = new ContractOriginal;
    }
    if (this.contractDetail == undefined) {
      this.contractDetail = new ContractDetailModel;
    }
    this.contractOriginal.businessName = this.currentPartner.partnerName;
    this.contractDetail.website = this.currentPartner.website;
    this.contractDetail.addressBusiness = this.currentPartner.addressLine2 + ', ' + this.getProvinceName(this.currentPartner.provinceId);
    this.contractDetail.addressOffice = this.currentPartner.addressLine1 + ', ' + this.getProvinceName(this.currentPartner.provinceIdOffice);
    this.contractDetail.phone = this.currentPartner.phoneNumber;
    this.contractDetail.carrer = this.currentPartner.careerId;
  }

  /**
   * 
   * @param provinceId 
   * @returns trả về tên tỉnh thành dựa trên provinceId.
   */
  getProvinceName(provinceId: string): string {
    var provinceName = '';
    if (provinceId && provinceId !== '') {
      this.provinceList.forEach(province => {
        if (provinceId === province.id) {
          provinceName = province.tinh;
        }
      });
    }
    return provinceName;
  }
/**
 * tải danh sách hợp đồng dựa trên partnerId.
 * @param partnerId 
 */
  getContractsByPartnerId(partnerId) {
    this.merchantService.getContractList(partnerId).subscribe(foundedContract => {
      this.contractList = foundedContract.list;
    });
  }

  /**
   * thiết lập loại hợp đồng
   */
  checkTypeContract() {

    this.typeContract = true;
    this.typeSubContract = false;
  }
 /**
   * thiết lập loại phụ lục hợp đồng
   * Gán thông tin vào phụ lục từ hợp đồng cha khi tạo mới phụ lục
   * @param data gọi đến từ html khi click thêm mới phụ lục, data là dữ liệu hợp đồng cha
   */
  checkTypeSubContract(data: any) {
    //Khi tạo mới phụ lục, gán contractCode của hợp đồng cha vào parentContractCodePL để sử dụng trong phụ lục PL15v2
    this.parentContractCodePL = data.contractCode;
    this.contractDetail = JSON.parse(data.dataContractDetail);
    this.contractOriginal = new ContractOriginal;
    this.contractOriginal.contractType = 'PL';
    this.contractOriginal.id = '0';
    this.contractOriginal.state = '';
    this.contractOriginal.partnerId = data.partnerId.toString();
    this.contractOriginal.parentId = data.contractId.toString();
    this.contractOriginal.businessName = data.businessName;
    this.contractOriginal.contractNumber = data.contractNumber;
    this.contractOriginal.userAction = data.userAction;
    this.contractOriginal.signatureDate = data.signatureDate;
    this.contractOriginal.rangeDate = data.rangeDate;
    // if (data.rangeDate) {
    //   this.contractOriginal.rangeDate = new Date(data.rangeDate);
    // } else {
    //   this.contractOriginal.rangeDate = null;
    // }
    console.log('rangeDate', data.rangeDate,'rangeDateString', data.rangeDateString);
    this.contractOriginal.representative = data.representative;
    this.contractType = 'PL';
    this.parentId = this.contractOriginal.parentId;
    this.contractId = this.contractOriginal.id;
    this.parentId = this.contractOriginal.parentId;
    this.parentContractCodeTempl = data.contractCode;

    this.typeContract = false;
    this.typeSubContract = true;
    this.checkAddNewSubContract = true;

    // this.contractService.loadContractTemplate("PL",data.contractCode).subscribe(data => {
    //   this.subContractTemplateList = data?.list?.map((conctract) => {
    //     return { subContractName: conctract.S_NAME, subContractCode: conctract.S_CONTRACT_CODE ,N_ID_TEMPLATE: conctract.N_ID_TEMPLATE,N_ID_VERSION: conctract.N_ID_VERSION, S_VERSION: conctract.S_VERSION};
    //   });
    //   this.contractTemplateDBFull = data?.list;
    // });
  }

  /**
   * xử lý việc gửi thông tin hợp đồng lên server. kiểm tra và thiết lập các thông tin cần thiết trước khi gọi API để lưu hoặc cập nhật hợp đồng.
   */
  onSubmitContract(type: string) {
    const model = new ContractOriginal(this.contractOriginal);
    const modelDetail = new ContractDetailModel(this.contractDetail);
    console.log('onsubmitContract', this.contractDetail);

    model.id = this.contractId === undefined || this.contractId === '0' ? '' : this.contractId;
    model.partnerId = this.currentPartnerId;
    model.parentId = this.parentId === undefined ? '0' : this.parentId;
    model.contractCode = this.contractCode;
    model.contractName = this.contractName;
    model.userAction = this.global.activeProfile.name;
    model.contractType = this.contractType;
    model.businessName = this.contractOriginal.businessName === undefined ? '' : this.contractOriginal.businessName;
    model.contractNumber = this.contractOriginal.contractNumber === undefined ? '' : this.handleContractNumber(this.contractOriginal.contractNumber, this.contractCode);
    model.signatureDate = this.contractOriginal.signatureDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'yyyy-MM-dd');
    model.rangeDate = this.contractOriginal.rangeDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'yyyy-MM-dd');
    model.state = this.contractOriginal.state === '' ? 'wait for approve' : this.contractOriginal.state;
    model.representative = this.contractOriginal.representative === undefined ? '' : this.contractOriginal.representative;
    model.idForm = this.N_ID_TEMPLATE === undefined ? '' : this.N_ID_TEMPLATE;
    model.idTemplate = this.N_ID_TEMPLATE === undefined ? '' : this.N_ID_TEMPLATE;
    model.idVersion = this.S_VERSION === undefined ? '' : this.S_VERSION;

    modelDetail.peopleId = this.contractDetail.peopleId === undefined ? '' : this.contractDetail.peopleId;
    modelDetail.carrer = this.contractDetail.carrer === undefined ? '' : this.contractDetail.carrer;
    modelDetail.branch = this.contractDetail.branch === undefined ? '' : this.contractDetail.branch;
    modelDetail.shortName = this.contractDetail.shortName === undefined ? '' : this.contractDetail.shortName;
    modelDetail.addressBusiness = this.contractDetail.addressBusiness === undefined ? '' : this.contractDetail.addressBusiness;
    modelDetail.addressOffice = this.contractDetail.addressOffice === undefined ? '' : this.contractDetail.addressOffice;
    modelDetail.website = this.contractDetail.website === undefined ? '' : this.contractDetail.website;
    modelDetail.email = this.contractDetail.email === undefined ? '' : this.contractDetail.email;
    modelDetail.phone = this.contractDetail.phone === undefined ? '' : this.contractDetail.phone;
    modelDetail.numberBusiness = this.contractDetail.numberBusiness === undefined ? '' : this.contractDetail.numberBusiness;
    modelDetail.accountBank = this.contractDetail.accountBank === undefined ? '' : this.contractDetail.accountBank;
    modelDetail.signaturer = this.contractDetail.signaturer === undefined ? '' : this.contractDetail.signaturer;
    modelDetail.position = this.contractDetail.position === undefined ? '' : this.contractDetail.position;
    modelDetail.cardType = this.contractDetail.cardType === undefined ? '' : this.contractDetail.cardType;
    modelDetail.infoCard = this.contractDetail.infoCard === undefined ? '' : this.contractDetail.infoCard;
    modelDetail.khoanDamBao = this.contractDetail.khoanDamBao === undefined ? '' : this.contractDetail.khoanDamBao;
    modelDetail.khoanDamBaoMoi = this.contractDetail.khoanDamBaoMoi === undefined ? '' : this.contractDetail.khoanDamBaoMoi;
    modelDetail.kyHanFD = this.contractDetail.kyHanFD === undefined ? '' : this.contractDetail.kyHanFD;
    modelDetail.otherInfo = this.contractDetail.otherInfo === undefined ? '' : this.contractDetail.otherInfo;
    modelDetail.dayApprove = this.contractDetail.dayApprove === undefined ? '' : this.contractDetail.dayApprove;
    modelDetail.subTableMerchant = this.contractDetail.subTableMerchant === undefined ? [] : this.contractDetail.subTableMerchant;
    modelDetail.subTableNoMerchant01 = this.contractDetail.subTableNoMerchant01 === undefined ? [] : this.contractDetail.subTableNoMerchant01;
    modelDetail.subTableNoMerchant001 = this.contractDetail.subTableNoMerchant001 === undefined ? [] : this.contractDetail.subTableNoMerchant001;
    modelDetail.subTableNoMerchant02 = this.contractDetail.subTableNoMerchant02 === undefined ? [] : this.contractDetail.subTableNoMerchant02;
    modelDetail.subTableNoMerchant002 = this.contractDetail.subTableNoMerchant002 === undefined ? [] : this.contractDetail.subTableNoMerchant002;
    modelDetail.subTableNoMerchant03 = this.contractDetail.subTableNoMerchant03 === undefined ? [] : this.contractDetail.subTableNoMerchant03;
    modelDetail.subTableNoMerchant003 = this.contractDetail.subTableNoMerchant003 === undefined ? [] : this.contractDetail.subTableNoMerchant003;
    modelDetail.subTableFee = this.contractDetail.subTableFee === undefined ? [] : this.contractDetail.subTableFee;
    modelDetail.subTableMerchantID = this.contractDetail.subTableMerchantID === undefined ? [] : this.contractDetail.subTableMerchantID;
    modelDetail.subTableShopifyMerchantID = this.contractDetail.subTableShopifyMerchantID === undefined ? [] : this.contractDetail.subTableShopifyMerchantID;
    modelDetail.subTableNonShopifyMerchantID = this.contractDetail.subTableNonShopifyMerchantID === undefined ? [] : this.contractDetail.subTableNonShopifyMerchantID;
    modelDetail.cardList = this.contractDetail.cardListArray === undefined ? '' : this.contractDetail.cardListArray.toString();
    modelDetail.paygateList = this.contractDetail.paygateListArray === undefined ? '' : this.contractDetail.paygateListArray.toString();
    modelDetail.stkGiaiKhoanh = this.contractDetail.stkGiaiKhoanh === undefined ? '' : this.contractDetail.stkGiaiKhoanh;
    modelDetail.tgTamUng = this.contractDetail.tgTamUng === undefined ? '' : this.contractDetail.tgTamUng;
    modelDetail.tgTamUngSelection = this.contractDetail.tgTamUngSelection === undefined ? '' : this.contractDetail.tgTamUngSelection;
    modelDetail.inputTgTamUngKhac = this.contractDetail.inputTgTamUngKhac === undefined ? '' : this.contractDetail.inputTgTamUngKhac;
    modelDetail.inputTgTamUngKhacKetThucPhien = this.contractDetail.inputTgTamUngKhacKetThucPhien === undefined ? '' : this.contractDetail.inputTgTamUngKhacKetThucPhien;
    modelDetail.ptTamUng = this.contractDetail.ptTamUng === undefined ? '' : this.contractDetail.ptTamUng;
    modelDetail.ptTamUngMoi = this.contractDetail.ptTamUngMoi === undefined ? '' : this.contractDetail.ptTamUngMoi;
    modelDetail.otherCard = this.contractDetail.otherCard === undefined ? '' : this.contractDetail.otherCard;
    modelDetail.vietNamCard = this.contractDetail.vietNamCard === undefined ? '' : this.contractDetail.vietNamCard;
    modelDetail.cardTransactionFee = this.contractDetail.cardTransactionFee === undefined ? '' : this.contractDetail.cardTransactionFee;
    modelDetail.monthFee = this.contractDetail.monthFee === undefined ? '' : this.contractDetail.monthFee;
    modelDetail.registerFee = this.contractDetail.registerFee || '';
    modelDetail.shopifyFee = this.contractDetail.shopifyFee || '';
    modelDetail.feeForCard = this.contractDetail.feeForCard === undefined ? '' : this.contractDetail.feeForCard;
    modelDetail.feeForMobile = this.contractDetail.feeForMobile === undefined ? '' : this.contractDetail.feeForMobile;

    modelDetail.detailEmailAddress = this.contractDetail.detailEmailAddress === undefined ? '' : this.contractDetail.detailEmailAddress;
    modelDetail.alertEmailAddress = this.contractDetail.alertEmailAddress === undefined ? '' : this.contractDetail.alertEmailAddress;
    modelDetail.kyQuyValue = this.contractDetail.kyQuyValue === undefined ? '' : this.contractDetail.kyQuyValue;
    modelDetail.kyQuy = this.contractDetail.kyQuy === undefined ? '' : this.contractDetail.kyQuy;
    modelDetail.khoanhGiuValue = this.contractDetail.khoanhGiuValue === undefined ? '' : this.contractDetail.khoanhGiuValue;
    modelDetail.khoanhGiu = this.contractDetail.khoanhGiu === undefined ? '' : this.contractDetail.khoanhGiu;
    modelDetail.americanCard = this.contractDetail.americanCard === undefined ? '' : this.contractDetail.americanCard;
    modelDetail.jcbCard = this.contractDetail.jcbCard === undefined ? '' : this.contractDetail.jcbCard;
    modelDetail.masterCard = this.contractDetail.masterCard === undefined ? '' : this.contractDetail.masterCard;
    modelDetail.visaCard = this.contractDetail.visaCard === undefined ? '' : this.contractDetail.visaCard;
    modelDetail.domesticCard02 = this.contractDetail.domesticCard02 === undefined ? '' : this.contractDetail.domesticCard02;
    modelDetail.internationalCard02 = this.contractDetail.internationalCard02 === undefined ? '' : this.contractDetail.internationalCard02;
    modelDetail.domesticCardToken02 = this.contractDetail.domesticCardToken02 === undefined ? '' : this.contractDetail.domesticCardToken02;
    modelDetail.domesticCard01 = this.contractDetail.domesticCard01 === undefined ? '' : this.contractDetail.domesticCard01;
    modelDetail.internationalCard01 = this.contractDetail.internationalCard01 === undefined ? '' : this.contractDetail.internationalCard01;
    modelDetail.domesticCardToken01 = this.contractDetail.domesticCardToken01 === undefined ? '' : this.contractDetail.domesticCardToken01;
    modelDetail.hinhThucThuPhiTheoThang03 = this.contractDetail.hinhThucThuPhiTheoThang03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang03;
    modelDetail.hinhThucThuPhiTheoNgay03 = this.contractDetail.hinhThucThuPhiTheoNgay03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay03;
    modelDetail.hinhThucBaoCo03 = this.contractDetail.hinhThucBaoCo03 === undefined ? '' : this.contractDetail.hinhThucBaoCo03;
    modelDetail.hinhThucThuPhiTheoThang02 = this.contractDetail.hinhThucThuPhiTheoThang02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang02;
    modelDetail.hinhThucThuPhiTheoNgay02 = this.contractDetail.hinhThucThuPhiTheoNgay02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay02;
    modelDetail.hinhThucBaoCo02 = this.contractDetail.hinhThucBaoCo02 === undefined ? '' : this.contractDetail.hinhThucBaoCo02;
    modelDetail.hinhThucThuPhiTheoThang01 = this.contractDetail.hinhThucThuPhiTheoThang01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang01;
    modelDetail.hinhThucThuPhiTheoNgay01 = this.contractDetail.hinhThucThuPhiTheoNgay01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay01;
    modelDetail.hinhThucBaoCo01 = this.contractDetail.hinhThucBaoCo01 === undefined ? '' : this.contractDetail.hinhThucBaoCo01;
    modelDetail.secure03 = this.contractDetail.secure03 === undefined ? '' : this.contractDetail.secure03;
    modelDetail.secure02 = this.contractDetail.secure02 === undefined ? '' : this.contractDetail.secure02;
    modelDetail.secure01 = this.contractDetail.secure01 === undefined ? '' : this.contractDetail.secure01;
    modelDetail.accountFee03 = this.contractDetail.accountFee03 === undefined ? '' : this.contractDetail.accountFee03;
    modelDetail.accountFee02 = this.contractDetail.accountFee02 === undefined ? '' : this.contractDetail.accountFee02;
    modelDetail.accountFee01 = this.contractDetail.accountFee01 === undefined ? '' : this.contractDetail.accountFee01;
    modelDetail.internationalCard03 = this.contractDetail.internationalCard03 === undefined ? '' : this.contractDetail.internationalCard03;
    modelDetail.internationalCard04 = this.contractDetail.internationalCard04 === undefined ? '' : this.contractDetail.internationalCard04;
    modelDetail.domesticCard03 = this.contractDetail.domesticCard03 === undefined ? '' : this.contractDetail.domesticCard03;
    modelDetail.domesticCard04 = this.contractDetail.domesticCard04 === undefined ? '' : this.contractDetail.domesticCard04;
    modelDetail.changeContent = this.contractDetail.changeContent === undefined ? '' : this.contractDetail.changeContent;
    modelDetail.subTablePause = this.contractDetail.subTablePause === undefined ? [] : this.contractDetail.subTablePause;
    modelDetail.typeFeeInstallment = this.contractDetail.typeFeeInstallment === undefined ? '' : this.contractDetail.typeFeeInstallment;
    modelDetail.authorizedPersonName = this.contractDetail.authorizedPersonName ?? '';
    modelDetail.authorizedPersonId = this.contractDetail.authorizedPersonId ?? '';
    modelDetail.authorizedIssuedDate = this.contractDetail.authorizedIssuedDate ?? '';
    modelDetail.authorizedIssuedBy = this.contractDetail.authorizedIssuedBy ?? '';
    modelDetail.accountName = this.contractDetail.accountName ?? '';
    modelDetail.accountNumber = this.contractDetail.accountNumber ?? '';
    modelDetail.authorizationPeriodFrom = this.contractDetail.authorizationPeriodFrom ?? '';
    modelDetail.authorizationPeriodTo = this.contractDetail.authorizationPeriodTo ?? '';
    modelDetail.authorizedBirthDate = this.contractDetail.authorizedBirthDate ?? '';
    modelDetail.authorizedAddress = this.contractDetail.authorizedAddress ?? '';
    modelDetail.authorizationNumber = this.contractDetail.authorizationNumber ?? '';

    modelDetail.feeService = this.contractDetail.feeService === undefined ? '' : this.contractDetail.feeService;
    modelDetail.autoFillStandardFee = this.contractDetail.autoFillStandardFee === undefined ? '' : this.contractDetail.autoFillStandardFee;
    modelDetail.feeTransDomesticAndApp = this.contractDetail.feeTransDomesticAndApp || '';
    modelDetail.feeApp = this.contractDetail.feeApp === undefined ? '' : this.contractDetail.feeApp;
    modelDetail.feeVietQR = this.contractDetail.feeVietQR === undefined ? '' : this.contractDetail.feeVietQR;
    modelDetail.feePaymentDomesticAndApp = this.contractDetail.feePaymentDomesticAndApp === undefined ? '' : this.contractDetail.feePaymentDomesticAndApp;
    modelDetail.feeTransInternational = this.contractDetail.feeTransInternational || '';
    modelDetail.approveCardType01International = this.contractDetail.approveCardType01International === undefined ? '' : this.contractDetail.approveCardType01International;
    modelDetail.americanExpress01International = this.contractDetail.americanExpress01International === undefined ? '' : this.contractDetail.americanExpress01International;
    modelDetail.approveCardType02International = this.contractDetail.approveCardType02International === undefined ? '' : this.contractDetail.approveCardType02International;
    modelDetail.americanExpress02International = this.contractDetail.americanExpress02International === undefined ? '' : this.contractDetail.americanExpress02International;
    modelDetail.insideDomestic = this.contractDetail.insideDomestic === undefined ? '' : this.contractDetail.insideDomestic;
    modelDetail.outsideDomestic = this.contractDetail.outsideDomestic === undefined ? '' : this.contractDetail.outsideDomestic;
    modelDetail.hinhThucThuPhi = this.contractDetail.hinhThucThuPhi === undefined ? '' : this.contractDetail.hinhThucThuPhi;
    modelDetail.khoanDamBaoSelection = this.contractDetail.khoanDamBaoSelection === undefined ? '' : this.contractDetail.khoanDamBaoSelection;
    modelDetail.khoanDamBaoInput = this.contractDetail.khoanDamBaoInput === undefined ? '' : this.contractDetail.khoanDamBaoInput;
    modelDetail.inputHinhThucThuPhiKhac = this.contractDetail.inputHinhThucThuPhiKhac === undefined ? '' : this.contractDetail.inputHinhThucThuPhiKhac;
    modelDetail.inputTgTamUngKhac = this.contractDetail.inputTgTamUngKhac === undefined ? '' : this.contractDetail.inputTgTamUngKhac;
    modelDetail.inputTgTamUngKhacKetThucPhien = this.contractDetail.inputTgTamUngKhacKetThucPhien === undefined ? '' : this.contractDetail.inputTgTamUngKhacKetThucPhien;
    modelDetail.kyQuyType = this.contractDetail.kyQuyType === undefined ? '' : this.contractDetail.kyQuyType;
    modelDetail.kyQuyAutoFill = this.contractDetail.kyQuyAutoFill === undefined ? '' : this.contractDetail.kyQuyAutoFill;
    modelDetail.openByBank = this.contractDetail.openByBank === undefined ? '' : this.contractDetail.openByBank;
    modelDetail.inputKyQuyKhac = this.contractDetail.inputKyQuyKhac === undefined ? '' : this.contractDetail.inputKyQuyKhac;
    modelDetail.danhXung = this.contractDetail.danhXung === undefined ? '' : this.contractDetail.danhXung;
    modelDetail.keepPercent = this.contractDetail.keepPercent === undefined ? '' : this.contractDetail.keepPercent;

    modelDetail.percentQrMobile = this.contractDetail.percentQrMobile === undefined ? '' : this.contractDetail.percentQrMobile;
    modelDetail.percentQrGrab = this.contractDetail.percentQrGrab === undefined ? '' : this.contractDetail.percentQrGrab;
    modelDetail.percentQrShopee = this.contractDetail.percentQrShopee === undefined ? '' : this.contractDetail.percentQrShopee;
    modelDetail.percentQrZalo = this.contractDetail.percentQrZalo === undefined ? '' : this.contractDetail.percentQrZalo;
    modelDetail.percentQrMoMo = this.contractDetail.percentQrMoMo === undefined ? '' : this.contractDetail.percentQrMoMo;
    modelDetail.percentQrOther = this.contractDetail.percentQrOther === undefined ? '' : this.contractDetail.percentQrOther;
    modelDetail.inforOther = this.contractDetail.inforOther === undefined ? '' : this.contractDetail.inforOther;
    modelDetail.percentVietQR = this.contractDetail.percentVietQR === undefined ? '' : this.contractDetail.percentVietQR;

    //Phu phi shopify PL16
    modelDetail.autoFillStandardFeeShopify = this.contractDetail.autoFillStandardFeeShopify === undefined ? '' : this.contractDetail.autoFillStandardFeeShopify;
    modelDetail.feeTransDomesticAndAppShopify = this.contractDetail.feeTransDomesticAndAppShopify === undefined ? '' : this.contractDetail.feeTransDomesticAndAppShopify;
    modelDetail.feeAppShopify = this.contractDetail.feeAppShopify === undefined ? '' : this.contractDetail.feeAppShopify;
    modelDetail.feePaymentDomesticAndAppShopify = this.contractDetail.feePaymentDomesticAndAppShopify === undefined ? '' : this.contractDetail.feePaymentDomesticAndAppShopify;
    modelDetail.feeTransInternationalShopify = this.contractDetail.feeTransInternationalShopify === undefined ? '' : this.contractDetail.feeTransInternationalShopify;
    modelDetail.approveCardType01InternationalShopify = this.contractDetail.approveCardType01InternationalShopify === undefined ? '' : this.contractDetail.approveCardType01InternationalShopify;
    modelDetail.americanExpress01InternationalShopify = this.contractDetail.americanExpress01InternationalShopify === undefined ? '' : this.contractDetail.americanExpress01InternationalShopify;
    modelDetail.approveCardType02InternationalShopify = this.contractDetail.approveCardType02InternationalShopify === undefined ? '' : this.contractDetail.approveCardType02InternationalShopify;
    modelDetail.americanExpress02InternationalShopify = this.contractDetail.americanExpress02InternationalShopify === undefined ? '' : this.contractDetail.americanExpress02InternationalShopify;
    modelDetail.feeServiceShopify = this.contractDetail.feeServiceShopify === undefined ? '' : this.contractDetail.feeServiceShopify;

    modelDetail.percentQrMobileShopify = this.contractDetail.percentQrMobileShopify === undefined ? '' : this.contractDetail.percentQrMobileShopify;
    modelDetail.percentQrGrabShopify = this.contractDetail.percentQrGrabShopify === undefined ? '' : this.contractDetail.percentQrGrabShopify;
    modelDetail.percentQrShopeeShopify = this.contractDetail.percentQrShopeeShopify === undefined ? '' : this.contractDetail.percentQrShopeeShopify;
    modelDetail.percentQrZaloShopify = this.contractDetail.percentQrZaloShopify === undefined ? '' : this.contractDetail.percentQrZaloShopify;
    modelDetail.percentQrMoMoShopify = this.contractDetail.percentQrMoMoShopify === undefined ? '' : this.contractDetail.percentQrMoMoShopify;
    modelDetail.percentQrOtherShopify = this.contractDetail.percentQrOtherShopify === undefined ? '' : this.contractDetail.percentQrOtherShopify;
    modelDetail.inforOtherShopify = this.contractDetail.inforOtherShopify === undefined ? '' : this.contractDetail.inforOtherShopify;

    modelDetail.bnplFee = this.contractDetail.bnplFee === undefined ? '' : this.contractDetail.bnplFee;
    modelDetail.bnplFeeHomeCredit = this.contractDetail.bnplFeeHomeCredit || '';
    modelDetail.bnplFeeFundiin = this.contractDetail.bnplFeeFundiin || '';
    modelDetail.bnplFeeAmigo = this.contractDetail.bnplFeeAmigo || '';
    modelDetail.bnplFeeKredivo = this.contractDetail.bnplFeeKredivo || '';

    modelDetail.noiDungThayDoi = this.contractDetail.noiDungThayDoi;
    modelDetail.uyQuyen = this.contractDetail.uyQuyen;

    //HD14
    modelDetail.representative = this.contractDetail.representative === undefined ? '' : this.contractDetail.representative;
    modelDetail.permanentAddress = this.contractDetail.permanentAddress === undefined ? '' : this.contractDetail.permanentAddress;
    modelDetail.issuedBy = this.contractDetail.issuedBy === undefined ? '' : this.contractDetail.issuedBy;
    modelDetail.hoaDonVAT = this.contractDetail.hoaDonVAT === undefined ? '' : this.contractDetail.hoaDonVAT;

    //CV DCHD
    modelDetail.adjustment = this.contractDetail.adjustment === undefined ? [] : this.contractDetail.adjustment;
    modelDetail.contractContentBasis = this.contractDetail.contractContentBasis === undefined ? '' : this.contractDetail.contractContentBasis;

    //BBTLv2
    modelDetail.ngayThanhLy = this.contractDetail.ngayThanhLy ?? '';
    //PL15v2 phụ lục 3D
    modelDetail.thoiGianApDung = this.contractDetail.thoiGianApDung ?? '';
    //type create
    if ((this.contractId === undefined || this.contractId === '0') && this.version) {
      console.log('create')
      modelDetail.version = this.version;
    }
    //type edit new contract
    else if ((this.contractId != undefined || this.contractId != '0') && this.version) {
      console.log('edit new contract')
      modelDetail.version = 'v5';
    }
    //type edit old contract
    else {
      console.log('old contract')
      modelDetail.version = '';
    }

    //gọi đến khi lưu HD
    if (type === '0') {
      console.log('type1:' + type);
      console.log('modelDetail.version: ' + modelDetail.version)
      //todo: thêm thông tin form và template vào body lấy từ data mới load khi click add new contract actionAddNewContract -> rồi update service ContractHandler.java pushContract() lưu DB
      const body = {
        'id': model.id,
        'partnerId': model.partnerId,
        'parentId': model.parentId,
        'contractCode': model.contractCode,
        'contractName': model.contractName,
        'businessName': model.businessName,
        'signatureDate': model.signatureDate,
        'rangeDate': model.rangeDate,
        'userAction': model.userAction,
        'state': model.state,
        'contractNumber': model.contractNumber,
        'contractType': model.contractType,
        'order': this.handleContractOrder(model.contractCode),
        'representative': model.representative,

        'dataContractDetail': JSON.stringify(modelDetail),
        'idForm': model.idForm,
        'idTemplate': model.idTemplate,
        'idVersion': model.idVersion
      };
      this.merchantService.pushContract(body).subscribe(data => {
        if (data.n_result === '200') {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.success('Cập nhật ' + this.contractName + ' thành công', 'Thành công');
            this.checkEditContractDetail = false;
            this.contractService.setEditing(false);
          } else {
            this.toastr.success('Tạo mới ' + this.contractName + ' thành công', 'Thành công');
            this.backContractManagement();
          }
          if (this.subContractOther) {
            this.subContractOther.genPreviousContractDetail();
          }
          if (this.subContractOtherv2) {
            this.subContractOtherv2.genPreviousContractDetail();
          }
        } else {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.error('Cập nhật ' + this.contractName + ' không thành công', 'Thất bại');
          } else {
            this.toastr.error('Tạo mới ' + this.contractName + ' không thành công', 'Thất bại');
          }
        }
      });

    } else {
      console.log('type2:' + type);
      var checkExport = false;
      if (this.contractCode === 'HD03-01' || this.contractCode === 'HD04' || this.contractCode === 'HD05' || this.contractCode === 'HD06' || this.contractCode === 'HD08'
        || this.contractCode === 'HD09' || this.contractCode === 'HD11' || this.contractCode === 'HD12' || this.contractCode === 'HD13' || this.contractCode === 'PL01'
        || this.contractCode === 'PL02' || this.contractCode === 'PL03' || this.contractCode === 'PL04' || this.contractCode === 'PL05' || this.contractCode === 'PL06'
        || this.contractCode === 'PL07'|| this.contractCode === 'PL-K' || this.contractCode === 'PL08' || this.contractCode === 'PL09' || this.contractCode === 'PL12' || this.contractCode === 'PL14' || this.contractCode === 'PLBNPL' || this.contractCode === 'PL16') {
        checkExport = true;
      } else if (this.contractCode === 'BBTL' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD07' || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01' || this.parentContractCode === 'HD13')) {
        checkExport = true;
      } else if ((this.contractCode === 'PL11' || this.contractCode === 'PL15' || this.contractCode === 'PL15v2') && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'BBNT' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'VBUQ') {
        checkExport = true;
      } if (this.contractCode === 'BBNT') {
        checkExport = true;
      }

      if (checkExport === true) {

        var contractCode = '';
        var carrer = '';
        var branch = '';
        var unionBox = '';
        var otherBankBox = '';
        var appMobileBox = '';
        var visaBox = '';
        var masterCardBox = '';
        var amexBox = '';
        var jcbBox = '';
        var unionBox = '';
        var domesticBox = '';
        var mpgsBox = '';
        var migsBox = '';
        var cyberSourceBox = '';
        var accountNumberBaoCo = '';
        var accountNameBaoCo = '';
        var bankBaoCo = '';
        var accountNumberThuPhi = '';
        var accountNameThuPhi = '';
        var bankThuPhi = '';

        var americanExpressBox = '';
        var approveDomesticCardBox = '';
        var approveInternationalCardBox = '';
        var approveInstallmentBox = '';
        var approveOnepayDomesticCardBox = '';
        var approveOnepayMobileAppBox = '';
        var approveBnblBox = '';
        var hinhThucThuPhi = '';

        if (model.contractCode === 'BBTL' || model.contractCode === 'PL11' || model.contractCode === 'PL15') {
          if (model.contractCode === 'BBTL' && this.parentContractCode === 'HD07') {
            contractCode = model.contractCode + '-CUP';
          } else if (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD13') {
            contractCode = model.contractCode + '-2BEN';
          } else {
            contractCode = model.contractCode + '-3BEN';
          }
        } else if (model.contractCode === 'BBNT') {
          contractCode = model.contractCode + '-' + this.parentContractCode;
        } else if (model.contractCode === 'HD13' && (modelDetail.version=='' || modelDetail.version=='v2')) {
          var checkInstallment = '';
          var checkShopify = '';
          modelDetail.cardListArray.forEach(element => {
            if (element === 'ApproveInstallment') {
              checkInstallment = element;
            }
            if (element === 'ApproveShopify') {
              checkShopify = element;
            }
          });

          if (checkInstallment !== '' && modelDetail.kyQuyType === 'kyQuyKhac' ||
            modelDetail.tgTamUngSelection === 'other' || modelDetail.feeService === 'specialFee' || modelDetail.hinhThucThuPhi === 'Khac') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH0';
              }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH1';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH2';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH3';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH4';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH5';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH6';
            }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH7';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH8';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH9';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH10';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              }else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH11';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH12';
              }
          } else if (checkShopify !== '') {
            contractCode = model.contractCode + '_Shopify';
          } else {
            if (modelDetail.version != 'v2') {
              contractCode = model.contractCode + '_TH00';
            } else {
              contractCode = model.contractCode + '_TH0';
            }
          }
        } else if (model.contractCode === 'HD13' && (modelDetail.version=='v3' || modelDetail.version=='v4' || modelDetail.version=='v5')) {
          contractCode = 'HD13';
        } else {
          contractCode = model.contractCode;
        }

        this.branchList.forEach(element => {
          if (element.id === modelDetail.branch) {
            branch = element.name;
          }
        });

        this.carrerList.forEach(element => {
          if (element.value === modelDetail.carrer) {
            carrer = element.label;
          }
        });

        if (modelDetail.cardListArray.length > 0) {
          modelDetail.cardListArray.forEach(element => {
            if (element === 'appMobile') {
              appMobileBox = 'appMobileBox';
            } else if (element === 'vcbND' || element === 'otherBank') {
              otherBankBox = 'otherBankBox';
            } else if (element === 'Visa') {
              visaBox = 'Visa';
            } else if (element === 'AMEX') {
              amexBox = 'AMEX';
            } else if (element === 'JCB') {
              jcbBox = 'JCB';
            } else if (element === 'UnionPay') {
              unionBox = 'UnionPay';
            } else if (element === 'domesticCard') {
              domesticBox = 'domesticCard';
            } else if (element === 'AmericanExpress') {
              americanExpressBox = 'AmericanExpress';
            } else if (element === 'ApproveDomesticCard') {
              approveDomesticCardBox = 'ApproveDomesticCard';
            } else if (element === 'ApproveInternationalCard') {
              approveInternationalCardBox = 'ApproveInternationalCard';
            } else if (element === 'ApproveInstallment') {
              approveInstallmentBox = 'ApproveInstallment';
            } else if (element === 'ApproveOnepayDomesticCard') {
              approveOnepayDomesticCardBox = 'ApproveOnepayDomesticCard';
            } else if (element === 'ApproveOnepayMobileApp') {
              approveOnepayMobileAppBox = 'ApproveOnepayMobileApp';
            } else if (element === 'MasterCard') {
              masterCardBox = 'MasterCard';
            } else if (element === 'ApproveBNPL') {
              approveBnblBox = 'ApproveBNPL';
            }
          });
        }

        if (modelDetail.paygateListArray.length > 0) {
          modelDetail.paygateListArray.forEach(element => {
            if (element === 'MPGS') {
              mpgsBox = 'MPGS';
            } else if (element === 'MiGS') {
              migsBox = 'MiGS';
            } else if (element === 'Cybersource') {
              cyberSourceBox = 'Cybersource';
            }
          });
        }
        //Check only BNBL option then block export file
        if (model.contractCode === 'HD13' && approveBnblBox === 'ApproveBNPL' && visaBox !== 'Visa' && masterCardBox !== 'MasterCard' && amexBox !== 'AMEX' && jcbBox !== 'JCB' && domesticBox !== 'domesticCard' && americanExpressBox !== 'AmericanExpress'
          && approveDomesticCardBox !== 'ApproveDomesticCard' && approveInternationalCardBox !== 'ApproveInternationalCard' && approveInstallmentBox !== 'ApproveInstallment'
          && approveOnepayDomesticCardBox !== 'ApproveOnepayDomesticCard' && approveOnepayMobileAppBox !== 'ApproveOnepayMobileApp') {
          this.toastr.warning('Iportal không hỗ trợ xuất với trường hợp Hợp đồng này');
        } else {

          if (contractCode === 'PL01' || contractCode === 'HD03-01') {
            accountNameBaoCo = modelDetail.subTableNoMerchant01[0].accountName;
            accountNumberBaoCo = modelDetail.subTableNoMerchant01[0].accountNumber;
            bankBaoCo = modelDetail.subTableNoMerchant01[0].bank;
            if (modelDetail.accountFee01 === 'accountFee010') {
              accountNameThuPhi = accountNameBaoCo;
              accountNumberThuPhi = accountNumberBaoCo;
              bankThuPhi = bankBaoCo;
            } else {
              accountNameThuPhi = modelDetail.subTableNoMerchant001[0].accountName;
              accountNumberThuPhi = modelDetail.subTableNoMerchant001[0].accountNumber;
              bankThuPhi = modelDetail.subTableNoMerchant001[0].bank;
            }
          }

          const body = {
            'parentshortname': this.merchantId ? this.merchantId.replace(" ", "") : "",
            'id': model.id,
            'partnerId': model.partnerId,
            'parentId': model.parentId,
            'contractCode': contractCode,
            'contractName': model.contractName,
            'businessName': model.businessName,
            'signatureDate': model.signatureDate === undefined || model.signatureDate === '' ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'dd/MM/yyyy'),
            'rangeDate': model.rangeDate === undefined || model.rangeDate === '' ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'dd/MM/yyyy'),
            'userAction': model.userAction,
            'state': model.state,
            'contractNumber': model.contractNumber,
            'contractType': model.contractType,
            'keepPercent': !modelDetail.keepPercent ? '' : modelDetail.keepPercent.toString(),
            'parentContractFullName': this.parentContractFullName === undefined ? '' : this.parentContractFullName,
            'parentContractNumber': this.parentContractNumber === undefined ? '' : this.parentContractNumber,
            'parentSignatureDate': this.parentSignatureDate === undefined ? '' : this.parentSignatureDate,
            'parentPtTamUng': this.parentPtTamUng === undefined ? '' : this.parentPtTamUng,
            'parentTgTamUng': this.parentTgTamUng === undefined ? '' : this.parentTgTamUng,
            'parentStkGiaiKhoanh': this.parentStkGiaiKhoanh === undefined ? '' : this.parentStkGiaiKhoanh,
            'parentCardTransactionFee': this.parentCardTransactionFee === undefined ? '' : this.parentCardTransactionFee,
            'parentFeeForCard': this.parentFeeForCard === undefined ? '' : this.parentFeeForCard,
            'parentAccountBank': this.parentAccountBank === undefined ? '' : this.parentAccountBank,
            'parentTableMerchant': this.parentTableMerchant,

            'career': modelDetail.carrer,
            'timeAdvance': modelDetail.tgTamUngSelection,
            'inputTgTamUngKhac': modelDetail.inputTgTamUngKhac,
            'inputTgTamUngKhacKetThucPhien': modelDetail.inputTgTamUngKhacKetThucPhien,
            'branch': branch,
            'peopleId': modelDetail.peopleId,
            'shortName': modelDetail.shortName,
            'addressBusiness': modelDetail.addressBusiness,
            'addressOffice': modelDetail.addressOffice,
            'phone': modelDetail.phone,
            'email': modelDetail.email,
            'website': modelDetail.website,
            'numberBusiness': modelDetail.numberBusiness,
            'signaturer': modelDetail.signaturer,
            'position': modelDetail.position,
            'registerFee': modelDetail.registerFee === undefined || modelDetail.registerFee === null || modelDetail.registerFee === '' ? '...' : modelDetail.registerFee.toString(),
            'monthFee': modelDetail.monthFee === undefined || modelDetail.monthFee === null || modelDetail.monthFee === '' ? '...' : modelDetail.monthFee.toString(),
            'shopifyFee': modelDetail.shopifyFee === undefined || modelDetail.shopifyFee === null || modelDetail.shopifyFee === '' ? '...' : modelDetail.shopifyFee.toString(),
            'cardTransactionFee': modelDetail.cardTransactionFee,
            'feeForCard': modelDetail.feeForCard,
            'vietNamCard': modelDetail.vietNamCard,
            'otherCard': modelDetail.otherCard,
            'ptTamUng': modelDetail.ptTamUng,
            'ptTamUngMoi': modelDetail.ptTamUngMoi,
            'tgTamUng': modelDetail.tgTamUng,
            'khoanDamBao': modelDetail.khoanDamBao,
            'khoanDamBaoMoi': modelDetail.khoanDamBaoMoi,
            'kyHanFD': modelDetail.kyHanFD,
            'stkGiaiKhoanh': (modelDetail.stkGiaiKhoanh === '' || modelDetail.stkGiaiKhoanh === undefined || modelDetail.stkGiaiKhoanh === null) && contractCode === 'HD09' ? '...' : modelDetail.stkGiaiKhoanh,
            'accountBank': modelDetail.accountBank,
            'feeForMobile': modelDetail.feeForMobile,
            'alertEmailAddress': modelDetail.alertEmailAddress,
            'detailEmailAddress': modelDetail.detailEmailAddress,
            'otherInfo': modelDetail.otherInfo,
            'dayApprove': modelDetail.dayApprove,
            'cardType': modelDetail.cardType,
            'changeContent': modelDetail.changeContent,

            'subTableNoMerchant01': modelDetail.subTableNoMerchant01,
            'subTableMerchant': modelDetail.subTableMerchant,
            'subTableFee': modelDetail.subTableFee,
            'merchantTablePause': modelDetail.subTablePause,

            'subTableShopifyMerchant': modelDetail.subTableShopifyMerchantID,
            'subTableNonShopifyMerchant' : modelDetail.subTableNonShopifyMerchantID,

            'internationalCard01': modelDetail.internationalCard01,
            'domesticCard01': modelDetail.domesticCard01,
            'domesticCardToken01': modelDetail.domesticCardToken01,
            'internationalCard02': modelDetail.internationalCard02,
            'domesticCard02': modelDetail.domesticCard02,
            'domesticCardToken02': modelDetail.domesticCardToken02,
            'internationalCard03': modelDetail.internationalCard03,
            'domesticCard03': modelDetail.domesticCard03,
            'internationalCard04': modelDetail.internationalCard04,
            'domesticCard04': modelDetail.domesticCard04,
            'visaCard': modelDetail.visaCard,
            'masterCard': modelDetail.masterCard,
            'jcbCard': modelDetail.jcbCard,
            'americanCard': modelDetail.americanCard,
            'kyQuyValue': modelDetail.kyQuyValue,
            'khoanhGiuValue': modelDetail.khoanhGiuValue,
            'hinhThucBaoCo01': modelDetail.hinhThucBaoCo01 === 'hinhThucBaoCo010' ? 'Báo có 1 phiên/ngày' : 'Báo có _ _ phiên/ngày vào lúc _ _',
            'hinhThucThuPhiTheoNgay01': modelDetail.hinhThucThuPhiTheoNgay01 === '' ? '' : 'Hình thức thu phí theo ngày: '
              + (modelDetail.hinhThucThuPhiTheoNgay01 === 'hinhThucThuPhiTheoNgay010' ? 'Thu phí cùng bút toán báo có' : '01 bút toán thu phí riêng'),
            'hinhThucThuPhiTheoThang01': modelDetail.hinhThucThuPhiTheoThang01 === '' ? '' : 'Hình thức thu phí theo tháng: 01 bút toán thu phí riêng',
            'accountNameBaoCo': accountNameBaoCo,
            'accountNameThuPhi': accountNameThuPhi,
            'accountNumberBaoCo': accountNumberBaoCo,
            'accountNumberThuPhi': accountNumberThuPhi,
            'bankBaoCo': bankBaoCo,
            'bankThuPhi': bankThuPhi,
            'feeService': modelDetail.feeService,
            'feeTransInternational': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternational, 2),
            'feeTransDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndApp, 0),
            'feeApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeApp, 0),
            'feeVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.feeVietQR, 0),
            'feePaymentDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndApp, 1),
            'percentQrMobile': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobile, 1),
            'percentQrGrab': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrab, 1),
            'percentQrShopee': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopee, 1),
            'percentQrZalo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZalo, 1),
            'percentQrMoMo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMo, 1),
            'percentQrOther': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOther, 1),
            'percentVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.percentVietQR, 1),

            //Phu phi Shopify PL16
            'feeServiceShopify': modelDetail.feeServiceShopify,
            'feeTransInternationalShopify': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternationalShopify, 2),
            'feeTransDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndAppShopify, 0),
            'feeAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeAppShopify, 0),
            'feePaymentDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndAppShopify, 1),
            'approveCardType01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01InternationalShopify),
            'americanExpress01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01InternationalShopify),
            'approveCardType02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02InternationalShopify),
            'americanExpress02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02InternationalShopify),

            'percentQrMobileShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobileShopify, 1),
            'percentQrGrabShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrabShopify, 1),
            'percentQrShopeeShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopeeShopify, 1),
            'percentQrZaloShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZaloShopify, 1),
            'percentQrMoMoShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMoShopify, 1),
            'percentQrOtherShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOtherShopify, 1),

            'approveCardType01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01International),
            'americanExpress01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01International),
            'approveCardType02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02International),
            'americanExpress02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02International),
            'khoanDamBaoInput': modelDetail.khoanDamBaoInput === undefined || modelDetail.khoanDamBaoInput === null ? '' : modelDetail.khoanDamBaoInput.toString(),
            'openByBank': modelDetail.openByBank,
            'danhXung': modelDetail.danhXung,

            // VBUQ
            'authorizedPersonName': modelDetail.authorizedPersonName,
            'authorizedPersonId': modelDetail.authorizedPersonId,
            'authorizedIssuedDate': modelDetail.authorizedIssuedDate ? this.datePipe.transform(modelDetail.authorizedIssuedDate, 'dd/MM/yyyy') : '',
            'authorizedIssuedBy': modelDetail.authorizedIssuedBy,
            'accountName': modelDetail.accountName,
            'accountNumber': modelDetail.accountNumber,
            'authorizationPeriodFrom': modelDetail.authorizationPeriodFrom ? this.datePipe.transform(modelDetail.authorizationPeriodFrom, 'dd/MM/yyyy') : '',
            // 'authorizationPeriodTo': modelDetail.authorizationPeriodTo ? this.datePipe.transform(modelDetail.authorizationPeriodTo, 'dd/MM/yyyy') : '',
            'authorizationPeriodTo': modelDetail.authorizationPeriodTo,
            'authorizedBirthDate': modelDetail.authorizedBirthDate ? this.datePipe.transform(modelDetail.authorizedBirthDate, 'dd/MM/yyyy') : '',
            'authorizedAddress': modelDetail.authorizedAddress,
            'authorizationNumber': modelDetail.authorizationNumber,

            //check box
            'appMobileBox': appMobileBox,
            'otherBankBox': otherBankBox,
            'visaBox': visaBox,
            'masterCardBox': masterCardBox,
            'amexBox': amexBox,
            'jcbBox': jcbBox,
            'unionBox': unionBox,
            'domesticBox': domesticBox,
            'migsBox': migsBox,
            'mpgsBox': mpgsBox,
            'cyberSourceBox': cyberSourceBox,
            'secureBox': modelDetail.secure01.toString(),
            'kyQuyBox': modelDetail.kyQuy.length === 0 ? '' : modelDetail.kyQuy.toString(),
            'khoanhGiuBox': modelDetail.khoanhGiu.length === 0 ? '' : modelDetail.khoanhGiu.toString(),
            'americanExpressBox': americanExpressBox,
            'approveDomesticCardBox': approveDomesticCardBox,
            'approveInternationalCardBox': approveInternationalCardBox,
            'approveInstallmentBox': approveInstallmentBox,
            'approveOnepayDomesticCardBox': approveOnepayDomesticCardBox,
            'approveOnepayMobileAppBox': approveOnepayMobileAppBox,
            'approveBnblBox': approveBnblBox,

            //bnpl
            'bnplFee': modelDetail.bnplFee === undefined || modelDetail.bnplFee === null || modelDetail.bnplFee === '' ? '...' : modelDetail.bnplFee.toString(),
            'bnplFeeHomeCredit': modelDetail.bnplFeeHomeCredit || '',
            'bnplFeeFundiin': modelDetail.bnplFeeFundiin || '',
            'bnplFeeAmigo': modelDetail.bnplFeeAmigo || '',
            'bnplFeeKredivo': modelDetail.bnplFeeKredivo || '',

            'noiDungThayDoi': modelDetail.noiDungThayDoi || '',
            'uyQuyen': modelDetail.uyQuyen || '',

            'hinhThucThuPhi': modelDetail.hinhThucThuPhi,
            'khoanDamBaoSelection': modelDetail.khoanDamBaoSelection,
            'kyQuyType': modelDetail.kyQuyType,
            'version': modelDetail.version,
            'idTemplate': this.idTemplate,
            'emailExport': this.global.activeProfile.email,

            //BBTLv2
            'ngayThanhLy': modelDetail.ngayThanhLy ? this.datePipe.transform(modelDetail.ngayThanhLy, 'dd/MM/yyyy') : '',
            //PL15v2 phụ lục 3D
            'thoiGianApDung': modelDetail.thoiGianApDung ? this.datePipe.transform(modelDetail.thoiGianApDung, 'dd/MM/yyyy') : '',
          };
          console.log(body);
          console.log('exportContract:'+this.idTemplate);
          return this.merchantService.exportContract(body).subscribe(response => {
            this.global.downloadEmit.emit(true);
          });
        }
      } else {
        this.toastr.warning('Hiện chưa có template cho văn bản này !', 'Cảnh báo');
      }
    }
  }

/**
 * xử lý việc xuất hợp đồng ra file template. Nó kiểm tra và thiết lập các thông tin cần thiết trước khi gọi API để xuất hợp đồng.
 * @param type 
 * @returns 
 */
  onSubmitExportTemplateContract(type: string) {
    const model = new ContractOriginal(this.contractOriginal);
    const modelDetail = new ContractDetailModel(this.contractDetail);

    model.id = this.contractId === undefined || this.contractId === '0' ? '' : this.contractId;
    model.partnerId = this.currentPartnerId;
    model.parentId = this.parentId === undefined ? '0' : this.parentId;
    model.contractCode = this.contractCode;
    model.contractName = this.contractName;
    model.userAction = this.global.activeProfile.name;
    model.contractType = this.contractType;
    model.businessName = this.contractOriginal.businessName === undefined ? '' : this.contractOriginal.businessName;
    model.contractNumber = this.contractOriginal.contractNumber === undefined ? '' : this.handleContractNumber(this.contractOriginal.contractNumber, this.contractCode);
    model.signatureDate = this.contractOriginal.signatureDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'yyyy-MM-dd');
    model.rangeDate = this.contractOriginal.rangeDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'yyyy-MM-dd');
    model.state = this.contractOriginal.state === '' ? 'wait for approve' : this.contractOriginal.state;
    model.representative = this.contractOriginal.representative === undefined ? '' : this.contractOriginal.representative;

    modelDetail.peopleId = this.contractDetail.peopleId === undefined ? '' : this.contractDetail.peopleId;
    modelDetail.carrer = this.contractDetail.carrer === undefined ? '' : this.contractDetail.carrer;
    modelDetail.branch = this.contractDetail.branch === undefined ? '' : this.contractDetail.branch;
    modelDetail.shortName = this.contractDetail.shortName === undefined ? '' : this.contractDetail.shortName;
    modelDetail.addressBusiness = this.contractDetail.addressBusiness === undefined ? '' : this.contractDetail.addressBusiness;
    modelDetail.addressOffice = this.contractDetail.addressOffice === undefined ? '' : this.contractDetail.addressOffice;
    modelDetail.website = this.contractDetail.website === undefined ? '' : this.contractDetail.website;
    modelDetail.email = this.contractDetail.email === undefined ? '' : this.contractDetail.email;
    modelDetail.phone = this.contractDetail.phone === undefined ? '' : this.contractDetail.phone;
    modelDetail.numberBusiness = this.contractDetail.numberBusiness === undefined ? '' : this.contractDetail.numberBusiness;
    modelDetail.accountBank = this.contractDetail.accountBank === undefined ? '' : this.contractDetail.accountBank;
    modelDetail.signaturer = this.contractDetail.signaturer === undefined ? '' : this.contractDetail.signaturer;
    modelDetail.position = this.contractDetail.position === undefined ? '' : this.contractDetail.position;
    modelDetail.cardType = this.contractDetail.cardType === undefined ? '' : this.contractDetail.cardType;
    modelDetail.infoCard = this.contractDetail.infoCard === undefined ? '' : this.contractDetail.infoCard;
    modelDetail.khoanDamBao = this.contractDetail.khoanDamBao === undefined ? '' : this.contractDetail.khoanDamBao;
    modelDetail.khoanDamBaoMoi = this.contractDetail.khoanDamBaoMoi === undefined ? '' : this.contractDetail.khoanDamBaoMoi;
    modelDetail.kyHanFD = this.contractDetail.kyHanFD === undefined ? '' : this.contractDetail.kyHanFD;
    modelDetail.otherInfo = this.contractDetail.otherInfo === undefined ? '' : this.contractDetail.otherInfo;
    modelDetail.dayApprove = this.contractDetail.dayApprove === undefined ? '' : this.contractDetail.dayApprove;
    modelDetail.subTableMerchant = this.contractDetail.subTableMerchant === undefined ? [] : this.contractDetail.subTableMerchant;
    modelDetail.subTableNoMerchant01 = this.contractDetail.subTableNoMerchant01 === undefined ? [] : this.contractDetail.subTableNoMerchant01;
    modelDetail.subTableNoMerchant001 = this.contractDetail.subTableNoMerchant001 === undefined ? [] : this.contractDetail.subTableNoMerchant001;
    modelDetail.subTableNoMerchant02 = this.contractDetail.subTableNoMerchant02 === undefined ? [] : this.contractDetail.subTableNoMerchant02;
    modelDetail.subTableNoMerchant002 = this.contractDetail.subTableNoMerchant002 === undefined ? [] : this.contractDetail.subTableNoMerchant002;
    modelDetail.subTableNoMerchant03 = this.contractDetail.subTableNoMerchant03 === undefined ? [] : this.contractDetail.subTableNoMerchant03;
    modelDetail.subTableNoMerchant003 = this.contractDetail.subTableNoMerchant003 === undefined ? [] : this.contractDetail.subTableNoMerchant003;
    modelDetail.subTableFee = this.contractDetail.subTableFee === undefined ? [] : this.contractDetail.subTableFee;
    modelDetail.subTableMerchantID = this.contractDetail.subTableMerchantID === undefined ? [] : this.contractDetail.subTableMerchantID;
    modelDetail.subTableShopifyMerchantID = this.contractDetail.subTableShopifyMerchantID === undefined ? [] : this.contractDetail.subTableShopifyMerchantID;
    modelDetail.subTableNonShopifyMerchantID = this.contractDetail.subTableNonShopifyMerchantID === undefined ? [] : this.contractDetail.subTableNonShopifyMerchantID;
    modelDetail.cardList = this.contractDetail.cardListArray === undefined ? '' : this.contractDetail.cardListArray.toString();
    modelDetail.paygateList = this.contractDetail.paygateListArray === undefined ? '' : this.contractDetail.paygateListArray.toString();
    modelDetail.stkGiaiKhoanh = this.contractDetail.stkGiaiKhoanh === undefined ? '' : this.contractDetail.stkGiaiKhoanh;
    modelDetail.tgTamUng = this.contractDetail.tgTamUng === undefined ? '' : this.contractDetail.tgTamUng;
    modelDetail.tgTamUngSelection = this.contractDetail.tgTamUngSelection === undefined ? '' : this.contractDetail.tgTamUngSelection;
    modelDetail.ptTamUng = this.contractDetail.ptTamUng === undefined ? '' : this.contractDetail.ptTamUng;
    modelDetail.ptTamUngMoi = this.contractDetail.ptTamUngMoi === undefined ? '' : this.contractDetail.ptTamUngMoi;
    modelDetail.otherCard = this.contractDetail.otherCard === undefined ? '' : this.contractDetail.otherCard;
    modelDetail.vietNamCard = this.contractDetail.vietNamCard === undefined ? '' : this.contractDetail.vietNamCard;
    modelDetail.cardTransactionFee = this.contractDetail.cardTransactionFee === undefined ? '' : this.contractDetail.cardTransactionFee;
    modelDetail.monthFee = this.contractDetail.monthFee === undefined ? '' : this.contractDetail.monthFee;
    modelDetail.registerFee = this.contractDetail.registerFee || '';
    modelDetail.shopifyFee = this.contractDetail.shopifyFee || '';
    modelDetail.feeForCard = this.contractDetail.feeForCard === undefined ? '' : this.contractDetail.feeForCard;
    modelDetail.feeForMobile = this.contractDetail.feeForMobile === undefined ? '' : this.contractDetail.feeForMobile;

    modelDetail.detailEmailAddress = this.contractDetail.detailEmailAddress === undefined ? '' : this.contractDetail.detailEmailAddress;
    modelDetail.alertEmailAddress = this.contractDetail.alertEmailAddress === undefined ? '' : this.contractDetail.alertEmailAddress;
    modelDetail.kyQuyValue = this.contractDetail.kyQuyValue === undefined ? '' : this.contractDetail.kyQuyValue;
    modelDetail.kyQuy = this.contractDetail.kyQuy === undefined ? '' : this.contractDetail.kyQuy;
    modelDetail.khoanhGiuValue = this.contractDetail.khoanhGiuValue === undefined ? '' : this.contractDetail.khoanhGiuValue;
    modelDetail.khoanhGiu = this.contractDetail.khoanhGiu === undefined ? '' : this.contractDetail.khoanhGiu;
    modelDetail.americanCard = this.contractDetail.americanCard === undefined ? '' : this.contractDetail.americanCard;
    modelDetail.jcbCard = this.contractDetail.jcbCard === undefined ? '' : this.contractDetail.jcbCard;
    modelDetail.masterCard = this.contractDetail.masterCard === undefined ? '' : this.contractDetail.masterCard;
    modelDetail.visaCard = this.contractDetail.visaCard === undefined ? '' : this.contractDetail.visaCard;
    modelDetail.domesticCard02 = this.contractDetail.domesticCard02 === undefined ? '' : this.contractDetail.domesticCard02;
    modelDetail.internationalCard02 = this.contractDetail.internationalCard02 === undefined ? '' : this.contractDetail.internationalCard02;
    modelDetail.domesticCardToken02 = this.contractDetail.domesticCardToken02 === undefined ? '' : this.contractDetail.domesticCardToken02;
    modelDetail.domesticCard01 = this.contractDetail.domesticCard01 === undefined ? '' : this.contractDetail.domesticCard01;
    modelDetail.internationalCard01 = this.contractDetail.internationalCard01 === undefined ? '' : this.contractDetail.internationalCard01;
    modelDetail.domesticCardToken01 = this.contractDetail.domesticCardToken01 === undefined ? '' : this.contractDetail.domesticCardToken01;
    modelDetail.hinhThucThuPhiTheoThang03 = this.contractDetail.hinhThucThuPhiTheoThang03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang03;
    modelDetail.hinhThucThuPhiTheoNgay03 = this.contractDetail.hinhThucThuPhiTheoNgay03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay03;
    modelDetail.hinhThucBaoCo03 = this.contractDetail.hinhThucBaoCo03 === undefined ? '' : this.contractDetail.hinhThucBaoCo03;
    modelDetail.hinhThucThuPhiTheoThang02 = this.contractDetail.hinhThucThuPhiTheoThang02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang02;
    modelDetail.hinhThucThuPhiTheoNgay02 = this.contractDetail.hinhThucThuPhiTheoNgay02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay02;
    modelDetail.hinhThucBaoCo02 = this.contractDetail.hinhThucBaoCo02 === undefined ? '' : this.contractDetail.hinhThucBaoCo02;
    modelDetail.hinhThucThuPhiTheoThang01 = this.contractDetail.hinhThucThuPhiTheoThang01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang01;
    modelDetail.hinhThucThuPhiTheoNgay01 = this.contractDetail.hinhThucThuPhiTheoNgay01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay01;
    modelDetail.hinhThucBaoCo01 = this.contractDetail.hinhThucBaoCo01 === undefined ? '' : this.contractDetail.hinhThucBaoCo01;
    modelDetail.secure03 = this.contractDetail.secure03 === undefined ? '' : this.contractDetail.secure03;
    modelDetail.secure02 = this.contractDetail.secure02 === undefined ? '' : this.contractDetail.secure02;
    modelDetail.secure01 = this.contractDetail.secure01 === undefined ? '' : this.contractDetail.secure01;
    modelDetail.accountFee03 = this.contractDetail.accountFee03 === undefined ? '' : this.contractDetail.accountFee03;
    modelDetail.accountFee02 = this.contractDetail.accountFee02 === undefined ? '' : this.contractDetail.accountFee02;
    modelDetail.accountFee01 = this.contractDetail.accountFee01 === undefined ? '' : this.contractDetail.accountFee01;
    modelDetail.internationalCard03 = this.contractDetail.internationalCard03 === undefined ? '' : this.contractDetail.internationalCard03;
    modelDetail.internationalCard04 = this.contractDetail.internationalCard04 === undefined ? '' : this.contractDetail.internationalCard04;
    modelDetail.domesticCard03 = this.contractDetail.domesticCard03 === undefined ? '' : this.contractDetail.domesticCard03;
    modelDetail.domesticCard04 = this.contractDetail.domesticCard04 === undefined ? '' : this.contractDetail.domesticCard04;
    modelDetail.changeContent = this.contractDetail.changeContent === undefined ? '' : this.contractDetail.changeContent;
    modelDetail.subTablePause = this.contractDetail.subTablePause === undefined ? [] : this.contractDetail.subTablePause;
    modelDetail.typeFeeInstallment = this.contractDetail.typeFeeInstallment === undefined ? '' : this.contractDetail.typeFeeInstallment;
    modelDetail.authorizedPersonName = this.contractDetail.authorizedPersonName ?? '';
    modelDetail.authorizedPersonId = this.contractDetail.authorizedPersonId ?? '';
    modelDetail.authorizedIssuedDate = this.contractDetail.authorizedIssuedDate ?? '';
    modelDetail.authorizedIssuedBy = this.contractDetail.authorizedIssuedBy ?? '';
    modelDetail.accountName = this.contractDetail.accountName ?? '';
    modelDetail.accountNumber = this.contractDetail.accountNumber ?? '';
    modelDetail.authorizationPeriodFrom = this.contractDetail.authorizationPeriodFrom ?? '';
    modelDetail.authorizationPeriodTo = this.contractDetail.authorizationPeriodTo ?? '';
    modelDetail.authorizedBirthDate = this.contractDetail.authorizedBirthDate ?? '';
    modelDetail.authorizedAddress = this.contractDetail.authorizedAddress ?? '';
    modelDetail.authorizationNumber = this.contractDetail.authorizationNumber ?? '';

    modelDetail.feeService = this.contractDetail.feeService === undefined ? '' : this.contractDetail.feeService;
    modelDetail.autoFillStandardFee = this.contractDetail.autoFillStandardFee === undefined ? '' : this.contractDetail.autoFillStandardFee;
    modelDetail.feeTransDomesticAndApp = this.contractDetail.feeTransDomesticAndApp || '';
    modelDetail.feeApp = this.contractDetail.feeApp === undefined ? '' : this.contractDetail.feeApp;
    modelDetail.feeVietQR = this.contractDetail.feeVietQR === undefined ? '' : this.contractDetail.feeVietQR;
    modelDetail.feePaymentDomesticAndApp = this.contractDetail.feePaymentDomesticAndApp === undefined ? '' : this.contractDetail.feePaymentDomesticAndApp;
    modelDetail.feeTransInternational = this.contractDetail.feeTransInternational || '';
    modelDetail.approveCardType01International = this.contractDetail.approveCardType01International === undefined ? '' : this.contractDetail.approveCardType01International;
    modelDetail.americanExpress01International = this.contractDetail.americanExpress01International === undefined ? '' : this.contractDetail.americanExpress01International;
    modelDetail.approveCardType02International = this.contractDetail.approveCardType02International === undefined ? '' : this.contractDetail.approveCardType02International;
    modelDetail.americanExpress02International = this.contractDetail.americanExpress02International === undefined ? '' : this.contractDetail.americanExpress02International;
    modelDetail.insideDomestic = this.contractDetail.insideDomestic === undefined ? '' : this.contractDetail.insideDomestic;
    modelDetail.outsideDomestic = this.contractDetail.outsideDomestic === undefined ? '' : this.contractDetail.outsideDomestic;
    modelDetail.hinhThucThuPhi = this.contractDetail.hinhThucThuPhi === undefined ? '' : this.contractDetail.hinhThucThuPhi;
    modelDetail.khoanDamBaoSelection = this.contractDetail.khoanDamBaoSelection === undefined ? '' : this.contractDetail.khoanDamBaoSelection;
    modelDetail.khoanDamBaoInput = this.contractDetail.khoanDamBaoInput === undefined ? '' : this.contractDetail.khoanDamBaoInput;
    modelDetail.inputHinhThucThuPhiKhac = this.contractDetail.inputHinhThucThuPhiKhac === undefined ? '' : this.contractDetail.inputHinhThucThuPhiKhac;
    modelDetail.inputTgTamUngKhac = this.contractDetail.inputTgTamUngKhac === undefined ? '' : this.contractDetail.inputTgTamUngKhac;
    modelDetail.inputTgTamUngKhacKetThucPhien = this.contractDetail.inputTgTamUngKhacKetThucPhien === undefined ? '' : this.contractDetail.inputTgTamUngKhacKetThucPhien;
    modelDetail.kyQuyType = this.contractDetail.kyQuyType === undefined ? '' : this.contractDetail.kyQuyType;
    modelDetail.kyQuyAutoFill = this.contractDetail.kyQuyAutoFill === undefined ? '' : this.contractDetail.kyQuyAutoFill;
    modelDetail.openByBank = this.contractDetail.openByBank === undefined ? '' : this.contractDetail.openByBank;
    modelDetail.inputKyQuyKhac = this.contractDetail.inputKyQuyKhac === undefined ? '' : this.contractDetail.inputKyQuyKhac;
    modelDetail.danhXung = this.contractDetail.danhXung === undefined ? '' : this.contractDetail.danhXung;
    modelDetail.keepPercent = this.contractDetail.keepPercent === undefined ? '' : this.contractDetail.keepPercent;

    modelDetail.percentQrMobile = this.contractDetail.percentQrMobile === undefined ? '' : this.contractDetail.percentQrMobile;
    modelDetail.percentQrGrab = this.contractDetail.percentQrGrab === undefined ? '' : this.contractDetail.percentQrGrab;
    modelDetail.percentQrShopee = this.contractDetail.percentQrShopee === undefined ? '' : this.contractDetail.percentQrShopee;
    modelDetail.percentQrZalo = this.contractDetail.percentQrZalo === undefined ? '' : this.contractDetail.percentQrZalo;
    modelDetail.percentQrMoMo = this.contractDetail.percentQrMoMo === undefined ? '' : this.contractDetail.percentQrMoMo;
    modelDetail.percentQrOther = this.contractDetail.percentQrOther === undefined ? '' : this.contractDetail.percentQrOther;
    modelDetail.inforOther = this.contractDetail.inforOther === undefined ? '' : this.contractDetail.inforOther;
    modelDetail.percentVietQR = this.contractDetail.percentVietQR === undefined ? '' : this.contractDetail.percentVietQR;


    //Phu phi shopify PL16
    modelDetail.autoFillStandardFeeShopify = this.contractDetail.autoFillStandardFeeShopify === undefined ? '' : this.contractDetail.autoFillStandardFeeShopify;
    modelDetail.feeTransDomesticAndAppShopify = this.contractDetail.feeTransDomesticAndAppShopify === undefined ? '' : this.contractDetail.feeTransDomesticAndAppShopify;
    modelDetail.feeAppShopify = this.contractDetail.feeAppShopify === undefined ? '' : this.contractDetail.feeAppShopify;
    modelDetail.feePaymentDomesticAndAppShopify = this.contractDetail.feePaymentDomesticAndAppShopify === undefined ? '' : this.contractDetail.feePaymentDomesticAndAppShopify;
    modelDetail.feeTransInternationalShopify = this.contractDetail.feeTransInternationalShopify === undefined ? '' : this.contractDetail.feeTransInternationalShopify;
    modelDetail.approveCardType01InternationalShopify = this.contractDetail.approveCardType01InternationalShopify === undefined ? '' : this.contractDetail.approveCardType01InternationalShopify;
    modelDetail.americanExpress01InternationalShopify = this.contractDetail.americanExpress01InternationalShopify === undefined ? '' : this.contractDetail.americanExpress01InternationalShopify;
    modelDetail.approveCardType02InternationalShopify = this.contractDetail.approveCardType02InternationalShopify === undefined ? '' : this.contractDetail.approveCardType02InternationalShopify;
    modelDetail.americanExpress02InternationalShopify = this.contractDetail.americanExpress02InternationalShopify === undefined ? '' : this.contractDetail.americanExpress02InternationalShopify;
    modelDetail.feeServiceShopify = this.contractDetail.feeServiceShopify === undefined ? '' : this.contractDetail.feeServiceShopify;

    modelDetail.percentQrMobileShopify = this.contractDetail.percentQrMobileShopify === undefined ? '' : this.contractDetail.percentQrMobileShopify;
    modelDetail.percentQrGrabShopify = this.contractDetail.percentQrGrabShopify === undefined ? '' : this.contractDetail.percentQrGrabShopify;
    modelDetail.percentQrShopeeShopify = this.contractDetail.percentQrShopeeShopify === undefined ? '' : this.contractDetail.percentQrShopeeShopify;
    modelDetail.percentQrZaloShopify = this.contractDetail.percentQrZaloShopify === undefined ? '' : this.contractDetail.percentQrZaloShopify;
    modelDetail.percentQrMoMoShopify = this.contractDetail.percentQrMoMoShopify === undefined ? '' : this.contractDetail.percentQrMoMoShopify;
    modelDetail.percentQrOtherShopify = this.contractDetail.percentQrOtherShopify === undefined ? '' : this.contractDetail.percentQrOtherShopify;
    modelDetail.inforOtherShopify = this.contractDetail.inforOtherShopify === undefined ? '' : this.contractDetail.inforOtherShopify;

    modelDetail.bnplFee = this.contractDetail.bnplFee === undefined ? '' : this.contractDetail.bnplFee;
    modelDetail.bnplFeeHomeCredit = this.contractDetail.bnplFeeHomeCredit || '';
    modelDetail.bnplFeeFundiin = this.contractDetail.bnplFeeFundiin || '';
    modelDetail.bnplFeeAmigo = this.contractDetail.bnplFeeAmigo || '';
    modelDetail.bnplFeeKredivo = this.contractDetail.bnplFeeKredivo || '';

    modelDetail.noiDungThayDoi = this.contractDetail.noiDungThayDoi;
    modelDetail.uyQuyen = this.contractDetail.uyQuyen;

    //HD14
    modelDetail.representative = this.contractDetail.representative === undefined ? '' : this.contractDetail.representative;
    modelDetail.permanentAddress = this.contractDetail.permanentAddress === undefined ? '' : this.contractDetail.permanentAddress;
    modelDetail.issuedBy = this.contractDetail.issuedBy === undefined ? '' : this.contractDetail.issuedBy;
    modelDetail.hoaDonVAT = this.contractDetail.hoaDonVAT === undefined ? '' : this.contractDetail.hoaDonVAT;

    //BBTLv2
    modelDetail.ngayThanhLy = this.contractDetail.ngayThanhLy ?? '';
    //PL15v2 phụ lục 3D
    modelDetail.thoiGianApDung = this.contractDetail.thoiGianApDung ?? '';

    if (type === '0') {
      const body = {
        'id': model.id,
        'partnerId': model.partnerId,
        'parentId': model.parentId,
        'contractCode': model.contractCode,
        'contractName': model.contractName,
        'businessName': model.businessName,
        'signatureDate': model.signatureDate,
        'rangeDate': model.rangeDate,
        'userAction': model.userAction,
        'state': model.state,
        'contractNumber': model.contractNumber,
        'contractType': model.contractType,
        'order': this.handleContractOrder(model.contractCode),
        'representative': model.representative,

        'dataContractDetail': JSON.stringify(modelDetail),
      };
      this.merchantService.pushContract(body).subscribe(data => {
        if (data.n_result === '200') {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.success('Cập nhật ' + this.contractName + ' thành công', 'Thành công');
            this.checkEditContractDetail = false;
            this.contractService.setEditing(false);
          } else {
            this.toastr.success('Tạo mới ' + this.contractName + ' thành công', 'Thành công');
            this.backContractManagement();
          }
        } else {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.error('Cập nhật ' + this.contractName + ' không thành công', 'Thất bại');
          } else {
            this.toastr.error('Tạo mới ' + this.contractName + ' không thành công', 'Thất bại');
          }
        }
      });

    } else {//update template động
      console.log(this.contractCode)
      var checkExport = false;
      if (this.contractCode === 'HD03-01' || this.contractCode === 'HD04' || this.contractCode === 'HD05' || this.contractCode === 'HD06' || this.contractCode === 'HD08'
        || this.contractCode === 'HD09' || this.contractCode === 'HD11' || this.contractCode === 'HD12' || this.contractCode === 'HD13' || this.contractCode === 'PL01'
        || this.contractCode === 'PL02' || this.contractCode === 'PL03' || this.contractCode === 'PL04' || this.contractCode === 'PL05' || this.contractCode === 'PL06'
        || this.contractCode === 'PL-K'||  this.contractCode === 'PL07' || this.contractCode === 'PL08' || this.contractCode === 'PL09' || this.contractCode === 'PL12' || this.contractCode === 'PL14' || this.contractCode === 'PLBNPL' || this.contractCode === 'PL16') {
        checkExport = true;
      } else if (this.contractCode === 'BBTL' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD07' || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if ((this.contractCode === 'PL11' || this.contractCode === 'PL15' || this.contractCode === 'PL15v2') && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'BBNT' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'VBUQ') {
        checkExport = true;
      } if (this.contractCode === 'BBNT') {
        checkExport = true;
      }

      if (checkExport === true) {

        var contractCode = '';
        var carrer = '';
        var branch = '';
        var unionBox = '';
        var otherBankBox = '';
        var appMobileBox = '';
        var visaBox = '';
        var masterCardBox = '';
        var amexBox = '';
        var jcbBox = '';
        var unionBox = '';
        var domesticBox = '';
        var mpgsBox = '';
        var migsBox = '';
        var cyberSourceBox = '';
        var accountNumberBaoCo = '';
        var accountNameBaoCo = '';
        var bankBaoCo = '';
        var accountNumberThuPhi = '';
        var accountNameThuPhi = '';
        var bankThuPhi = '';

        var americanExpressBox = '';
        var approveDomesticCardBox = '';
        var approveInternationalCardBox = '';
        var approveInstallmentBox = '';
        var approveOnepayDomesticCardBox = '';
        var approveOnepayMobileAppBox = '';
        var approveBnblBox = '';

        if (model.contractCode === 'BBTL' || model.contractCode === 'PL11' || model.contractCode === 'PL15') {
          if (model.contractCode === 'BBTL' && this.parentContractCode === 'HD07') {
            contractCode = model.contractCode + '-CUP';
          } else if (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD08') {
            contractCode = model.contractCode + '-2BEN';
          } else {
            contractCode = model.contractCode + '-3BEN';
          }
        } else if (model.contractCode === 'BBNT') {
          contractCode = model.contractCode + '-' + this.parentContractCode;
        } else if (model.contractCode === 'HD13') {
          var checkInstallment = '';
          var checkShopify = '';
          modelDetail.cardListArray.forEach(element => {
            if (element === 'ApproveInstallment') {
              checkInstallment = element;
            }
            if (element === 'ApproveShopify') {
              checkShopify = element;
            }
          });
          if (checkInstallment !== '' && modelDetail.kyQuyType === 'kyQuyKhac' ||
            modelDetail.tgTamUngSelection === 'other' || modelDetail.feeService === 'specialFee' || modelDetail.hinhThucThuPhi === 'Khac') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH0';
              }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH1';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH2';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH3';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH4';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH5';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH6';
            }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH7';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH8';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH9';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH10';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH11';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH12';
              }
          } else if (checkShopify !== '') {
            contractCode = model.contractCode + '_Shopify';
          } else {
            if (modelDetail.version != 'v2') {
              contractCode = model.contractCode + '_TH00';
            } else {
              contractCode = model.contractCode + '_TH0';
            }
          }
        } else {
          contractCode = model.contractCode;
        }

        this.branchList.forEach(element => {
          if (element.id === modelDetail.branch) {
            branch = element.name;
          }
        });

        this.carrerList.forEach(element => {
          if (element.value === modelDetail.carrer) {
            carrer = element.label;
          }
        });

        if (modelDetail.cardListArray.length > 0) {
          modelDetail.cardListArray.forEach(element => {
            if (element === 'appMobile') {
              appMobileBox = 'appMobileBox';
            } else if (element === 'vcbND' || element === 'otherBank') {
              otherBankBox = 'otherBankBox';
            } else if (element === 'Visa') {
              visaBox = 'Visa';
            } else if (element === 'AMEX') {
              amexBox = 'AMEX';
            } else if (element === 'JCB') {
              jcbBox = 'JCB';
            } else if (element === 'UnionPay') {
              unionBox = 'UnionPay';
            } else if (element === 'domesticCard') {
              domesticBox = 'domesticCard';
            } else if (element === 'AmericanExpress') {
              americanExpressBox = 'AmericanExpress';
            } else if (element === 'ApproveDomesticCard') {
              approveDomesticCardBox = 'ApproveDomesticCard';
            } else if (element === 'ApproveInternationalCard') {
              approveInternationalCardBox = 'ApproveInternationalCard';
            } else if (element === 'ApproveInstallment') {
              approveInstallmentBox = 'ApproveInstallment';
            } else if (element === 'ApproveOnepayDomesticCard') {
              approveOnepayDomesticCardBox = 'ApproveOnepayDomesticCard';
            } else if (element === 'ApproveOnepayMobileApp') {
              approveOnepayMobileAppBox = 'ApproveOnepayMobileApp';
            } else if (element === 'MasterCard') {
              masterCardBox = 'MasterCard';
            } else if (element === 'ApproveBNPL') {
              approveBnblBox = 'ApproveBNPL';
            }
          });
        }

        if (modelDetail.paygateListArray.length > 0) {
          modelDetail.paygateListArray.forEach(element => {
            if (element === 'MPGS') {
              mpgsBox = 'MPGS';
            } else if (element === 'MiGS') {
              migsBox = 'MiGS';
            } else if (element === 'Cybersource') {
              cyberSourceBox = 'Cybersource';
            }
          });
        }
        //Check only BNBL option then block export file
        if (model.contractCode === 'HD13' && approveBnblBox === 'ApproveBNPL' && visaBox !== 'Visa' && masterCardBox !== 'MasterCard' && amexBox !== 'AMEX' && jcbBox !== 'JCB' && domesticBox !== 'domesticCard' && americanExpressBox !== 'AmericanExpress'
          && approveDomesticCardBox !== 'ApproveDomesticCard' && approveInternationalCardBox !== 'ApproveInternationalCard' && approveInstallmentBox !== 'ApproveInstallment'
          && approveOnepayDomesticCardBox !== 'ApproveOnepayDomesticCard' && approveOnepayMobileAppBox !== 'ApproveOnepayMobileApp') {
          this.toastr.warning('Iportal không hỗ trợ xuất với trường hợp Hợp đồng này');
        } else {

          if (contractCode === 'PL01' || contractCode === 'HD03-01') {
            accountNameBaoCo = modelDetail.subTableNoMerchant01[0].accountName;
            accountNumberBaoCo = modelDetail.subTableNoMerchant01[0].accountNumber;
            bankBaoCo = modelDetail.subTableNoMerchant01[0].bank;
            if (modelDetail.accountFee01 === 'accountFee010') {
              accountNameThuPhi = accountNameBaoCo;
              accountNumberThuPhi = accountNumberBaoCo;
              bankThuPhi = bankBaoCo;
            } else {
              accountNameThuPhi = modelDetail.subTableNoMerchant001[0].accountName;
              accountNumberThuPhi = modelDetail.subTableNoMerchant001[0].accountNumber;
              bankThuPhi = modelDetail.subTableNoMerchant001[0].bank;
            }
          }

          const body = {
            'parentshortname': this.merchantId ? this.merchantId.replace(" ", "") : "",
            'id': model.id,
            'partnerId': model.partnerId,
            'parentId': model.parentId,
            'contractCode': contractCode,
            'contractName': model.contractName,
            'businessName': model.businessName,
            'signatureDate': model.signatureDate === undefined || model.signatureDate === '' ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'dd/MM/yyyy'),
            'rangeDate': model.rangeDate === undefined || model.rangeDate === '' ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'dd/MM/yyyy'),
            'userAction': model.userAction,
            'state': model.state,
            'contractNumber': model.contractNumber,
            'contractType': model.contractType,
            'keepPercent': !modelDetail.keepPercent ? '' : modelDetail.keepPercent.toString(),
            'parentContractFullName': this.parentContractFullName === undefined ? '' : this.parentContractFullName,
            'parentContractNumber': this.parentContractNumber === undefined ? '' : this.parentContractNumber,
            'parentSignatureDate': this.parentSignatureDate === undefined ? '' : this.parentSignatureDate,
            'parentPtTamUng': this.parentPtTamUng === undefined ? '' : this.parentPtTamUng,
            'parentTgTamUng': this.parentTgTamUng === undefined ? '' : this.parentTgTamUng,
            'parentStkGiaiKhoanh': this.parentStkGiaiKhoanh === undefined ? '' : this.parentStkGiaiKhoanh,
            'parentCardTransactionFee': this.parentCardTransactionFee === undefined ? '' : this.parentCardTransactionFee,
            'parentFeeForCard': this.parentFeeForCard === undefined ? '' : this.parentFeeForCard,
            'parentAccountBank': this.parentAccountBank === undefined ? '' : this.parentAccountBank,
            'parentTableMerchant': this.parentTableMerchant,

            'career': modelDetail.carrer,
            'timeAdvance': modelDetail.tgTamUngSelection,
            'branch': branch,
            'peopleId': modelDetail.peopleId,
            'shortName': modelDetail.shortName,
            'addressBusiness': modelDetail.addressBusiness,
            'addressOffice': modelDetail.addressOffice,
            'phone': modelDetail.phone,
            'email': modelDetail.email,
            'website': modelDetail.website,
            'numberBusiness': modelDetail.numberBusiness,
            'signaturer': modelDetail.signaturer,
            'position': modelDetail.position,
            'registerFee': modelDetail.registerFee === undefined || modelDetail.registerFee === null || modelDetail.registerFee === '' ? '...' : modelDetail.registerFee.toString(),
            'monthFee': modelDetail.monthFee === undefined || modelDetail.monthFee === null || modelDetail.monthFee === '' ? '...' : modelDetail.monthFee.toString(),
            'shopifyFee': modelDetail.shopifyFee === undefined || modelDetail.shopifyFee === null || modelDetail.shopifyFee === '' ? '...' : modelDetail.shopifyFee.toString(),
            'cardTransactionFee': modelDetail.cardTransactionFee,
            'feeForCard': modelDetail.feeForCard,
            'vietNamCard': modelDetail.vietNamCard,
            'otherCard': modelDetail.otherCard,
            'ptTamUng': modelDetail.ptTamUng,
            'ptTamUngMoi': modelDetail.ptTamUngMoi,
            'tgTamUng': modelDetail.tgTamUng,
            'khoanDamBao': modelDetail.khoanDamBao,
            'khoanDamBaoMoi': modelDetail.khoanDamBaoMoi,
            'kyHanFD': modelDetail.kyHanFD,
            'stkGiaiKhoanh': (modelDetail.stkGiaiKhoanh === '' || modelDetail.stkGiaiKhoanh === undefined || modelDetail.stkGiaiKhoanh === null) && contractCode === 'HD09' ? '...' : modelDetail.stkGiaiKhoanh,
            'accountBank': modelDetail.accountBank,
            'feeForMobile': modelDetail.feeForMobile,
            'alertEmailAddress': modelDetail.alertEmailAddress,
            'detailEmailAddress': modelDetail.detailEmailAddress,
            'otherInfo': modelDetail.otherInfo,
            'dayApprove': modelDetail.dayApprove,
            'cardType': modelDetail.cardType,
            'changeContent': modelDetail.changeContent,

            'subTableNoMerchant01': modelDetail.subTableNoMerchant01,
            'subTableMerchant': modelDetail.subTableMerchant,
            'subTableFee': modelDetail.subTableFee,
            'merchantTablePause': modelDetail.subTablePause,

            'subTableShopifyMerchant': modelDetail.subTableShopifyMerchantID,
            'subTableNonShopifyMerchant' : modelDetail.subTableNonShopifyMerchantID,

            'internationalCard01': modelDetail.internationalCard01,
            'domesticCard01': modelDetail.domesticCard01,
            'domesticCardToken01': modelDetail.domesticCardToken01,
            'internationalCard02': modelDetail.internationalCard02,
            'domesticCard02': modelDetail.domesticCard02,
            'domesticCardToken02': modelDetail.domesticCardToken02,
            'internationalCard03': modelDetail.internationalCard03,
            'domesticCard03': modelDetail.domesticCard03,
            'internationalCard04': modelDetail.internationalCard04,
            'domesticCard04': modelDetail.domesticCard04,
            'visaCard': modelDetail.visaCard,
            'masterCard': modelDetail.masterCard,
            'jcbCard': modelDetail.jcbCard,
            'americanCard': modelDetail.americanCard,
            'kyQuyValue': modelDetail.kyQuyValue,
            'khoanhGiuValue': modelDetail.khoanhGiuValue,
            'hinhThucBaoCo01': modelDetail.hinhThucBaoCo01 === 'hinhThucBaoCo010' ? 'Báo có 1 phiên/ngày' : 'Báo có _ _ phiên/ngày vào lúc _ _',
            'hinhThucThuPhiTheoNgay01': modelDetail.hinhThucThuPhiTheoNgay01 === '' ? '' : 'Hình thức thu phí theo ngày: '
              + (modelDetail.hinhThucThuPhiTheoNgay01 === 'hinhThucThuPhiTheoNgay010' ? 'Thu phí cùng bút toán báo có' : '01 bút toán thu phí riêng'),
            'hinhThucThuPhiTheoThang01': modelDetail.hinhThucThuPhiTheoThang01 === '' ? '' : 'Hình thức thu phí theo tháng: 01 bút toán thu phí riêng',
            'accountNameBaoCo': accountNameBaoCo,
            'accountNameThuPhi': accountNameThuPhi,
            'accountNumberBaoCo': accountNumberBaoCo,
            'accountNumberThuPhi': accountNumberThuPhi,
            'bankBaoCo': bankBaoCo,
            'bankThuPhi': bankThuPhi,
            'feeService': modelDetail.feeService,
            'feeTransInternational': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternational, 2),
            'feeTransDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndApp, 0),
            'feeApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeApp, 0),
            'feeVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.feeVietQR, 0),
            'feePaymentDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndApp, 1),
            'percentQrMobile': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobile, 1),
            'percentQrGrab': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrab, 1),
            'percentQrShopee': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopee, 1),
            'percentQrZalo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZalo, 1),
            'percentQrMoMo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMo, 1),
            'percentQrOther': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOther, 1),
            'percentVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.percentVietQR, 1),

            //Phu phi Shopify PL16
            'feeServiceShopify': modelDetail.feeServiceShopify,
            'feeTransInternationalShopify': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternationalShopify, 2),
            'feeTransDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndAppShopify, 0),
            'feeAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeAppShopify, 0),
            'feePaymentDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndAppShopify, 1),
            'approveCardType01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01InternationalShopify),
            'americanExpress01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01InternationalShopify),
            'approveCardType02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02InternationalShopify),
            'americanExpress02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02InternationalShopify),

            'percentQrMobileShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobileShopify, 1),
            'percentQrGrabShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrabShopify, 1),
            'percentQrShopeeShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopeeShopify, 1),
            'percentQrZaloShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZaloShopify, 1),
            'percentQrMoMoShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMoShopify, 1),
            'percentQrOtherShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOtherShopify, 1),

            'approveCardType01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01International),
            'americanExpress01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01International),
            'approveCardType02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02International),
            'americanExpress02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02International),
            'khoanDamBaoInput': modelDetail.khoanDamBaoInput === undefined || modelDetail.khoanDamBaoInput === null ? '' : modelDetail.khoanDamBaoInput.toString(),
            'openByBank': modelDetail.openByBank,
            'danhXung': modelDetail.danhXung,

            // VBUQ
            'authorizedPersonName': modelDetail.authorizedPersonName,
            'authorizedPersonId': modelDetail.authorizedPersonId,
            'authorizedIssuedDate': modelDetail.authorizedIssuedDate ? this.datePipe.transform(modelDetail.authorizedIssuedDate, 'dd/MM/yyyy') : '',
            'authorizedIssuedBy': modelDetail.authorizedIssuedBy,
            'accountName': modelDetail.accountName,
            'accountNumber': modelDetail.accountNumber,
            'authorizationPeriodFrom': modelDetail.authorizationPeriodFrom ? this.datePipe.transform(modelDetail.authorizationPeriodFrom, 'dd/MM/yyyy') : '',
            // 'authorizationPeriodTo': modelDetail.authorizationPeriodTo ? this.datePipe.transform(modelDetail.authorizationPeriodTo, 'dd/MM/yyyy') : '',
            'authorizationPeriodTo': modelDetail.authorizationPeriodTo,
            'authorizedBirthDate': modelDetail.authorizedBirthDate ? this.datePipe.transform(modelDetail.authorizedBirthDate, 'dd/MM/yyyy') : '',
            'authorizedAddress': modelDetail.authorizedAddress,
            'authorizationNumber': modelDetail.authorizationNumber,

            //check box
            'appMobileBox': appMobileBox,
            'otherBankBox': otherBankBox,
            'visaBox': visaBox,
            'masterCardBox': masterCardBox,
            'amexBox': amexBox,
            'jcbBox': jcbBox,
            'unionBox': unionBox,
            'domesticBox': domesticBox,
            'migsBox': migsBox,
            'mpgsBox': mpgsBox,
            'cyberSourceBox': cyberSourceBox,
            'secureBox': modelDetail.secure01.toString(),
            'kyQuyBox': modelDetail.kyQuy.length === 0 ? '' : modelDetail.kyQuy.toString(),
            'khoanhGiuBox': modelDetail.khoanhGiu.length === 0 ? '' : modelDetail.khoanhGiu.toString(),
            'americanExpressBox': americanExpressBox,
            'approveDomesticCardBox': approveDomesticCardBox,
            'approveInternationalCardBox': approveInternationalCardBox,
            'approveInstallmentBox': approveInstallmentBox,
            'approveOnepayDomesticCardBox': approveOnepayDomesticCardBox,
            'approveOnepayMobileAppBox': approveOnepayMobileAppBox,
            'approveBnblBox': approveBnblBox,

            //bnpl
            'bnplFee': modelDetail.bnplFee === undefined || modelDetail.bnplFee === null || modelDetail.bnplFee === '' ? '...' : modelDetail.bnplFee.toString(),
            'bnplFeeHomeCredit': modelDetail.bnplFeeHomeCredit || '',
            'bnplFeeFundiin': modelDetail.bnplFeeFundiin || '',
            'bnplFeeAmigo': modelDetail.bnplFeeAmigo || '',
            'bnplFeeKredivo': modelDetail.bnplFeeKredivo || '',

            'noiDungThayDoi': modelDetail.noiDungThayDoi || '',
            'uyQuyen': modelDetail.uyQuyen || '',

            //BBTLv2
            'ngayThanhLy': modelDetail.ngayThanhLy ? this.datePipe.transform(modelDetail.ngayThanhLy, 'dd/MM/yyyy') : '',
            //PL15v2 phụ lục 3D
            'thoiGianApDung': modelDetail.thoiGianApDung ? this.datePipe.transform(modelDetail.thoiGianApDung, 'dd/MM/yyyy') : '',
          };
          console.log(body);
          return this.merchantService.loadTemplateContract(body).subscribe(response => {
            // this.global.downloadEmit.emit(true);
            if(response){
            this.textHD = response.key;

                  // asBlob() return Promise<Blob|Buffer>
            let data = {
              // 'subject': resp.email_subject,
              // 'emailTo': this.disputeDetail.sendDisputeTo,
              'emailContent': this.textHD,
              'contractId': this.contractId,
          }
            this.dialogService.open(EditTemplateComponent, {
              header: 'Edit Template HD',
              contentStyle: { "width": "950px", "min-height": "100%", "overflow": "auto" },
              baseZIndex: 10000,
              data: JSON.stringify(data),
          });
          }
          });
        }
      } else {
        this.toastr.warning('Hiện chưa có template cho văn bản này !', 'Cảnh báo');
      }
    }
  }
/**
 * Export sub contract (PL-K)
 * @param type 
 * @returns 
 */
  onSubmitExportTemplateContractSub(type: string) {
    const model = new ContractOriginal(this.contractOriginal);
    const modelDetail = new ContractDetailModel(this.contractDetail);

    model.id = this.contractId === undefined || this.contractId === '0' ? '' : this.contractId;
    model.partnerId = this.currentPartnerId;
    model.parentId = this.parentId === undefined ? '0' : this.parentId;
    model.contractCode = this.contractCode;
    model.contractName = this.contractName;
    model.userAction = this.global.activeProfile.name;
    model.contractType = this.contractType;
    model.businessName = this.contractOriginal.businessName === undefined ? '' : this.contractOriginal.businessName;
    model.contractNumber = this.contractOriginal.contractNumber === undefined ? '' : this.handleContractNumber(this.contractOriginal.contractNumber, this.contractCode);
    model.signatureDate = this.contractOriginal.signatureDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'yyyy-MM-dd');
    model.rangeDate = this.contractOriginal.rangeDate === undefined ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'yyyy-MM-dd');
    model.state = this.contractOriginal.state === '' ? 'wait for approve' : this.contractOriginal.state;

    modelDetail.peopleId = this.contractDetail.peopleId === undefined ? '' : this.contractDetail.peopleId;
    modelDetail.carrer = this.contractDetail.carrer === undefined ? '' : this.contractDetail.carrer;
    modelDetail.branch = this.contractDetail.branch === undefined ? '' : this.contractDetail.branch;
    modelDetail.shortName = this.contractDetail.shortName === undefined ? '' : this.contractDetail.shortName;
    modelDetail.addressBusiness = this.contractDetail.addressBusiness === undefined ? '' : this.contractDetail.addressBusiness;
    modelDetail.addressOffice = this.contractDetail.addressOffice === undefined ? '' : this.contractDetail.addressOffice;
    modelDetail.website = this.contractDetail.website === undefined ? '' : this.contractDetail.website;
    modelDetail.email = this.contractDetail.email === undefined ? '' : this.contractDetail.email;
    modelDetail.phone = this.contractDetail.phone === undefined ? '' : this.contractDetail.phone;
    modelDetail.numberBusiness = this.contractDetail.numberBusiness === undefined ? '' : this.contractDetail.numberBusiness;
    modelDetail.accountBank = this.contractDetail.accountBank === undefined ? '' : this.contractDetail.accountBank;
    modelDetail.signaturer = this.contractDetail.signaturer === undefined ? '' : this.contractDetail.signaturer;
    modelDetail.position = this.contractDetail.position === undefined ? '' : this.contractDetail.position;
    modelDetail.cardType = this.contractDetail.cardType === undefined ? '' : this.contractDetail.cardType;
    modelDetail.infoCard = this.contractDetail.infoCard === undefined ? '' : this.contractDetail.infoCard;
    modelDetail.khoanDamBao = this.contractDetail.khoanDamBao === undefined ? '' : this.contractDetail.khoanDamBao;
    modelDetail.khoanDamBaoMoi = this.contractDetail.khoanDamBaoMoi === undefined ? '' : this.contractDetail.khoanDamBaoMoi;
    modelDetail.kyHanFD = this.contractDetail.kyHanFD === undefined ? '' : this.contractDetail.kyHanFD;
    modelDetail.otherInfo = this.contractDetail.otherInfo === undefined ? '' : this.contractDetail.otherInfo;
    modelDetail.dayApprove = this.contractDetail.dayApprove === undefined ? '' : this.contractDetail.dayApprove;
    modelDetail.subTableMerchant = this.contractDetail.subTableMerchant === undefined ? [] : this.contractDetail.subTableMerchant;
    modelDetail.subTableNoMerchant01 = this.contractDetail.subTableNoMerchant01 === undefined ? [] : this.contractDetail.subTableNoMerchant01;
    modelDetail.subTableNoMerchant001 = this.contractDetail.subTableNoMerchant001 === undefined ? [] : this.contractDetail.subTableNoMerchant001;
    modelDetail.subTableNoMerchant02 = this.contractDetail.subTableNoMerchant02 === undefined ? [] : this.contractDetail.subTableNoMerchant02;
    modelDetail.subTableNoMerchant002 = this.contractDetail.subTableNoMerchant002 === undefined ? [] : this.contractDetail.subTableNoMerchant002;
    modelDetail.subTableNoMerchant03 = this.contractDetail.subTableNoMerchant03 === undefined ? [] : this.contractDetail.subTableNoMerchant03;
    modelDetail.subTableNoMerchant003 = this.contractDetail.subTableNoMerchant003 === undefined ? [] : this.contractDetail.subTableNoMerchant003;
    modelDetail.subTableFee = this.contractDetail.subTableFee === undefined ? [] : this.contractDetail.subTableFee;
    modelDetail.subTableMerchantID = this.contractDetail.subTableMerchantID === undefined ? [] : this.contractDetail.subTableMerchantID;
    modelDetail.subTableShopifyMerchantID = this.contractDetail.subTableShopifyMerchantID === undefined ? [] : this.contractDetail.subTableShopifyMerchantID;
    modelDetail.subTableNonShopifyMerchantID = this.contractDetail.subTableNonShopifyMerchantID === undefined ? [] : this.contractDetail.subTableNonShopifyMerchantID;
    modelDetail.cardList = this.contractDetail.cardListArray === undefined ? '' : this.contractDetail.cardListArray.toString();
    modelDetail.paygateList = this.contractDetail.paygateListArray === undefined ? '' : this.contractDetail.paygateListArray.toString();
    modelDetail.stkGiaiKhoanh = this.contractDetail.stkGiaiKhoanh === undefined ? '' : this.contractDetail.stkGiaiKhoanh;
    modelDetail.tgTamUng = this.contractDetail.tgTamUng === undefined ? '' : this.contractDetail.tgTamUng;
    modelDetail.tgTamUngSelection = this.contractDetail.tgTamUngSelection === undefined ? '' : this.contractDetail.tgTamUngSelection;
    modelDetail.ptTamUng = this.contractDetail.ptTamUng === undefined ? '' : this.contractDetail.ptTamUng;
    modelDetail.ptTamUngMoi = this.contractDetail.ptTamUngMoi === undefined ? '' : this.contractDetail.ptTamUngMoi;
    modelDetail.otherCard = this.contractDetail.otherCard === undefined ? '' : this.contractDetail.otherCard;
    modelDetail.vietNamCard = this.contractDetail.vietNamCard === undefined ? '' : this.contractDetail.vietNamCard;
    modelDetail.cardTransactionFee = this.contractDetail.cardTransactionFee === undefined ? '' : this.contractDetail.cardTransactionFee;
    modelDetail.monthFee = this.contractDetail.monthFee === undefined ? '' : this.contractDetail.monthFee;
    modelDetail.registerFee = this.contractDetail.registerFee || '';
    modelDetail.shopifyFee = this.contractDetail.shopifyFee || '';
    modelDetail.feeForCard = this.contractDetail.feeForCard === undefined ? '' : this.contractDetail.feeForCard;
    modelDetail.feeForMobile = this.contractDetail.feeForMobile === undefined ? '' : this.contractDetail.feeForMobile;

    modelDetail.detailEmailAddress = this.contractDetail.detailEmailAddress === undefined ? '' : this.contractDetail.detailEmailAddress;
    modelDetail.alertEmailAddress = this.contractDetail.alertEmailAddress === undefined ? '' : this.contractDetail.alertEmailAddress;
    modelDetail.kyQuyValue = this.contractDetail.kyQuyValue === undefined ? '' : this.contractDetail.kyQuyValue;
    modelDetail.kyQuy = this.contractDetail.kyQuy === undefined ? '' : this.contractDetail.kyQuy;
    modelDetail.khoanhGiuValue = this.contractDetail.khoanhGiuValue === undefined ? '' : this.contractDetail.khoanhGiuValue;
    modelDetail.khoanhGiu = this.contractDetail.khoanhGiu === undefined ? '' : this.contractDetail.khoanhGiu;
    modelDetail.americanCard = this.contractDetail.americanCard === undefined ? '' : this.contractDetail.americanCard;
    modelDetail.jcbCard = this.contractDetail.jcbCard === undefined ? '' : this.contractDetail.jcbCard;
    modelDetail.masterCard = this.contractDetail.masterCard === undefined ? '' : this.contractDetail.masterCard;
    modelDetail.visaCard = this.contractDetail.visaCard === undefined ? '' : this.contractDetail.visaCard;
    modelDetail.domesticCard02 = this.contractDetail.domesticCard02 === undefined ? '' : this.contractDetail.domesticCard02;
    modelDetail.internationalCard02 = this.contractDetail.internationalCard02 === undefined ? '' : this.contractDetail.internationalCard02;
    modelDetail.domesticCardToken02 = this.contractDetail.domesticCardToken02 === undefined ? '' : this.contractDetail.domesticCardToken02;
    modelDetail.domesticCard01 = this.contractDetail.domesticCard01 === undefined ? '' : this.contractDetail.domesticCard01;
    modelDetail.internationalCard01 = this.contractDetail.internationalCard01 === undefined ? '' : this.contractDetail.internationalCard01;
    modelDetail.domesticCardToken01 = this.contractDetail.domesticCardToken01 === undefined ? '' : this.contractDetail.domesticCardToken01;
    modelDetail.hinhThucThuPhiTheoThang03 = this.contractDetail.hinhThucThuPhiTheoThang03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang03;
    modelDetail.hinhThucThuPhiTheoNgay03 = this.contractDetail.hinhThucThuPhiTheoNgay03 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay03;
    modelDetail.hinhThucBaoCo03 = this.contractDetail.hinhThucBaoCo03 === undefined ? '' : this.contractDetail.hinhThucBaoCo03;
    modelDetail.hinhThucThuPhiTheoThang02 = this.contractDetail.hinhThucThuPhiTheoThang02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang02;
    modelDetail.hinhThucThuPhiTheoNgay02 = this.contractDetail.hinhThucThuPhiTheoNgay02 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay02;
    modelDetail.hinhThucBaoCo02 = this.contractDetail.hinhThucBaoCo02 === undefined ? '' : this.contractDetail.hinhThucBaoCo02;
    modelDetail.hinhThucThuPhiTheoThang01 = this.contractDetail.hinhThucThuPhiTheoThang01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoThang01;
    modelDetail.hinhThucThuPhiTheoNgay01 = this.contractDetail.hinhThucThuPhiTheoNgay01 === undefined ? '' : this.contractDetail.hinhThucThuPhiTheoNgay01;
    modelDetail.hinhThucBaoCo01 = this.contractDetail.hinhThucBaoCo01 === undefined ? '' : this.contractDetail.hinhThucBaoCo01;
    modelDetail.secure03 = this.contractDetail.secure03 === undefined ? '' : this.contractDetail.secure03;
    modelDetail.secure02 = this.contractDetail.secure02 === undefined ? '' : this.contractDetail.secure02;
    modelDetail.secure01 = this.contractDetail.secure01 === undefined ? '' : this.contractDetail.secure01;
    modelDetail.accountFee03 = this.contractDetail.accountFee03 === undefined ? '' : this.contractDetail.accountFee03;
    modelDetail.accountFee02 = this.contractDetail.accountFee02 === undefined ? '' : this.contractDetail.accountFee02;
    modelDetail.accountFee01 = this.contractDetail.accountFee01 === undefined ? '' : this.contractDetail.accountFee01;
    modelDetail.internationalCard03 = this.contractDetail.internationalCard03 === undefined ? '' : this.contractDetail.internationalCard03;
    modelDetail.internationalCard04 = this.contractDetail.internationalCard04 === undefined ? '' : this.contractDetail.internationalCard04;
    modelDetail.domesticCard03 = this.contractDetail.domesticCard03 === undefined ? '' : this.contractDetail.domesticCard03;
    modelDetail.domesticCard04 = this.contractDetail.domesticCard04 === undefined ? '' : this.contractDetail.domesticCard04;
    modelDetail.changeContent = this.contractDetail.changeContent === undefined ? '' : this.contractDetail.changeContent;
    modelDetail.subTablePause = this.contractDetail.subTablePause === undefined ? [] : this.contractDetail.subTablePause;
    modelDetail.typeFeeInstallment = this.contractDetail.typeFeeInstallment === undefined ? '' : this.contractDetail.typeFeeInstallment;
    modelDetail.authorizedPersonName = this.contractDetail.authorizedPersonName ?? '';
    modelDetail.authorizedPersonId = this.contractDetail.authorizedPersonId ?? '';
    modelDetail.authorizedIssuedDate = this.contractDetail.authorizedIssuedDate ?? '';
    modelDetail.authorizedIssuedBy = this.contractDetail.authorizedIssuedBy ?? '';
    modelDetail.accountName = this.contractDetail.accountName ?? '';
    modelDetail.accountNumber = this.contractDetail.accountNumber ?? '';
    modelDetail.authorizationPeriodFrom = this.contractDetail.authorizationPeriodFrom ?? '';
    modelDetail.authorizationPeriodTo = this.contractDetail.authorizationPeriodTo ?? '';
    modelDetail.authorizedBirthDate = this.contractDetail.authorizedBirthDate ?? '';
    modelDetail.authorizedAddress = this.contractDetail.authorizedAddress ?? '';
    modelDetail.authorizationNumber = this.contractDetail.authorizationNumber ?? '';

    modelDetail.feeService = this.contractDetail.feeService === undefined ? '' : this.contractDetail.feeService;
    modelDetail.autoFillStandardFee = this.contractDetail.autoFillStandardFee === undefined ? '' : this.contractDetail.autoFillStandardFee;
    modelDetail.feeTransDomesticAndApp = this.contractDetail.feeTransDomesticAndApp || '';
    modelDetail.feeApp = this.contractDetail.feeApp === undefined ? '' : this.contractDetail.feeApp;
    modelDetail.feeVietQR = this.contractDetail.feeVietQR === undefined ? '' : this.contractDetail.feeVietQR;
    modelDetail.feePaymentDomesticAndApp = this.contractDetail.feePaymentDomesticAndApp === undefined ? '' : this.contractDetail.feePaymentDomesticAndApp;
    modelDetail.feeTransInternational = this.contractDetail.feeTransInternational || '';
    modelDetail.approveCardType01International = this.contractDetail.approveCardType01International === undefined ? '' : this.contractDetail.approveCardType01International;
    modelDetail.americanExpress01International = this.contractDetail.americanExpress01International === undefined ? '' : this.contractDetail.americanExpress01International;
    modelDetail.approveCardType02International = this.contractDetail.approveCardType02International === undefined ? '' : this.contractDetail.approveCardType02International;
    modelDetail.americanExpress02International = this.contractDetail.americanExpress02International === undefined ? '' : this.contractDetail.americanExpress02International;
    modelDetail.insideDomestic = this.contractDetail.insideDomestic === undefined ? '' : this.contractDetail.insideDomestic;
    modelDetail.outsideDomestic = this.contractDetail.outsideDomestic === undefined ? '' : this.contractDetail.outsideDomestic;
    modelDetail.hinhThucThuPhi = this.contractDetail.hinhThucThuPhi === undefined ? '' : this.contractDetail.hinhThucThuPhi;
    modelDetail.khoanDamBaoSelection = this.contractDetail.khoanDamBaoSelection === undefined ? '' : this.contractDetail.khoanDamBaoSelection;
    modelDetail.khoanDamBaoInput = this.contractDetail.khoanDamBaoInput === undefined ? '' : this.contractDetail.khoanDamBaoInput;
    modelDetail.inputHinhThucThuPhiKhac = this.contractDetail.inputHinhThucThuPhiKhac === undefined ? '' : this.contractDetail.inputHinhThucThuPhiKhac;
    modelDetail.inputTgTamUngKhac = this.contractDetail.inputTgTamUngKhac === undefined ? '' : this.contractDetail.inputTgTamUngKhac;
    modelDetail.inputTgTamUngKhacKetThucPhien = this.contractDetail.inputTgTamUngKhacKetThucPhien === undefined ? '' : this.contractDetail.inputTgTamUngKhacKetThucPhien;
    modelDetail.kyQuyType = this.contractDetail.kyQuyType === undefined ? '' : this.contractDetail.kyQuyType;
    modelDetail.kyQuyAutoFill = this.contractDetail.kyQuyAutoFill === undefined ? '' : this.contractDetail.kyQuyAutoFill;
    modelDetail.openByBank = this.contractDetail.openByBank === undefined ? '' : this.contractDetail.openByBank;
    modelDetail.inputKyQuyKhac = this.contractDetail.inputKyQuyKhac === undefined ? '' : this.contractDetail.inputKyQuyKhac;
    modelDetail.danhXung = this.contractDetail.danhXung === undefined ? '' : this.contractDetail.danhXung;
    modelDetail.keepPercent = this.contractDetail.keepPercent === undefined ? '' : this.contractDetail.keepPercent;

    modelDetail.percentQrMobile = this.contractDetail.percentQrMobile === undefined ? '' : this.contractDetail.percentQrMobile;
    modelDetail.percentQrGrab = this.contractDetail.percentQrGrab === undefined ? '' : this.contractDetail.percentQrGrab;
    modelDetail.percentQrShopee = this.contractDetail.percentQrShopee === undefined ? '' : this.contractDetail.percentQrShopee;
    modelDetail.percentQrZalo = this.contractDetail.percentQrZalo === undefined ? '' : this.contractDetail.percentQrZalo;
    modelDetail.percentQrMoMo = this.contractDetail.percentQrMoMo === undefined ? '' : this.contractDetail.percentQrMoMo;
    modelDetail.percentQrOther = this.contractDetail.percentQrOther === undefined ? '' : this.contractDetail.percentQrOther;
    modelDetail.inforOther = this.contractDetail.inforOther === undefined ? '' : this.contractDetail.inforOther;
    modelDetail.percentVietQR = this.contractDetail.percentVietQR === undefined ? '' : this.contractDetail.percentVietQR;

    //Phu phi shopify PL16
    modelDetail.autoFillStandardFeeShopify = this.contractDetail.autoFillStandardFeeShopify === undefined ? '' : this.contractDetail.autoFillStandardFeeShopify;
    modelDetail.feeTransDomesticAndAppShopify = this.contractDetail.feeTransDomesticAndAppShopify === undefined ? '' : this.contractDetail.feeTransDomesticAndAppShopify;
    modelDetail.feeAppShopify = this.contractDetail.feeAppShopify === undefined ? '' : this.contractDetail.feeAppShopify;
    modelDetail.feePaymentDomesticAndAppShopify = this.contractDetail.feePaymentDomesticAndAppShopify === undefined ? '' : this.contractDetail.feePaymentDomesticAndAppShopify;
    modelDetail.feeTransInternationalShopify = this.contractDetail.feeTransInternationalShopify === undefined ? '' : this.contractDetail.feeTransInternationalShopify;
    modelDetail.approveCardType01InternationalShopify = this.contractDetail.approveCardType01InternationalShopify === undefined ? '' : this.contractDetail.approveCardType01InternationalShopify;
    modelDetail.americanExpress01InternationalShopify = this.contractDetail.americanExpress01InternationalShopify === undefined ? '' : this.contractDetail.americanExpress01InternationalShopify;
    modelDetail.approveCardType02InternationalShopify = this.contractDetail.approveCardType02InternationalShopify === undefined ? '' : this.contractDetail.approveCardType02InternationalShopify;
    modelDetail.americanExpress02InternationalShopify = this.contractDetail.americanExpress02InternationalShopify === undefined ? '' : this.contractDetail.americanExpress02InternationalShopify;
    modelDetail.feeServiceShopify = this.contractDetail.feeServiceShopify === undefined ? '' : this.contractDetail.feeServiceShopify;

    modelDetail.percentQrMobileShopify = this.contractDetail.percentQrMobileShopify === undefined ? '' : this.contractDetail.percentQrMobileShopify;
    modelDetail.percentQrGrabShopify = this.contractDetail.percentQrGrabShopify === undefined ? '' : this.contractDetail.percentQrGrabShopify;
    modelDetail.percentQrShopeeShopify = this.contractDetail.percentQrShopeeShopify === undefined ? '' : this.contractDetail.percentQrShopeeShopify;
    modelDetail.percentQrZaloShopify = this.contractDetail.percentQrZaloShopify === undefined ? '' : this.contractDetail.percentQrZaloShopify;
    modelDetail.percentQrMoMoShopify = this.contractDetail.percentQrMoMoShopify === undefined ? '' : this.contractDetail.percentQrMoMoShopify;
    modelDetail.percentQrOtherShopify = this.contractDetail.percentQrOtherShopify === undefined ? '' : this.contractDetail.percentQrOtherShopify;
    modelDetail.inforOtherShopify = this.contractDetail.inforOtherShopify === undefined ? '' : this.contractDetail.inforOtherShopify;

    modelDetail.bnplFee = this.contractDetail.bnplFee === undefined ? '' : this.contractDetail.bnplFee;
    modelDetail.bnplFeeHomeCredit = this.contractDetail.bnplFeeHomeCredit || '';
    modelDetail.bnplFeeFundiin = this.contractDetail.bnplFeeFundiin || '';
    modelDetail.bnplFeeAmigo = this.contractDetail.bnplFeeAmigo || '';
    modelDetail.bnplFeeKredivo = this.contractDetail.bnplFeeKredivo || '';

    modelDetail.noiDungThayDoi = this.contractDetail.noiDungThayDoi;
    modelDetail.uyQuyen = this.contractDetail.uyQuyen;

    modelDetail.version = 'v2' ;

    //BBTLv2
    modelDetail.ngayThanhLy = this.contractDetail.ngayThanhLy ?? '';
    //PL15v2 phụ lục 3D
    modelDetail.thoiGianApDung = this.contractDetail.thoiGianApDung ?? '';
    if (type === '0') {
      const body = {
        'id': model.id,
        'partnerId': model.partnerId,
        'parentId': model.parentId,
        'contractCode': model.contractCode,
        'contractName': model.contractName,
        'businessName': model.businessName,
        'signatureDate': model.signatureDate,
        'rangeDate': model.rangeDate,
        'userAction': model.userAction,
        'state': model.state,
        'contractNumber': model.contractNumber,
        'contractType': model.contractType,
        'order': this.handleContractOrder(model.contractCode),

        'dataContractDetail': JSON.stringify(modelDetail),
      };
      this.merchantService.pushContract(body).subscribe(data => {
        if (data.n_result === '200') {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.success('Cập nhật ' + this.contractName + ' thành công', 'Thành công');
            this.checkEditContractDetail = false;
            this.contractService.setEditing(false);
          } else {
            this.toastr.success('Tạo mới ' + this.contractName + ' thành công', 'Thành công');
            this.backContractManagement();
          }
        } else {
          if (this.contractId !== undefined && this.contractId !== '' && this.contractId !== '0') {
            this.toastr.error('Cập nhật ' + this.contractName + ' không thành công', 'Thất bại');
          } else {
            this.toastr.error('Tạo mới ' + this.contractName + ' không thành công', 'Thất bại');
          }
        }
      });

    } else {
      console.log(this.contractCode)
      var checkExport = false;
      if (this.contractCode === 'HD03-01' || this.contractCode === 'HD04' || this.contractCode === 'HD05' || this.contractCode === 'HD06' || this.contractCode === 'HD08'
        || this.contractCode === 'HD09' || this.contractCode === 'HD11' || this.contractCode === 'HD12' || this.contractCode === 'HD13' || this.contractCode === 'PL01'
        || this.contractCode === 'PL02' || this.contractCode === 'PL03' || this.contractCode === 'PL04' || this.contractCode === 'PL05' || this.contractCode === 'PL06'
        || this.contractCode === 'PL-K'||  this.contractCode === 'PL07' || this.contractCode === 'PL08' || this.contractCode === 'PL09' || this.contractCode === 'PL12' || this.contractCode === 'PL14' || this.contractCode === 'PLBNPL' || this.contractCode === 'PL16') {
        checkExport = true;
      } else if (this.contractCode === 'BBTL' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD07' || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if ((this.contractCode === 'PL11' || this.contractCode === 'PL15' || this.contractCode === 'PL15v2') && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'BBNT' && (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD05' || this.parentContractCode === 'HD06'
        || this.parentContractCode === 'HD08' || this.parentContractCode === 'HD03-01')) {
        checkExport = true;
      } else if (this.contractCode === 'VBUQ') {
        checkExport = true;
      } if (this.contractCode === 'BBNT') {
        checkExport = true;
      }

      if (checkExport === true) {

        var contractCode = '';
        var carrer = '';
        var branch = '';
        var unionBox = '';
        var otherBankBox = '';
        var appMobileBox = '';
        var visaBox = '';
        var masterCardBox = '';
        var amexBox = '';
        var jcbBox = '';
        var unionBox = '';
        var domesticBox = '';
        var mpgsBox = '';
        var migsBox = '';
        var cyberSourceBox = '';
        var accountNumberBaoCo = '';
        var accountNameBaoCo = '';
        var bankBaoCo = '';
        var accountNumberThuPhi = '';
        var accountNameThuPhi = '';
        var bankThuPhi = '';

        var americanExpressBox = '';
        var approveDomesticCardBox = '';
        var approveInternationalCardBox = '';
        var approveInstallmentBox = '';
        var approveOnepayDomesticCardBox = '';
        var approveOnepayMobileAppBox = '';
        var approveBnblBox = '';

        if (model.contractCode === 'BBTL' || model.contractCode === 'PL11' || model.contractCode === 'PL15') {
          if (model.contractCode === 'BBTL' && this.parentContractCode === 'HD07') {
            contractCode = model.contractCode + '-CUP';
          } else if (this.parentContractCode === 'HD04' || this.parentContractCode === 'HD08') {
            contractCode = model.contractCode + '-2BEN';
          } else {
            contractCode = model.contractCode + '-3BEN';
          }
        } else if (model.contractCode === 'BBNT') {
          contractCode = model.contractCode + '-' + this.parentContractCode;
        } else if (model.contractCode === 'HD13') {
          var checkInstallment = '';
          var checkShopify = '';
          modelDetail.cardListArray.forEach(element => {
            if (element === 'ApproveInstallment') {
              checkInstallment = element;
            }
            if (element === 'ApproveShopify') {
              checkShopify = element;
            }
          });
          if (checkInstallment !== '' && modelDetail.kyQuyType === 'kyQuyKhac' ||
            modelDetail.tgTamUngSelection === 'other' || modelDetail.feeService === 'specialFee' || modelDetail.hinhThucThuPhi === 'Khac') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH0';
              }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH1';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH2';
            }
          } else if (checkInstallment === '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep') {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH3';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH4';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyStandard' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH5';
            }
          } else if (checkInstallment !== '' && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.kyQuyType === 'kyQuyKeep' && modelDetail.subTableFee.length > 0) {
            if (modelDetail.version != 'v2' && checkShopify == '') {
              contractCode = model.contractCode + '_TH00';
            } else if (checkShopify !== '') {
              contractCode = model.contractCode + '_Shopify';
            } else {
              contractCode = model.contractCode + '_TH6';
            }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH7';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't2' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH8';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH9';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '2'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH10';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'Mien') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH11';
              }
          } else if (checkInstallment !== '' && modelDetail.tgTamUngSelection === 't1' && modelDetail.typeFeeInstallment === '1'
            && modelDetail.hinhThucThuPhi === 'ThuPhiCungButToanBaoCo' && modelDetail.khoanDamBaoSelection === 'KyQuy') {
              if (modelDetail.version != 'v2' && checkShopify == '') {
                contractCode = model.contractCode + '_TH00';
              } else if (checkShopify !== '') {
                contractCode = model.contractCode + '_Shopify';
              } else {
                contractCode = model.contractCode + '_TH12';
              }
          } else if (checkShopify !== '') {
            contractCode = model.contractCode + '_Shopify';
          } else {
            if (modelDetail.version != 'v2') {
              contractCode = model.contractCode + '_TH00';
            } else {
              contractCode = model.contractCode + '_TH0';
            }
          }
        } else {
          contractCode = model.contractCode;
        }

        this.branchList.forEach(element => {
          if (element.id === modelDetail.branch) {
            branch = element.name;
          }
        });

        this.carrerList.forEach(element => {
          if (element.value === modelDetail.carrer) {
            carrer = element.label;
          }
        });

        if (modelDetail.cardListArray.length > 0) {
          modelDetail.cardListArray.forEach(element => {
            if (element === 'appMobile') {
              appMobileBox = 'appMobileBox';
            } else if (element === 'vcbND' || element === 'otherBank') {
              otherBankBox = 'otherBankBox';
            } else if (element === 'Visa') {
              visaBox = 'Visa';
            } else if (element === 'AMEX') {
              amexBox = 'AMEX';
            } else if (element === 'JCB') {
              jcbBox = 'JCB';
            } else if (element === 'UnionPay') {
              unionBox = 'UnionPay';
            } else if (element === 'domesticCard') {
              domesticBox = 'domesticCard';
            } else if (element === 'AmericanExpress') {
              americanExpressBox = 'AmericanExpress';
            } else if (element === 'ApproveDomesticCard') {
              approveDomesticCardBox = 'ApproveDomesticCard';
            } else if (element === 'ApproveInternationalCard') {
              approveInternationalCardBox = 'ApproveInternationalCard';
            } else if (element === 'ApproveInstallment') {
              approveInstallmentBox = 'ApproveInstallment';
            } else if (element === 'ApproveOnepayDomesticCard') {
              approveOnepayDomesticCardBox = 'ApproveOnepayDomesticCard';
            } else if (element === 'ApproveOnepayMobileApp') {
              approveOnepayMobileAppBox = 'ApproveOnepayMobileApp';
            } else if (element === 'MasterCard') {
              masterCardBox = 'MasterCard';
            } else if (element === 'ApproveBNPL') {
              approveBnblBox = 'ApproveBNPL';
            }
          });
        }

        if (modelDetail.paygateListArray.length > 0) {
          modelDetail.paygateListArray.forEach(element => {
            if (element === 'MPGS') {
              mpgsBox = 'MPGS';
            } else if (element === 'MiGS') {
              migsBox = 'MiGS';
            } else if (element === 'Cybersource') {
              cyberSourceBox = 'Cybersource';
            }
          });
        }
        //Check only BNBL option then block export file
        if (model.contractCode === 'HD13' && approveBnblBox === 'ApproveBNPL' && visaBox !== 'Visa' && masterCardBox !== 'MasterCard' && amexBox !== 'AMEX' && jcbBox !== 'JCB' && domesticBox !== 'domesticCard' && americanExpressBox !== 'AmericanExpress'
          && approveDomesticCardBox !== 'ApproveDomesticCard' && approveInternationalCardBox !== 'ApproveInternationalCard' && approveInstallmentBox !== 'ApproveInstallment'
          && approveOnepayDomesticCardBox !== 'ApproveOnepayDomesticCard' && approveOnepayMobileAppBox !== 'ApproveOnepayMobileApp') {
          this.toastr.warning('Iportal không hỗ trợ xuất với trường hợp Hợp đồng này');
        } else {

          if (contractCode === 'PL01' || contractCode === 'HD03-01') {
            accountNameBaoCo = modelDetail.subTableNoMerchant01[0].accountName;
            accountNumberBaoCo = modelDetail.subTableNoMerchant01[0].accountNumber;
            bankBaoCo = modelDetail.subTableNoMerchant01[0].bank;
            if (modelDetail.accountFee01 === 'accountFee010') {
              accountNameThuPhi = accountNameBaoCo;
              accountNumberThuPhi = accountNumberBaoCo;
              bankThuPhi = bankBaoCo;
            } else {
              accountNameThuPhi = modelDetail.subTableNoMerchant001[0].accountName;
              accountNumberThuPhi = modelDetail.subTableNoMerchant001[0].accountNumber;
              bankThuPhi = modelDetail.subTableNoMerchant001[0].bank;
            }
          }

          const body = {
            'parentshortname': this.merchantId ? this.merchantId.replace(" ", "") : "",
            'id': model.id,
            'partnerId': model.partnerId,
            'parentId': model.parentId,
            'contractCode': contractCode,
            'contractName': model.contractName,
            'businessName': model.businessName,
            'signatureDate': model.signatureDate === undefined || model.signatureDate === '' ? '' : this.datePipe.transform(this.contractOriginal.signatureDate, 'dd/MM/yyyy'),
            'rangeDate': model.rangeDate === undefined || model.rangeDate === '' ? '' : this.datePipe.transform(this.contractOriginal.rangeDate, 'dd/MM/yyyy'),
            'userAction': model.userAction,
            'state': model.state,
            'contractNumber': model.contractNumber,
            'contractType': model.contractType,
            'keepPercent': !modelDetail.keepPercent ? '' : modelDetail.keepPercent.toString(),
            'parentContractFullName': this.parentContractFullName === undefined ? '' : this.parentContractFullName,
            'parentContractNumber': this.parentContractNumber === undefined ? '' : this.parentContractNumber,
            'parentSignatureDate': this.parentSignatureDate === undefined ? '' : this.parentSignatureDate,
            'parentPtTamUng': this.parentPtTamUng === undefined ? '' : this.parentPtTamUng,
            'parentTgTamUng': this.parentTgTamUng === undefined ? '' : this.parentTgTamUng,
            'parentStkGiaiKhoanh': this.parentStkGiaiKhoanh === undefined ? '' : this.parentStkGiaiKhoanh,
            'parentCardTransactionFee': this.parentCardTransactionFee === undefined ? '' : this.parentCardTransactionFee,
            'parentFeeForCard': this.parentFeeForCard === undefined ? '' : this.parentFeeForCard,
            'parentAccountBank': this.parentAccountBank === undefined ? '' : this.parentAccountBank,
            'parentTableMerchant': this.parentTableMerchant,

            'career': modelDetail.carrer,
            'timeAdvance': modelDetail.tgTamUngSelection,
            'inputTgTamUngKhac': modelDetail.inputTgTamUngKhac,
            'inputTgTamUngKhacKetThucPhien': modelDetail.inputTgTamUngKhacKetThucPhien,
            'branch': branch,
            'peopleId': modelDetail.peopleId,
            'shortName': modelDetail.shortName,
            'addressBusiness': modelDetail.addressBusiness,
            'addressOffice': modelDetail.addressOffice,
            'phone': modelDetail.phone,
            'email': modelDetail.email,
            'website': modelDetail.website,
            'numberBusiness': modelDetail.numberBusiness,
            'signaturer': modelDetail.signaturer,
            'position': modelDetail.position,
            'registerFee': modelDetail.registerFee === undefined || modelDetail.registerFee === null || modelDetail.registerFee === '' ? '...' : modelDetail.registerFee.toString(),
            'monthFee': modelDetail.monthFee === undefined || modelDetail.monthFee === null || modelDetail.monthFee === '' ? '...' : modelDetail.monthFee.toString(),
            'shopifyFee': modelDetail.shopifyFee === undefined || modelDetail.shopifyFee === null || modelDetail.shopifyFee === '' ? '...' : modelDetail.shopifyFee.toString(),
            'cardTransactionFee': modelDetail.cardTransactionFee,
            'feeForCard': modelDetail.feeForCard,
            'vietNamCard': modelDetail.vietNamCard,
            'otherCard': modelDetail.otherCard,
            'ptTamUng': modelDetail.ptTamUng,
            'ptTamUngMoi': modelDetail.ptTamUngMoi,
            'tgTamUng': modelDetail.tgTamUng,
            'khoanDamBao': modelDetail.khoanDamBao,
            'khoanDamBaoMoi': modelDetail.khoanDamBaoMoi,
            'kyHanFD': modelDetail.kyHanFD,
            'stkGiaiKhoanh': (modelDetail.stkGiaiKhoanh === '' || modelDetail.stkGiaiKhoanh === undefined || modelDetail.stkGiaiKhoanh === null) && contractCode === 'HD09' ? '...' : modelDetail.stkGiaiKhoanh,
            'accountBank': modelDetail.accountBank,
            'feeForMobile': modelDetail.feeForMobile,
            'alertEmailAddress': modelDetail.alertEmailAddress,
            'detailEmailAddress': modelDetail.detailEmailAddress,
            'otherInfo': modelDetail.otherInfo,
            'dayApprove': modelDetail.dayApprove,
            'cardType': modelDetail.cardType,
            'changeContent': modelDetail.changeContent,

            'subTableNoMerchant01': modelDetail.subTableNoMerchant01,
            'subTableMerchant': modelDetail.subTableMerchant,
            'subTableFee': modelDetail.subTableFee,
            'merchantTablePause': modelDetail.subTablePause,

            'subTableShopifyMerchant': modelDetail.subTableShopifyMerchantID,
            'subTableNonShopifyMerchant' : modelDetail.subTableNonShopifyMerchantID,

            'internationalCard01': modelDetail.internationalCard01,
            'domesticCard01': modelDetail.domesticCard01,
            'domesticCardToken01': modelDetail.domesticCardToken01,
            'internationalCard02': modelDetail.internationalCard02,
            'domesticCard02': modelDetail.domesticCard02,
            'domesticCardToken02': modelDetail.domesticCardToken02,
            'internationalCard03': modelDetail.internationalCard03,
            'domesticCard03': modelDetail.domesticCard03,
            'internationalCard04': modelDetail.internationalCard04,
            'domesticCard04': modelDetail.domesticCard04,
            'visaCard': modelDetail.visaCard,
            'masterCard': modelDetail.masterCard,
            'jcbCard': modelDetail.jcbCard,
            'americanCard': modelDetail.americanCard,
            'kyQuyValue': modelDetail.kyQuyValue,
            'khoanhGiuValue': modelDetail.khoanhGiuValue,
            'hinhThucBaoCo01': modelDetail.hinhThucBaoCo01 === 'hinhThucBaoCo010' ? 'Báo có 1 phiên/ngày' : 'Báo có _ _ phiên/ngày vào lúc _ _',
            'hinhThucThuPhiTheoNgay01': modelDetail.hinhThucThuPhiTheoNgay01 === '' ? '' : 'Hình thức thu phí theo ngày: '
              + (modelDetail.hinhThucThuPhiTheoNgay01 === 'hinhThucThuPhiTheoNgay010' ? 'Thu phí cùng bút toán báo có' : '01 bút toán thu phí riêng'),
            'hinhThucThuPhiTheoThang01': modelDetail.hinhThucThuPhiTheoThang01 === '' ? '' : 'Hình thức thu phí theo tháng: 01 bút toán thu phí riêng',
            'accountNameBaoCo': accountNameBaoCo,
            'accountNameThuPhi': accountNameThuPhi,
            'accountNumberBaoCo': accountNumberBaoCo,
            'accountNumberThuPhi': accountNumberThuPhi,
            'bankBaoCo': bankBaoCo,
            'bankThuPhi': bankThuPhi,
            'feeService': modelDetail.feeService,
            'feeTransInternational': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternational, 2),
            'feeTransDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndApp, 0),
            'feeApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeApp, 0),
            'feeVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.feeVietQR, 0),
            'feePaymentDomesticAndApp': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndApp, 1),
            'percentQrMobile': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobile, 1),
            'percentQrGrab': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrab, 1),
            'percentQrShopee': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopee, 1),
            'percentQrZalo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZalo, 1),
            'percentQrMoMo': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMo, 1),
            'percentQrOther': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOther, 1),
            'percentVietQR': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveDischargeVietQR', modelDetail.cardListArray, modelDetail.percentVietQR, 1),

            //Phu phi Shopify PL16
            'feeServiceShopify': modelDetail.feeServiceShopify,
            'feeTransInternationalShopify': this.handleFeeForDomesticOrInternational('', '', modelDetail.cardListArray, modelDetail.feeTransInternationalShopify, 2),
            'feeTransDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeTransDomesticAndAppShopify, 0),
            'feeAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feeAppShopify, 0),
            'feePaymentDomesticAndAppShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.feePaymentDomesticAndAppShopify, 1),
            'approveCardType01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01InternationalShopify),
            'americanExpress01InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01InternationalShopify),
            'approveCardType02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02InternationalShopify),
            'americanExpress02InternationalShopify': modelDetail.feeServiceShopify === 'specialFee' ? 'Không áp dụng'
            : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02InternationalShopify),

            'percentQrMobileShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMobileShopify, 1),
            'percentQrGrabShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrGrabShopify, 1),
            'percentQrShopeeShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrShopeeShopify, 1),
            'percentQrZaloShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrZaloShopify, 1),
            'percentQrMoMoShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrMoMoShopify, 1),
            'percentQrOtherShopify': this.handleFeeForDomesticOrInternational('ApproveOnepayDomesticCard', 'ApproveOnepayMobileApp', modelDetail.cardListArray, modelDetail.percentQrOtherShopify, 1),

            'approveCardType01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.approveCardType01International),
            'americanExpress01International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveDomesticCard', modelDetail.cardListArray, modelDetail.americanExpress01International),
            'approveCardType02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForDomesticOrInternational('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.approveCardType02International),
            'americanExpress02International': modelDetail.feeService === 'specialFee' ? 'Không áp dụng'
              : this.handleApproveFeeForAmerican('ApproveInternationalCard', modelDetail.cardListArray, modelDetail.americanExpress02International),
            'khoanDamBaoInput': modelDetail.khoanDamBaoInput === undefined || modelDetail.khoanDamBaoInput === null ? '' : modelDetail.khoanDamBaoInput.toString(),
            'openByBank': modelDetail.openByBank,
            'danhXung': modelDetail.danhXung,

            // VBUQ
            'authorizedPersonName': modelDetail.authorizedPersonName,
            'authorizedPersonId': modelDetail.authorizedPersonId,
            'authorizedIssuedDate': modelDetail.authorizedIssuedDate ? this.datePipe.transform(modelDetail.authorizedIssuedDate, 'dd/MM/yyyy') : '',
            'authorizedIssuedBy': modelDetail.authorizedIssuedBy,
            'accountName': modelDetail.accountName,
            'accountNumber': modelDetail.accountNumber,
            'authorizationPeriodFrom': modelDetail.authorizationPeriodFrom ? this.datePipe.transform(modelDetail.authorizationPeriodFrom, 'dd/MM/yyyy') : '',
            // 'authorizationPeriodTo': modelDetail.authorizationPeriodTo ? this.datePipe.transform(modelDetail.authorizationPeriodTo, 'dd/MM/yyyy') : '',
            'authorizationPeriodTo': modelDetail.authorizationPeriodTo,
            'authorizedBirthDate': modelDetail.authorizedBirthDate ? this.datePipe.transform(modelDetail.authorizedBirthDate, 'dd/MM/yyyy') : '',
            'authorizedAddress': modelDetail.authorizedAddress,
            'authorizationNumber': modelDetail.authorizationNumber,

            //check box
            'appMobileBox': appMobileBox,
            'otherBankBox': otherBankBox,
            'visaBox': visaBox,
            'masterCardBox': masterCardBox,
            'amexBox': amexBox,
            'jcbBox': jcbBox,
            'unionBox': unionBox,
            'domesticBox': domesticBox,
            'migsBox': migsBox,
            'mpgsBox': mpgsBox,
            'cyberSourceBox': cyberSourceBox,
            'secureBox': modelDetail.secure01.toString(),
            'kyQuyBox': modelDetail.kyQuy.length === 0 ? '' : modelDetail.kyQuy.toString(),
            'khoanhGiuBox': modelDetail.khoanhGiu.length === 0 ? '' : modelDetail.khoanhGiu.toString(),
            'americanExpressBox': americanExpressBox,
            'approveDomesticCardBox': approveDomesticCardBox,
            'approveInternationalCardBox': approveInternationalCardBox,
            'approveInstallmentBox': approveInstallmentBox,
            'approveOnepayDomesticCardBox': approveOnepayDomesticCardBox,
            'approveOnepayMobileAppBox': approveOnepayMobileAppBox,
            'approveBnblBox': approveBnblBox,

            //bnpl
            'bnplFee': modelDetail.bnplFee === undefined || modelDetail.bnplFee === null || modelDetail.bnplFee === '' ? '...' : modelDetail.bnplFee.toString(),
            'bnplFeeHomeCredit': modelDetail.bnplFeeHomeCredit || '',
            'bnplFeeFundiin': modelDetail.bnplFeeFundiin || '',
            'bnplFeeAmigo': modelDetail.bnplFeeAmigo || '',
            'bnplFeeKredivo': modelDetail.bnplFeeKredivo || '',

            'noiDungThayDoi': modelDetail.noiDungThayDoi || '',
            'uyQuyen': modelDetail.uyQuyen || '',
            'khoanDamBaoSelection': modelDetail.khoanDamBaoSelection,
            'kyQuyType': modelDetail.kyQuyType,
            'inputKyQuyKhac': modelDetail.inputKyQuyKhac,
            'version': modelDetail.version,

            //BBTLv2
            'ngayThanhLy': modelDetail.ngayThanhLy ? this.datePipe.transform(modelDetail.ngayThanhLy, 'dd/MM/yyyy') : '',
            //PL15v2 phụ lục 3D
            'thoiGianApDung': modelDetail.thoiGianApDung ? this.datePipe.transform(modelDetail.thoiGianApDung, 'dd/MM/yyyy') : '',
          };
          return this.merchantService.exportDownloadSubContract(body).subscribe(response => {
            // this.global.downloadEmit.emit(true);
            if(response){
              const opt = {
                margins: {
                  top: 1132,
                  left: 1698,
                  right: 849,
                  bottom: 1132
                },
                orientation: 'portrait' as const,
                paperWidth: 11905.511,
                paperHeight: 16837.795,
              };
            var text = response.templateHtml;
            asBlob(text , opt).then(data => {
            saveAs(data, response.fileName+ '.docx') // save as docx file
          })
          }
          });
        }
      } else {
        this.toastr.warning('Hiện chưa có template cho văn bản này !', 'Cảnh báo');
      }
    }
  }
  handleApproveFeeForDomesticOrInternational(type: string, cardList = [], inputData: string): string {
    if (this.contractCode === 'PL-K') {
      return inputData? inputData.toString() : '';
    }
    var outputData = 'Không áp dụng';
    var checkExist = false;
    var checkExistCard = false;
    if (cardList.length > 0) {
      cardList.forEach(card => {
        if (card === 'Visa' || card === 'MasterCard' || card === 'JCB' || card === 'UnionPay') {
          checkExistCard = true;
        } else if (card === type) {
          checkExist = true;
        }
      });
    }
    if (checkExist && checkExistCard) {
      if (inputData && inputData !== '') {
        outputData = inputData + '%';
      } else {
        outputData = 'Không áp dụng';
      }
    }
    return outputData;
  }

  handleApproveFeeForAmerican(type: string, cardList = [], inputData: string): string {
    if (this.contractCode === 'PL-K') {
      return inputData? inputData.toString() : '';
    }
    var outputData = '';
    var checkExist = false;
    var checkExistEmerican = false;
    if (cardList.length > 0) {
      cardList.forEach(card => {
        if (card === 'AmericanExpress') {
          checkExistEmerican = true;
        } else if (card === type) {
          checkExist = true;
        }
      });
    }
    if (checkExist && checkExistEmerican) {
      if (inputData && inputData !== '') {
        outputData = inputData + '%';
      } else {
        outputData = 'Không áp dụng';
      }
    } else {
      outputData = 'Không áp dụng';
    }
    return outputData;
  }

  handleFeeForDomesticOrInternational(type01: string, type02: string, cardList = [], inputData: any, type: number): string {
    if (this.contractCode === 'PL-K') {
      return inputData? inputData.toString() : '';
    }
    var outputData = '';
    var checkExist = false;
    if (cardList.length > 0) {
      cardList.forEach(card => {
        if (type === 2) {
          if (card === 'Visa' || card === 'MasterCard' || card === 'JCB' || card === 'UnionPay' || card === 'AmericanExpress') {
            checkExist = true;
            if (inputData && inputData !== '') {
              outputData = inputData.toString();
            } else {
              outputData = 'Không áp dụng';
            }
          }
        } else {
          if (card === type01 || card === type02) {
            checkExist = true;
            if (inputData && inputData !== '') {
              if (type === 0) {
                outputData = inputData.toString();
              } else {
                outputData = inputData + '%';
              }
            } else {
              if (type === 0) {
                outputData = 'Không áp dụng';
              } else {
                outputData = 'Không áp dụng';
              }
            }
          }
        }
      });
      if (!checkExist) {
        outputData = 'Không áp dụng';
      }
    }
    return outputData;
  }

  handleContractOrder(contractCode: string): string {
    var order = '';
    if (contractCode === 'HD04' || contractCode === 'PL09') {
      order = '1';
    } else if (contractCode === 'HD08' || contractCode === 'PL02' || contractCode === 'PL02_v2') {
      order = '2';
    } else if (contractCode === 'HD03-01' || contractCode === 'PL14') {
      order = '3';
    } else if (contractCode === 'HD09' || contractCode === 'PL03') {
      order = '4';
    } else if (contractCode === 'HD05' || contractCode === 'PL04') {
      order = '5';
    } else if (contractCode === 'HD06' || contractCode === 'PL05') {
      order = '6';
    } else if (contractCode === 'PL07') {
      order = '7';
    } else if (contractCode === 'PL08') {
      order = '8';
    } else if (contractCode === 'PL06') {
      order = '9';
    } else if (contractCode === 'PL11') {
      order = '10';
    } else if (contractCode === 'PL12') {
      order = '11';
    } else if (contractCode === 'PL15') {
      order = '12';
    } else if (contractCode === 'PL15v2') {
      order = '12';
    } else if (contractCode === 'PL01') {
      order = '13';
    } else if (contractCode === 'BBNT') {
      order = '14';
    } else if (contractCode === 'BBTL') {
      order = '15';
    } else if (contractCode === 'PL16') {
      order = '16';
    } else if (contractCode === 'PLBNPL') {
      order = '17';
    } else {
      order = '18';

    }
    return order;
  }

  handleContractNumber(contractNumber: string, contractCode: string): string {
    if (contractCode === 'BBNT' || (contractCode === 'BBTL') || (contractCode === 'BBTT') || (contractCode === 'CV-Di')) {
      return '';
    } else {
      return contractNumber;
    }
  }

  exportContract() {
    console.log('exportContract');
    this.onSubmitContract('1');
  }

  exportContractHtml(bilingual: boolean) {
    console.log('exportContractHtml:'+this.idTemplate);
    if(this.idTemplate){  
      this.exportContractByTemplateDocx(bilingual);
    } else {
      this.contractService.exportContractHtml(this.contractId, this.contractCode, this.contractDetail.version, bilingual);
    }
  }

  exportContractByTemplateDocx(bilingual: boolean) {
    const params = new HttpParams()
      .set("id", this.contractId)
      .set("contractCode", this.contractCode)
      .set("version", this.contractDetail.version)
      .set("bilingual", bilingual || false)
      .set("emailExport", this.global.activeProfile.email);
    this.contractService.exportContractBytemplateDocx(params).subscribe( response => {
      if (response?.nerror+'' === '200') {
        this.global.downloadEmit.emit(true);
      } else if (response?.nerror+'' === '500') {
        this.toastr.error('Hiện chưa có template cho loại hợp đồng này', 'Error');
      } else {
        this.toastr.error('Lỗi trong quá trình xuất file contract. Liên hệ quản trị viên để biết thêm chi tiết.', 'Error');
      }
    });
  }

  /**
   * Export sub contract (PL-K)
   */
  exportSubContract() {
    this.onSubmitExportTemplateContractSub('1');
  }

  submitFormContract() {
    console.log('version: ' + this.contractDetail.version)
    if (this.contractCode === 'HD01') {
      if (this.contractHD01.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD02') {
      if (this.contractHD02.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD03-01') {
      if (this.contractHD03.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD04') {
      if (this.contractHD04.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD05') {
      if (this.contractHD05.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD06') {
      if (this.contractHD06.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD07') {
      if (this.contractHD07.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD08') {
      if (this.contractHD08.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD09') {
      if (this.contractHD09.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD10') {
      if (this.contractHD10.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD11') {
      if (this.contractHD11.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD12') {
      if (this.contractHD12.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD13' && this.version != 'v2' && this.version != 'v3' && this.version != 'v4' && this.version != 'v5') {
      if (this.contractHD13.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD13' && this.version === 'v2') {
      if (this.contractHD13V2.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD13' && this.version === 'v3') {
      if (this.contractHD13V3.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD13' && this.version === 'v4') {
      if (this.contractHD13V4.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD13' && this.version === 'v5') {
      if (this.contractHD13V5.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'HD14') {
      if (this.contractHD14.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL01') {
      if (this.subContractPL01.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL02') {
      if (this.subContractPL02.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    }else if (this.contractCode === 'PL02_v2') {
      if (this.subContractPl02v2.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL03') {
      if (this.subContractPL03.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL04') {
      if (this.subContractPL04.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL05') {
      if (this.subContractPL05.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL06') {
      if (this.subContractPL06.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL07') {
      if (this.subContractPL07.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL08') {
      if (this.subContractPL08.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL09') {
      if (this.subContractPL09.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL10') {
      if (this.subContractPL10.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL11') {
      if (this.subContractPL11.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL12') {
      if (this.subContractPL12.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL13') {
      if (this.subContractPL13.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL14') {
      if (this.subContractPL14.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL15') {
      if (this.subContractPL15.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL15v2') {
      if (this.subContractPL15v2.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PLBNPL') {
      if (this.subContractPLBNPL.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'BBNT') {
      if (this.subContractBBNT.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'BBTL') {
      if (this.subContractBBTL.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'BBTLv2') {
      if (this.subContractBBTLv2.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'BBTT') {
      if (this.subContractBBTT.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'CV-Den') {
      if (this.subContractCVDen.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'CV-Di') {
      if (this.subContractCVDi.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'VBUQ') {
      if (this.subContractVBUQ.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL16') {
      if (this.subContractPL16.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL-K') {
      if (this.subContractOther.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'PL-Kv2') {
      if (this.subContractOtherv2.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    } else if (this.contractCode === 'CV-DCHD') {
      if (this.subContractCVDCHD.validateForm() === true) {
        this.onSubmitContract('0');
      } else {
        this.toastr.warning('Đề nghị nhập đầy đủ thông tin ' + this.contractName, 'Cảnh báo');
      }
    }
  }

  goContractDetail(data: any) {
    if (this.isActive('new_contract') || this.isActive('approve_contract') || this.isActive('edit_contract')
      || this.isActive('remove_contract') || this.isActive('detail_contract')) {
      this.checkAddNewContract = false;
      this.checkAddNewSubContract = false;
      if (data.parentId !== undefined && data.parentId !== null && data.parentId !== 0) {
        this.merchantService.getContractDetail(data.parentId).subscribe(response => {
          if (response) {
            this.contractFullName.forEach(element => {
              if (element.contractCode === response.contract.contractCode) {
                this.parentContractFullName = element.contractName;
                this.parentContractNumber = response.contract.contractNumber;
                this.parentContractCode = response.contract.contractCode;
                this.parentSignatureDate = this.datePipe.transform(response.contract.signatureDate, 'dd/MM/yyyy');
                this.parentContractDetail = JSON.parse(response.contract.dataContractDetail);
                this.parentTableMerchant = this.parentContractDetail.subTableMerchant;
                this.parentTgTamUng = this.parentContractDetail.tgTamUng;
                this.parentPtTamUng = this.parentContractDetail.ptTamUng;
                this.parentStkGiaiKhoanh = this.parentContractDetail.stkGiaiKhoanh;
                this.parentCardTransactionFee = this.parentContractDetail.cardTransactionFee;
                this.parentFeeForCard = this.parentContractDetail.feeForCard;
                this.parentAccountBank = this.parentContractDetail.accountBank;
                this.parentContractCodePL = response.contract.contractCode;
              }
            });
          }
        });

        this.checkUpdateSubContract = true;
        this.checkUpdateContract = false;
      } else {
        this.checkUpdateSubContract = false;
        this.checkUpdateContract = true;
      }
      this.goDetail(data, 'DETAIL');
    }
  }

  cloneCopy(data: any, type: string) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Bạn có muốn tạo bản sao của ${data.contractName} không ?</div>`)
      .title('Lưu ý !')
      .yes('Có')
      .no('Không')
      .confirm().subscribe(ok => {
        if (ok) {
          this.checkUpdateSubContract = false;
          this.checkUpdateContract = false;
          this.checkEditContractDetail = true;
          this.contractService.setEditing(true);
          if (type === 'HD') {
            this.typeContract = true;
            this.typeSubContract = false;
            this.checkAddNewContract = true;
            this.checkAddNewSubContract = false;
          } else {
            this.typeContract = false;
            this.typeSubContract = true;
            this.checkAddNewContract = false;
            this.checkAddNewSubContract = true;
          }
          this.goDetail(data, type);
        }
      });
  }

  goDetail(data: any, type: string) {
    this.contractDetail = JSON.parse(data.dataContractDetail);
    this.version = (data.contractCode == 'HD13' && type != 'DETAIL') ? 'v5' : this.contractDetail.version;
    this.contractOriginal = new ContractOriginal;
    this.contractOriginal.contractType = data.contractType;
    if (type === 'DETAIL') {
      this.contractOriginal.id = data.contractId.toString();
      this.contractOriginal.state = data.stateContract;
      this.contractOriginal.idForm = data.idForm;
      this.contractOriginal.idTemplate = data.idTemplate;
    } else {
      this.contractOriginal.id = '0';
      this.contractOriginal.state = '';
    }

    this.contractOriginal.partnerId = data.partnerId.toString();
    this.contractOriginal.parentId = data.parentId.toString();
    this.contractOriginal.businessName = data.businessName;
    this.contractOriginal.contractName = data.contractName;
    this.contractOriginal.contractNumber = data.contractNumber;
    this.contractOriginal.userAction = data.userAction;
    this.contractOriginal.signatureDate = data.signatureDate;
    this.contractOriginal.rangeDate = data.rangeDate;
    this.contractOriginal.contractCode = data.contractCode;
    this.contractOriginal.representative = data.representative;
    this.contractType = data.contractType;

    this.parentId = this.contractOriginal.parentId;
    this.contractId = this.contractOriginal.id;
    this.contractName = this.contractOriginal.contractName;
    this.viewListContractDetail = true;
    this.contractCode = this.contractOriginal.contractCode;
    this.idForm = this.contractOriginal.idForm;
    this.idTemplate = this.contractOriginal.idTemplate;
    this.exportContractList.forEach(element => {
      if (element === this.contractCode) {
        this.checkExportContract = true;
      }
    });
    console.log('goDetail', ', type:', type, ', verion:', this.version, 'contractCode: ', data.contractCode, 'this.contractCode: ', this.contractCode);
  }


  deleteContract() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Bạn có muốn xóa ${this.contractName} không ?</div>`)
      .title('Lưu ý !')
      .yes('Có')
      .no('Không')
      .confirm().subscribe(ok => {
        if (ok) {
          const body = {
            'id': this.contractId
          };
          this.merchantService.deleteContract(body).subscribe(data => {
            if (data.n_result === '200') {
              this.toastr.success('Xoá ' + this.contractName + ' thành công', 'Thành công');
              this.backContractManagement();
            } else {
              this.toastr.error('Xoá ' + this.contractName + ' thất bại', 'Thất bại');
            }
          });
        }
      });


  }

  approveContract() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Bạn có muốn duyệt ${this.contractName} không ?</div>`)
      .title('Lưu ý !')
      .yes('Có')
      .no('Không')
      .confirm().subscribe(ok => {
        if (ok) {
          const body = {
            'id': this.contractId
          };
          this.merchantService.approveContractOriginal(body).subscribe(data => {
            if (data.n_result === '200') {
              this.toastr.success('Duyệt ' + this.contractName + ' thành công', 'Thành công');
              //insert log
              var body = {
                "idContract": this.contractId,
                "email": this.global.activeProfile.email,
                "action": "approve",
                "idFile": 0
              };
              this.contractService.insertContractHistory(body).subscribe(data => {
                if (data.nError === '200') {
                  console.log('insert log approve success');
                } else {
                  console.log('insert log approve failed'+ data.sError);
                }
              });
              this.backContractManagement();
            } else {
              this.toastr.error('Duyệt ' + this.contractName + ' thất bại', 'Thất bại');
            }
          });
        }
      });
  }

  removeApproveContract() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Bạn có muốn hủy duyệt ${this.contractName} không ?</div>`)
      .title('Lưu ý !')
      .yes('Có')
      .no('Không')
      .confirm().subscribe(ok => {
        if (ok) {
          const body = {
            'id': this.contractId,
            'email': this.global.activeProfile.email
          };
          this.merchantService.removeApproveContractOriginal(body).subscribe(data => {
            if (data.n_result === '200') {
              this.toastr.success('Hủy duyệt ' + this.contractName + ' thành công', 'Thành công');
              //insert log
              // var body = {
              //   "idContract": this.contractId,
              //   "email": this.global.activeProfile.email,
              //   "action": "remove_approve",
              //   "idFile": 0
              // };
              // this.contractService.insertContractHistory(body).subscribe(data => {
              //   if (data.n_result === '200') {
              //     console.log('insert log remove approve success');
              //   } else {
              //     console.log('insert log remove approve failed');
              //   }
              // });
              this.backContractManagement();
            } else if (data.n_result === '201') {
              this.toastr.warning('Hủy duyệt ' + this.contractName + ' thành công, Lưu log hủy duyệt thất bại', 'Cảnh báo');
              this.backContractManagement();
            } else {
              this.toastr.error('Hủy duyệt ' + this.contractName + ' thất bại', 'Thất bại');
            }
          });
        }
      });
  }

  backContractManagement() {
    this.checkEditContractDetail = false;
    this.contractService.setEditing(false);
    this.viewListContractDetail = false;
    this.subResetData();
    this.getContractsByPartnerId(this.currentPartnerId);
  }

  subResetData() {
    this.checkModal = false;
    this.checkAddNewContract = false;
    this.checkAddNewSubContract = false;
    this.checkUpdateContract = false;
    this.checkUpdateSubContract = false;
    this.checkEditContractDetail = false;
    this.contractService.setEditing(false);

    this.contractDetail = new ContractDetailModel;
    this.contractOriginal = new ContractOriginal;

    this.contractType = undefined;
    this.contractId = undefined;
    this.parentId = undefined;
    this.contractTemplate = undefined;
    this.subContractTemplate = undefined;
    this.contractCode = undefined;
    this.contractName = undefined;
    this.parentContractFullName = undefined;
    this.parentContractNumber = undefined;
    this.parentSignatureDate = undefined;
    this.parentTableMerchant = [];
    this.checkExportContract = false;
  }

  checkEditContract() {
    this.checkEditContractDetail = true;
    this.contractService.setEditing(true);
  }

  cancelEditContract() {
    if (this.contractId === undefined || this.contractId === null || this.contractId === '' || this.contractId === '0') {
      this.backContractManagement();
    } else {
      if (this.contractOriginalTemp !== undefined) {
        this.contractOriginal = this.contractOriginalTemp;
        this.contractDetail = this.contractDetailTemp;
      }
      this.checkEditContractDetail = false;
      this.contractService.setEditing(false);
    }
  }

  refreshFormContract() {
    this.contractDetailTemp = this.contractDetail;
    this.contractOriginalTemp = this.contractOriginal;

    this.contractDetail = new ContractDetailModel;
    this.contractOriginal = new ContractOriginal;
    this.contractOriginal.id = this.contractOriginalTemp.id;
    this.contractOriginal.parentId = this.contractOriginalTemp.parentId;
    this.contractOriginal.partnerId = this.contractOriginalTemp.partnerId;
    this.contractOriginal.contractCode = this.contractOriginalTemp.contractCode;
    this.contractOriginal.contractName = this.contractOriginalTemp.contractName;
    this.contractOriginal.contractType = this.contractOriginalTemp.contractType;
    this.contractOriginal.state = this.contractOriginalTemp.state;
  }

  deleteDataPause($event) {
    this.contractDetail.subTablePause.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTablePause.splice(index, 1);
      }
    });
  }

  deleteSubmerchant($event) {
    console.log(this.contractDetail);
    this.contractDetail.subTableMerchant.forEach((value, index) => {
      if (value.merchantId === $event.id) {
        this.contractDetail.subTableMerchant.splice(index, 1);
      }
    });
  }

  deleteSubmerchantShopify($event) {
    console.log(this.contractDetail);
    this.contractDetail.subTableShopifyMerchantID.forEach((value, index) => {
      if (value.merchantId === $event.id) {
        this.contractDetail.subTableShopifyMerchantID.splice(index, 1);
      }
    });
  }
  deleteSubmerchantNonShopify($event) {
    console.log(this.contractDetail);
    this.contractDetail.subTableNonShopifyMerchantID.forEach((value, index) => {
      if (value.merchantId === $event.id) {
        this.contractDetail.subTableNonShopifyMerchantID.splice(index, 1);
      }
    });
  }

  deleteTableData01($event) {
    this.contractDetail.subTableNoMerchant01.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant01.splice(index, 1);
      }
    });
  }

  deleteTableData001($event) {
    this.contractDetail.subTableNoMerchant001.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant001.splice(index, 1);
      }
    });
  }

  deleteTableData02($event) {
    this.contractDetail.subTableNoMerchant02.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant02.splice(index, 1);
      }
    });
  }

  deleteTableData002($event) {
    this.contractDetail.subTableNoMerchant002.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant002.splice(index, 1);
      }
    });
  }

  deleteTableData03($event) {
    this.contractDetail.subTableNoMerchant03.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant03.splice(index, 1);
      }
    });
  }

  deleteTableData003($event) {
    this.contractDetail.subTableNoMerchant003.forEach((value, index) => {
      if (value.dataId === $event.id) {
        this.contractDetail.subTableNoMerchant003.splice(index, 1);
      }
    });
  }

  checkViewListContract(): boolean {
    return this.isActive('list_contract');
  }

  isActive(functionName: string): boolean {
    return (this.currentPartner && this.currentPartner.isAssigned && this.global.isActive(functionName))
      || (this.currentPartner && !this.currentPartner.isAssigned && this.global.isActive('unassigned_' + functionName));
  }

  

  ref: DynamicDialogRef;
  reviewPdf() {
    this.ref = this.dialogService.open(ContractReviewComponent, {
        header: 'Review document',
        width: '1400px',
        contentStyle: { "max-height": "1100px", "width": "1400px" },
        baseZIndex: 100,
        data: {
            contractId: this.contractId,
        }
    });
    this.ref.onClose.subscribe((body: any) => {
        // if (body.N_ID != null && body.S_EMAIL != null) {
        //     this.notiService.updateEmailInGroup(body).subscribe(data => {
        //         if (data?.nerror+'' === '200') {
        //             this.toastr.success('Update successfully');
        //             this.searchEnter();
        //         } else {
        //             this.toastr.error('Update failed');
        //         }
        //     });
        // } else {
        //     this.toastr.warning('Error update email!');
        // }
    });
  }

   /**
    * Open dialog history
    * @param contractId
    * @returns open list history contract dialog
    */
  openDialogHistory() {
    this.ref = this.dialogService.open(ContractHistoryComponent, {
        header: 'History',
        width: '1000px',
        contentStyle: { "max-height": "500px", "overflow": "visible" },
        baseZIndex: 10000,
        data: {
            contractId: this.contractId,
        }
    });
    this.ref.onClose.subscribe((body: any) => {
        if (body.N_ID != null && body.S_EMAIL != null) {
            // this.notiService.updateEmailInGroup(body).subscribe(data => {
            //     if (data?.nerror+'' === '200') {
            //         this.toastr.success('Update successfully');
            //         this.searchEnter();
            //     } else {
            //         this.toastr.error('Update failed');
            //     }
            // });
        } else {
            this.toastr.warning('Error update email!');
        }
    });
  }

}
