import { Component, OnInit } from '@angular/core';
import { ContractService } from '../../contract.service';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastrService } from 'ngx-toastr';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { Globals } from '@core/global';
import { MerchantService } from '@service/merchant.service';

@Component({
  selector: 'app-contract-review',
  templateUrl: './contract-review.component.html',
  styleUrls: ['./contract-review.component.css']
})
export class ContractReviewComponent implements OnInit {
  pdfSrc: any;
  pdfSrc1: any;
  pdfSrc2: any;
  page = 1;
  contractId: any;
  scanFileMessage: string = '';
  contractFileMessage: string = '';
  isLoadingScan: boolean = false;
  isLoadingContract: boolean = false;
  isValidScanFile: boolean = false;
  isValidContractFile: boolean = false;
  stateContract: string = '';
  idFileScan: number = 0;
  contractNumber: string = '';
  
  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig,public contractService: ContractService,
    private confirmService: ConfirmService,
    private toastr: ToastrService,
    private global: Globals,
    private merchantService: MerchantService) { }

  ngOnInit(): void {
    this.reviewFilePdf();
  }

  
  /* review file pdf */
  reviewFilePdf() {
    this.contractId = this.config.data.contractId;
    this.scanFileMessage = '';
    this.contractFileMessage = '';
    
    // Load scan file
    this.isLoadingScan = true;
    this.contractService.downloadContractCompared(this.contractId, "scan").subscribe({
      next: (pdfBlob: any) => {
        this.isLoadingScan = false;
        if (pdfBlob && pdfBlob.size > 0) {
          if (pdfBlob.type === 'application/pdf') {
            const fileReader = new FileReader();
            fileReader.onload = () => {
              this.pdfSrc1 = fileReader.result;
            };
            fileReader.readAsArrayBuffer(pdfBlob);
            this.isValidScanFile = true;
          } else if (pdfBlob.type === 'application/json' || pdfBlob.type === 'text/plain') {
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const json = JSON.parse(reader.result as string);
                this.scanFileMessage = json.message || 'File not found';
              } catch (e) {
                this.scanFileMessage = 'File not found';
              }
            };
            reader.readAsText(pdfBlob);
          } else {
            this.scanFileMessage = 'File not found';
          }
        } else {
          this.scanFileMessage = 'File not found';
        }
      },
      error: (error) => {
        this.isLoadingScan = false;
        console.error('Error loading scan file:', error);
        if (error.status === 400) {
          this.scanFileMessage = 'File not found';
        } else if (error.status === 404) {
          this.scanFileMessage = 'File not found';
        } else {
          this.scanFileMessage = 'Error loading file';
        }
      }
    });

    // Load contract file
    this.isLoadingContract = true;
    this.contractService.downloadContractCompared(this.contractId, "contract").subscribe({
      next: (pdfBlob: any) => {
        this.isLoadingContract = false;
        if (pdfBlob && pdfBlob.size > 0) {
          if (pdfBlob.type === 'application/pdf') {
            const fileReader = new FileReader();
            fileReader.onload = () => {
              this.pdfSrc2 = fileReader.result;
              this.isValidContractFile = true;
            };
            fileReader.readAsArrayBuffer(pdfBlob);
          } else if (pdfBlob.type === 'application/json' || pdfBlob.type === 'text/plain') {
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const json = JSON.parse(reader.result as string);
                this.contractFileMessage = json.message || 'File not found';
              } catch (e) {
                this.contractFileMessage = 'File not found';
              }
            };
            reader.readAsText(pdfBlob);
          } else {
            this.contractFileMessage = 'File not found';
          }
        } else {
          this.contractFileMessage = 'File not found';
        }
      },
      error: (error) => {
        this.isLoadingContract = false;
        console.error('Error loading contract file:', error);
        if (error.status === 400) {
          this.contractFileMessage = 'File not found';
        } else if (error.status === 404) {
          this.contractFileMessage = 'File not found';
        } else {
          this.contractFileMessage = 'Error loading file';
        }
      }
    });

    // Get contract file scan information if found scan file and contract file
    this.contractService.getContractFileScanInformation(this.contractId).subscribe({
      next: (data: any) => {
        console.log('Contract file scan information:', data);
        if (data.nerror === '200') {
          this.stateContract = data.data.S_STATUS;
          this.idFileScan = data.data.N_ID;
          this.contractNumber = data.data.S_CONTRACT_NUMBER;
          console.log('stateContract:', this.stateContract);
        } 
      },
      error: (error) => {
          console.error('Error getting contract file scan information:', error);
        }
      });
  }

  previousPage() {
    if (this.page > 1) {
      this.page--;
    }
  }

  nextPage() {
    this.page++;
  }

  onApprove() {
    console.log('Approve clicked', this.contractId);
    // TODO: Gọi API approve ở đây
    this.confirmService.build()
      .message(`<div class='content-confirm'> Bạn có muốn duyệt ${this.contractNumber} không ?</div>`)
      .title('Lưu ý !')
      .yes('Có')
      .no('Không')
      .confirm().subscribe(ok => {
        if (ok) {
          const body = {
            'id': this.contractId
          };
          this.merchantService.approveContractOriginal(body).subscribe(data => {
            if (data.n_result === '200') {
              this.toastr.success('Duyệt ' + this.contractNumber + ' thành công', 'Thành công');
              //insert log
              var body = {
                "idContract": this.contractId,
                "email": this.global.activeProfile.email,
                "idFile": this.idFileScan,
                "action": "approve"
              };
              this.contractService.insertContractHistory(body).subscribe(data => {
                if (data.nError === '200') {
                  console.log('insert log approve success');
                } else {
                  console.log('insert log approve failed'+ data.sError);
                }
              });
            } else {
              this.toastr.error('Duyệt ' + this.contractNumber + ' thất bại', 'Thất bại');
            }
          });
        }
      });
  }

  onReject() {
    console.log('Reject clicked', this.contractId);
    // TODO: Gọi API reject ở đây
  }
}
