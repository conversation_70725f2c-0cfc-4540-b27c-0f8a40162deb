<!-- <div style="width:1000px;height:900px;">
    
    <pdf-viewer [src]="pdfSrc" [page]="1" zoom="0.8" [original-size]="false" [render-text]="true" style="display: block;width:400px;height:500px;object-fit: contain;"></pdf-viewer>
</div> -->

<div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 16px; gap: 8px;">
  <button (click)="onApprove()" style="background: #4caf50; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold;" *ngIf="stateContract === 'wait for approve' && isValidScanFile && isValidContractFile">Approve</button>
  <!-- <button (click)="onReject()" style="background: #f44336; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold;" *ngIf="isValidScanFile && isValidContractFile && stateContract === 'wait for approve'">Reject</button> -->
</div>

<div style="display: flex;">
  <!-- Left side -->
  <div style="flex: 1; padding: 10px;position: relative;">
    <div
      style="position: absolute; top: 43px; left: 10px; background-color: white; border: 1px solid #b1b1b1; padding: 5px;">
      Old version
    </div>
    <h5 style="padding-bottom:9px;">
      Scanned file 
      <span *ngIf="isLoadingScan">(Loading...)</span>
      <span *ngIf="scanFileMessage" style="color: red;">({{scanFileMessage}})</span>
    </h5>
    <div *ngIf="pdfSrc1 && !scanFileMessage">
      <pdf-viewer [src]="pdfSrc1" [page]="page" zoom="0.9" [original-size]="false" [render-text]="true"
        style="display: block; width: 100%; height: 800px; object-fit: contain;border: solid 1px #b1b1b1;padding-top:31px;"></pdf-viewer>
    </div>
    <div *ngIf="!pdfSrc1 && scanFileMessage && !isLoadingScan" 
         style="display: flex; align-items: center; justify-content: center; height: 800px; border: solid 1px #b1b1b1; background-color: #f5f5f5;">
      <span style="color: #666; font-size: 16px;">{{scanFileMessage}}</span>
    </div>
    <div *ngIf="isLoadingScan" 
         style="display: flex; align-items: center; justify-content: center; height: 800px; border: solid 1px #b1b1b1; background-color: #f5f5f5;">
      <span style="color: #666; font-size: 16px;">Loading...</span>
    </div>
  </div>

  <!-- Right side -->
  <div style="flex: 1; padding: 10px; position: relative;">
    <div
      style="position: absolute; top: 42px; left: 10px; background-color: white; border: 1px solid #b1b1b1; padding: 5px;">
      New version
    </div>
    <div style="display: flex; justify-content: right; align-items: center;">
      <span style="margin-right:10px;">
        Contract file
        <span *ngIf="isLoadingContract">(Loading...)</span>
        <span *ngIf="contractFileMessage" style="color: red;">({{contractFileMessage}})</span>
      </span>
      <span style="margin-right:10px;">Page {{page}}</span>
      <button (click)="previousPage()">Previous Page</button>
      <button (click)="nextPage()">Next Page</button>
    </div>
    <div *ngIf="pdfSrc2 && !contractFileMessage">
      <pdf-viewer [src]="pdfSrc2" [page]="page" zoom="0.9" [original-size]="false" [render-text]="true"
        style="display: block; width: 100%; height: 800px; object-fit: contain;border: solid 1px #b1b1b1;padding-top:31px;"></pdf-viewer>
    </div>
    <div *ngIf="!pdfSrc2 && contractFileMessage && !isLoadingContract" 
         style="display: flex; align-items: center; justify-content: center; height: 800px; border: solid 1px #b1b1b1; background-color: #f5f5f5;">
      <span style="color: #666; font-size: 16px;">{{contractFileMessage}}</span>
    </div>
    <div *ngIf="isLoadingContract" 
         style="display: flex; align-items: center; justify-content: center; height: 800px; border: solid 1px #b1b1b1; background-color: #f5f5f5;">
      <span style="color: #666; font-size: 16px;">Loading...</span>
    </div>
  </div>
</div>