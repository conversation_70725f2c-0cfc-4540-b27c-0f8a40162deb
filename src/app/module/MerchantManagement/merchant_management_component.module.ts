import { NgModule }       from '@angular/core';
import { CommonModule }   from '@angular/common';
import { MerchantManagementComponent } from './merchantManagement/merchant_management_component';
import { MerchantManagementMobileComponent } from './merchantManagement/merchantManagementMobile/merchant_management_mobile_component';
import { TabManagementComponent } from './merchantManagement/tab/tab_management_component';
import { EmailManagementComponent } from './merchantChildRouter/emailManagement/email_management_component';
import { ResultantManagementComponent } from './merchantChildRouter/resultantManagement/resultant_management_component';

// decentralization
import { DecentralizationManagementComponent } from './merchantChildRouter/decentralizationManagement/decentralization_management_component';
import { PermissionTabComponent } from './merchantChildRouter/decentralizationManagement/permissionTab/permission_tab.component';
import { PartnerTabComponent } from './merchantChildRouter/decentralizationManagement/partnerTab/partner_tab.component';
import { SaleTabComponent } from './merchantChildRouter/decentralizationManagement/saleTab/sale_tab.component';
import { ImportPartnerFileComponent } from './merchantChildRouter/decentralizationManagement/partnerTab/importPartnerFile/import_partner_file.component';
import { SearchUserComponent } from './merchantChildRouter/decentralizationManagement/searchUser/search_user.component';
// import { CreateEmailComponent } from '@module/MerchantManagement/merchantChildRouter/emailManagement/createEmail/create_email_component';
// import { PanelLeftComponent } from '@module/MerchantManagement/merchantManagement/panelLeft/panel_left_component';

// pipe
import { CheckSubscribePipe } from './pipes/check-subscribe.pipe';

import { MerchantManagementRoutingModule } from './merchant_management-routing.module';
import {MessageService} from 'primeng/api';
// tab
import { MerchantInfoComponent } from '@module/MerchantManagement/merchantManagement/tab/merchantInfo/merchant_info_component';
import { CardTypeQRDialogComponent, MerchantIdComponent, CardDirectDebitDialogComponent } from '@module/MerchantManagement/merchantManagement/tab/merchantid/merchant_id_component';
import { CardBNPLDialogComponent } from '@module/MerchantManagement/merchantManagement/tab/merchantid/merchant_id_component';

import { ContractManagementComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/contract_component';

import { AppChildFormMerchantComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-merchant/app-child-form-merchant.component';
import { AppChildFormMerchantIdComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-merchant-id/app-child-form-merchant-id.component';
import { AppChildFormMerchantIdV3Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-merchant-id-v3/app-child-form-merchant-id-v3.component';
import { AppChildFormShopifyMerchantIdComponent } from './merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-shopify-merchant-id/app-child-form-shopify-merchant-id.component';

import { AppChildFormPauseComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-pause/app-child-form-pause.component';
import { AppChildTablePauseComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-pause/app-child-table-pause.component';
import { AppChildFormNoMerchantComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-no-merchant/app-child-form-no-merchant.component';
import { AppChildFormInstallmentComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-installment/app-child-form-installment.component';
import { AppChildTableInstallmentComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-installment/app-child-table-installment.component';
import { AppChildTableMerchantComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-merchant/app-child-table-merchant.component';
import { AppChildTableMerchantIdComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-merchant-id/app-child-table-merchant-id.component';
import { AppChildTableMerchantIdV3Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-merchant-id-v3/app-child-table-merchant-id-v3.component';
import { AppChildTableBbntComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-bbnt/app-child-table-bbnt.component';
import { AppChildTableShopifyMerchantIdComponent } from './merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-shopify-merchant-id/app-child-table-shopify-merchant-id.component';
import { AppChildTableNoMerchant01Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-01/app-child-table-no-merchant-01.component';
import { AppChildTableNoMerchant001Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-001/app-child-table-no-merchant-001.component';
import { AppChildTableNoMerchant02Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-02/app-child-table-no-merchant-02.component';
import { AppChildTableNoMerchant002Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-002/app-child-table-no-merchant-002.component';
import { AppChildTableNoMerchant03Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-03/app-child-table-no-merchant-03.component';
import { AppChildTableNoMerchant003Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-no-merchant/app-child-table-no-merchant-003/app-child-table-no-merchant-003.component';

import { AppContractHD01Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd01/app-contract-hd01.component';
import { AppContractHD02Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd02/app-contract-hd02.component';
import { AppContractHD03Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd03/app-contract-hd03.component';
import { AppContractHD04Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd04/app-contract-hd04.component';
import { AppContractHD05Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd05/app-contract-hd05.component';
import { AppContractHD06Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd06/app-contract-hd06.component';
import { AppContractHD07Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd07/app-contract-hd07.component';
import { AppContractHD08Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd08/app-contract-hd08.component';
import { AppContractHD09Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd09/app-contract-hd09.component';
import { AppContractHD10Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd10/app-contract-hd10.component';
import { AppContractHD11Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd11/app-contract-hd11.component';
import { AppContractHD12Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd12/app-contract-hd12.component';
import { AppContractHD13Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd13/app-contract-hd13.component';
import { AppContractHD13V2Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd13-v2/app-contract-hd13-v2.component';
import { AppContractHD13V3Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd13-v3/app-contract-hd13-v3.component';
import { AppContractHD13V4Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd13-v4/app-contract-hd13-v4.component';
import { AppContractHD13V5Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd13-v5/app-contract-hd13-v5.component';
import { AppContractHD14Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-contract-template/app-contract-hd14/app-contract-hd14.component';

import { AppSubContractPL01Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl01/app-sub-contract-pl01.component';
import { AppSubContractPL02Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl02/app-sub-contract-pl02.component';
import { AppSubContractPL03Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl03/app-sub-contract-pl03.component';
import { AppSubContractPL04Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl04/app-sub-contract-pl04.component';
import { AppSubContractPL05Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl05/app-sub-contract-pl05.component';
import { AppSubContractPL06Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl06/app-sub-contract-pl06.component';
import { AppSubContractPL07Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl07/app-sub-contract-pl07.component';
import { AppSubContractPL08Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl08/app-sub-contract-pl08.component';
import { AppSubContractPL09Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl09/app-sub-contract-pl09.component';
import { AppSubContractPL10Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl10/app-sub-contract-pl10.component';
import { AppSubContractPL11Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl11/app-sub-contract-pl11.component';
import { AppSubContractPL12Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl12/app-sub-contract-pl12.component';
import { AppSubContractPL13Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl13/app-sub-contract-pl13.component';
import { AppSubContractPL14Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl14/app-sub-contract-pl14.component';
import { AppSubContractPL15Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl15/app-sub-contract-pl15.component';
import { AppSubContractPL15v2Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl15v2/app-sub-contract-pl15v2.component';
import { AppSubContractPLBNPLComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-plBNPL/app-sub-contract-plBNPL.component';
import { AppSubContractBBNTComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-BBNT/app-sub-contract-BBNT.component';
import { AppSubContractBBTLComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-BBTL/app-sub-contract-BBTL.component';
import { AppSubContractBBTTComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-BBTT/app-sub-contract-BBTT.component';
import { AppSubContractCVDenComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-cv-den/app-sub-contract-cv-den.component';
import { AppSubContractCVDiComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-cv-di/app-sub-contract-cv-di.component';
import { AppSubContractVBUQComponent } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-VBUQ/app-sub-contract-VBUQ.component';
import { AppSubContractPL16Component } from '@module/MerchantManagement/merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl16/app-sub-contract-pl16.component';
import { AppSubContractOtherComponent } from './merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-other/app-sub-contract-other.component';

import { ContractInformationComponent } from '@module/MerchantManagement/merchantManagement/tab/contract-old/contractInformation/contract_information_component';
import { ContractManagementOldComponent } from '@module/MerchantManagement/merchantManagement/tab/contract-old/contract-component-old';
import { SalesComponent } from '@module/MerchantManagement/merchantManagement/tab/sales/sales_component';
import { CardDialogComponent } from '@module/MerchantManagement/merchantManagement/tab/contract-old/contractInformation/contract_information_component';
import { CardTypeDialogComponent } from '@module/MerchantManagement/merchantManagement/tab/merchantid/merchant_id_component';
import { AdvanceSearchComponent } from '@module/MerchantManagement/merchantManagement/merchant_management_component';
import { CreateEmailComponent } from '@module/MerchantManagement/merchantChildRouter/emailManagement/createEmail/create_email_component';
import { AcceptanceComponent } from '@module/MerchantManagement/merchantManagement/tab/acceptance/acceptance_component';
import { OnsiteComponent } from '@module/MerchantManagement/merchantManagement/tab/onsite/onsite_component';
import { MyFilterPipe } from '@module/MerchantManagement/merchantManagement/tab/onsite/onsite_component';
import { MerchantManagementFooterComponent } from './merchantManagement/footer/app-footercomponent';
import { DataService } from '@module/MerchantManagement/merchantManagement/tab/data.service';
import { ContractService } from './merchantManagement/tab/contract.service';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ToastrModule } from 'ngx-toastr';
// angular meterial
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
// Primeng
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { CheckboxModule } from 'primeng/checkbox';
import { PaginatorModule } from 'primeng/paginator';
import { TabViewModule } from 'primeng/tabview';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { FileUploadModule } from 'primeng/fileupload';
import { EditorModule } from 'primeng/editor';
import {RadioButtonModule} from 'primeng/radiobutton';
import {InputTextareaModule} from 'primeng/inputtextarea';
import {TooltipModule} from 'primeng/tooltip';
import { PickListModule } from "primeng/picklist";
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { UpdatedDatePipe } from './pipes/updated-date.pipe';
import { ContactPositionPipe } from './pipes/contact-position.pipe';
import { OldContractTypePipe } from './pipes/old-contract-type.pipe';
import { MetadataGatePipe } from './pipes/metadata-gate.pipe';
import { AcceptanceTypePipe } from './pipes/acceptance-type.pipe';
import { AppChildFormBbntComponent } from './merchantManagement/tab/contract/app-list-template/app-child-form/app-child-form-bbnt/app-child-form-bbnt.component';
import { AppChildTableNonShopifyMerchantIdComponent } from './merchantManagement/tab/contract/app-list-template/app-child-table/app-child-table-non-shopify-merchant-id/app-child-table-non-shopify-merchant-id.component';
import { FgPhamViHopTacComponent } from './merchantManagement/tab/contract/form-group/fg-pham-vi-hop-tac/fg-pham-vi-hop-tac.component';
import { FgTaiKhoanBaoCoComponent } from './merchantManagement/tab/contract/form-group/fg-tai-khoan-bao-co/fg-tai-khoan-bao-co.component';
import { FgThoiGianTamUngComponent } from './merchantManagement/tab/contract/form-group/fg-thoi-gian-tam-ung/fg-thoi-gian-tam-ung.component';
import { FgPhiDichVuComponent } from './merchantManagement/tab/contract/form-group/fg-phi-dich-vu/fg-phi-dich-vu.component';
import { FgHinhThucThuPhiComponent } from './merchantManagement/tab/contract/form-group/fg-hinh-thuc-thu-phi/fg-hinh-thuc-thu-phi.component';

import { EditTemplateComponent } from './merchantManagement/tab/contract/edit-gd-hd/edit-gd-hd.component';
import { DialogService } from 'primeng/dynamicdialog';
import { SharedModule } from '@shared/shared.module';
import { FgKhoanDamBaoKnttComponent } from './merchantManagement/tab/contract/form-group/fg-khoan-dam-bao-kntt/fg-khoan-dam-bao-kntt.component';
import { FgThongTinKhacComponent } from './merchantManagement/tab/contract/form-group/fg-thong-tin-khac/fg-thong-tin-khac.component';
import { FgNgayApDungComponent } from './merchantManagement/tab/contract/form-group/fg-ngay-ap-dung/fg-ngay-ap-dung.component';
import { FgContractOriginalOnepayEcomComponent } from './merchantManagement/tab/contract/form-group/fg-contract-original-onepay-ecom/fg-contract-original-onepay-ecom.component';
import { FirstUppercasePipe } from './pipes/FirstUppercase.pipe';
import { CKEditorModule } from "@ckeditor/ckeditor5-angular";
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { ContractReviewComponent } from './merchantManagement/tab/contract/contract-review/contract-review.component';
import { ContractHistoryComponent } from './merchantManagement/tab/contract/contract-history/contract-history.component';
import { AppSubContractCvDchdComponent } from './merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-cv-dchd/app-sub-contract-cv-dchd.component';
import { AppSubContractPl02v2Component } from './merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-pl02v2/app-sub-contract-pl02v2.component';
import { AppSubContractOtherv2Component } from './merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-otherv2/app-sub-contract-otherv2.component';
import { AppSubContractBBTLv2Component } from './merchantManagement/tab/contract/app-list-template/app-list-sub-contract-template/app-sub-contract-BBTLv2/app-sub-contract-BBTLv2.component';

@NgModule({
  imports: [
    CommonModule,
    MerchantManagementRoutingModule,
    // Material
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSelectModule,
    MatButtonModule,
    MatCheckboxModule,
    MatIconModule,
    MatChipsModule,
    InputNumberModule,
    MatRadioModule,
    MatPaginatorModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatAutocompleteModule,
    // Primeng
    DropdownModule,
    CalendarModule,
    OverlayPanelModule,
    TableModule,
    MultiSelectModule,
    InputTextModule,
    CheckboxModule,
    TabViewModule,
    PaginatorModule,
    FileUploadModule,
    FormsModule,
    ReactiveFormsModule,
    EditorModule,
    RadioButtonModule,
    InputTextareaModule,
    TooltipModule,
    PickListModule,
    DialogModule,
    SharedModule,
    ToastrModule.forRoot({
      timeOut: 5000,
      positionClass: 'toast-top-right',
      preventDuplicates: true,
    }),
    CKEditorModule,
    PdfViewerModule
  ],
  declarations: [
    EditTemplateComponent,
    MerchantManagementComponent,
    MerchantManagementMobileComponent,
    EmailManagementComponent,
    ResultantManagementComponent,
    // Decentralization management
    DecentralizationManagementComponent,
    PermissionTabComponent,
    PartnerTabComponent,
    SaleTabComponent,
    ImportPartnerFileComponent,
    SearchUserComponent,
    CheckSubscribePipe,
    // PanelLeftComponent,
    TabManagementComponent,
    // merchant management
    MerchantInfoComponent,
    MerchantIdComponent,
    AcceptanceComponent,
    OnsiteComponent,
    ContractInformationComponent,
    ContractManagementOldComponent,
    CardDialogComponent,
    CardTypeDialogComponent,
    CardTypeQRDialogComponent,
    CardBNPLDialogComponent,
    AdvanceSearchComponent,
    CreateEmailComponent,
    MerchantManagementFooterComponent,
    SalesComponent,
    MyFilterPipe,
    UpdatedDatePipe,
    ContactPositionPipe,
    FirstUppercasePipe,

    ContractManagementComponent,
    AppChildFormMerchantComponent,
    AppChildFormMerchantIdComponent,
    AppChildFormMerchantIdV3Component,
    AppChildFormShopifyMerchantIdComponent,
    AppChildFormInstallmentComponent,
    AppChildTableInstallmentComponent,
    AppChildTableMerchantComponent,
    AppChildTableBbntComponent,
    AppChildTableMerchantIdComponent,
    AppChildTableMerchantIdV3Component,
    AppChildTableShopifyMerchantIdComponent,
    AppChildTableNonShopifyMerchantIdComponent,
    AppChildFormNoMerchantComponent,
    AppChildTableNoMerchant01Component,
    AppChildTableNoMerchant001Component,
    AppChildTableNoMerchant02Component,
    AppChildTableNoMerchant002Component,
    AppChildTableNoMerchant03Component,
    AppChildTableNoMerchant003Component,
    AppChildFormPauseComponent,
    AppChildTablePauseComponent,
    AppChildFormBbntComponent,

    AppContractHD01Component,
    AppContractHD02Component,
    AppContractHD03Component,
    AppContractHD04Component,
    AppContractHD05Component,
    AppContractHD06Component,
    AppContractHD07Component,
    AppContractHD08Component,
    AppContractHD09Component,
    AppContractHD10Component,
    AppContractHD11Component,
    AppContractHD12Component,
    AppContractHD13Component,
    AppContractHD13V2Component,
    AppContractHD13V3Component,
    AppContractHD13V4Component,
    AppContractHD13V5Component,
    AppContractHD14Component,

    AppSubContractPL01Component,
    AppSubContractPL02Component,
    AppSubContractPL03Component,
    AppSubContractPL04Component,
    AppSubContractPL05Component,
    AppSubContractPL06Component,
    AppSubContractPL07Component,
    AppSubContractPL08Component,
    AppSubContractPL09Component,
    AppSubContractPL10Component,
    AppSubContractPL11Component,
    AppSubContractPL12Component,
    AppSubContractPL13Component,
    AppSubContractPL14Component,
    AppSubContractPL15Component,
    AppSubContractPL15v2Component,
    AppSubContractBBNTComponent,
    AppSubContractBBTLComponent,
    AppSubContractBBTTComponent,
    AppSubContractCVDenComponent,
    AppSubContractCVDiComponent,
    AppSubContractVBUQComponent,
    AppSubContractPLBNPLComponent,
    AppSubContractPL16Component,
    AppSubContractOtherComponent,
    OldContractTypePipe,
    MetadataGatePipe,
    AcceptanceTypePipe,
    FgPhamViHopTacComponent,
    FgTaiKhoanBaoCoComponent,
    FgThoiGianTamUngComponent,
    FgPhiDichVuComponent,
    FgHinhThucThuPhiComponent,
    FgKhoanDamBaoKnttComponent,
    FgThongTinKhacComponent,
    FgNgayApDungComponent,
    FgContractOriginalOnepayEcomComponent,
    ContractReviewComponent,
    CardDirectDebitDialogComponent
    ,
    ContractHistoryComponent,
    AppSubContractCvDchdComponent,
    AppSubContractPl02v2Component,
    AppSubContractOtherv2Component,
    AppSubContractBBTLv2Component
  ],
  providers: [MatDatepickerModule, DataService, DialogService, ContractService,
    { provide: MAT_DIALOG_DATA, useValue: {} },
    { provide: MatDialogRef, useValue: {} }],
  entryComponents: [CardDialogComponent, AdvanceSearchComponent, CardTypeDialogComponent, CardTypeQRDialogComponent,CardDirectDebitDialogComponent, CardBNPLDialogComponent]
})
export class MerchantManagementModule {}
