import { Component, OnInit, Input, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router, RouterEvent } from '@angular/router';
import { Globals } from '@core/global';
import { ToastrService } from 'ngx-toastr';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subscription } from 'rxjs';
// Add Acquire Rull
import { AcquirerRuleService } from '@service/acquirer-rule.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { Location } from '@angular/common';
import { DatePipe, DecimalPipe } from '@angular/common';
import { SwitchScript } from 'app/model/switch-script.model';
import { HttpParams } from '@angular/common/http';
import { HotSwitchCyberMpgsService } from '@service/hot-switch-cyber-mpgs.service';
import _ from 'lodash';
import { PaymentOPFeeService } from '@service/payment_reconciliation/payment2-op-fee.service';
import { percentageNotDivide } from '@shared/utils/utils';
import { element } from 'protractor';

@Component({
  selector: 'app-acq-rule-group',
  templateUrl: './acq-rule-group-refresh.component.html',
  styleUrls: ['./acq-rule-group-refresh.component.css'],
  providers: [DialogService, PaymentOPFeeService],
})
export class AcqRuleGroupRefreshComponent implements OnInit {
  public ruleGroup = {
    id: null,
    approvalId: null,
    name: "",
    type: "Default",
    description: "",
    merchantIds: [],
    merchantIdsText: "",
    rules: [],
    isEditable: true,
    isReview: false,
    backUps: [],
    listCases: [],
    case: null,
    isBackUpDefault: null,
    approval: true,
    status: "new",
    enable: true,
    mcc: "",
  };
  // public hotSwitchService: HotSwitchCyberMpgsService;
  public listGroupAcqBackUp = [];
  isAllMerchant = false;

  public loading: boolean;
  public keyword = '';
  public page = 0;
  public first = 0;

  public typeOpt = {
    label: 'Type', type: 'dropdown',
    options: [
      { label: 'Default', value: 'Default' },
    ]
  };
  public deleteMerchantId = "";
  control = new FormControl();
  listMerchantIds = [];
  filteredMerchantIds = [];
  // public filteredMerchant = ["g356y54y5tyv3554t", "fgt4ghytrghtrhgyju"]
  public resultsLength: number;
  public data: Array<any>;
  public pageSize: string;
  public sub: Subscription;
  public merchantSearchText: "";
  public name1: "";
  public deleteRuleIdx: -1;
  public listFullMerchant = [];
  public mapListFullMerchant = {};
  listAllBackUp = [];
  // config back up
  public listCases = [{ label: '1', value: 1 }, { label: '2', value: 2 }, { label: '3', value: 3 }, { label: '4', value: 4 }];
  public mapCases = {}
  public currentCase = 1
  public listConfigCases = {}
  public option = [{ label: 'No Switching', value: '0' }, { label: 'New', value: 'New' }, { label: 'Select', value: 'Select' }]
  public backUpType = [{ label: 'Default', value: true }, { label: 'Exception', value: false }]
  switchScript: SwitchScript = {
    id: 0,
    script: '',
    createDate: '',
    desc: ''
  };
  public switchScripts: Array<any>
  ///////////

  // public ruleGroupBackUp = {
  //   option: [{ label: '', value: '' }, { label: 'New', value: 'New' }, { label: 'Select', value: 'Select' }],
  //   backUpType: [{ label: 'Default', value: 'Default' }, { label: 'Exception', value: 'Exception' }],
  //   case: [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }, { label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
  //   groupName: "",
  //   description: "",
  //   merchantIds: [],
  //   rules: [],
  //   groupBackUpExist: []
  // }
  //

  issuers = [];
  binGroup = [];
  acquirers = [];
  acquirersView = {};
  sources = [{ 'name': 'Direct', 'value': 'Direct' }, { 'name': 'ApplePay', 'value': 'ApplePay' }, { 'name': 'SamsungPay', 'value': 'SamsungPay' }, { 'name': 'GooglePay', 'value': 'GooglePay' }]
  binCountry = [{ 'name': '', 'value': '' }, { 'name': 'VN', 'value': 'VN' }, { 'name': 'FOREIGN', 'value': 'FOREIGN' }]
  bankIdType = [{ 'name': '2B', 'value': '2B' }]
  bankMerchantId = [];
  listBankMid: any;
  sSource: any
  sAcq: any;
  sBankIdType: any;
  sBinCountry: any;
  sIssuer: any;
  sBinGroup: any;
  sDescription: any;
  sBin: any;
  sBankMerchantId: any;
  sId: any;
  isCheckVisa: any;
  isCheckMaster: any;
  isCheckJcb: any;
  isCheckAmex: any;
  tempRules = [];
  idEdit: any = '';
  isCreateBackUp: any;
  idBackUpAddRule: any;
  tmpSelectedMerchants = [];
  listMCC = [];
  listMerchantFeeInfoData = [];
  isShowingSuggest: boolean = false;
  isEnabledButtonSuggest: boolean = false;
  percentageNotDivide = percentageNotDivide;

  constructor(
    public dialogService: DialogService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    public global: Globals,
    private _location: Location,
    //Add Acquirer Rule
    public hotSwitchService: HotSwitchCyberMpgsService,
    public acquirerRuleService: AcquirerRuleService,
    private confirmService: ConfirmService,
    public datepipe: DatePipe,
    private paymentOPFeeService: PaymentOPFeeService,
  ) {
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    let result = confirm("Changes you made may not be saved.");
    if (result) {
      // Do more processing...
    }
    event.returnValue = false; // stay on same page
  }

  ngOnInit(): void {
    this.ruleGroup.id = this.route.snapshot.paramMap.get("id") ?? null;
    this.ruleGroup.approvalId = this.route.snapshot.paramMap.get("approvalId") ?? null;
    this.sub = this.route
      .queryParams
      .subscribe(params => {
        this.init(params);
      });
    this.getListMerchantID();
    this.ruleGroup.merchantIds = [];
    this.getAllInfoConfigRule();

    this.getListCase();
    // this.onInitCase();
  }
  getListCase() {
    // get list cases
    let request = new HttpParams()
    this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
      if (data && data?.list && data.list.length > 0) {
        this.listCases = data.list.map((c) => {
          return {
            label: c.script ?? "",
            value: c.id
          }
        })
        this.listCases.forEach((e) => {
          this.mapCases[e.value] = e.label
        })
        if (this.listCases.length == 1) { this.onInitCase() };
      }
    })
  }
  onInitCase() {
    this.listConfigCases[this.currentCase] = {
      caseId: this.currentCase,
      caseName: this.mapCases[this.currentCase] ?? "",
      isDisable: false,
      updatedDate: this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss'),
      status: "new",
      approval: true,
      listBackUps: [
      ]

    }
  }
  onAddBackUp() {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
    // check default existed
    let existedDefault = null
    if (this.listConfigCases[this.currentCase].listBackUps && this.listConfigCases[this.currentCase].listBackUps.length > 0) {
      existedDefault = this.listConfigCases[this.currentCase].listBackUps.find((e) => {
        return e.isBackUpDefault
      })
    }
    let backUpOptions = !existedDefault || (existedDefault && existedDefault.option != '0') ? this.option : this.option.filter((e) => { return e.value != '0' })
    let backUpTypes = !existedDefault ? this.backUpType : this.backUpType.filter((e) => { return e.value == false })
    let tmpId = this.makeUUID()
    // get current case config
    this.listConfigCases[this.currentCase].listBackUps.push({
      tmpId: tmpId,
      option: existedDefault ? 'New' : '0',
      id: existedDefault ? null : -1,
      name: existedDefault ? this.genBackUpName(this.currentCase) : '',
      type: "",
      description: "",
      merchantIds: [],
      merchantIdsText: "",
      tmpSelectedMerchants: [],
      rules: existedDefault ? JSON.parse(JSON.stringify(existedDefault.rules)) : [],
      backUps: null,
      case: this.currentCase,
      isBackUpDefault: existedDefault ? false : true,
      isAllMerchant: false,
      listMerchantIds: [],
      selectedBackUp: null,
      listBackUpType: backUpTypes,
      listOption: backUpOptions,
      status: "new",
      approval: true,
      enable: true,
    })
    let idx = this.listConfigCases[this.currentCase].listBackUps.length - 1
    this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds)

  }

  setDefaultBackUpValue() {

  }
  merchantIdsDialog(data: any) {
    // this.merchantIdsData = data
    // this.visibleListMerchantIds = true
  }
  onCheckAllMerchant() {
    if (this.isAllMerchant) {
      this.listMerchantIds = _.sortBy(this.listFullMerchant, ['acqGroupId', 'name']);
    } else {
      this.listMerchantIds = this.listFullMerchant.filter(merchant => !merchant.acqGroupId)
    }

  }
  onDisableCase(idx) {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

    this.ruleGroup.listCases[idx].isDisable = true;
  }
  onEnableCase(idx) {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

    this.ruleGroup.listCases[idx].isDisable = false;
  }
  onDeleteCase(idx) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to delete this case ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && idx >= 0) {
          this.ruleGroup.listCases.splice(idx, 1)

        }
      })
  }
  onChangeBackUpName() {
    // reload select option dropdown
    //get list

    this.listAllBackUp = this.getListAllBackUp()
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

  }

  getAvailableMerchants(selectedMerchants) {

    //get other selected
    let otherSelectedMerchantIds = []
    this.listConfigCases[this.currentCase]?.listBackUps.forEach((e) => {
      if (e.merchantIds && e.merchantIds.length > 0) {
        let filteredSelectedMerchant = e.merchantIds
        if (selectedMerchants && selectedMerchants.length > 0) {
          filteredSelectedMerchant = e.merchantIds.filter(x => !selectedMerchants.includes(x));
        }
        otherSelectedMerchantIds.push(...filteredSelectedMerchant);


      }

    })

    return this.ruleGroup.merchantIds.filter(x => !otherSelectedMerchantIds.includes(x)).map((e) => {
      return {
        name: e,
        value: e
      }
    })
  }

  getAvailableSelectedMerchant(selectedMerchants) {
    if (selectedMerchants && selectedMerchants.length > 0) {
      return selectedMerchants.filter((e) => {
        return this.ruleGroup.merchantIds.includes(e)
      })
    } else {
      return selectedMerchants;
    }
  }
  onChangeMerchantSelect(isUpdateListSelectedMerchant) {
    for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
      this.listConfigCases[this.currentCase].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)

      if (isUpdateListSelectedMerchant) this.listConfigCases[this.currentCase].listBackUps[i].merchantIds = this.getAvailableSelectedMerchant(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)
    }
  }

  resetAllMerchantOption() {
    for (let item in this.listConfigCases) {
      for (let i = 0; i < this.listConfigCases[item].listBackUps.length; i++) {
        this.listConfigCases[item].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[item].listBackUps[i].merchantIds)
      }
    }

  }
  onChangeBackUpOption(idx) {

    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
    // if (this.listConfigCases[this.currentCase].listBackUps[idx].option
    switch (this.listConfigCases[this.currentCase].listBackUps[idx].option) {
      case '0':
        this.listConfigCases[this.currentCase].listBackUps[idx] = {
          tmpId: this.listConfigCases[this.currentCase].listBackUps[idx].tmpId,
          option: '0',
          id: -1,
          name: "",
          type: "",
          description: "",
          merchantIds: [],
          tmpSelectedMerchants: [],
          merchantIdsText: "",
          rules: [],
          backUps: null,
          case: this.currentCase,
          isBackUpDefault: true,
          isAllMerchant: false,
          listMerchantIds: [],
          selectedBackUp: null,
          listOption: this.option,
          listBackUpType: [],
        }
        // reset other backup to exp
        for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
          if (i != idx) {

            // reset select option of selected back up
            if (this.listConfigCases[this.currentCase].listBackUps[i].option == 'Select' && this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp == this.listConfigCases[this.currentCase].listBackUps[idx].tmpId) {
              this.listConfigCases[this.currentCase].listBackUps[i].option = 'New';
              this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp = null;
            }
            this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
            // reset option
            this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option.filter((e) => { return e.value != '0' })
            // reset back up type no switching
            this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
            // if (this.listConfigCases[this.currentCase].listBackUps[i].option == '0') {
            //   this.listConfigCases[this.currentCase].listBackUps[i].name = this.genBackUpName(this.currentCase)
            //   this.listConfigCases[this.currentCase].listBackUps[i].id = null
            //   this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
            // }
          }
        }
        this.listAllBackUp = this.getListAllBackUp()
        this.onChangeMerchantSelect(false);
        break;
      case 'Select':
        // get list existed back up
        this.listAllBackUp = this.getListAllBackUp()

        // reset id if old option is no switching
        if (this.listConfigCases[this.currentCase].listBackUps[idx].id == -1) {
          this.listConfigCases[this.currentCase].listBackUps[idx].id = 0
          this.listConfigCases[this.currentCase].listBackUps[idx].listBackUpType = this.backUpType
          // this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
          // reset full option of other
          for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
            this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option;
            if (i != idx) {
              this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
              this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })

            }
          }
        }
        if (this.listConfigCases[this.currentCase].listBackUps[idx].name == '') {
          this.listConfigCases[this.currentCase].listBackUps[idx].name = this.genBackUpName(this.currentCase)
        }
        if (this.listAllBackUp.length > 0) {
          let firstTmpId = this.listAllBackUp[0].value;
          let backUp = this.getBackUpByTmpId(firstTmpId)
          if (backUp != null) {
            this.listConfigCases[this.currentCase].listBackUps[idx].description = backUp.description ?? ""
            this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp = backUp.tmpId
            if (backUp.tmpId != this.listConfigCases[this.currentCase].listBackUps[idx].tmpId) {
              this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(backUp.rules))
              this.listConfigCases[this.currentCase].listBackUps[idx].rules.forEach((e, index) => {
                this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].id = 0
                this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].approval = true;
              })
            }
          }
          // get back up by temp id

        }
        break;
      case 'New':
        if (this.listConfigCases[this.currentCase].listBackUps[idx].id == -1) {
          this.listConfigCases[this.currentCase].listBackUps[idx].id = 0;
          this.listConfigCases[this.currentCase].listBackUps[idx].isBackUpDefault = true;
          this.listConfigCases[this.currentCase].listBackUps[idx].listBackUpType = this.backUpType
          this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
          for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
            this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option;
            if (i != idx) {
              this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
              this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
            }
          }
        }
        if (this.listConfigCases[this.currentCase].listBackUps[idx].name == '') {
          this.listConfigCases[this.currentCase].listBackUps[idx].name = this.genBackUpName(this.currentCase)
        }
        this.listAllBackUp = this.getListAllBackUp()
        break;
    }
  }
  genBackUpName(scase) {
    return "BACK_UP_" + scase + "_" + Date.now()
  }
  onChangeCase() {
    if (!this.listConfigCases[this.currentCase]?.listBackUps || this.listConfigCases[this.currentCase].listBackUps.length == 0) {

      this.listConfigCases[this.currentCase] = {
        caseId: this.currentCase,
        caseName: this.mapCases[this.currentCase] ?? "",
        approval: true,
        listBackUps: [
        ]
      }

    }
  }
  onDeleteBackUp(idx) {
    if (idx != null) {
      //
      this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
      let tmpBackUp = this.listConfigCases[this.currentCase]?.listBackUps[idx];

      for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {

        if (tmpBackUp && tmpBackUp.isBackUpDefault) {
          // reset other back up type
          if (i != idx) {
            this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType
          }
          // reset order back up option if delete no switching
          if (tmpBackUp.option == '0') {
            this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option
          }

        }
        if (tmpBackUp.tmpId == this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp && this.listConfigCases[this.currentCase].listBackUps[i].option == 'Select') {
          this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
          this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp = null
        }


      }

    }
    this.listConfigCases[this.currentCase].listBackUps.splice(idx, 1);
    this.onChangeMerchantSelect(true)
  }


  getBackUpByTmpId(tmpId) {
    let backUp = null;
    if (tmpId != null && tmpId != '') {
      for (let item in this.listConfigCases) {
        this.listConfigCases[item].listBackUps.forEach((e) => {

          if (e.tmpId == tmpId) {
            backUp = e
          }
        })
      }
    }
    return backUp
  }
  getListAllBackUp() {
    let listAllBackUp = []
    for (let item in this.listConfigCases) {
      this.listConfigCases[item].listBackUps.forEach((e) => {
        if (e.name != null && e.name != '' && e.status != 'delete') {
          listAllBackUp.push({ label: e.name, value: e.tmpId })
        }
      })
    }
    return listAllBackUp;
  }
  onChangeBackUpSelect(idx) {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

    let backUp = this.getBackUpByTmpId(this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp)
    // this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp = this.listAllBackUp[0].value
    this.listConfigCases[this.currentCase].listBackUps[idx].description = backUp?.description ?? ""
    if (backUp?.tmpId && this.listConfigCases[this.currentCase].listBackUps[idx].tmpId != backUp.tmpId) {
      this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(backUp.rules))?.map((e) => {
        let tmpRule = e
        tmpRule.status = 'New'
        tmpRule.id = 0
        tmpRule.approval = true
        return tmpRule
      }) ?? []
    }
  }
  onChangeBackUpType(idx) {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

    switch (this.listConfigCases[this.currentCase].listBackUps[idx].isBackUpDefault) {
      case true:
        if (this.listConfigCases[this.currentCase].listBackUps[idx].rules.length > 0) {
          this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
        }
        this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds = []
        this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = []
        // reset all other back up type to exception
        for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
          if (i != idx) {
            this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
            this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
          }

        }
        if (!this.listConfigCases[this.currentCase].listBackUps[idx].rules || this.listConfigCases[this.currentCase].listBackUps[idx].rules.length == 0) {
          this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
        }

        // for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
        //   if (i != idx) {
        //     console.log("changeeeee " + i)
        //     this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
        //     // reset back up type no switching
        //     if (this.listConfigCases[this.currentCase].listBackUps[i].option == '0') {
        //       this.listConfigCases[this.currentCase].listBackUps[i].name = this.genBackUpName(this.currentCase)
        //       this.listConfigCases[this.currentCase].listBackUps[i].id = null
        //       this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
        //     }
        //   }
        // }
        this.listAllBackUp = this.getListAllBackUp()
        break;
      case false:
        // reset backup types
        for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
          this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType
          this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;

        }


    }
  }

  private setAcqRuleGroupFromApproval(res: any) {
    if (res.type !== 'DELETE') {
      this.ruleGroup.id = res.newValue.id
      this.ruleGroup.name = res.newValue.groupName ?? "";
      this.ruleGroup.description = res.newValue.description ?? "";
      this.ruleGroup.type = res.newValue.type;
      this.ruleGroup.merchantIds = res.newValue.merchantIds ?? [];
      this.ruleGroup.rules = [];
      this.ruleGroup.isEditable = false;
      this.ruleGroup.isReview = true;
      if (res?.newValue.rules && res?.newValue.rules[0]) {
        res.newValue.rules.forEach(rule => this.ruleGroup.rules.push({
          id: rule?.id,
          source: rule?.source,
          cardType: rule.cardTypes ? rule.cardTypes.join("|") : "",
          acquirer: rule.acquirer ?? "",
          issuer: rule.issuers ? rule.issuers.join("|") : "",
          binGroup: rule.binGroups ? rule.binGroups.join("|") : "",
          bin: rule.bins ? rule.bins.join("|") : "",
          level: rule.level ?? "",
          binCountry: rule.binCountry ?? null,
          bankMerchantId: rule.bankMerchantId ?? null,
          status: rule.status ?? null
        }));
      }
    } else {
      this.ruleGroup.id = res.oldValue.id
      this.ruleGroup.name = res.oldValue.groupName ?? "";
      this.ruleGroup.description = res.oldValue.description ?? "";
      this.ruleGroup.type = res.oldValue.type;
      this.ruleGroup.merchantIds = res.oldValue.merchantIds ?? [];
      this.ruleGroup.rules = [];
      this.ruleGroup.isEditable = false;
      this.ruleGroup.isReview = true;
      if (res?.oldValue.rules && res?.oldValue.rules[0]) {
        res.oldValue.rules.forEach(rule => this.ruleGroup.rules.push({
          id: rule?.id,
          source: rule?.source,
          cardType: rule.cardTypes ? rule.cardTypes.join("|") : "",
          acquirer: rule.acquirer ?? "",
          issuer: rule.issuers ? rule.issuers.join("|") : "",
          binGroup: rule.binGroups ? rule.binGroups.join("|") : "",
          bin: rule.bins ? rule.bins.join("|") : "",
          level: rule.level ?? "",
          binCountry: rule.binCountry ?? null,
          bankMerchantId: rule.bankMerchantId ?? null,
          status: rule.status ?? null
        }));
      }
    }

  }




  private getAcqRuleGroupById() {
    if (!this.ruleGroup.id) {
      return null;
    }
    this.acquirerRuleService.GetAcqRuleGroup(this.ruleGroup.id, true).subscribe(res => {
      if (!res || !res.id) {
        return null;
      };
      this.setFullAcqGroupFromResponse(res);
    });
  }
  private setFullAcqGroupFromResponse(res) {
    this.ruleGroup = this.setAcqRuleGroupFromResponse(res);
    if (res.back_ups && res.back_ups.length > 0) {
      res.back_ups.forEach((b) => {
        this.ruleGroup.backUps.push(this.setAcqRuleGroupFromResponse(b))
      })
    }
  }
  private setAcqRuleGroupFromResponse(res: any) {
    let ruleGroup = {
      id: null,
      approvalId: null,
      name: "",
      type: "Default",
      description: "",
      merchantIds: [],
      merchantIdsText: "",
      rules: [],
      isEditable: true,
      isReview: false,
      backUps: [],
      case: null,
      listCases: [],
      isBackUpDefault: null,
      approval: null,
      status: "",
      enable: null,
      mcc: "",
    }
    ruleGroup.id = res.id
    ruleGroup.name = res.group_name ?? "";
    ruleGroup.description = res.description ?? "";
    ruleGroup.type = res.type;
    ruleGroup.merchantIds = res.merchant_ids ?? [];
    ruleGroup.rules = [];
    ruleGroup.isEditable = res.is_editable ?? true;
    if (res.case && res.case?.length > 0) {
      ruleGroup.merchantIdsText = this.getShortenText(ruleGroup.merchantIds, 10) ?? "";

      ruleGroup.case = res.case
      ruleGroup.isBackUpDefault = res.is_back_up_default ?? false;
    } else {
      // if (ruleGroup.merchantIds.length > 0) {
      //   // get list of merchant backup options
      //   this.listBackUpMerOpt = this.getListBackUpMerOptions(ruleGroup.merchantIds)
      // }
    }
    if (res?.rules && res?.rules[0]) {
      res.rules.forEach(rule => ruleGroup.rules.push({
        id: rule?.id,
        source: rule?.source,
        cardType: rule.card_types ? rule.card_types.join("|") : "",
        acquirer: rule.acquirer ?? "",
        issuer: rule.issuers ? rule.issuers.join("|") : "",
        binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
        bin: rule.bins ? rule.bins.join("|") : "",
        level: rule.level ?? "",
        binCountry: rule.bin_country ?? null,
        bankMerchantId: rule.bank_merchant_id ?? null,
        status: 'exist'
      }));
    }
    return ruleGroup;
  }

  getListBackUpMerOptions(list) {

    return this.listFullMerchant.filter((i) => {
      return list.includes(i.value)
    });
  }
  getShortenText(list, max) {
    if (list == null && list.length == 0) {
      return "";
    }
    let s = list.toString();
    if (s.length <= max) {
      return s;
    }
    return s.slice(0, max) + "...";
  }

  getListValueFromMerchantIds(list) {
    let listValue = [];
    list.forEach((e) => {


      if (e.trim() in this.mapListFullMerchant) {
        listValue.push(this.mapListFullMerchant[e.trim()]);
      }
    })
    return listValue;

  }



  _filter(value) {
    if (value._filterValue.includes(" ")) {

      let listSearchMerchant = []
      listSearchMerchant = this.getListValueFromMerchantIds(value._filterValue.toUpperCase().split(" ") ?? []);
      if (listSearchMerchant && listSearchMerchant.length > 0) {

        value._filteredOptions = this.listMerchantIds.filter((item) => listSearchMerchant.includes(item.value));
      }
    } else {
      value._filteredOptions = this.listMerchantIds.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

    }

  }


  _filterBackUpMerchant(value, idx) {
    if (value._filterValue.includes(" ")) {

      let listSearchMerchant = []
      listSearchMerchant = this.getListValueFromMerchantIds(value._filterValue.toUpperCase().split(" ") ?? []);

      if (listSearchMerchant && listSearchMerchant.length > 0) {

        value._filteredOptions = this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds.filter((item) => listSearchMerchant.includes(item.value));
      }
    } else {
      value._filteredOptions = this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

    }

  }
  identify(index, item) {
    return item && item.id;
  }

  private _normalizeValue(value: string): string {
    return value.toUpperCase().replace(/\s/g, '');
  }
  init(param?: any) {
  }



  redirectBack() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to exit ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {
          this.router.navigate(['/system-management/acq-group-config']);
          // this._location.back();
        }
      })
  }

  deleteAcqRule(idx) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Rule ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && idx >= 0) {
          this.ruleGroup.rules.splice(idx, 1)

        }
      })
  }


  // onDeleteBackUp(idx) {
  //   this.confirmService.build()
  //     .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Group Back Up?</div>`)
  //     .title('Notice!')
  //     .no('No')
  //     .yes('Yes').confirm().subscribe(result => {
  //       if (result) {
  //         this.ruleGroup.backUps.splice(idx, 1)
  //       }
  //     })
  // }

  onClickedOutside() {
  }
  removeMerchantID(id: String) {

    if (id) {
      var index = this.ruleGroup.merchantIds.indexOf(id);
      if (index !== -1) {
        this.ruleGroup.merchantIds.splice(index, 1);
        this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
        this.onChangeGroupMerchantSelect()
        // this.onChangeMerchantSelect(true)
      }

    }


  }
  removeBackUpMerchantID(id: String, idx) {
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

    if (id) {
      var index = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds.indexOf(id);
      if (index !== -1) {
        this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds.splice(index, 1);
        this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
        this.onChangeMerchantSelect(true)
      }

    }


  }

  saveGroup() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to save Acquirer Rule Group ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {

          if (!this.validateGroup()) {
            return
          }
          if (!this.ruleGroup.id) {
            this.acquirerRuleService.AddAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
              if (data.status && data.status == "Successful") {
                this.toastr.success('Saved Group successfully.');
                this.router.navigate(['/system-management/acq-group-config']);
              }
            });
          } else {
            this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
              if (data.status && data.status == "Successful") {
                this.toastr.success('Saved Group successfully.');
                this.router.navigate(['/system-management/acq-group-config']);
              }

            });
          }

        }
      })
  }

  prepareRuleGroupData() {
    let jRules = [];
    if (this.ruleGroup.type != "Default") {
      this.ruleGroup.merchantIds = [];
    }

    this.ruleGroup.rules.forEach((rule) => {
      let acquirerId;
      this.acquirers.filter(data => {
        if (data['name'] === rule.acquirer) acquirerId = data['value']
      });
      jRules.push({
        "id": rule?.id,
        "source": rule.source,
        "card_types": rule.cardType ? rule.cardType.split("|") : [],
        "acquirer": Number(acquirerId),
        "issuers": rule.issuer ? rule.issuer.split("|") : [],
        "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
        "bins": rule.bin ? rule.bin.split("|") : [],
        "level": Number(rule.level),
        "status": rule.status ?? "",
        "bin_country": rule.binCountry,
        "bank_merchant_id": rule.bankMerchantId ?? "",
        "type": rule.type ?? "",
        "enable": rule.enable,
        "updated_date": rule.updateDate,
        "approval": rule.approval,
      })
    })


    let jCases = [];
    this.ruleGroup.listCases.forEach((e) => {
      jCases.push(this.prepareCaseToSave(e))
    })
    // let jGroupBackUp = [];
    // this.ruleGroup.backUps.forEach(ruleGroupBackUp => {
    //   let jRuleBK = [];
    //   ruleGroupBackUp.rules.forEach((rule) => {
    //     jRuleBK.push({
    //       "id": rule?.id,
    //       "source": rule.source,
    //       "card_types": rule.cardType ? rule.cardType.split("|") : [],
    //       "acquirer": Number(rule.acquirer),
    //       "issuers": rule.issuer ? rule.issuer.split("|") : [],
    //       "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
    //       "bins": rule.bin ? rule.bin.split("|") : [],
    //       "level": Number(rule.level),
    //       "status": rule.status ?? "",
    //       "bin_country": rule.binCountry,
    //       "bank_merchant_id": rule.bankMerchantId ?? "",
    //     })
    //   })
    //   jGroupBackUp.push({
    //     "id": ruleGroupBackUp.name.includes("Current Config") ? -1 : ruleGroupBackUp?.id,
    //     "group_name": ruleGroupBackUp.name,
    //     "description": ruleGroupBackUp.description,
    //     "case": ruleGroupBackUp.case,
    //     "is_back_up_default": ruleGroupBackUp.isBackUpDefault,
    //     "rules": jRuleBK,
    //     "merchant_ids": ruleGroupBackUp.merchantIds
    //   })
    // })
    const _mcc = this.ruleGroup?.mcc;
    return {
      "id": this.ruleGroup?.id,
      "type": this.ruleGroup.type,
      "group_name": this.ruleGroup.name,
      "merchant_ids": this.getMerchantIdFromListValue(this.ruleGroup.merchantIds.sort() ?? []),
      "rules": jRules,
      "description": this.ruleGroup.description,
      "cases": jCases,
      "approval": this.ruleGroup.approval,
      "status": this.ruleGroup.status,
      "enable": this.ruleGroup.enable,
      "updated_date": this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss'),
      "mcc": _.get(_mcc, "name"),
    }
  }
  getMerchantIdFromListValue(list) {
    let listMerchantId = [];
    list.forEach((e) => {
      let item = this.getKeyByValue(this.mapListFullMerchant, e.trim());
      if (item && item != '') {
        listMerchantId.push(item);
      }
    })
    return listMerchantId;
  }
  getKeyByValue(object, value) {
    return Object.keys(object).find(key =>
      object[key] === value);
  }

  prepareCaseToSave(caseItem) {
    if (!caseItem || !caseItem.listBackUps || caseItem.listBackUps.length == 0) {
      return null
    }
    let jListBackUps = []
    caseItem.listBackUps.forEach((e) => {
      jListBackUps.push({
        "id": e.id,
        "group_name": e.name,
        "description": e.description,
        "case": e.case + '',
        "is_back_up_default": e.isBackUpDefault,
        "rules": this.prepareBackUpRules(e.rules),
        "merchant_ids": this.getMerchantIdFromListValue(e.merchantIds),
        "approval": e.approval,
        "enable": this.ruleGroup.enable,
        "status": e.status,
        "updated_date": this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss'),
      })
    })

    return {
      "case_id": caseItem.caseId,
      "case_name": caseItem.caseName,
      "status": caseItem.status,
      "updated_date": caseItem.updatedDate,
      "back_ups": jListBackUps,
      "approval": caseItem.approval,
      "enable": !caseItem.isDisable,
    }
  }

  prepareBackUpRules(rules) {

    if (!rules || rules.length == 0) {
      return null
    }
    let jrules = []
    rules.forEach((rule) => {
      let acquirerId;
      this.acquirers.filter(data => {
        if (data['name'] === rule.acquirer) acquirerId = data['value']
      });
      jrules.push({
        "id": rule?.id,
        "source": rule.source,
        "card_types": rule.cardType ? rule.cardType.split("|") : [],
        "acquirer": Number(acquirerId),
        "issuers": rule.issuer ? rule.issuer.split("|") : [],
        "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
        "bins": rule.bin ? rule.bin.split("|") : [],
        "level": Number(rule.level),
        "status": rule.status ?? "",
        "type": rule.type ?? "",
        "enable": rule.enable,
        "updated_date": rule.updateDate,
        "approval": rule.approval,
        "bin_country": rule.binCountry,
        "bank_merchant_id": rule.bankMerchantId ?? "",
      })
    })
    return jrules;
  }

  validateGroup() {
    if (this.ruleGroup.name == "") {
      this.toastr.error('Group Name is required', 'Error');
      return false;
    }
    if (this.ruleGroup.type == "") {
      this.toastr.error('Group Type is required', 'Error');
      return false;
    }
    if (this.ruleGroup.rules.length == 0) {
      this.toastr.error('Rule is required', 'Error');
      return false;
    }
    return true;

  }

  makeUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (a, b) {
      return b = Math.random() * 16, (a == 'y' ? b & 3 | 8 : b | 0).toString(16);
    });
  }
  getListMerchantID() {
    let request = new HttpParams()
      .set("id", this.ruleGroup.id)
      .set("source", "ACQUIRER_RULE_GROUP");
    this.acquirerRuleService.GetAllMerchantId(request).subscribe(res => {
      this._getListMCC(res.data);
      if (res && res.data.length > 0) {
        let _data = res.data;
        _data = _.sortBy(_data, ['mcc', 'merchant_id']);
        _data.forEach((i) => {
          let sGroup = i.acq_group_name != "" && i.acq_group_name != null ? "_(" + i.acq_group_id + "_" + i.acq_group_name + ")" : ""
          if (i) this.listMerchantIds.push(
            {
              name: i.merchant_id
                + "(" + i.mcc + "_" + i.payment_method + "_" + i.currency + "_" + i.token_cvv + ")" + sGroup,
              value: i.merchant_id
                + "(" + i.mcc + "_" + i.payment_method + "_" + i.currency + "_" + i.token_cvv + ")" + sGroup, acqGroupId: i.acq_group_id ?? ""
            });
        });
        this.listFullMerchant = this.listMerchantIds;
        this.listFullMerchant.forEach((e) => {
          let merchId = e.value.substring(0, e.value.indexOf('(')).trim();
          if (merchId && merchId != '') {
            this.mapListFullMerchant[merchId] = e.value
          }
        })
        this.onCheckAllMerchant()

      }
      if (this.ruleGroup.id) {
        this.getAcqRuleGroupById();
      }
    })
  }

  _getListMCC(merchants : any) {
    const lstMccUnique  = _.uniq(_.map(merchants, 'mcc'));
      this.listMCC = lstMccUnique.map((mcc: any) => ({
          name: mcc,
      }));
  }

  // _onChangeMCC(value, event) {
  //     if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
  //         let filterOpt = value?._filteredOptions ?? []
  //         if (filterOpt && filterOpt.length > 0) {
  //             filterOpt = filterOpt.map((e) => {
  //                 return e.value
  //             })

  //             this.ruleGroup.merchantIds = this.tmpSelectedMerchants.filter((e) => {
  //                 return !filterOpt.includes(e)
  //             })
  //             this.tmpSelectedMerchants = this.ruleGroup.merchantIds
  //         } else {
  //             this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
  //         }
  //     } else {
  //         this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
  //     }

  // }

  // _filterMCC(value : any) {
  //     value._filteredOptions = this.listMCC.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)));
  // }

  _onChangeMerchant(value, event) {
    // handle delete all

    if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
      //delete all
      console.log("delete all---------------------")
      let filterOpt = value?._filteredOptions ?? []
      if (filterOpt && filterOpt.length > 0) {
        // in filtering
        filterOpt = filterOpt.map((e) => {
          return e.value
        })

        this.ruleGroup.merchantIds = this.tmpSelectedMerchants.filter((e) => {
          return !filterOpt.includes(e)
        })

        this.tmpSelectedMerchants = this.ruleGroup.merchantIds
      } else {
        this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
      }
    } else {

      this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
    }
    // console.log(this.ruleGroup.merchantIds)
    this.onChangeGroupMerchantSelect()

  }
  onChangeGroupMerchantSelect() {
    //gen back ups merchant
    for (var i = 0; i < this.ruleGroup?.listCases?.length; i++) {
      if (this.ruleGroup?.listCases[i].listBackUps?.length > 0) {
        for (var j = 0; j < this.ruleGroup?.listCases[i].listBackUps.length; j++) {
          if (this.ruleGroup?.listCases[i].listBackUps[j]?.merchantIds) {
            this.ruleGroup.listCases[i].listBackUps[j].merchantIds = this.ruleGroup?.listCases[i].listBackUps[j].merchantIds.filter((e) => { return this.ruleGroup.merchantIds.includes(e) })
          }
        }
      }
    }
    // this.onChangeMerchantSelect(true);
  }
  // getMerchantLabelFromValue(merchantId: string) {
  //   for (var i = 0; i < this.listFullMerchant.length; i++) {
  //     let e = this.listFullMerchant[i]
  //     if (e.value == merchantId) {

  //       return e.name
  //     }
  //   }
  // }

  // back up config
  prepareConfigBackUp(caseId: any) {
    //reset list merchant

    this.currentCase = 1;
    if (caseId) this.currentCase = caseId;
    this.listConfigCases = {}
    if (!this.ruleGroup.listCases || this.ruleGroup.listCases.length === 0) {
      this.onInitCase()
    }
    this.ruleGroup.listCases.forEach((e) => {
      if (e.caseId) {
        e.tmpSelectedMerchants = e.merchantIds
        this.listConfigCases[e.caseId] = JSON.parse(JSON.stringify(e))
      }
    })
    this.sortBackUp();
    this.resetAllMerchantOption()


  }

  sortBackUp() {
    for (let item in this.listConfigCases) {
      for (let i = 0; i < this.listConfigCases[item].listBackUps.length; i++) {
        if (this.listConfigCases[item].listBackUps[i].isBackUpDefault) {
          this.array_move(this.listConfigCases[item].listBackUps, i, 0);
        }
      }
    }
  }
  array_move(arr, old_index, new_index) {
    if (new_index >= arr.length) {
      var k = new_index - arr.length + 1;
      while (k--) {
        arr.push(undefined);
      }
    }
    arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
    return arr;
  };

  SaveBackUpConfig() {
    if (!this.validateListCase()) {
      return;
    }
    // save
    this.ruleGroup.listCases = []
    for (let item in this.listConfigCases) {
      if (this.listConfigCases[item].listBackUps.length > 0) {
        this.ruleGroup.listCases.push(this.listConfigCases[item]);
      }
    }

    this.toastr.success('Successful', "Config Back Up Successfully");
    // this.listConfigCases =
    window.document.getElementById("closeCases").click();
  }
  validateListCase() {
    if (Object.keys(this.listConfigCases).length == 0) {
      this.toastr.error('Nothing has changed', 'Error');
      return false;
    }

    for (let item in this.listConfigCases) {
      for (let i = 0; i < this.listConfigCases[item]?.listBackUps?.length; i++) {
        if (!this.validateBackUp(this.listConfigCases[item]?.listBackUps[i], i + 1)) {
          return false;
        }
      }
    }
    return true;
  }
  validateBackUp(backUp, no) {
    if (!backUp) {
      return true;
    }
    if (backUp?.id == -1 && backUp?.option == '0') {
      return true;
    }
    if (backUp?.name == null || backUp.name == '') {
      this.toastr.error('Back up no.' + no + ' case ' + backUp?.case + ' name is invalid', 'Error');
      return false;
    }
    if (backUp?.rules == null || backUp.rules.length == 0) {
      this.toastr.error('Back up no.' + no + ' case ' + backUp?.case + ' rules is invalid', 'Error');
      return false;
    }
    // TODO:VALIDATE RULES


    return true;

  }
  //Add Acquirer Rule
  getAllInfoConfigRule() {
    this.acquirerRuleService.GetInfomationConfigAcqRule().subscribe(data => {
      data.issuer.forEach(value => {
        this.issuers.push({ name: value, value: value });
      });
      data.binGroup.forEach(value => {
        this.binGroup.push({ name: value, value: value });
      });

      data.binGroupInfo.forEach(value => {
        this.binGroupInfo.push({ name: value['groupName'], source: value['source'] == null ? '' :  value['source'] });
      });

      for (const key in data.acquirer) {
        this.acquirers.push({ name: key, value: data.acquirer[key] })
        this.acquirersView[data.acquirer[key]] = key;
      }
      this.listBankMid = data.bankMid;

    });
  }

  defaultTempData() {
    this.tempRules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
    this.clearData();
    this.isCreateBackUp = false;
  }
  clearData() {
    this.isSourceDirect = true;
    this.sId = 0;
    this.sAcq = this.acquirers[0]["value"];
    this.sSource = this.sources[0]["value"];
    this.sIssuer = "";
    this.sBinGroup = "";
    this.isCheckVisa = false;
    this.isCheckMaster = false;
    this.isCheckJcb = false;
    this.isCheckAmex = false;
    this.sBin = '';
    this.sDescription = '';
    this.sBankIdType = this.bankIdType[0]["value"];
    this.sBinCountry = "";
    this.getBankMerchantId();
    this.sBankMerchantId = this.bankMerchantId[0]["value"];
  }

  getBankMerchantId() {
    let key = this.sAcq + '-' + this.sBankIdType;
    this.bankMerchantId = [];
    this.listBankMid[key].forEach(data => {
      this.bankMerchantId.push({ name: data, value: data });
    });
    this.sBankMerchantId = this.bankMerchantId[0]["value"];
  }

  addRule() {
    if (this.validateRule()) {
      let tempRule = {
        id: null,
        source: "",
        cardType: "",
        binCountry: "",
        issuer: "",
        binGroup: "",
        bin: "",
        acquirer: "",
        bankMerchantId: "",
        type: "",
        level: null,
        status: "",
        updateDate: "",
        description: "",
        approval: null,
        enable: null
      };
      tempRule.id = this.sId ? this.sId : null;
      tempRule.source = this.sSource;
      tempRule.cardType = this.getCardType()?.length > 0 ? this.getCardType().join("|") : "";
      tempRule.binCountry = this.sBinCountry;
      tempRule.issuer = this.sIssuer?.length > 0 ? this.sIssuer.sort().join("|") : "";
      tempRule.binGroup = this.sBinGroup?.length > 0 ? this.sBinGroup.sort().join("|") : "";
      tempRule.bin = this.sBin ? this.sBin : "";
      tempRule.acquirer = this.acquirersView[this.sAcq] ? this.acquirersView[this.sAcq] : "";
      tempRule.bankMerchantId = this.sBankMerchantId ? this.sBankMerchantId : "";
      tempRule.type = this.sBankIdType ? this.sBankIdType : "";
      tempRule.level = this.getLevel();
      tempRule.status = "new";
      tempRule.enable = true;
      tempRule.approval = true;
      tempRule.updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
      tempRule.description = this.sDescription ? this.sDescription : "";
      if (this.idEdit?.length === 0) {
        this.tempRules.push(tempRule);
      } else {
        this.tempRules[this.idEdit] = tempRule;
        this.idEdit = '';
      }
      this.clearData();
      this.toastr.success('Successful', "Add Rule");
    }
  }
  getCardType() {
    let cardType = [];
    if (this.isCheckVisa) cardType.push('Visa');
    if (this.isCheckMaster) cardType.push('Mastercard');
    if (this.isCheckJcb) cardType.push('Jcb');
    if (this.isCheckAmex) cardType.push('Amex');
    return cardType;
  }

  validateRule() {
    //Validate Card Type
    if (!this.isCheckVisa && !this.isCheckMaster && !this.isCheckJcb && !this.isCheckAmex) {
      this.toastr.error('Card type is required', 'Error');
      return false;
  }
  //check Rule Level 5
  var checkRuleEmpty = this.sBinGroup.length == 0 && !this.sBinCountry && !this.sBin && !this.sIssuer;
  //Validate Existed
  if (this.idEdit?.length === 0) {
      if (this.tempRules.filter((data) => {
          if (!data.binCountry) data.binCountry = '';
          return data.status !== 'delete' && data.source === this.sSource &&
              data.cardType?.split("|").filter(item => { return this.getCardType().join("|").includes(item) }).length > 0
              && ((data.binCountry === this.sBinCountry
              && data.issuer?.split("|").filter(item => { return this.sIssuer.includes(item) }).length > 0
              && data.binGroup?.split("|").filter(item => { return this.sBinGroup.includes(item) }).length > 0
              && data.bin?.split("|").filter(item => { return this.sBin.split("|").filter(i => { return i === item }).length > 0 }).length > 0
              && !checkRuleEmpty) || (checkRuleEmpty && !data.issuer && !data.binCountry && data.binGroup.length == 0 && !data.bin))
      }).length > 0) {
          this.toastr.warning('Acquirer Rule Already Existed , It Will Not Be Added To List Acquirer Rule ');
          return false;
      }
  } else {
      if (this.tempRules.filter((data, index) => {
          if (!data.binCountry) data.binCountry = '';
          return Number(index) !== Number(this.idEdit) && data.status !== 'delete' && data.source === this.sSource &&
              data.cardType?.split("|").filter(item => { return this.getCardType().join("|").includes(item) }).length > 0
              && ((data.binCountry === this.sBinCountry
              && data.issuer?.split("|").filter(item => { return this.sIssuer.includes(item) }).length > 0
              && data.binGroup?.split("|").filter(item => { return this.sBinGroup.includes(item) }).length > 0
              && data.bin?.split("|").filter(item => { return this.sBin.split("|").filter(i => { return i === item }).length > 0 }).length > 0
              && !checkRuleEmpty) || (checkRuleEmpty && !data.issuer && !data.binCountry && data.binGroup.length == 0 && !data.bin))
      }).length > 0) {
          this.toastr.warning('Acquirer Rule Is Existed , It Will Not Be Added To List Acquirer Rule');
          return false;
      }
  }

  return true;
  }
  onChangeBackUpMerchantSelect(idx, value, event) {

    if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
      //delete all
      console.log("delete all---------------------")
      let filterOpt = value?._filteredOptions ?? []
      console.log(filterOpt)
      console.log(filterOpt)

      console.log(this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds)
      console.log(this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants)

      if (filterOpt && filterOpt.length > 0) {
        // in filtering
        filterOpt = filterOpt.map((e) => {
          return e.value
        })

        this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds = this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants.filter((e) => {
          return !filterOpt.includes(e)
        })

        this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
        console.log(this.ruleGroup.merchantIds)
      } else {
        this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds;
      }
    } else {
      this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
    }
    // handle remove by search
    if (idx != null && idx >= 0) {
      for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
        if (idx != i) {
          this.listConfigCases[this.currentCase].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)

        }

      }
    }
  }
  disableRuleTemp(index: any) {
    this.tempRules[Number(index)].enable = false;
    this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.tempRules[Number(index)].approval = true;
  }
  enableRuleTemp(index: any) {
    this.tempRules[Number(index)].enable = true;
    this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.tempRules[Number(index)].approval = true;
  }
  deleteRuleTemp(index: any) {
    if (this.tempRules[Number(index)].status === 'new') {
      this.tempRules.splice(Number(index), 1);
    } else {
      this.tempRules[Number(index)].status = 'delete';
      this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
      this.tempRules[Number(index)].approval = true;
    }
  }

  disableRule(index: any) {
    this.ruleGroup.rules[Number(index)].enable = false;
    this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.ruleGroup.rules[Number(index)].approval = true;
  }
  enableRule(index: any) {
    this.ruleGroup.rules[Number(index)].enable = true;
    this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.ruleGroup.rules[Number(index)].approval = true;
  }
  deleteRule(index: any) {
    if (this.ruleGroup.rules[Number(index)].status === 'new') {
      this.ruleGroup.rules.splice(Number(index), 1);
    } else {
      this.ruleGroup.rules[Number(index)].status = 'delete';
      this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
      this.ruleGroup.rules[Number(index)].approval = true;
    }
  }
  editRuleTemp(index: any) {
    this.idEdit = index;
    this.clearData();
    this.sId = this.tempRules[Number(index)].id ?? 0;
    this.sSource = this.tempRules[Number(index)].source;
    let cardType = this.tempRules[Number(index)].cardType?.split("|");
    if (cardType.includes("Visa")) this.isCheckVisa = true;
    if (cardType.includes("Mastercard")) this.isCheckMaster = true;
    if (cardType.includes("Jcb")) this.isCheckJcb = true;
    if (cardType.includes("Amex")) this.isCheckAmex = true;
    this.sBinCountry = this.tempRules[Number(index)].binCountry;
    this.sIssuer = this.tempRules[Number(index)].issuer?.length > 0 ? this.tempRules[Number(index)].issuer?.split("|") : '';
    this.sBinGroup = this.tempRules[Number(index)].binGroup?.length > 0 ? this.tempRules[Number(index)].binGroup?.split("|") : '';
    this.sBin = this.tempRules[Number(index)].bin;
    let acquirer = this.acquirers.filter(data => {
      return data['name'] === this.tempRules[Number(index)].acquirer;
    })
    this.sAcq = acquirer.length > 0 ? acquirer[0].value : '';
    this.sBankIdType = this.tempRules[Number(index)].type;
    this.sBankMerchantId = this.tempRules[Number(index)].bankMerchantId;
    this.sDescription = this.tempRules[Number(index)].description;
  }

  saveRule() {
    if (this.tempRules?.length === 0) {
      this.toastr.error('Rule is empty', 'Error');
      return;
    }
    this.ruleGroup.rules = JSON.parse(JSON.stringify(this.tempRules));
    window.document.getElementById("close").click();
    this.toastr.success('Successful', "Save");
  }
  getLevel() {
    if (this.sBinCountry?.length > 0) return 4;
    if (this.sIssuer?.length > 0) return 3;
    if (this.sBinGroup?.length > 0) return 2;
    if (this.sBin) return 1;
    return 5;
  }


  defaultTempDataBackUp(idx: any) {
    this.idBackUpAddRule = Number(idx);
    this.tempRules = JSON.parse(JSON.stringify(this.listConfigCases[this.currentCase].listBackUps[idx].rules));
    this.clearData();
    this.isCreateBackUp = true;
  }

  saveRuleBackUp() {
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules = JSON.parse(JSON.stringify(this.tempRules));
    window.document.getElementById("close").click();
    this.toastr.success('Successful', "Save");
    this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

  }

  disableRuleBackUp(tmpId: any, index: any) {
    this.listConfigCases[this.currentCase].listBackUps.filter((data, idx) => {
      if (data.tmpId === tmpId) {
        this.idBackUpAddRule = idx;
      }
    })
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].enable = false;
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].approval = true;
  }
  enableRuleBackUp(tmpId: any, index: any) {
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].enable = true;
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
    this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].approval = true;
  }
  deleteRuleBackUp(indexBackUp, index) {
    if (this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].status === 'new') {
      this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules.splice(index, 1);
    } else {
      this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].status = 'delete';
      this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
      this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].approval = true;
    }
  }
  // END


  addSwitchScript() {
    const message = 'Are you sure to save this Switch Script?';
    this.confirmService.build().message(message)
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {
          if (!this.flgEditCase) {
            this.switchScript.id = 0;
          }
          this.hotSwitchService.addSwitchScript(this.switchScript).subscribe((res) => {
            if (res.result == "Failed") {
              this.toastr.error('An error occurred.');
            } else {
              this.toastr.success('Saved successfully.');
            }
            this.configSwitchScript();
            this.getListCase();
          })
        }
      });
  }

  configSwitchScript() {
    let request = new HttpParams()
    return this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
      this.loading = false;
      this.switchScripts = data.list;
    });
  }

  deleteGroup(switch_id) {
    const message = 'Are you sure to delete switch script?';
    this.confirmService.build().message(message)
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {
          this.hotSwitchService.DeleteSwitchScript(switch_id).subscribe(
            (res) => {
              if (res.result == "Success") {
                this.toastr.success('Delete Switch Script Success', 'Success');
              } else {
                this.toastr.error('Delete Switch Script Failed', 'Error');
              }
              this.configSwitchScript();
              this.getListCase();
              this.reloadConfigCaseAfterDelete(switch_id);
            })
        }
      })
  }
  reloadConfigCaseAfterDelete(id) {
    if (this.currentCase == id) {
      this.prepareConfigBackUp(1);

    } else {
      this.prepareConfigBackUp(this.currentCase);

    }
    delete this.listConfigCases[id];
    let idx = this.ruleGroup.listCases.findIndex(element => { return element.caseId === id })
    this.ruleGroup.listCases.splice(idx, 1);
  }
  searchSwitchScript() {
    const params = new HttpParams()
      .set('keyword', this.keyword ? this.keyword.trim() : '')
    return this.hotSwitchService.getListSwitchScript(params).subscribe(data => {
      this.loading = false;
      this.switchScripts = data.list;
    });
  }

  clearSwitchScript() {
    this.switchScript.script = '';
    this.switchScript.desc = ''
    this.flgEditCase = false;
  }
  flgEditCase: any = false;
  showDetail(switch_id) {
    this.hotSwitchService.getSwitchScript(switch_id).subscribe(
      (res) => {
        this.flgEditCase = true;
        this.switchScript.id = res.id;
        this.switchScript.script = res.script;
        this.switchScript.desc = res.desc;
      })
  }

  public suggestAcquirerByMCC(mcc) {
    try {
        const cardList = this.getCardType();
        if (_.isEmpty(cardList)) {
            this.toastr.warning('Vui lòng chọn card type.');
            return;
        }
        if (cardList.length > 1) {
            this.toastr.warning('Vui lòng chọn 1 loại card type khi sử dụng chức năng này.');
            return;
        }
        this.loading = true;
        const cardType = cardList[0].toUpperCase();
        const service = 'QT';
        var merchantIds = this.ruleGroup.merchantIds.map(a => a.substring(0, a.indexOf("(")));
        const params = {
            service,
            merchantId: merchantIds,
            findAllBankMerchantId: true,
        }
        this.paymentOPFeeService.getMerchantInfo(params).subscribe(data => {
            this.loading = false;
            const sourceData = data.merchantFeeInfos;
            const processData = _.isEmpty(_.filter(sourceData, (d) => d.mcc === mcc)) ? sourceData : _.filter(sourceData, (d) => d.mcc === mcc);
            this.listMerchantFeeInfoData = _.filter(processData,
                (mcFee) => this.visibleMerchantFee(mcFee.transactionType) && mcFee.cardType === cardType);
            this.isShowingSuggest = true;
        });
    } catch (error) {
        console.log(error);
        this.loading = false;
        this.isShowingSuggest = false;
    }
  }

  public applySuggest(acq : any) {
    const acqId = acq.suggestAcquirerId;
    const bankMerchantId = acq.suggestBankMerchantId;
    const key2B = acqId + "-2B";
    const key3B = acqId + "-3B";
    const keys = _.concat(key2B, key3B);
    _.forEach(keys, (key, indexKey) => {
        let sBankIdType = indexKey == 0 ? '2B' : '3B';
        this.listBankMid[key].forEach((data, index) => {
            if (data === bankMerchantId) {
                this.sAcq = acqId.toString();
                this.sBankIdType = sBankIdType;
                this.sBankMerchantId = bankMerchantId;
                this.getBankMerchantId();
            }
        });
    });
  }

  private visibleMerchantFee(tranType : String) {
    if (_.isEmpty(tranType))
        return true;
    if (_.includes(tranType.toLowerCase(), 'purchase') || _.includes(tranType.toLowerCase(), 'all'))
        return true;
    return false;
  }

  binGroupInfo = [];
  isSourceDirect = true;
  binGroupFilter = this.binGroup;
  /**
       * Filter dữ liệu bin group theo source để điền vào combobox, gọi vào khi change combobox source hoặc khi click Edit 1 Rule
       * @params source source bin group (Direct, GooglePay, ApplePay, SamsungPay)
       */
      public filterBinGroupBySource(source) {

          this.binGroupFilter = this.binGroup;
          if ("direct" === source.toLowerCase()) {
              this.isSourceDirect = true;
              return;
        } else
          this.isSourceDirect = false;
          let filterValue = _.filter(this.binGroupInfo,
          ele => _.get(ele, 'source', '').toLowerCase() === source.toLowerCase());

          let filterGroupName = [];
          _.forEach(filterValue, ele => {
          filterGroupName.push(ele["name"]);
          })

          this.binGroupFilter = _.filter(this.binGroup, (ele) => {
          return _.includes(filterGroupName, ele["name"]);
          });
          this.sBinGroup = _.filter(this.sBinGroup, (ele) => {
            return _.includes(filterGroupName, ele)
          });
      }
}
