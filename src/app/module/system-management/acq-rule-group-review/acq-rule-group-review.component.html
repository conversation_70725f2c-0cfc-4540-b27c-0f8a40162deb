<div class="wrapper">
  <div style="min-height:80px;position:relative; background-color:#F6F6F6;padding-left: 50px;padding-right: 50px;"
      class="row">
      <h2 class="header-top-center col-sm-8">Acquirer Rule Group</h2>
      <div class=" col-sm-4 d-flex justify-content-end">
          <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
              (click)="navigateListAcquirerRule()">Exit</button>
          <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isChangeDefaultRuleGroup && !isSwitching" class="action-button"
              style="margin-right: 10px; background-color:red ;" (click)="rejectAcqRuleGroup()">Reject</button>
          <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isChangeDefaultRuleGroup && !isSwitching" class="action-button"
              style="margin-right: 10px; background-color:#689F38 ;" (click)="approvalAcqRuleGroup()">Approve</button>
          <button *ngIf="isChangeDefaultRuleGroup && !isEnableDisableDelete && !isSwitching" class="action-button"
              (click)="editAcqRuleGroup()">Save</button>
      </div>
      <h2 class="header-top-center col-sm-12" style="color:red;" *ngIf="isSwitching">Cấu hình hiện tại đang chuyển đổi sang các kịch bản lỗi. Hệ thống sẽ tự động tắt chức năng cập nhật</h2>
  </div>
  <div style="margin: 50px;">
      <div>
          <!--Khối thông tin cơ bản-->
          <div class="row d-flex flex-gap-10 align-items-center">
            <h5>Acquirer Rule Group Info</h5>
            <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeBasicInfoBlock" pButton
              [ngStyle]="{backgroundColor:'var(--pink-400)'}" (click)="rejectAcqRuleGroup()"
              label="Reject"></button>
            <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeBasicInfoBlock" pButton
              [ngStyle]="{backgroundColor:'var(--green-400)'}" (click)="approvalAcqRuleGroup()"
              label="Approve"></button>
          </div>
          <div class="row" style="padding: 10px;margin-top: 10px;" *ngIf="isWaitForApproval">
              <div *ngIf="ruleGroup.approval"><p-checkbox binary="true" inputId="binary"
                      [style]="{'margin': '0px 20px 10px 10px;'}" [(ngModel)]="isCheckDetailAcqRuleGroup"
                      [value]="isCheckDetailAcqRuleGroup"></p-checkbox></div>
              <h6 style="margin-right : 10px ; font-size : 13px">Created By : {{userCreate}} - Created Date :
                  {{dateCreate}}</h6>
              <h6 style="margin-right : 10px ; font-size : 13px"> | </h6>
              <h6 style="margin-right : 10px ; font-size : 13px">Updated By : {{userUpdate}} - Updated Date :
                  {{userUpdate?dateUpdate:''}}</h6>
              <h6 *ngIf="!isEnableDisableDelete" style="margin-right : 10px ;"> <a
                      class="a-no-href color-link text-center" style="color: blue;" data-target="#compareRule"
                      data-toggle="modal" (click)="compareAcquirerRuleGroupDefault()">Compare</a></h6>
          </div>
          <div class="row" style="padding: 10px;">
              <div class="col-sm-1">
                  <p class="label font-text">Group Name</p>
              </div>
              <div class="col-sm-10">
                  <input id="client_merchant_id" class="form-control h-100" pInputText maxlength="50"
                      [(ngModel)]="ruleGroup.name" [disabled]="!ruleGroup?.enable" (keyup)="changeValueDetail()">
              </div>
          </div>
          <div class="row" style="padding: 10px;">
              <div class="col-sm-1">
                  <p class="label font-text">Description</p>
              </div>
              <div class="col-sm-10">
                  <textarea class="form-control h-100" style="width:100%; font-size: 12px !important" maxlength="300"
                      [(ngModel)]="ruleGroup.description"  [disabled]="!ruleGroup?.enable"  pInputTextarea (keyup)="changeValueDetail()">
                </textarea>
              </div>
          </div>

          <!-- <div class="row" style="padding: 10px;">
              <div class="col-sm-1">
                  <p class="label font-text">MCC</p>
              </div>
              <div class="col-sm-10">
                  <form class="d-flex">
                      <p-dropdown
                          [options]="listMCC"
                          [filter]="true"
                          filterBy="name"
                          optionLabel="name"
                          [showClear]="true"
                          name="{{selectedMcc}}"
                          [style]="{'width':'300px'}"
                          dropdownIcon="pi pi-angle-down"
                          [disabled] = "true">

                      </p-dropdown>
                  </form>
              </div>
          </div> -->

          <div class="row" style="padding: 10px;">
              <div class="col-sm-1">
                  <p class="label font-text">MerchantID</p>
              </div>
              <div class="col-sm-10">
                  <form class="d-flex">
                      <p-checkbox binary="true" name="uniqueName" inputId="binary" [(ngModel)]="isAllMerchant"
                          (click)="onCheckAllMerchant()"></p-checkbox>
                      <div style="align-self: center;padding-left: 5px;padding-right: 10px;">All</div>
                      <p-multiSelect matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                      [disabled]="!ruleGroup?.enable" [disabled]="!ruleGroup?.enable"
                          matTooltipClass="my-custom-tooltip" class="selectMerchant" #multiselect
                          [style]="{'width':'300px'}" [options]="listMerchantIds" [(ngModel)]="ruleGroup.merchantIds"
                          dropdownIcon="pi pi-angle-down" [displaySelectedLabel]=false optionLabel="name"
                          (onChange)="_onChangeMerchant(multiselect,$event)" (onFilter)="_filter(multiselect)"
                          optionValue="value" [virtualScroll]="true">
                      </p-multiSelect>

                  </form>
              </div>
          </div>

          <div class="row">
              <div class="col-sm-1"></div>
              <div class="col-sm-10 scroll">
                  <div class="row" style="margin-left: 10px;">
                      <div class="mr-2 mb-2" *ngFor="let merchantId of ruleGroup.merchantIds; index as i">
                          <div *ngIf="ruleGroup.merchantIds &&ruleGroup.merchantIds.length > 0" class="chip"
                              matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                              matTooltipClass="my-custom-tooltip">
                              {{merchantId}}
                              <span><i class="pi pi-times" (click)="removeMerchantID(merchantId)"></i> </span>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

         <!--Khối thông tin rule-->
          <div class="row d-flex flex-gap-10" style="margin-top: 20px">
            <button pButton class="action-button" data-target="#acqRuleModal" data-toggle="modal"
              [disabled]="isEnableDisableDelete || !ruleGroup?.enable"
              label="Config Acquirer Rule" (click)="defaultTempData()"></button>
              <button pButton data-target="#acq-rule-history" data-toggle="modal"
              label="History" (click)="loadAcqRuleHistory()"></button>
            <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeRulesBlock" pButton
              [ngStyle]="{backgroundColor:'var(--pink-400)'}" (click)="rejectAcqRuleGroup()"
              label="Reject"></button>
            <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeRulesBlock" pButton
              [ngStyle]="{backgroundColor:'var(--green-400)'}" (click)="approvalAcqRuleGroup()"
              label="Approve"></button>
          </div>

          <div class="row" style="padding: 10px;" *ngIf="isWaitForApproval">
              <h6 style="margin-right : 10px ; font-size : 13px">Created By : {{userCreate}} - Created Date :
                  {{dateCreate}}</h6>
              <h6 style="margin-right : 10px ; font-size : 13px"> | </h6>
              <h6 style="margin-right : 10px ; font-size : 13px">Updated By : {{userUpdate}} - Updated Date :
                  {{userUpdate?dateUpdate:''}}</h6>
          </div>

          <div class="row" style="padding: 10px;">
              <p-table [lazy]="false" [rowHover]="true" scrollHeight="70vh" [scrollable]='true'
                  [value]="ruleGroup.rules" [rows]="ruleGroup.rules.length" [totalRecords]="ruleGroup.rules.length">
                  <ng-template pTemplate="header">
                      <tr>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:5%">
                              <p-checkbox binary="true" inputId="binary" [(ngModel)]="isCheckAllRule"
                                  [value]="isCheckAllRule" (onChange)="selectAllRule()"></p-checkbox>
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:5%">
                              No
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Source
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Card Type
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Bin Country
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Issuer
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Bin Group
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Bin
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Acquirer
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              MPGS/CBS ID
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Type
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Level
                          </th>
                          <th scope="col" pSortableColumn="approval" class="text-center"
                              style="background-color:#a4d5ab ;">
                              Status <p-sortIcon field="approval"></p-sortIcon>
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ;">
                              Update Date
                          </th>
                          <th scope="col" class="text-center" style="width: 8%;background-color:#a4d5ab ;">
                              Action
                          </th>
                      </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                      <tr>
                          <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important"
                              *ngIf="rule.approval"> <p-checkbox binary="true" inputId="binary"
                                  [(ngModel)]="rule.isChecked" [value]="rule.isChecked"
                                  (onChange)="onChecked(i)"></p-checkbox> </td>
                          <td *ngIf="!rule.approval" style="width:5%;border-right: 1px solid lightgray !important">
                          </td>
                          <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important">
                              {{i+1}}</td>
                          <td class="text-center" style="border-right: 1px solid lightgray !important">{{rule.source}}
                          </td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.cardType}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.binCountry}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.issuer}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.binGroup}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.bin}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.acquirer}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.bankMerchantId}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.type}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.level}}</td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              <div
                                  [ngClass]="{'edit-color': rule.approval && rule.status !== 'delete', 'delete-color' :  rule.approval && (rule.status === 'delete'|| !rule.enable),'default-color':!rule.approval }">
                                  {{rule.approval?'Wait for approval':'Approved'}}
                              </div>
                          </td>
                          <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                              {{rule.updateDate}}</td>
                          <td class="text-center" style="width: 8%;">
                              <button *ngIf="global.isActive('update_acq_config')" pButton pRipple title="Delete" icon="pi pi-trash"
                                  class="p-button-rounded p-button-warning" (click)="deleteRule(i)"
                                  [disabled]="!ruleGroup?.enable ||!rule.enable || rule.status == 'delete'"></button>
                              <button *ngIf="global.isActive('update_acq_config') && rule.enable" pButton pRipple title="Disable" icon="pi pi-minus" [disabled]="rule.status == 'delete' || !ruleGroup?.enable "
                                  class="p-button-rounded p-button-danger" (click)="disableRule(i)"> </button>
                              <button *ngIf="global.isActive('update_acq_config') && !rule.enable" pButton pRipple title="Enable" icon="pi pi-check" [disabled]="rule.status == 'delete' || !ruleGroup?.enable "
                                  class="p-button-rounded p-button-success" (click)="enableRule(i)"> </button>
                          </td>
                      </tr>
                  </ng-template>
                  <ng-template pTemplate="emptymessage">
                      <tr>
                          <td [attr.colspan]="12" class="text-center empty_results">
                              No Results Has Been Shown
                          </td>
                      </tr>
                  </ng-template>
              </p-table>
          </div>

          <!--Khối thông tin kịch bản lỗi-->
          <div class="row d-flex flex-gap-10" style="margin-top: 20px">
              <button [disabled]="ruleGroup.merchantIds.length == 0 || isEnableDisableDelete || !ruleGroup?.enable " pButton class="action-button"
                (click)="prepareConfigBackUp(0)" data-target="#createBackUp" data-toggle="modal"
                label="Config Group Back Up"></button>
              <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeBackupsBlock" pButton
                [ngStyle]="{backgroundColor:'var(--pink-400)'}" (click)="rejectAcqRuleGroup()"
                label="Reject"></button>
              <button *ngIf="global.isActive('acq_group_approve') && isWaitForApproval && !isSwitching && !isChangeBackupsBlock" pButton
                [ngStyle]="{backgroundColor:'var(--green-400)'}" (click)="approvalAcqRuleGroup()"
                label="Approve"></button>
          </div>

          <div class="row" style="padding: 10px;" *ngIf="isWaitForApproval">
              <h6 style="margin-right : 10px ; font-size : 13px">Created By : {{userCreate}} - Created Date :
                  {{dateCreate}}</h6>
              <h6 style="margin-right : 10px ; font-size : 13px"> | </h6>
              <h6 style="margin-right : 10px ; font-size : 13px">Updated By : {{userUpdate}} - Updated Date :
                  {{userUpdate?dateUpdate:''}}</h6>
          </div>

          <div class="row" style="margin-top: 20px; ">
              <div class="font-text" style="
                  padding: 10px;
                  width: 100%;text-align: left; background-color:#a4d5ab; border-bottom-style:inset">Scenario Backup
              </div>
              <p-table [lazy]="false" [rowHover]="true" scrollHeight="70vh" [scrollable]='true'
                  [value]="ruleGroup.listCases" [rows]="ruleGroup.listCases.length"
                  [totalRecords]="ruleGroup.listCases.length">
                  <ng-template pTemplate="header">
                      <tr>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:5%">
                              <p-checkbox binary="true" inputId="binary" [(ngModel)]="isCheckAllListCase"
                                  [value]="isCheckAllListCase" (onChange)="selectAllListCase()"></p-checkbox>
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:5%">
                              No
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:10%">
                              Id
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:30%">
                              Scenario
                          </th>
                          <th scope="col" pSortableColumn="approval" class="text-center"
                              style="background-color:#a4d5ab ; width:15%">
                              Status <p-sortIcon field="approval"></p-sortIcon>
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:15%">
                              Updated Date
                          </th>
                          <th scope="col" class="text-center" style="background-color:#a4d5ab ; width:20%">
                              Action
                          </th>

                      </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-case let-i='rowIndex'>
                      <tr>
                          <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important;"
                              *ngIf="case.approval">
                              <p-checkbox binary="true" inputId="binary" [(ngModel)]="case.isChecked"
                                  [value]="case.isChecked" (onChange)="onCheckedCase(i)"></p-checkbox>
                          </td>
                          <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important;"
                              *ngIf="!case.approval">
                          </td>
                          <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important;">
                              {{i+1}}</td>
                          <td class="text-center" style="width:10%;border-right: 1px solid lightgray !important;">
                              {{'CASE'+case.caseId}}</td>
                          <td class="text-center" style="width:30%;border-right: 1px solid lightgray !important;">
                              {{case.caseName}}</td>
                          <td class="text-center" style="width:15%;border-right: 1px solid lightgray !important;">
                              <div
                                  [ngClass]="{'edit-color': case.approval && case.status !== 'delete', 'delete-color' :  case.approval && (case.status === 'delete'),'default-color':!case.approval }">
                                  {{case.approval?'Wait for approval':'Approved'}}
                              </div>
                          </td>
                          <td class="text-center" style="width:15%;border-right: 1px solid lightgray !important;">
                              {{case.updatedDate}}</td>
                          <td class="text-center">
                              <button pButton pRipple title="Edit" icon="pi pi-pencil" data-target="#createBackUp" data-toggle="modal" [disabled]="case.status == 'delete'"
                                  class="p-button-rounded p-button-success p-mr-2" (click)="onEditCase(i)"></button>
                              <button pButton pRipple icon="pi pi-trash" class="p-button-rounded p-button-warning"
                                  (click)="onDeleteCase(i)" [disabled]="case.status == 'delete' || !ruleGroup.enable"></button>
                          </td>
                  </ng-template>
                  <ng-template pTemplate="emptymessage">
                      <tr>
                          <td [attr.colspan]="11" class="text-center empty_results">
                              No Results Has Been Shown
                          </td>
                      </tr>
                  </ng-template>
              </p-table>
          </div>
      </div>
  </div>
</div>



<!-- View Compare Back Up -->

<div class="modal fade" id="compareBackUp" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999;overflow:scroll">
  <div class="modal-dialog" role="document" style="max-width:2100px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Compare Back Up</h5>
              <div class=" col-sm-8 d-flex justify-content-end">
                  <p-dropdown class="dropdown" [style]="{'margin-right': '10px','font-weight':700}"
                      [options]="listBackUpCompareOpt" [(ngModel)]="backUpCompareOpt"
                      (onChange)="onChangeBackUpCompareOpt() " optionLabel="label" optionValue='value'>
                  </p-dropdown>
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="closeCompareBackUp">Exit</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && isWaitForApprovalBackUpInCompare && !isChangeBackUpBackUpInCompare && !isChangeInConfigBackUp && !isChangeDefaultRuleGroup"
                      class="action-button " style="margin-right: 10px; background-color:red ;"
                      (click)="rejectBackUpInCompare()">Reject</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && isWaitForApprovalBackUpInCompare && !isChangeBackUpBackUpInCompare && !isChangeInConfigBackUp && !isChangeDefaultRuleGroup"
                      class="action-button " style="margin-right: 10px; background-color:#689F38 ;"
                      (click)="approvalBackUpInCompare()">Approval</button>
                  <button *ngIf="isChangeBackUpBackUpInCompare && !isEnableDisableDelete" class="action-button"
                      (click)="onSaveCompareBackUp()">Save</button>
              </div>
          </div>
          <div class="modal-body">

              <!-- Old -->
              <div style="width:48% ; float: left;">
                  <div>
                      <h4>(Old Version)</h4>
                  </div>




                  <div class="row" style="padding-left: 10px;">
                      <div class="col-sm-1 font-text">
                          <p>Option</p>

                      </div>
                      <div class="col-sm-5">
                          <p-dropdown class="dropdown" [disabled]="true" [style]="{'width': '95%'}"
                              [options]="oldBackUp?.listOption" optionLabel="label" optionValue='value'
                              [(ngModel)]="oldBackUp.option"> </p-dropdown>
                      </div>

                  </div>
                  <div *ngIf="oldBackUp.option!='0'">
                      <div class="row" style="padding: 10px;">
                          <div class="col-sm-1">
                              <p class="label  font-text">Group Name <span
                                      style="color: red;display: inline-flex;">(*)</span>
                              </p>
                          </div>
                          <div class="col-sm-10">
                              <input id="client_merchant_id" [disabled]="true" class="form-control h-100" pInputText
                                  maxlength="50" [(ngModel)]="oldBackUp.name">
                          </div>
                      </div>
                      <div class="row" style="padding: 10px;">
                          <div class="col-sm-1">
                              <p class="label  font-text">Back Up Type <span
                                      style="color: red;display: inline-flex;">(*)</span>
                              </p>
                          </div>
                          <div class="col-sm-10">
                              <p-dropdown class="dropdown" [disabled]="true" [options]="oldBackUp.listBackUpType"
                                  [(ngModel)]="oldBackUp.isBackUpDefault" optionLabel="label" optionValue='value'>
                              </p-dropdown>
                          </div>
                      </div>
                      <div class="row" style="padding: 10px;">
                          <div class="col-sm-1">
                              <p class="label font-text">Description</p>
                          </div>
                          <div class="col-sm-10">
                              <textarea [(ngModel)]="oldBackUp.description" [disabled]="true"
                                  class="form-control h-100" style="width:100%; font-size: 12px !important"
                                  maxlength="300" pInputTextarea>
                                  </textarea>
                          </div>
                      </div>


                      <div *ngIf="!oldBackUp.isBackUpDefault" class="row" style="padding: 10px;">
                          <div class="col-sm-1 ">
                              <p class="label font-text">MerchantID</p>
                          </div>
                          <div class="col-sm-10">
                          </div>
                      </div>
                      <div class="row" *ngIf="!oldBackUp.isBackUpDefault">
                          <div class="col-sm-1"></div>
                          <div class="col-sm-10 scroll">
                              <div class="row" style="margin-left: 10px;">
                                  <div class="mr-2 mb-2" *ngFor="let merchantId of oldBackUp.merchantIds">
                                      <div *ngIf="oldBackUp.merchantIds && oldBackUp.merchantIds.length > 0"
                                          class="chip"
                                          matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                          matTooltipClass="my-custom-tooltip">
                                          {{merchantId}}

                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <!-- Star -->
                      <div *ngIf="oldBackUp.isBackUpDefault" class="row" style="padding: 10px;visibility: hidden;">
                          <div class="col-sm-1 ">
                              <p class="label font-text">MerchantID</p>
                          </div>
                          <div class="col-sm-10">
                          </div>
                      </div>
                      <div class="row" *ngIf="oldBackUp.isBackUpDefault" style="visibility: hidden;">
                          <div class="col-sm-1"></div>
                          <div class="col-sm-10 scroll">
                              <div class="row" style="margin-left: 10px;">
                                  <div class="mr-2 mb-2" *ngFor="let merchantId of oldBackUp.merchantIds">
                                      <div *ngIf="oldBackUp.merchantIds && oldBackUp.merchantIds.length > 0"
                                          class="chip"
                                          matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                          matTooltipClass="my-custom-tooltip">
                                          {{merchantId}}

                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <!-- End -->

                      <div class="row d-flex" style="margin: 10px 0px 10px 10px">
                          <button pButton class="action-button" style="background-color: white;"
                              disabled="true"></button>
                      </div>


                      <div class="row" style="margin-top: 20px">
                          <p-table [value]="oldBackUp.rules" [rows]="oldBackUp.rules.length" [lazy]="true"
                              [rowHover]="true" [totalRecords]="oldBackUp.rules.length" scrollHeight="70vh"
                              [scrollable]='true'>
                              <ng-template pTemplate="header">
                                  <tr>
                                      <th scope="col" class="text-center background-table">
                                          No
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Source
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Card Type
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Bin Country
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Issuer
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Bin Group
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Bin
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Acquirer
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          MPGS/CBS ID
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Type
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Level
                                      </th>
                                      <th scope="col" class="text-center background-table">
                                          Status
                                      </th>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                                  <tr *ngIf="rule.status !== 'delete'">
                                      <td class="text-center" style="border-right: 1px solid lightgray !important;">
                                          {{i+1}}</td>
                                      <td class="text-center" style="border-right: 1px solid lightgray !important;">
                                          {{rule.source}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.cardType}}</td>
                                      <td class="text-center" style="border-right: 1px solid lightgray !important;">
                                          {{rule.binCountry}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.issuer}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.binGroup}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.bin}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.acquirer}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.bankMerchantId}}
                                      </td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.type}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{rule.level}}</td>
                                      <td class="text-center"
                                          style="overflow: auto;border-right: 1px solid lightgray !important;">
                                          {{'Approved'}}</td>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                  <tr>
                                      <td [attr.colspan]="11" class="text-center empty_results">
                                          No Results Has Been Shown
                                      </td>
                                  </tr>
                              </ng-template>
                          </p-table>
                      </div>
                  </div>
              </div>


              <!-- New -->
              <div style="width:50% ; float: left;margin-left:1%;border:1px black ; border-left:solid">
                  <div>
                      <div>
                          <div class="row" style="padding-left: 20px;">
                              <p-checkbox binary="true" inputId="binary" [style]="{'margin': '0px 20px 10px 10px;'}"
                                  *ngIf="newBackUp.approval" [(ngModel)]="isCheckDetailBackUpIncompare"
                                  [value]="isCheckDetailBackUpIncompare"></p-checkbox>
                              <h4 style="margin-left:10px">(New Version)</h4>
                          </div>
                          <div style="padding-left: 10px;" class="row">
                              <div class="col-sm-1">
                                  <p class="font-text">Option</p>
                              </div>
                              <div class="col-sm-5">
                                  <p-dropdown class="dropdown" [style]="{'width': '95%'}"
                                      [options]="newBackUp.listOption" optionLabel="label"
                                      (onChange)="onChangeNewBackUpOption()" optionValue='value'
                                      [(ngModel)]="newBackUp.option"> </p-dropdown>
                              </div>
                              <div class="col-sm-5"
                                  *ngIf="newBackUp.option =='Select' && listAllBackUp && listAllBackUp.length>0">
                                  <p-dropdown class="dropdown" [style]="{'width': '95%'}" [options]="listAllBackUp"
                                      optionLabel="label" (onChange)="onChangeNewBackUpSelect()" optionValue='value'
                                      [(ngModel)]="newBackUp.selectedBackUp"> </p-dropdown>
                              </div>
                          </div>
                      </div>

                      <div *ngIf="newBackUp.option!='0'">
                          <div class="row" style="padding: 10px;">
                              <div class="col-sm-1">
                                  <p class="label font-text">Group Name <span
                                          style="color: red;display: inline-flex;">(*)</span></p>
                              </div>
                              <div class="col-sm-10">
                                  <input id="client_merchant_id" class="form-control h-100" pInputText maxlength="50"
                                      [(ngModel)]="newBackUp.name" (keyup)="changeValueDetailBackUpInCompare()">
                              </div>
                          </div>
                          <div class="row" style="padding: 10px;">
                              <div class="col-sm-1 ">
                                  <p class="label font-text">Back Up Type <span
                                          style="color: red;display: inline-flex;">(*)</span></p>
                              </div>
                              <div class="col-sm-10">
                                  <p-dropdown class="dropdown" [options]="newBackUp.listBackUpType"
                                      [(ngModel)]="newBackUp.isBackUpDefault" optionLabel="label" optionValue='value'
                                      (onchange)="onChangeNewBackUpType()">
                                  </p-dropdown>
                              </div>
                          </div>
                          <div class="row" style="padding: 10px;">
                              <div class="col-sm-1">
                                  <p class="label  font-text">Description</p>
                              </div>
                              <div class="col-sm-10">
                                  <textarea [(ngModel)]="newBackUp.description" class="form-control h-100"
                                      (keyup)="changeValueDetailBackUpInCompare()"
                                      style="width:100%; font-size: 12px !important" maxlength="300" pInputTextarea>
                        </textarea>
                              </div>
                          </div>


                          <div *ngIf="!newBackUp.isBackUpDefault" class="row" style="padding: 10px;">
                              <div class="col-sm-1">
                                  <p class="label  font-text">MerchantID</p>
                              </div>
                              <div class="col-sm-10">
                                  <form class="d-flex">
                                      <p-multiSelect class="selectMerchant" #multiselect3 [style]="{'width':'300px'}"
                                          matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                          matTooltipClass="my-custom-tooltip" dropdownIcon="pi pi-angle-down"
                                          [options]="newBackUp.listMerchantIds" [(ngModel)]="newBackUp.merchantIds"
                                          (onFilter)="_filteCompareBackUpMerchant(multiselect3)"
                                          (onChange)="onChangeCompareBackUpMerchant(multiselect3, $event)" [displaySelectedLabel]=false
                                          optionLabel="name" optionValue="value">
                                      </p-multiSelect>
                                  </form>
                              </div>
                          </div>
                          <div class="row" *ngIf="!newBackUp.isBackUpDefault">
                              <div class="col-sm-1"></div>
                              <div class="col-sm-10 scroll">
                                  <div class="row" style="margin-left: 10px;">
                                      <div class="mr-2 mb-2" *ngFor="let merchantId of highlightedMerchantIds">
                                          <div *ngIf="highlightedMerchantIds && highlightedMerchantIds.length > 0"
                                              class="chip {{merchantId.status == 1 ? 'merchant-add':merchantId.status == -1 ?'merchant-delete':''}}"
                                              matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                              matTooltipClass="my-custom-tooltip">
                                              {{merchantId.value}}
                                              <span *ngIf="merchantId.status != -1"><i class="pi pi-times"
                                                      (click)="removeComapareBackUpMerchantID(merchantId.value)"></i>
                                              </span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>

                          <!-- Start -->
                          <div *ngIf="newBackUp.isBackUpDefault" class="row" style="padding: 10px;visibility: hidden;">
                              <div class="col-sm-1">
                                  <p class="label  font-text">MerchantID</p>
                              </div>
                              <div class="col-sm-10">

                              </div>
                          </div>

                          <div class="row" *ngIf="newBackUp.isBackUpDefault" style="visibility: hidden;">
                              <div class="col-sm-1"></div>
                              <div class="col-sm-10 scroll">
                                  <div class="row" style="margin-left: 10px;">
                                      <div class="mr-2 mb-2" *ngFor="let merchantId of highlightedMerchantIds">
                                          <div *ngIf="highlightedMerchantIds && highlightedMerchantIds.length > 0"
                                              class="chip {{merchantId.status == 1 ? 'merchant-add':merchantId.status == -1 ?'merchant-delete':''}}"
                                              matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                              matTooltipClass="my-custom-tooltip">
                                              {{merchantId.value}}
                                              <span *ngIf="merchantId.status != -1"><i class="pi pi-times"
                                                      (click)="removeComapareBackUpMerchantID(merchantId.value)"></i>
                                              </span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <!-- END -->

                          <div class="row d-flex" style="margin: 10px 0px 10px 10px">
                              <button pButton class="action-button" data-target="#acqRuleModal" data-toggle="modal"
                                  (click)="defaultTempDataBackUpInCompare(i)" label="Config Acquirer Rule"></button>
                          </div>
                          <div class="row" style="margin-top: 20px;margin-left:10px">
                              <p-table [value]="newBackUp.rules" [rows]="newBackUp.rules.length" [lazy]="false"
                                  [rowHover]="true" [totalRecords]="newBackUp.rules.length" scrollHeight="70vh"
                                  [scrollable]='true'>
                                  <ng-template pTemplate="header">
                                      <tr>
                                          <th scope="col" class="text-center background-table">
                                              <p-checkbox binary="true" inputId="binary"
                                                  [(ngModel)]="isCheckAllRuleBackUpInCompare"
                                                  [value]="isCheckAllRuleBackUpInCompare"
                                                  (onChange)="selectAllRuleBackUpInCompare()"></p-checkbox>
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              No
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Source
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Card Type
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Bin Country
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Issuer
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Bin Group
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Bin
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Acquirer
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              MPGS/CBS ID
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Type
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Level
                                          </th>
                                          <th scope="col" pSortableColumn="approval"
                                              class="text-center background-table">
                                              Status <p-sortIcon field="approval"></p-sortIcon>
                                          </th>
                                          <th scope="col" class="text-center background-table">
                                              Update Date
                                          </th>
                                          <th scope="col" class="text-center background-table" style="width: 8%;">
                                              Action
                                          </th>
                                      </tr>
                                  </ng-template>
                                  <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                                      <tr>
                                          <td class="{{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              *ngIf="rule.approval"
                                              style="text-align:center;border-right: 1px solid lightgray !important;">
                                              <p-checkbox binary="true" inputId="binary" [(ngModel)]="rule.isChecked"
                                                  [value]="rule.isChecked"
                                                  (onChange)="onCheckedBackUpInCompare(i)"></p-checkbox>
                                          </td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              *ngIf="!rule.approval"
                                              style="border-right: 1px solid lightgray !important;"></td>
                                          <td style="border-right: 1px solid lightgray !important;"
                                              class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                              {{i+1}}</td>
                                          <td style="border-right: 1px solid lightgray !important;"
                                              class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                              {{rule.source}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.cardType}}</td>
                                          <td style="border-right: 1px solid lightgray !important;"
                                              class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                              {{rule.binCountry}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.issuer}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.binGroup}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.bin}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.acquirer}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.bankMerchantId}}
                                          </td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.type}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.level}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.approval?'Wait for approval':'Approved'}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="overflow: auto;border-right: 1px solid lightgray !important;">
                                              {{rule.updateDate}}</td>
                                          <td class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}"
                                              style="width: 8%">
                                              <button pButton pRipple title="Delete" icon="pi pi-trash"
                                                  class="p-button-rounded p-button-warning"
                                                  (click)="deleteRuleNewBackUp(i)"
                                                  [disabled]="!rule.enable  || rule.status == 'delete'"></button>
                                              <button *ngIf="rule.enable" pButton pRipple title="Disable"
                                                  icon="pi pi-minus" class="p-button-rounded p-button-danger"
                                                  (click)="disableRuleNewBackUp( i)"
                                                  [disabled]="rule.status == 'delete'"> </button>
                                              <button *ngIf="!rule.enable" pButton pRipple title="Enable"
                                                  icon="pi pi-check" class="p-button-rounded p-button-success"
                                                  (click)="enableRuleNewBackUp(i)"
                                                  [disabled]="rule.status == 'delete'"> </button>
                                          </td>
                                      </tr>
                                  </ng-template>
                                  <ng-template pTemplate="emptymessage">
                                      <tr>
                                          <td [attr.colspan]="11" class="text-center empty_results">
                                              No Results Has Been Shown
                                          </td>
                                      </tr>
                                  </ng-template>
                              </p-table>
                          </div>
                      </div>
                  </div>


              </div>

          </div>
      </div>
  </div>
</div>




<!-- View Compare -->

<div class="modal fade" id="compareRule" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999">
  <div class="modal-dialog" role="document" style="max-width:2100px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Compare Acquirer Rule Group</h5>
              <div class=" col-sm-4 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="closeCompare">Exit</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && isWaitForApprovalInCompare && !isChangeDefaultNewRuleGroup && !isChangeDefaultRuleGroup"
                      class="action-button " style="margin-right: 10px; background-color:red ;"
                      (click)="rejectInCompare()">Reject</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && isWaitForApprovalInCompare && !isChangeDefaultNewRuleGroup && !isChangeDefaultRuleGroup"
                      class="action-button " style="margin-right: 10px; background-color:#689F38 ;"
                      (click)="approvalInCompare()">Approval</button>
                  <button *ngIf="isChangeDefaultNewRuleGroup && !isEnableDisableDelete" class="action-button"
                      (click)="saveDefaultInCompare()">Save</button>
              </div>
          </div>
          <div class="modal-body">
              <!-- Old -->
              <div style="width:41% ; float: left;">
                  <div style="margin-left: 50px;margin-bottom:20px">
                      <h4>(Old Version)</h4>
                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2">
                          <p class="label  font-text">Group Name <span
                                  style="color: red;display: inline-flex;">(*)</span></p>
                      </div>
                      <div class="col-sm-6">
                          <input id="client_merchant_id" class="form-control h-100" [readonly]="true"
                              [(ngModel)]="oldRuleGroup.name">
                      </div>
                  </div>
                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">
                          <p class="label font-text">Description</p>
                      </div>
                      <div class="col-sm-6">
                          <textarea class="form-control h-100" style="width:100%; font-size: 12px !important"
                              [(ngModel)]="oldRuleGroup.description" maxlength="300" [readonly]="true" pInputTextarea>
                            </textarea>
                      </div>
                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">
                          <p class="label font-text">MerchantIDs</p>
                      </div>
                      <div class="col-sm-10">
                      </div>


                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">

                      </div>
                      <div class="col-sm-10 scroll">
                          <div class="row" style="margin-left: 10px;">
                              <div class="mr-2 mb-2" *ngFor="let merchantId of oldRuleGroup.merchantIds; index as i">
                                  <div *ngIf="oldRuleGroup.merchantIds &&oldRuleGroup.merchantIds.length > 0"
                                      matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                      matTooltipClass="my-custom-tooltip" class="chip">
                                      {{merchantId}}
                                  </div>
                              </div>
                          </div>
                      </div>

                  </div>

                  <div class="row d-flex" style="margin:10px 0px 10px 50px">
                      <button pButton class="action-button" style="background-color: white;" disabled="true"></button>
                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px">
                      <p-table [lazy]="true" [rowHover]="true" scrollHeight="35vh" [scrollable]='true'
                          [value]="oldRuleGroup.rules" [rows]="oldRuleGroup.rules.length"
                          [totalRecords]="oldRuleGroup.rules.length">
                          <ng-template pTemplate="header">
                              <tr>
                                  <th scope="col" class="text-center background-table">
                                      No
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Source
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Card Type
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin Country
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Issuer
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin Group
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Acquirer
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      MPGS/CBS ID
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Type
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Level
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Status
                                  </th>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                              <tr>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">{{i+1}}
                                  </td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.source}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.cardType}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.binCountry}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.issuer}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.binGroup}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.bin}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.acquirer}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.bankMerchantId}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.type}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{rule.level}}</td>
                                  <td class="text-center"
                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                      {{'Approved'}}</td>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="emptymessage">
                              <tr>
                                  <td [attr.colspan]="12" class="text-center empty_results">
                                      No Results Has Been Shown
                                  </td>
                              </tr>
                          </ng-template>
                      </p-table>
                  </div>
              </div>


              <!-- New -->
              <div style="width:57% ; float: left;margin-left:2%;border:1px black ; border-left:solid">
                  <div class="row" style="margin-left: 50px;margin-bottom:20px">
                      <p-checkbox *ngIf="newRuleGroup.approval" binary="true" inputId="binary"
                          [style]="{'margin': '0px 20px 10px 10px;'}" [(ngModel)]="isCheckDetailIncompare"
                          [value]="isCheckDetailIncompare"></p-checkbox>
                      <h4 style="margin:0px 10px">(New Version)</h4>
                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">
                          <p class="label font-text">Group Name <span
                                  style="color: red;display: inline-flex;">(*)</span></p>
                      </div>
                      <div class="col-sm-6">
                          <input id="client_merchant_id" class="form-control h-100" [(ngModel)]="newRuleGroup.name"
                              (keyup)="changeNewValueDetail()">
                      </div>
                  </div>
                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">
                          <p class="label font-text">Description</p>
                      </div>
                      <div class="col-sm-6">
                          <textarea class="form-control h-100" style="width:100%; font-size: 12px !important"
                              [(ngModel)]="newRuleGroup.description" maxlength="300" pInputTextarea
                              (keyup)="changeNewValueDetail()">
                            </textarea>
                      </div>
                  </div>

                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2 ">
                          <p class="label font-text">MerchantIDs</p>
                      </div>
                      <div class="col-sm-10">
                          <form class="d-flex">
                              <p-checkbox binary="true" name="uniqueName" inputId="binary" [(ngModel)]="isAllMerchant"
                                  (click)="onCheckAllMerchant()"></p-checkbox>
                              <div style="align-self: center;padding-left: 5px;padding-right: 10px;">All</div>
                              <p-multiSelect class="selectMerchant" #multiselect2 [style]="{'width':'300px'}"
                                  matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                  matTooltipClass="my-custom-tooltip" dropdownIcon="pi pi-angle-down"
                                  [options]="listMerchantIds" [(ngModel)]="newRuleGroup.merchantIds"
                                  [displaySelectedLabel]=false optionLabel="name" optionValue="value"
                                  (onFilter)="_filter(multiselect2)"
                                  (onChange)="onChangeCompareMerchant(multiselect2,$event)">
                              </p-multiSelect>
                          </form>
                      </div>

                  </div>
                  <div class="row" style="margin-left: 50px ;margin-bottom:20px ">
                      <div class="col-sm-2"></div>
                      <div class="col-sm-9 scroll">
                          <div class="row" style="margin-left: 10px;">
                              <div class="mr-2 mb-2" *ngFor="let merchantId of highlightedMerchantIds; index as i">
                                  <div *ngIf="highlightedMerchantIds && highlightedMerchantIds.length > 0"
                                      class="chip {{merchantId.status == 1 ? 'merchant-add':merchantId.status == -1 ?'merchant-delete':''}}"
                                      matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                      matTooltipClass="my-custom-tooltip">
                                      {{merchantId.value}}
                                      <span *ngIf="merchantId.status != -1"><i class="pi pi-times"
                                              (click)="removeComapareMerchantID(merchantId.value)"></i> </span>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>

                  <div class="row d-flex" style="margin:10px 0px 10px 50px">
                      <button pButton class="action-button" data-target="#acqRuleModal" data-toggle="modal"
                          label="Config Acquirer Rule" (click)="defaultNewData()"></button>
                  </div>

                  <div class="row" style="margin:10px 0px 10px 50px">
                      <p-table [lazy]="false" [rowHover]="true" scrollHeight="35vh" [scrollable]='true'
                          [value]="newRuleGroup.rules" [rows]="newRuleGroup.rules.length"
                          [totalRecords]="newRuleGroup.rules.length">
                          <ng-template pTemplate="header">
                              <tr>
                                  <th scope="col" class="text-center background-table">
                                      <p-checkbox binary="true" inputId="binary" [(ngModel)]="isCheckAllRuleInCompare"
                                          [value]="isCheckAllRuleInCompare"
                                          (onChange)="selectAllRuleInComapre()"></p-checkbox>
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      No
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Source
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Card Type
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin Country
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Issuer
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin Group
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Bin
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Acquirer
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      MPGS/CBS ID
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Type
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Level
                                  </th>
                                  <th scope="col" pSortableColumn="approval" class="text-center background-table">
                                      Status <p-sortIcon field="approval"></p-sortIcon>
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Update Date
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Action
                                  </th>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                              <tr>
                                  <td *ngIf="rule.approval" style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      <p-checkbox binary="true" inputId="binary" [(ngModel)]="rule.isChecked"
                                          [value]="rule.isChecked" (onChange)="onCheckedInCompare(i)"></p-checkbox>
                                  </td>
                                  <td *ngIf="!rule.approval" style="border-right: 1px solid lightgray !important;">
                                  </td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{i+1}}
                                  </td>

                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.source}}
                                  </td>


                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.cardType}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.binCountry}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.issuer}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.binGroup}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.bin}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.acquirer}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.bankMerchantId}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.type}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.level}}</td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.approval?'Wait for
                                      approval':'Approved'}}
                                  </td>
                                  <td style="border-right: 1px solid lightgray !important;"
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      {{rule.updateDate}}</td>
                                  <td
                                      class="text-center {{rule.approval && rule.status !== 'delete'?'edit-color-background':(rule.approval && (rule.status === 'delete'|| !rule.enable))?'delete-color-background':'default-color-background'}}">
                                      <button pButton pRipple title="Delete" icon="pi pi-trash"
                                          class="p-button-rounded p-button-warning" (click)="deleteRuleCompare(i)"
                                          [disabled]="!rule.enable || rule.status == 'delete'"></button>
                                      <button *ngIf="rule.enable" pButton pRipple title="Disable" icon="pi pi-minus" [disabled]="rule.status == 'delete'"
                                          class="p-button-rounded p-button-danger" (click)="disableRuleCompare(i)">
                                      </button>
                                      <button *ngIf="!rule.enable" pButton pRipple title="Enable" icon="pi pi-check" [disabled]="rule.status == 'delete'"
                                          class="p-button-rounded p-button-success" (click)="enableRuleCompare(i)">
                                      </button>
                                  </td>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="emptymessage">
                              <tr>
                                  <td [attr.colspan]="15" class="text-center empty_results">
                                      No Results Has Been Shown
                                  </td>
                              </tr>
                          </ng-template>
                      </p-table>
                  </div>
              </div>

          </div>
      </div>
  </div>
</div>

<!-- Add Rule  -->

<div class="modal fade" id="acqRuleModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Config Acquirer Rule</h5>
              <div class=" col-sm-4 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="close">Exit</button>
                  <button
                      *ngIf="!isCreateBackUp && !isChangeNewRule && !isCreateBackUpInCompare && !isEnableDisableDelete"
                      class="action-button" (click)="saveRule()">Save</button>
                  <button *ngIf="isChangeNewRule && !isEnableDisableDelete" class="action-button"
                      (click)="saveNewRule()">Save</button>
                  <button
                      *ngIf="isCreateBackUp && !isChangeNewRule && !isCreateBackUpInCompare && !isEnableDisableDelete"
                      class="action-button" (click)="saveRuleBackUp()">Save</button>

                  <button
                      *ngIf="isCreateBackUpInCompare && !isCreateBackUp && !isChangeNewRule && !isEnableDisableDelete"
                      class="action-button" (click)="saveRuleBackUpInCompare()">Save</button>
              </div>
          </div>
          <div class="modal-body">
              <div style="padding: 10px;">
                  <div class="row" style="padding: 10px;">
                      <div class="col-sm-2">
                          <p class="label font-text">Group Name</p>
                      </div>
                      <div class="col-sm-10">
                          <input class="form-control h-100" readonly="true" style="border-color:black"
                              [(ngModel)]="ruleGroup.name">
                      </div>
                  </div>
                  <div class="row" style="margin-top: 20px; ">
                      <div class="background-table font-text"
                          style="
                      padding: 10px;
                      width: 100%;text-align: left; background-color:#a4d5ab;margin-left:10px;margin-right:10px;border-bottom-style:inset">
                          List Acquirer Rule</div>
                  </div>

                  <p-table [value]="tempRules" [rows]="tempRules.length" [totalRecords]="tempRules.length"
                      [lazy]="false" [rowHover]="true" scrollHeight="35vh" [scrollable]='true'>
                      <ng-template pTemplate="header">
                          <tr>
                              <th scope="col" class="text-center background-table">
                                  No
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Source
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Card Type
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Bin Country
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Issuer
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Bin Group
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Bin
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Acquirer
                              </th>
                              <th scope="col" class="text-center background-table">
                                  MPGS/CBS ID
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Type
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Level
                              </th>
                              <th scope="col" pSortableColumn="approval" class="text-center background-table">
                                  Status <p-sortIcon field="approval"></p-sortIcon>
                              </th>
                              <th scope="col" class="text-center background-table">
                                  Update Date
                              </th>
                              <th scope="col" class="text-center background-table" style="width: 8%;">
                                  Action
                              </th>
                          </tr>
                      </ng-template>
                      <ng-template pTemplate="body" let-t let-i='rowIndex'>
                          <tr>
                              <td class="text-center" style="border-right: 1px solid lightgray !important;">{{i+1}}
                              </td>
                              <td class="text-center" style="border-right: 1px solid lightgray !important;">
                                  {{t.source}}</td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.cardType}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">
                                  {{t.binCountry}}</td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.issuer}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.binGroup}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.bin}}</td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.acquirer}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">
                                  {{t.bankMerchantId}}</td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.type}}</td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">{{t.level}}
                              </td>

                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;"
                                  *ngIf="!t.approval">{{t.approval?'Wait
                                      for approval':'Approved'}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto; color:#ffcd83 ;border-right: 1px solid lightgray !important;"
                                  *ngIf="t.approval && t.status !== 'delete'">
                                  {{t.approval?'Wait for
                                      approval':'Approved'}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto; color:#EFD6D6 ;border-right: 1px solid lightgray !important;"
                                  *ngIf="t.approval && t.status === 'delete'">
                                  {{t.approval?'Wait for
                                  approval':'Approved'}}
                              </td>
                              <td class="text-center"
                                  style="overflow: auto;border-right: 1px solid lightgray !important;">
                                  {{t.updateDate}}</td>
                              <td style="width: 8%;">
                                  <button pButton pRipple title="Edit" icon="pi pi-pencil"
                                      class="p-button-rounded p-button-success p-mr-2" (click)="editRuleTemp(i)"
                                      [disabled]="!t.enable || t.status == 'delete'"></button>
                                  <button pButton pRipple title="Delete" icon="pi pi-trash"
                                      class="p-button-rounded p-button-warning" (click)="deleteRuleTemp(i)"
                                      [disabled]="!t.enable || t.status == 'delete'" ></button>
                                  <button *ngIf="t.enable" pButton pRipple title="Disable" icon="pi pi-minus"  [disabled]="t.status == 'delete'"
                                      class="p-button-rounded p-button-danger" (click)="disableRuleTemp(i)"> </button>
                                  <button *ngIf="!t.enable" pButton pRipple title="Enable" icon="pi pi-check" [disabled]="t.status == 'delete'"
                                      class="p-button-rounded p-button-success" (click)="enableRuleTemp(i)"> </button>
                              </td>
                          </tr>
                      </ng-template>
                      <ng-template pTemplate="emptymessage">
                          <tr>
                              <td [attr.colspan]="12" class="text-center empty_results">
                                  No Results Has Been Shown
                              </td>
                          </tr>
                      </ng-template>
                  </p-table>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Source<span class="red">(*)</span></p>
                      </div>
                      <div class="col-sm-3">
                          <p-dropdown dropdownIcon="pi pi-angle-down" [options]="sources" [(ngModel)]="sSource"
                              [style]="{'width':'100%','border-color': 'black'}" optionLabel="name"
                              optionValue="value" (ngModelChange)="filterBinGroupBySource(sSource)">
                          </p-dropdown>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Card Type<span class="red">(*)</span></p>
                      </div>
                      <div class="col-sm-2 row">
                          <p-checkbox binary="true" inputId="binary" [style]="{'margin-left': '10px'}"
                              [(ngModel)]="isCheckVisa"></p-checkbox>
                          <p style="position : relative ; margin : auto 2px auto 2px ">Visa</p>
                      </div>

                      <div class="col-sm-2 row">
                          <p-checkbox binary="true" inputId="binary" [style]="{'margin-left': '10px'}"
                              [(ngModel)]="isCheckMaster"></p-checkbox>
                          <p style="position : relative ; margin : auto 2px auto 2px ">Mastercard</p>
                      </div>

                      <div class="col-sm-2 row">
                          <p-checkbox binary="true" inputId="binary" [style]="{'margin-left': '10px'}"
                              [(ngModel)]="isCheckJcb"></p-checkbox>
                          <p style="position : relative ; margin : auto 2px auto 2px ">Jcb</p>
                      </div>

                      <div class="col-sm-2 row">
                          <p-checkbox binary="true" inputId="binary" [style]="{'margin-left': '10px'}"
                              [(ngModel)]="isCheckAmex"></p-checkbox>
                          <p style="position : relative ; margin : auto 2px auto 2px ">Amex</p>
                      </div>


                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Bin Country</p>
                      </div>
                      <div class="col-sm-3">
                          <div [ngClass]="(sIssuer?.length > 0 || sBinGroup?.length > 0 || sBin?.length > 0 || !isSourceDirect) ? 'dropdown-area-disabled' : ''">
                              <p-dropdown dropdownIcon="pi pi-angle-down" [options]="binCountry" [(ngModel)]="sBinCountry"
                                  [style]="{'width':'100%','border-color': 'black'}" optionLabel="name"
                                  [disabled]="sIssuer?.length > 0 || sBinGroup?.length > 0 || sBin?.length > 0 || !isSourceDirect"
                                  optionValue="value">
                              </p-dropdown>
                          </div>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Issuer</p>
                      </div>
                      <div class="col-sm-3">
                          <div [ngClass]="(sBinCountry?.length > 0 || sBinGroup?.length > 0 || sBin?.length > 0 || !isSourceDirect) ? 'dropdown-area-disabled' : ''">
                              <p-multiSelect dropdownIcon="pi pi-angle-down" [options]="issuers" [(ngModel)]="sIssuer"
                              [style]="{'width':'100%','border-color': 'black'}" optionLabel="name"
                              [disabled]="(sBinCountry?.length > 0 || sBinGroup?.length > 0 || sBin?.length > 0 || !isSourceDirect)"
                              optionValue="value">
                          </p-multiSelect>
                          </div>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Bin Group</p>
                      </div>
                      <div class="col-sm-3">
                          <div [ngClass]="(sBinCountry?.length > 0 || sIssuer?.length > 0 || sBin?.length > 0) ? 'dropdown-area-disabled' : ''">
                              <p-multiSelect dropdownIcon="pi pi-angle-down" [options]="binGroupFilter" [(ngModel)]="sBinGroup"
                              [style]="{'width':'100%','border-color': 'black'}" optionLabel="name"
                              [disabled]="(sBinCountry?.length > 0 || sIssuer?.length > 0 || sBin?.length > 0)"
                              optionValue="value">
                          </p-multiSelect>
                          </div>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Bin</p>
                      </div>
                      <div class="col-sm-3">
                          <input style="width:100%;height:35px;" [(ngModel)]="sBin" maxlength="2000"
                              [disabled]="(sBinCountry?.length > 0 || sIssuer?.length > 0 || sBinGroup?.length > 0) || !isSourceDirect">
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">Acquirer</p>
                      </div>
                      <div class="col-sm-3">
                          <p-dropdown dropdownIcon="pi pi-angle-down" [options]="acquirers" [(ngModel)]="sAcq"
                              (onChange)="getBankMerchantId()" [style]="{'width':'100%','border-color': 'black'}"
                              optionLabel="name" optionValue="value">
                          </p-dropdown>
                      </div>
                      <div class="col-sm-2">
                          <div class="row">
                              <button class="action-button col-sm-10" style="left: 20px;" (click) ="suggestAcquirerByMCC(ruleGroup.mcc)"
                              >Suggest Acquirer By MCC</button>
                          </div>
                      </div>
                      <div class="col-sm-5" style=" position: absolute; right: 0; bottom: 300px;" *ngIf = "isShowingSuggest">
                          <p-table [value]="listMerchantFeeInfoData" [scrollable]="true" scrollHeight="200px">
                            <ng-template pTemplate="caption">
                              <div class="table-header" style="display:flex;flex-direction:row-reverse">
                                <button pButton icon="pi pi-times" class="p-button-rounded p-button-text close-btn" (click)="isShowingSuggest = false"></button>
                              </div>
                              </ng-template>
                              <ng-template pTemplate="header">
                                  <tr>
                                      <th scope="col" class="text-center background-table"></th>
                                      <th scope="col" class="text-center background-table">Acquirer</th>
                                      <th scope="col" class="text-center background-table">MPGS/CBS ID</th>
                                      <th scope="col" class="text-center background-table">Bank Fix Fee (VND)</th>
                                      <th scope="col" class="text-center background-table">Bank Percent Fee (%)</th>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-data>
                                  <tr class="suggest-content">
                                      <td><button pButton pRipple class="p-mr-2" (click)="applySuggest(data)">Apply Acquirer</button></td>
                                      <td>{{ data.suggestAcquirer }}</td>
                                      <td>{{ data.suggestBankMerchantId }}</td>
                                      <td>{{ data.suggestFixFee | number }}</td>
                                      <td>{{ percentageNotDivide(data.suggestPercentFee) }}</td>
                                  </tr>
                              </ng-template>
                          </p-table>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2">
                          <p class="label  font-text">MPGS/CBS ID </p>
                      </div>
                      <div class="col-sm-3">
                          <!-- <p-dropdown dropdownIcon="pi pi-angle-down" [options]="bankIdType" [(ngModel)]="sBankIdType"
                              (onChange)="getBankMerchantId()" [style]="{'width':'20%','border-color': 'black'}"
                              optionLabel="name" optionValue="value">
                          </p-dropdown> -->
                          <p-dropdown dropdownIcon="pi pi-angle-down" [options]="bankMerchantId"
                              [(ngModel)]="sBankMerchantId"
                              [style]="{'width':'100%','border-color': 'black'}" optionLabel="name"
                              optionValue="value">
                          </p-dropdown>
                      </div>
                      <div class="col-sm-2">
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px">
                      <div class="col-sm-2 ">
                          <p class="label font-text">Description </p>
                      </div>
                      <div class="col-sm-10">
                          <textarea style="border:1px solid #ced4da; border-color:black; width:100%" rows="4"
                              [(ngModel)]="sDescription" cols="50" maxlength="4000"></textarea>
                      </div>
                  </div>

                  <div class="row" style="padding: 10px; margin-top:10px;float:right">
                      <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                          (click)="clearData()">Clear</button>
                      <button class="action-button" (click)="addRule()">Add/Update Rule</button>
                  </div>

              </div>
          </div>

      </div>
  </div>
</div>


<!--  -->
<!-- backup------------------------------------------- -->
<div class="modal fade" id="createBackUp" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="overflow:scroll">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content" style="width:100%">
          <!-- <div class="modal-header"> -->
          <div class="row" style="position:relative; padding:20px">
              <h2 class="header-top-center col-sm-8">Config Back Up</h2>
              <div class=" col-sm-4 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="closeCases">Exit</button>
                  <button class="action-button" style="margin-right: 10px; "  *ngIf="global.isActive('update_acq_config') && ruleGroup.enable" (click)="onAddBackUp()">Add</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && listConfigCases[currentCase]?.isWaitForApprovalInCase && !isChangeInConfigBackUp && !isChangeDefaultRuleGroup"
                      class="action-button " style="margin-right: 10px; background-color:red ;"
                      (click)="rejectBackUp()">Reject</button>
                  <button
                      *ngIf="global.isActive('acq_group_approve') && listConfigCases[currentCase]?.isWaitForApprovalInCase && !isChangeInConfigBackUp && !isChangeDefaultRuleGroup"
                      class="action-button " (click)="ApprovalBackUp()"
                      style="margin-right: 10px; background-color:#689F38 ;">Approval</button>
                  <button *ngIf="isChangeInConfigBackUp && !isEnableDisableDelete" class="action-button"
                      (click)="SaveBackUpConfig()">Save</button>
              </div>
          </div>
          <!-- </div> -->
          <div class="modal-body">
              <div class="row" style="padding-left: 2%;">
                  <div class="col-sm-1 font-text">Scenario</div>
                  <div class="col-sm-3">
                      <p-dropdown class="dropdown" [style]="{'width': '95%'}" [options]="listCases"
                          [(ngModel)]="currentCase" (onChange)="onChangeCase()" optionLabel="label"
                          optionValue='value'> </p-dropdown>
                  </div>
                  <div class="col-sm-2">
                      <button pButton class="action-button" (click)="configSwitchScript()" data-toggle="modal"
                          label="Config Scenario" data-target="#exampleModalCenter"></button>
                  </div>

              </div>
              <div *ngFor="let backup of listConfigCases[currentCase]?.listBackUps;trackBy:identify; let i = index">
                  <div class="row {{!backup.flgApprovalAllBackUp?'backup-default':(backup.status === 'delete'|| !backup.enable)? 'backup-delete':'backup-update'}}"
                      style="justify-content: center;">
                      <p-panel [toggleable]="true" class="panel-back-up">

                          <p-header class="d-flex panel-back-up-header">

                              <p-checkbox binary="true" name="uniqueName" inputId="binary"
                                  [(ngModel)]="backup.isCheckCaseBackUp" [value]="backup.isCheckCaseBackUp"
                                  (onChange)="selectCaseBackUp(i)" *ngIf="backup.flgApprovalAllBackUp"
                                  [style]="{'margin-left':'10px' }"></p-checkbox>
                              <div style="margin-left:10px; align-self: center;font-size:15px">{{!backup.name ||
                                  backup.name ==
                                  ''?backup.id == -1 ?"Current Config":"New Back Up":backup.name}} {{backup.id && backup.id !=-1 ? ' (BK-'+backup.id+')':''}}
                                  {{backup?.isBackUpDefault?'(DEFAULT)':''}}</div>


                              <a *ngIf="backup.status !='delete' && !isEnableDisableDelete && ruleGroup.enable" class="a-no-href color-link text-center"
                                  style="color: blue; margin-left: 10px ; font-size:15px" data-target="#compareBackUp"
                                  data-toggle="modal" (click)="prepareCompareBackUp(i)">Compare</a>

                          </p-header>


                          <ng-template pTemplate="icons">
                              <span style="align-self: center" *ngIf=" backup.status != 'delete' && ruleGroup.enable"><i
                                      class="pi pi-times" (click)="onDeleteBackUp(i)"></i>
                              </span>

                          </ng-template>

                          <div class="row" *ngIf="backup.approval">
                              <p-checkbox binary="true" inputId="binary" [style]="{'margin-left':'10px'}"
                                  [(ngModel)]="backup.isCheckDetailCaseBackUp"
                                  [value]="backup.isCheckDetailCaseBackUp"
                                  (onChange)="checkDetailCaseBackUp(i)"></p-checkbox>
                          </div>
                          <div class="row" style="padding: 10px;">
                              <div class="col-sm-1">
                                  <p class="font-text">Option</p>
                              </div>
                              <div class="col-sm-5">
                                  <p-dropdown [disabled]=" backup.status == 'delete' || !ruleGroup.enable" class="dropdown"
                                      [style]="{'width': '95%'}" [options]="backup.listOption" optionLabel="label"
                                      (onChange)="onChangeBackUpOption(i)" optionValue='value'
                                      [(ngModel)]="backup.option"> </p-dropdown>
                              </div>
                              <div class="col-sm-5"
                                  *ngIf="backup.option =='Select' && listAllBackUp && listAllBackUp.length>0">
                                  <p-dropdown [disabled]=" backup.status == 'delete' || !ruleGroup.enable" class="dropdown"
                                      [style]="{'width': '95%'}" [options]="listAllBackUp" optionLabel="label"
                                      (onChange)="onChangeBackUpSelect(i)" optionValue='value'
                                      [(ngModel)]="backup.selectedBackUp"> </p-dropdown>
                              </div>
                          </div>
                          <div>

                              <div *ngIf="backup.option!='0'">
                                  <div class="row" style="padding: 10px;">
                                      <div class="col-sm-1">
                                          <p class="label  font-text">Group Name <span
                                                  style="color: red;display: inline-flex;">(*)</span></p>
                                      </div>
                                      <div class="col-sm-10">
                                          <input [disabled]=" backup.status == 'delete' || !ruleGroup.enable" id="client_merchant_id"
                                              class="form-control h-100" pInputText maxlength="50"
                                              [(ngModel)]="backup.name" (change)="onChangeBackUpName()"
                                              (keyup)="changeValueDetailBackUp(i)">
                                      </div>
                                  </div>
                                  <div class="row" style="padding: 10px;">
                                      <div class="col-sm-1">
                                          <p class="label  font-text">Back Up Type <span
                                                  style="color: red;display: inline-flex;">(*)</span></p>
                                      </div>
                                      <div class="col-sm-10">
                                          <p-dropdown class="dropdown" [disabled]=" backup.status == 'delete' || !ruleGroup.enable"
                                              [options]="backup.listBackUpType" [(ngModel)]="backup.isBackUpDefault"
                                              optionLabel="label" optionValue='value'
                                              (onChange)="onChangeBackUpType(i)">
                                          </p-dropdown>
                                      </div>
                                  </div>
                                  <div class="row" style="padding: 10px;">
                                      <div class="col-sm-1">
                                          <p class="label  font-text">Description</p>
                                      </div>
                                      <div class="col-sm-10">
                                          <textarea [disabled]=" backup.status == 'delete' || !ruleGroup.enable"
                                              [(ngModel)]="backup.description" class="form-control h-100"
                                              style="width:100%; font-size: 12px !important" maxlength="300"
                                              (keyup)="changeValueDetailBackUp(i)" pInputTextarea>
                                </textarea>
                                      </div>
                                  </div>


                                  <div *ngIf="!backup.isBackUpDefault" class="row" style="padding: 10px;">
                                      <div class="col-sm-1 ">
                                          <p class="label font-text">MerchantID</p>
                                      </div>
                                      <div class="col-sm-10">
                                          <form class="d-flex">
                                              <p-multiSelect class="selectMerchant" #multiselect1
                                                  matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                                  matTooltipClass="my-custom-tooltip"
                                                  [disabled]=" backup.status == 'delete' || !ruleGroup.enable" [style]="{'width':'300px'}"
                                                  dropdownIcon="pi pi-angle-down" [options]="backup.listMerchantIds"
                                                  [(ngModel)]="backup.merchantIds"
                                                  (onFilter)="_filterBackUpMerchant(multiselect1,i)"
                                                  (onChange)="onChangeBackUpMerchantSelect(i,multiselect1,$event)"
                                                  [displaySelectedLabel]=false optionLabel="name" optionValue="value">
                                              </p-multiSelect>
                                          </form>
                                      </div>
                                  </div>
                                  <div class="row" *ngIf="!backup.isBackUpDefault">
                                      <div class="col-sm-1"></div>
                                      <div class="col-sm-10 scroll">
                                          <div class="row" style="margin-left: 10px;">
                                              <div class="mr-2 mb-2" *ngFor="let merchantId of backup.merchantIds">
                                                  <div *ngIf="backup.merchantIds &&backup.merchantIds.length > 0"
                                                      matTooltip="Merchant ID (MCC_PAYMETHOD_CURRENCY_CVV)_(GROUPID_GROUPNAME)"
                                                      matTooltipClass="my-custom-tooltip" class="chip">
                                                      {{merchantId}}
                                                      <span *ngIf=" backup.status != 'delete' && ruleGroup.enable"><i class="pi pi-times"
                                                              (click)="removeBackUpMerchantID(merchantId,i)"></i>
                                                      </span>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
                                  </div>
                                  <div class="row d-flex" style="margin-top: 20px">
                                      <button pButton class="action-button" data-target="#acqRuleModal"
                                          data-toggle="modal" [disabled]=" backup.status == 'delete' || !ruleGroup.enable"
                                          (click)="defaultTempDataBackUp(i)" label="Config Acquirer Rule"></button>
                                  </div>
                                  <div class="row" style="margin-top: 20px">
                                      <p-table [value]="backup.rules" [rows]="backup.rules.length" [lazy]="false"
                                          [rowHover]="true" [totalRecords]="backup.rules.length" scrollHeight="70vh"
                                          [scrollable]='true'>
                                          <ng-template pTemplate="header">
                                              <tr>
                                                  <th scope="col" class="text-center background-table">
                                                      <p-checkbox binary="true" inputId="binary"
                                                          [(ngModel)]="backup.isCheckAllRuleCaseBackUp"
                                                          [value]="backup.isCheckAllRuleCaseBackUp"
                                                          (onChange)="selectAllRuleBackUpCase(i)"></p-checkbox>
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      No
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Source
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Card Type
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Bin Country
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Issuer
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Bin Group
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Bin
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Acquirer
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      MPGS/CBS ID
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Type
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Level
                                                  </th>
                                                  <th scope="col" pSortableColumn="approval"
                                                      class="text-center background-table">
                                                      Status <p-sortIcon field="approval"></p-sortIcon>
                                                  </th>
                                                  <th scope="col" class="text-center background-table">
                                                      Update Date
                                                  </th>
                                                  <th scope="col" class="text-center background-table"
                                                      style="width: 8%;">
                                                      Action
                                                  </th>
                                              </tr>
                                          </ng-template>
                                          <ng-template pTemplate="body" let-rule let-index='rowIndex'>
                                              <tr>
                                                  <td class="text-center" *ngIf="rule.approval"
                                                      style="border-right: 1px solid lightgray !important;">
                                                      <p-checkbox binary="true" inputId="binary"
                                                          [(ngModel)]="rule.isChecked" [value]="rule.isChecked"
                                                          (onChange)="onCkeckedRuleBackUpCase(i)"></p-checkbox>
                                                  </td>
                                                  <td class="text-center" *ngIf="!rule.approval"
                                                      style="border-right: 1px solid lightgray !important;"></td>
                                                  <td class="text-center"
                                                      style="border-right: 1px solid lightgray !important;">
                                                      {{index+1}}</td>
                                                  <td class="text-center"
                                                      style="border-right: 1px solid lightgray !important;">
                                                      {{rule.source}}</td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.cardType}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.binCountry}}</td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.issuer}}</td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.binGroup}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.bin}}</td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.acquirer}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.bankMerchantId}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.type}}</td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.level}}</td>

                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;"
                                                      *ngIf="!rule.approval">{{rule.approval?'Wait
                                                      for approval':'Approved'}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto; color:#ffcd83 ;border-right: 1px solid lightgray !important;"
                                                      *ngIf="rule.approval && rule.status !== 'delete'">
                                                      {{rule.approval?'Wait for
                                                      approval':'Approved'}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto; color:#EFD6D6 ;border-right: 1px solid lightgray !important;"
                                                      *ngIf="rule.approval && rule.status === 'delete'">
                                                      {{rule.approval?'Wait for
                                                      approval':'Approved'}}
                                                  </td>
                                                  <td class="text-center"
                                                      style="overflow: auto;border-right: 1px solid lightgray !important;">
                                                      {{rule.updateDate}}
                                                  </td>
                                                  <td class="text-center" style="width: 8%;">
                                                      <button pButton pRipple title="Delete" icon="pi pi-trash"
                                                          class="p-button-rounded p-button-warning"
                                                          (click)="deleteRuleBackUp(i , index)"
                                                          [disabled]="!rule.enable ||  backup.status == 'delete' || rule.status == 'delete' || !ruleGroup.enable"></button>
                                                      <button *ngIf="rule.enable" pButton pRipple title="Disable"
                                                          [disabled]=" backup.status == 'delete'|| rule.status == 'delete' || !ruleGroup.enable"
                                                          icon="pi pi-minus" class="p-button-rounded p-button-danger"
                                                          (click)="disableRuleBackUp(backup.tmpId,i , index)">
                                                      </button>
                                                      <button *ngIf="!rule.enable" pButton pRipple title="Enable"
                                                          [disabled]=" backup.status == 'delete'|| rule.status == 'delete' || !ruleGroup.enable"
                                                          icon="pi pi-check" class="p-button-rounded p-button-success"
                                                          (click)="enableRuleBackUp(backup.tmpId,i ,index)"> </button>
                                                  </td>
                                              </tr>
                                          </ng-template>
                                          <ng-template pTemplate="emptymessage">
                                              <tr>
                                                  <td [attr.colspan]="11" class="text-center empty_results">
                                                      No Results Has Been Shown
                                                  </td>
                                              </tr>
                                          </ng-template>
                                      </p-table>
                                  </div>
                              </div>
                          </div>
                      </p-panel>
                  </div>
              </div>
          </div>
          <div class="modal-footer">

          </div>
      </div>
  </div>
</div>

<!-- Modal -->
<div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content">
          <div class="modal-body">
              <div class="wrapper" id="acq-rule-group">
                  <div style="min-height:80px;position:relative; background-color:#F6F6F6;padding-left: 50px;padding-right: 50px;"
                      class="row">
                      <div class="header-top-center col-sm-2" style="align-self: center !important;">
                          <h2 class="header-top-center">Scenario Backup</h2>
                      </div>
                      <div class="col-sm-8 d-flex" style="align-self: center;">
                          <input class="form-control" style="height:30px;margin-right: 10px;" [(ngModel)]="keyword" />
                          <button class="btn btn-primary" (click)="searchSwitchScript()"
                              style="width: 100px;margin-right: 10px;height: 30px;">
                              Search
                          </button>
                          <button class="btn btn-secondary" data-dismiss="modal"
                              style="width: 100px;margin-right: 10px;height: 30px;">
                              Exit
                          </button>
                          <button *ngIf="global.isActive('update_acq_config')" pButton class="btn btn-primary" data-toggle="modal" label="Add Scenario"
                              data-target="#addModalCenter" style="width: 200px;margin-right: 10px;height: 30px;"
                              (click)="clearSwitchScript()">
                          </button>
                      </div>
                  </div>
                  <div class="row" style="margin: 3%;">
                      <p-table [value]="switchScripts" [lazy]="true" [paginator]="false" [rows]="2"
                          [scrollable]="true" scrollHeight="37vh">
                          <ng-template pTemplate="header">
                              <tr>
                                  <th scope="col" class="text-center background-table" style="width:3%">
                                      No
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      ID
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Scenario
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Created Time
                                  </th>
                                  <th scope="col" class="text-center background-table">
                                      Action
                                  </th>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="body" let-switchScript let-i='rowIndex'>
                              <tr>
                                  <td class="text-center"
                                      style="width:3%;border-right: 1px solid lightgray !important">{{i+1}}</td>
                                  <td class="text-center" style="border-right: 1px solid lightgray !important">
                                      {{'CASE'+switchScript.id}}</td>
                                  <td class="text-center" style="border-right: 1px solid lightgray !important">
                                      {{switchScript.script}}</td>
                                  <td class="text-center" style="border-right: 1px solid lightgray !important">
                                      {{switchScript.createDate}}</td>
                                  <td class="text-center">
                                      <button *ngIf="global.isActive('update_acq_config')" pButton pRipple icon="pi pi-pencil"
                                          class="p-button-rounded p-button-success p-mr-2"
                                          (click)="showDetail(switchScript.id)" data-toggle="modal"
                                          data-target="#addModalCenter"></button>
                                      <button *ngIf="global.isActive('update_acq_config')" pButton pRipple icon="pi pi-trash"
                                          class="p-button-rounded p-button-warning"
                                          (click)="deleteGroup(switchScript.id)"></button>
                                  </td>
                              </tr>
                          </ng-template>
                          <ng-template pTemplate="emptymessage">
                              <tr>
                                  <td [attr.colspan]="9" class="text-center empty_results">
                                      No Results Has Been Shown
                                  </td>
                              </tr>
                          </ng-template>
                      </p-table>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>


<!-- Modal  Add-->
<div class="modal fade" id="addModalCenter" tabindex="-1" role="dialog" aria-labelledby="addModalCenterLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="addModalCenterLabel">{{switchScript.script == ''?'Add':'Edit'}} Scenario
              </h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body">
              <div class="form-group mb-3 row">
                  <label class="col-md-3 mb-0 d-flex">Scenario Name (*)</label>
                  <div class="col-md-6">
                      <input id="disabled-input" class="form-control h-100" pInputText
                          [(ngModel)]="switchScript.script" />
                  </div>
              </div>
              <div class="form-group mb-3 row">
                  <label class="col-md-3 mb-0 d-flex">Description</label>
                  <div class="col-md-6 textarea-wrapper">
                      <textarea pInputTextarea id="input" class="form-control" style="height: 150px;" maxlength="4000"
                          [(ngModel)]="switchScript.desc"></textarea>
                  </div>
              </div>
          </div>
          <div class="modal-footer" style="text-align: center;display: block;">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" data-dismiss="modal"
                  (click)="addSwitchScript()">Save</button>
          </div>
      </div>
  </div>
</div>

<!-- Modal History -->
<div class="modal fade" id="acq-rule-history" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Acq Rule History</h5>
          </div>
          <div class="modal-body">
              <div style="padding: 10px;">
                  <div class="row mt-3">
                      <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="fromDate" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeFromDate($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [maxDate]="toDate" [minDate]="minDate">
                              </p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">Từ ngày</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="toDate" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeToDate($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [minDate]="fromDate"></p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">Đến ngày</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group d-flex justify-content-end">
                            <button type="submit" (click) ="loadAcqRuleHistory()" pButton label="Tìm kiếm" icon="pi pi-search" iconPos="right" class="p-button-primary ml-2"></button>
                          </div>
                        </div>
                  </div>
                  <div class="row" style="margin-top: 20px; ">
                      <div class="row" style="padding: 10px;">
                          <p-table [lazy]="false" [rowHover]="true" scrollHeight="70vh" [scrollable]='true'
                              [value]="historyRules" [rows]="historyRules.length" [totalRecords]="historyRules.length">
                              <ng-template pTemplate="colgroup" let-columns>
                                  <colgroup>
                                      <col class="mat-column-no">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-lg">
                                      <col class="mat-column-common">
                                      <col class="mat-column-lg">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-lg">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                  </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header">
                                  <tr>
                                      <th scope="col" pSortableColumn="num" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          No  <p-sortIcon field="num"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="source" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Source  <p-sortIcon field="source"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="cardType" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Card Type  <p-sortIcon field="cardType"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binCountry" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Bin Country  <p-sortIcon field="binCountry"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="issuer" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Issuer  <p-sortIcon field="issuer"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Bin Group  <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="bin" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Bin  <p-sortIcon field="bin"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="acqId" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Acquirer  <p-sortIcon field="acqId"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="bankMerchantId" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          MPGS/CBS ID  <p-sortIcon field="bankMerchantId"></p-sortIcon>
                                      </th>
                                      <th scope="col" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Type
                                      </th>
                                      <th scope="col" pSortableColumn="level" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Level  <p-sortIcon field="level"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="actionName" class="text-center"
                                          style="background-color:hsl(129, 37%, 74%) ;">
                                          Action <p-sortIcon field="actionName"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="createdAt" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Updated Date  <p-sortIcon field="createdAt"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="isActivate" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Validity Status  <p-sortIcon field="isActivate"></p-sortIcon>
                                      </th>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-rule let-i='rowIndex'>
                                  <tr>
                                      <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important">
                                          {{rule.num}}</td>
                                      <td class="text-center" style="border-right: 1px solid lightgray !important">{{rule.source}}
                                      </td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.cardType}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.binCountry}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.issuer}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.binGroup}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.bin}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.acqId}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.bankMerchantId}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          2B</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.level}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          <!-- <div
                                              [ngClass]="{'edit-color': rule.approval && rule.status !== 'delete', 'delete-color' :  rule.approval && (rule.status === 'delete'|| !rule.enable),'default-color':!rule.approval }">
                                              {{rule.approval?'Wait for approval':'Approved'}}
                                          </div> -->
                                          {{rule.actionName}}
                                      </td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{rule.createdAt}}</td>
                                      <td class="text-center" [ngClass]="rule.isActivate != 1 ? 'inactive-status' : ''" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{convertTextIsActivate(rule.isActivate)}}</td>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                  <tr>
                                      <td [attr.colspan]="13" class="text-center empty_results">
                                          No Results Has Been Shown
                                      </td>
                                  </tr>
                              </ng-template>
                          </p-table>
                      </div>
                  </div>
              </div>
          </div>

      </div>
  </div>
</div>
