import { Component } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogRef } from "primeng/dynamicdialog";
import { FormArray, FormBuilder, FormControl, Validators } from '@angular/forms';
import { FormGroup } from '@angular/forms';
import { SelectItem } from 'primeng/api/selectitem';
import { SystemMaService } from '@service/system-ma.service';
import { ToastrService } from 'ngx-toastr';
@Component({
    templateUrl: './modal-partner-assign.html',
    styleUrls: ['./modal-partner-assign.css'],
    providers: [ToastrService]
})

export class ModalAssignPartnerComponent {

    // -- [ Properties ] ----------------------------------------------------------
    form: FormGroup;
    private dataUser: any;
    private dataPartner: any;
    public dataTable: Array<any>;
    public partnerSelections: SelectItem[];
    public flexScrollHeight = '200px';
    // -- [ Methods ] -------------------------------------------------------------
    constructor(
        private fb: FormBuilder,
        public config: DynamicDialogConfig,
        public ref: DynamicDialogRef,
        private toastr: ToastrService,
        private systemService: SystemMaService
    ) {
        this.dataUser = this.config.data.dataUser;
        this.dataPartner = this.config.data.dataPartner;
        this.form = this.fb.group({
            userId: new FormControl(this.dataUser?.id, Validators.compose([Validators.required])),
            partnerIds: new FormControl()
        });

        if (this.dataPartner) {
            this.partnerSelections = this.dataPartner.listPartner.map(m => {
                return { label: `${m.shortName}`, value: `${m.partnerId}` };
            });
        }

        this.systemService.getPartersByUser(this.dataUser?.id).subscribe(response => {
            let partnerAssign = response.map(item => item.partnerId);
            this.dataTable = response;
            this.form.patchValue({
                partnerIds: partnerAssign
            });
        });
    }

    changeAssignPartner (data: any) {
        let datachange = this.dataPartner.listPartner.filter(item1 =>
            data.some(item2 => item2 === item1.partnerId)
        );
        this.dataTable = datachange;
    }

    onSubmit() {
        // let partner = this.form.get('partnerIds') as FormArray;
        // if (partner.value.length > 20) {
        //     this.toastr.error('Please select Partner less than or equal to 20');
        //     return;
        // }
        if (this.form.valid) {
            this.ref.close(this.form.value);
        }
    }

    cancel() {
        this.ref.close();
    }
}

