import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PortalRoleGuard } from '@core/routerGuards/portal-guard.service';

import { RoleManagementSearchComponent } from '@module/system-management/role/role-management-search-component';
import { UserManagementSearchComponent } from '@module/system-management/user/user-management-search-component';
import { FunctionManagementSearchComponent } from './function/function-management-search-component';
import { DetailMerchantMspComponent } from './merchant-msp/detail-merchant-msp/detail-merchant-msp.component';
import { MerchantMspComponent } from './merchant-msp/list-merchant-msp/merchant-msp.component';
import { ShopifyDetailComponent } from './shopify-config/msp-merchant-detail/shopify-config-detail.component';
import { ShopifyComponent } from './shopify-config/shopify-config-search.component';
import { ShopifyApprovalComponent } from './shopify_approval/shopify-approval.component';
import { IpnListComponent } from './ipn-config/ipn-config-list/ipn-config-search.component';
import { IpnDetailComponent } from './ipn-config/ipn-config-detail/ipn-config-detail.component';
import { IpnAddEditComponent } from './ipn-config/ipn-config-addedit/ipn-config-addedit.component';
import { IpnApprovalComponent } from './ipn-config/ipn-config-approval/ipn-config-approval.component';
import { IPNRoleGuard } from '@core/routerGuards/ipn-role-guard.service';
import { IpnResendComponent } from './ipn-config/ipn-resend/list/ipn-resend-component';

import { HotSwitchQrComponent } from './hot-switch-qr/hot-switch-qr.component';
import { HistoryDefaultComponent } from './hot-switch-qr/history-default/history-default.component';
import { FileManagerComponent } from './File/File_ManagerCompoment';
import { UposConfigComponent } from './upos-config/upos-config.component';
import { DetailUposConfigComponent } from './upos-config/detail-upos-config/detail-upos-config.component';
import { HotSwitchAtmComponent } from './hot-switch-atm/hot-switch-atm.component';
import { HistoryDefaultAtmComponent } from './hot-switch-atm/history-default-atm/history-default-atm.component';
import { MenuManagementSearchComponent } from './menu/menu-management-search-component';
import { SqlExplainPlanComponent } from './sql-explain-plan/sql-explain-plan.component';
import { RoleMaSearchComponent } from './system_ma/role/role-ma-search-component';
import { UserMaSearchComponent } from './system_ma/user/user-ma-search-component';
import { PermissionSearchComponent } from './system_ma/permission/permission-ma-search-component';
import { AcqRuleGroupReviewComponent } from './acq-rule-group-review/acq-rule-group-review.component';
import { HotSwitchCyberMpgsComponent } from './Cyber-Mpgs-Management/hot-switch-cyber-mpgs/hot-switch-cyber-mpgs.component';
import { MerChantConfigRuleApprovalComponent } from './Cyber-Mpgs-Management/hot-switch-cyber-mpgs/merchant-config-rule-approval/merchant-config-rule-approval.component';
import { MerchantAcqGroupListComponent } from './merchant-acq-group-list/merchant-acq-group-list.component';
import { AcqRuleGroupListComponent } from './acq-rule-group-list/acq-rule-group-list.component';
import { AcqRuleGroupComponent } from './acq-rule-group/acq-rule-group.component';
import { HotSwitchGroupCyberMpgsComponent } from './Cyber-Mpgs-Management/hot-switch-group-cyber-mpgs/hot-switch-group-cyber-mpgs.component';
import { HotSwitchGroupHistoryComponent } from './Cyber-Mpgs-Management/hot-switch-group-history/hot-switch-group-history.component';
import { AcqRuleGroupRefreshComponent } from './acq-rule-group-refresh/acq-rule-group-refresh.component';
import { QuerySQLComponent } from './query-sql/query-sql.component';
import { IssuerComponent } from './Cyber-Mpgs-Management/issuer/issuer.component';
import { AcquirerBinGroupComponent } from './Cyber-Mpgs-Management/acquirer-bin-group/acquirer-bin-group.component';
import { MccMappingComponent} from './Cyber-Mpgs-Management/mcc-mapping/mcc-mapping.component';
import { ExceptionRuleComponent } from './Cyber-Mpgs-Management/exception-rule/exception-rule.component';
import { ExceptionRuleDetailComponent } from './Cyber-Mpgs-Management/exception-rule/exception-rule-detail/exception-rule-detail.component';
import { HotSwitchVietQrComponent } from './hot-switch-vietqr/hot-switch-vietqr.component';
import { HistoryDefaultVietQrComponent } from './hot-switch-vietqr/history-default-vietqr/history-default-vietqr.component';
import { HotSwitchPaycollectComponent } from './hot-switch-paycollect/hot-switch-paycollect.component';
import { HistoryDefaultPaycollectComponent } from './hot-switch-paycollect/history-default-paycollect/history-default-paycollect.component';
import { SwitchWhiteExlistComponent } from './switch-white-exlist/switch-white-exlist.component';

const routes: Routes = [
    {
        path: 'role',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'role_management' },
        component: RoleManagementSearchComponent
    },
    {
        path: 'user',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'user_management' },
        component: UserManagementSearchComponent
    },
    {
        path: 'func',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'function_management' },
        component: FunctionManagementSearchComponent
    },
    {
        path: 'menu',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'menu_management' },
        component: MenuManagementSearchComponent
    },
    {
        path: 'role-ma',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'role_ma' },
        component: RoleMaSearchComponent
    },
    {
        path: 'user-ma',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'user_ma' },
        component: UserMaSearchComponent
    },
    {
        path: 'permission-ma',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'permission_ma' },
        component: PermissionSearchComponent
    },
    {
        path: 'msp',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'list_merchant_msp' },
        component: MerchantMspComponent
    },
    {
        path: 'msp/create',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'create_merchant_msp' },
        component: DetailMerchantMspComponent
    },
    {
        path: 'msp/detail/:id',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'edit_merchant_msp' },
        component: DetailMerchantMspComponent
    },
    {
        path: 'ipn-view',
        canActivate: [IPNRoleGuard, PortalRoleGuard],
        data: { functionName: 'ipn_config_view' },
        component: IpnListComponent
    },
    {
        path: 'ipn-add',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'ipn_config_update' },
        component: IpnAddEditComponent
    },
    {
        path: 'ipn-approve',
        canActivate: [IPNRoleGuard, PortalRoleGuard],
        data: { functionName: 'ipn_config_approve' },
        component: IpnApprovalComponent
    },
    {
        path: 'ipn-edit/:merchant/:service',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'ipn_config_update' },
        component: IpnAddEditComponent
    },
    {
        path: 'ipn-detail/:merchant/:service',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'ipn_config_view' },
        component: IpnDetailComponent
    },

    {
        path: 'ipn-resend',
        canActivate: [IPNRoleGuard, PortalRoleGuard],
        data: { functionName: 'ipn_resend' },
        component: IpnResendComponent
    },

    {
        path: 'shopify-config',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'shopify_config' },
        component: ShopifyComponent
    },
    {
        path: 'shopify-approval',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'shopify_approve' },
        component: ShopifyApprovalComponent
    },
    // {
    //     path: 'msp',
    //     canActivate: [PortalRoleGuard],
    //     data: { functionName: 'list_merchant_msp' },
    //     component: MerchantMspComponent
    // }
    // { path: '', component: ShopifyComponent },
    { path: ':shopifyDomain/:shopifyAppId', component: ShopifyDetailComponent },
    { path: '/shopify-approval', component: ShopifyApprovalComponent },
    {
        path: 'hot-switch-qr',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'hot_switch_qr' },
        component: HotSwitchQrComponent
    },
    {
        path: 'history-default',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'history_default' },
        component: HistoryDefaultComponent
    },
    {
        path: 'hot-switch-atm',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'hot_switch_atm' },
        component: HotSwitchAtmComponent
    },
    {
        path: 'history-default-atm',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'history_default_atm' },
        component: HistoryDefaultAtmComponent
    },
    {
        path: 'file-management',
        canActivate: [PortalRoleGuard],
        // data: { functionName: 'shopify_approve' },
        component: FileManagerComponent
    },
    {
      path: 'acq-rule-group',
      canActivate: [PortalRoleGuard],
      component: AcqRuleGroupRefreshComponent
    },
    { path: 'upos-config', component: UposConfigComponent },
    { path: 'upos-config/detail/:id', component: DetailUposConfigComponent },
    {
      path: 'hot-switch-cyber-mpgs',
      canActivate: [PortalRoleGuard],
      data: { functionName: 'hot_switch_cyber_mpgs' },
      component: HotSwitchCyberMpgsComponent
  },{
      path: 'merchant-config-rule-approval',
      canActivate: [PortalRoleGuard],
      // data: { functionName: 'ipn_config_approve' },
      component: MerChantConfigRuleApprovalComponent
  },
  {
      path: 'merchant-acq-group-config',
      canActivate: [PortalRoleGuard],
      component: MerchantAcqGroupListComponent
  },
  {
      path: 'acq-group-config',
      canActivate: [PortalRoleGuard],
      data: { functionName: 'view_acq_group' },
      component: AcqRuleGroupListComponent
  },{
      path: 'acq-group-config/detail/:id',
      canActivate: [PortalRoleGuard],
      data: { functionName: 'update_acq_config' },
      component: AcqRuleGroupReviewComponent
  },{
      path: 'acq-group-config/review/:approvalId',
      canActivate: [PortalRoleGuard],
      // data: { functionName: 'shopify_approve' },
      component: AcqRuleGroupReviewComponent
  }
  ,{
      path: 'acq-group-config',
      canActivate: [PortalRoleGuard],
      // data: { functionName: 'new-acq-group' },
      component: AcqRuleGroupComponent
  },
    {
      path:'sql-explain-plan',
      canActivate: [PortalRoleGuard],
      data: { functionName: 'sql_explain_plan' },
      component: SqlExplainPlanComponent
    },
    {
      path: 'hot-switch-group-cyber-mpgs',
      canActivate: [PortalRoleGuard],
      // data: { functionName: 'hot_switch_cyber_mpgs' },
      component: HotSwitchGroupCyberMpgsComponent
    },
    {
      path: 'hot-switch-group-history',
      canActivate: [PortalRoleGuard],
      // data: { functionName: 'hot_switch_cyber_mpgs' },
      component: HotSwitchGroupHistoryComponent
    },
    {
      path:'query-sql',
      canActivate: [PortalRoleGuard],
      data: { functionName: 'query_sql' },
      component: QuerySQLComponent
    },
    {
      path: 'issuers',
      canActivate: [PortalRoleGuard],
      component: IssuerComponent
    },
    {
      path: 'bin-groups',
      canActivate: [PortalRoleGuard],
      component: AcquirerBinGroupComponent
    },
    {
      path: 'mcc-mapping',
      canActivate: [PortalRoleGuard],
      component: MccMappingComponent
    },
    {
      path: 'exception-rule',
      canActivate: [PortalRoleGuard],
      component: ExceptionRuleComponent
    },
    {
      path: 'exception-rule/:id',
      canActivate: [PortalRoleGuard],
      component: ExceptionRuleDetailComponent
    },
    {
        path: 'hot-switch-vietqr',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'hot_switch_vietqr' },
        component: HotSwitchVietQrComponent
    },
    {
        path: 'history-default-vietqr',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'history_default_vietqr' },
        component: HistoryDefaultVietQrComponent
    },
    {
        path: 'hot-switch-paycollect',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'hot_switch_paycollect' },
        component: HotSwitchPaycollectComponent
    },
    {
        path: 'history-default-paycollect',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'history_default_paycollect' },
        component: HistoryDefaultPaycollectComponent
    },
    {
        path: 'switch-white-exlist',
        canActivate: [PortalRoleGuard],
        data: { functionName: 'switch_white_exlist' },
        component: SwitchWhiteExlistComponent
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [
        RouterModule
    ]
})

export class SystemRoutingModule { }
