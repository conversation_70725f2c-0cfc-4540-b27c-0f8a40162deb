import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { ToastrService } from 'ngx-toastr';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subscription } from 'rxjs';
// Add Acquire Rull
import { AcquirerRuleService } from '@service/acquirer-rule.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { Location } from '@angular/common';
import { HttpParams } from '@angular/common/http';
@Component({
  selector: 'app-acq-rule-group',
  templateUrl: './acq-rule-group.component.html',
  styleUrls: ['./acq-rule-group.component.css'],
  providers: [DialogService],
})
export class AcqRuleGroupComponent implements OnInit {
  public ruleGroup = {
    id: null,
    approvalId: null,
    name: "",
    type: "Default",
    description: "",
    merchantIds: [],
    merchantIdsText: "",
    rules: [],
    isEditable: true,
    isReview: false,
    backUps: [],
    case: null,
    isBackUpDefault: null,
  }
  public listGroupAcqBackUp = [];
  isAllMerchant = false;
  public tmpSelectedMerchants = [];
  public tmpSelectedMerchantsBackUp = [];
  public checked: boolean = false;
  public checkSetRunningDefault: boolean = false;
  public visibleListQr: boolean;
  public visibleNotice: boolean;
  public visibleSetDefault: boolean;
  public ref: DynamicDialogRef;
  public qrTypeData: string[];
  public switchQrType: string;
  public loading: boolean;
  public flexScrollHeight = '300px';
  public keyword = '';
  public contractCode = '';
  public qrTypeValue = '';
  public service: Array<string>;
  public selectedData: any[] = [];
  public page = 0;
  public first = 0;
  public tableDataTotal: number;
  public subscription: Subscription;
  public visibleConfirmSave: boolean;
  public visibleConfirmDelete: boolean;
  public visibleConfirmMerchantId: boolean;

  public typeOpt = {
    label: 'Type', type: 'dropdown',
    options: [
      { label: 'Default', value: 'Default' },
    ]
  };
  public deleteMerchantId = "";
  control = new FormControl();
  listMerchantIds = [];
  filteredMerchantIds = [];
  // public filteredMerchant = ["g356y54y5tyv3554t", "fgt4ghytrghtrhgyju"]
  public resultsLength: number;
  public data: Array<any>;
  public pageSize: string;
  public sub: Subscription;
  public merchantSearchText: "";
  public name1: "";
  public deleteRuleIdx: -1;
  public listFullMerchant = [];

  //Add Acquirer Rule
  description: string;
  level: string;
  sources = [];
  selectedSource: string;
  binCountries = [];
  selectedCountry: string;
  issuers = [];
  public selectedIssuer;
  binGroups = [];
  public selectedBinGroup;
  acquirers = [];
  public selectedAcquirer;
  bankMerchantIds = [];
  bankMerchantIdByAcq = [];
  public selectedBankMerchantId;
  isCheckVisa: boolean = false;
  isCheckMC: boolean = false;
  isCheckJCB: boolean = false;
  isCheckAmex: boolean = false;
  bin: string;
  flgBinCountry: boolean = false;
  flgIssuer: boolean = false;
  flgBinGroup: boolean = false;
  flgBin: boolean = false;
  flgCheckEditAcquirerRule: boolean = false;
  indexAcqRuleEdit: string;
  public visibleListMerchantIds = false;
  public merchantIdsData: [];
  // Add Acquirer Rule Group Back Up
  public listBackUpMerOpt = [];
  public selectedOption: any;
  public selectedBackUpType: any;
  public selectedCase: any;
  public groupNameBackUp: any;
  public listMidsBackUp: any;
  public descBackUp: any;
  public flgEmpty: boolean;
  public flgOptionSelect: boolean;
  public flgBackUpTypeDf: boolean;
  public flgEditAcqRuleGroupBackUp: boolean;
  public selectedGroupBackUpExist: any;
  public idEditAcqRuleGroupBackup: any;
  public ruleGroupBackUp = {
    option: [{ label: '', value: '' }, { label: 'New', value: 'New' }, { label: 'Select', value: 'Select' }],
    backUpType: [{ label: 'Default', value: 'Default' }, { label: 'Exception', value: 'Exception' }],
    case: [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }, { label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
    groupName: "",
    description: "",
    merchantIds: [],
    rules: [],
    groupBackUpExist: []
  }
  //
  constructor(
    public dialogService: DialogService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    public global: Globals,
    private _location: Location,
    //Add Acquirer Rule
    public acquirerRuleService: AcquirerRuleService,
    private confirmService: ConfirmService
  ) { }
  ngOnInit(): void {
    this.ruleGroup.id = this.route.snapshot.paramMap.get("id") ?? null;
    this.ruleGroup.approvalId = this.route.snapshot.paramMap.get("approvalId") ?? null;
    this.sub = this.route
      .queryParams
      .subscribe(params => {
        this.init(params);
      });
    this.getListMerchantID();
    this.getInfoConfigAcq();
    this.ruleGroup.merchantIds = [];
  }

  merchantIdsDialog(data: any) {
    this.merchantIdsData = data
    this.visibleListMerchantIds = true
  }

  defaultRuleGroupBackUp(): void {
    this.ruleGroupBackUp.merchantIds = this.getListMidsBackUp(this.ruleGroup.merchantIds);
    this.listMidsBackUp = [];
    this.ruleGroupBackUp.description = "";
    this.flgEmpty = true;
    this.flgBackUpTypeDf = true;
    this.flgOptionSelect = false;
    this.flgEditAcqRuleGroupBackUp = false;
    this.selectedOption = '';
    this.selectedBackUpType = 'Default';
    this.selectedCase = '1';
    this.groupNameBackUp = '';
    this.descBackUp = '';
    this.ruleGroupBackUp.rules = [];
    this.ruleGroupBackUp.groupBackUpExist = [];
  }
  changeOption(): void {
    if (this.selectedOption !== '') {
      this.flgEmpty = false;
    } else {
      this.flgEmpty = true;
    }
    if (this.selectedOption === 'Select') {
      this.flgOptionSelect = true;
    } else {
      this.flgOptionSelect = false;
    }

    if (this.selectedOption === 'Select') {
      this.ruleGroupBackUp.groupBackUpExist = [{ label: 'Default', value: 'Default' }];
      this.ruleGroup.backUps.forEach(element => {
        if (element.option !== '')
          this.ruleGroupBackUp.groupBackUpExist.push({ label: element['name'], value: element['name'] });
      })
      this.selectedGroupBackUpExist = 'Default';
      this.ruleGroupBackUp.rules = this.ruleGroup.rules;
    } else if (this.selectedOption === 'New') {
      this.selectedBackUpType = 'Default';
      this.flgBackUpTypeDf = true;
      this.selectedCase = '1';
      this.groupNameBackUp = '';
      this.descBackUp = '';
      this.listMidsBackUp = []
      this.ruleGroupBackUp.rules = [];
      this.flgEmpty = false;
    } else {
      this.selectedBackUpType = 'Default';
      this.flgBackUpTypeDf = true;
      this.selectedCase = '1';
      this.groupNameBackUp = '';
      this.descBackUp = '';
      this.listMidsBackUp = []
      this.ruleGroupBackUp.rules = this.ruleGroup.rules;
      this.flgEmpty = true;
    }
  }
  changeSelectCase(): void {
    if(this.idEditAcqRuleGroupBackup!== null){
      this.getMidsEdit();
    }else{
      if(this.selectedBackUpType === 'Exception'){
        let mids = [];
        this.listBackUpMerOpt = this.getListMidsBackUp(this.ruleGroup.merchantIds);
        this.ruleGroup.backUps.forEach(element => {
          if (this.selectedCase === element.case && !element.isBackUpDefault) {
            element.merchantIds.forEach(m =>{
              mids.push(m);
            })
          }
        })
        this.listBackUpMerOpt = this.listBackUpMerOpt.filter(i=>{
          return !mids.includes(i.value);
        })
      }
    }
  }

  removeMerchantIdBackUpGroup(id: String) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to remove this merchant ID ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && id) {

          var index = this.listMidsBackUp.indexOf(id);
          if (index !== -1) {
            this.listMidsBackUp.splice(index, 1);
          }

        }
      })
  }

  changeSelectBackUpExist(): void {
    if (this.ruleGroupBackUp.groupBackUpExist.length > 0) {
      if (this.selectedGroupBackUpExist === 'Default') {
        this.ruleGroupBackUp.description = "";
        this.flgEmpty = false;
        this.selectedBackUpType = 'Default';
        this.selectedCase = '1';
        this.groupNameBackUp = '';
        this.descBackUp = '';
        this.flgBackUpTypeDf = true;
        this.listMidsBackUp = [];
        this.ruleGroupBackUp.rules = this.ruleGroup.rules;
      } else {
        this.ruleGroup.backUps.forEach(element => {
          if (this.selectedGroupBackUpExist === element['name']) {
            this.flgEmpty = false;
            this.selectedBackUpType = 'Default';
            this.selectedCase = '1';
            this.groupNameBackUp = '';
            this.descBackUp = '';
            this.flgBackUpTypeDf = true;
            this.listMidsBackUp = [];
            this.ruleGroupBackUp.rules = element.rules;
          }
        })
      }
    }
  }

  changeBackUpType(): void {
    if (this.selectedBackUpType !== 'Default') {
      this.flgBackUpTypeDf = false;
    } else {
      this.flgBackUpTypeDf = true;
    }

    if (!this.flgBackUpTypeDf) {
      if(this.idEditAcqRuleGroupBackup!== null){
        this.getMidsEdit();
      }else{
        this.changeSelectCase();
      }
    }
  }

  getMidsEdit(){
    let mids = [];
    this.listBackUpMerOpt = this.getListMidsBackUp(this.ruleGroup.merchantIds);
    for(let i = 0 ; i<  this.ruleGroup.backUps.length;i++){
        if (this.selectedCase === this.ruleGroup.backUps[i].case && !this.ruleGroup.backUps[i].isBackUpDefault && i !== Number(this.idEditAcqRuleGroupBackup)) {
          this.ruleGroup.backUps[i].merchantIds.forEach(m =>{
            mids.push(m);
          })
        }
      }
      this.listBackUpMerOpt = this.listBackUpMerOpt.filter(i=>{
        return !mids.includes(i.value);
      })
  }

  editAcqRuleGroupBackUp(idx: string) {
    this.idEditAcqRuleGroupBackup = idx;
    this.flgEditAcqRuleGroupBackUp = true;
    if (!this.ruleGroup.id) {
      if (this.ruleGroup.backUps[idx].option !== '') {
        this.flgEmpty = false;
        this.flgOptionSelect = this.ruleGroup.backUps[idx].option === 'Select' ? true : false;
        this.selectedOption = this.ruleGroup.backUps[idx].option;
        this.selectedBackUpType = this.ruleGroup.backUps[idx].isBackUpDefault ? 'Default' : 'Exception';
        this.flgBackUpTypeDf = this.ruleGroup.backUps[idx].isBackUpDefault
        this.selectedCase = this.ruleGroup.backUps[idx].case;
        this.groupNameBackUp = this.ruleGroup.backUps[idx].name;
        this.descBackUp = this.ruleGroup.backUps[idx].description;
        this.ruleGroupBackUp.rules = this.ruleGroup.backUps[idx].rules;
        this.listMidsBackUp = this.ruleGroup.backUps[idx].merchantIds
      } else {
        this.selectedOption = this.ruleGroup.backUps[idx].option;
        this.selectedCase = this.ruleGroup.backUps[idx].case;
        this.ruleGroupBackUp.rules = this.ruleGroup.rules;
        this.flgEmpty = true;
      }
    } else {
      if (this.ruleGroup.backUps[idx].id === this.ruleGroup.id) {
        this.flgEmpty = true;
        this.flgOptionSelect = false;
        this.selectedOption = '';
        this.selectedCase = this.ruleGroup.backUps[idx].case;
      } else {
        this.flgEmpty = false;
        this.flgOptionSelect = false;
        this.selectedOption = 'New';
        this.selectedBackUpType = this.ruleGroup.backUps[idx].isBackUpDefault ? 'Default' : 'Exception';
        this.flgBackUpTypeDf = this.ruleGroup.backUps[idx].isBackUpDefault
        this.selectedCase = this.ruleGroup.backUps[idx].case;
        this.groupNameBackUp = this.ruleGroup.backUps[idx].name;
        this.descBackUp = this.ruleGroup.backUps[idx].description;
        this.ruleGroupBackUp.rules = this.ruleGroup.backUps[idx].rules;
        this.listMidsBackUp = this.ruleGroup.backUps[idx].merchantIds
      }
    }
    this.getMidsEdit();
  }

  getListMidsBackUp(merchantIds: any) {
    let list = [];
    for (var i = 0; i < merchantIds.length; i++) {
      for (var j = 0; j < this.listFullMerchant.length; j++) {
        let e = this.listFullMerchant[j];
        if (merchantIds[i] === e.value) {
          list.push(e);
        }
      }
    }
    return list
  }
  submitAcquirerGroupBackUp(): void {
    if (this.selectedOption === '') {
      this.groupNameBackUp = 'Current Config';
      this.selectedBackUpType = 'Default';
      this.ruleGroupBackUp.rules = this.ruleGroup.rules;
    }
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to save this Acquirer Rule Group Back Up?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          if (this.validateAcqGroupBackUp(this.ruleGroup.backUps)) {
            this.ruleGroup.backUps.push({ option: this.selectedOption, id: null, case: this.selectedCase, name: this.groupNameBackUp, rules: this.ruleGroupBackUp.rules, isBackUpDefault: (this.selectedBackUpType === 'Default') ? true : false, description: this.descBackUp, merchantIds: this.listMidsBackUp, merchantIdsText: this.getShortenText(this.listMidsBackUp, 10) ?? "" });
            window.document.getElementById("closePopUpGroupBackUp").click();
          }
        }
      })
  }

  editAcquirerGroupBackUp(): void {
    if (this.selectedOption === '') {
      this.groupNameBackUp = 'Current Config';
      this.selectedBackUpType = 'Default';
      this.ruleGroupBackUp.rules = this.ruleGroup.rules;
    }
    let acqGroupBackUp = [];
    for (var i = 0; i < this.ruleGroup.backUps.length; i++) {
      if (i !== this.idEditAcqRuleGroupBackup) {
        acqGroupBackUp.push(this.ruleGroup.backUps[i]);
      }
    }
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to save this Acquirer Rule Group Back Up?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {

          if (this.validateAcqGroupBackUp(acqGroupBackUp)) {
            this.ruleGroup.backUps[this.idEditAcqRuleGroupBackup] = ({ option: this.selectedOption, id: this.ruleGroup.backUps[this.idEditAcqRuleGroupBackup]['id'] ? this.ruleGroup.backUps[this.idEditAcqRuleGroupBackup]['id'] : null, case: this.selectedCase, name: this.groupNameBackUp, rules: this.ruleGroupBackUp.rules, isBackUpDefault: (this.selectedBackUpType === 'Default') ? true : false, description: this.descBackUp, merchantIds: this.listMidsBackUp, merchantIdsText: this.getShortenText(this.listMidsBackUp, 10) ?? "" });
            window.document.getElementById("closePopUpGroupBackUp").click();
          }
        }
      })
  }
  validateAcqGroupBackUp(ruleGroup: any) {
    var result = true;
    if (this.groupNameBackUp.length === 0) {
      this.toastr.error('Group name is required', 'Error');
      return false;
    }
    if (this.selectedOption !== '' && this.ruleGroupBackUp.rules.length == 0) {
      this.toastr.error('Rule is required', 'Error');
      return false;
    }
    if (this.groupNameBackUp === this.ruleGroup.name) {
      this.toastr.error('Group name is existed', 'Error');
      result = false;
      return;
    }
    ruleGroup.forEach(element => {
      if (this.groupNameBackUp === element.name && this.selectedOption !== '') {
        this.toastr.error('Group name is existed', 'Error');
        result = false;
        return;
      }
      if (element.case === this.selectedCase) {
        if (this.selectedBackUpType === 'Default' && element.isBackUpDefault) {
          this.toastr.error('Kịch bản : ' + element.case + ' group default is existed', 'Error');
          result = false;
          return;
        }
      }
    })
    if (this.selectedOption !== '') {
      let checkRule = false;
      let checkCardType = '';
      this.ruleGroupBackUp.rules.forEach(rule => {
        if (rule.level === '5' && rule.source === 'Card') {
          checkRule = true;
          checkCardType += rule.cardType;
        }
      })
      if (!checkRule) {
        this.toastr.error('Acquirer rule default is missing config');
        return false;
      }
      if (checkRule && checkCardType.length > 0) {
        let msg = '';
        if (!checkCardType.includes('Visa')) {
          msg = msg + 'Visa ';
        }
        if (!checkCardType.includes('Mastercard')) {
          msg = msg + 'Mastercard ';
        }
        if (!checkCardType.includes('Jcb')) {
          msg = msg + 'Jcb ';
        }
        if (!checkCardType.includes('Amex')) {
          msg = msg + 'Amex ';
        }
        if (msg.length > 0) {
          this.toastr.error('Card Type ' + msg + 'default is missing config');
          return false;
        }
      }
    }
    return result;
  }

  submitAcquirerRuleBackUp() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to save this Acquirer Rule?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          this.calculateLevel();
          let messageValidate = this.validateAcquirerRule(this.ruleGroupBackUp.rules);
          if (messageValidate) {
            this.toastr.error(messageValidate, 'Error');
          } else {
            this.addAcqRuleToRuleGroupBackUp();
            this.toastr.success('Saved successfully.');
            window.document.getElementById("closePopUp").click();
          }
        }
      })
  }

  editAcquirerRuleBackUp() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to edit this Acquirer Rule?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          this.calculateLevel();
          let messageValidate = this.validateAcquirerRule(this.ruleGroupBackUp.rules);
          if (messageValidate) {
            this.toastr.error(messageValidate, 'Error');
          } else {
            this.editAcqRuleToRuleGroupBackUp();
            this.indexAcqRuleEdit = '';
            this.toastr.success('Saved successfully.');
            window.document.getElementById("closePopUp").click();
          }
        }
      })

  }

  editAcqRuleBackUp(idx: string) {
    this.clearValue();
    this.indexAcqRuleEdit = idx;
    this.flgCheckEditAcquirerRule = true;
    this.selectedSource = this.ruleGroupBackUp.rules[idx]['source'];
    //CardType
    this.ruleGroupBackUp.rules[idx]['cardType'].split("|").forEach(element => {
      if (element === 'Visa') this.isCheckVisa = true;
      if (element === 'Mastercard') this.isCheckMC = true;
      if (element === 'Jcb') this.isCheckJCB = true;
      if (element === 'Amex') this.isCheckAmex = true;
    })
    //BinCountry
    this.selectedCountry = this.ruleGroupBackUp.rules[idx]['binCountry'];
    //BinGroup
    if (this.ruleGroupBackUp.rules[idx]['binGroup']) {
      let binGr = [];
      this.ruleGroupBackUp.rules[idx]['binGroup'].split("|").forEach(element => {
        binGr.push(element);
      })
      this.selectedBinGroup = binGr;
      this.flgBin = true;
      this.flgBinCountry = true;
      this.flgIssuer = true;
      this.flgBinGroup = false;
    }
    //Issuer
    if (this.ruleGroupBackUp.rules[idx]['issuer']) {
      let issuer = [];
      this.ruleGroupBackUp.rules[idx]['issuer'].split("|").forEach(element => {
        issuer.push(element);
      })
      this.selectedIssuer = issuer;
      this.flgBin = true;
      this.flgBinCountry = true;
      this.flgIssuer = false;
      this.flgBinGroup = true;
    }
    //Bin
    if (this.ruleGroupBackUp.rules[idx]['bin']) {
      this.bin = this.ruleGroupBackUp.rules[idx]['bin'];
      this.flgBin = false;
      this.flgBinCountry = true;
      this.flgIssuer = true;
      this.flgBinGroup = true;
    }

    //Acquirer Id
    this.selectedAcquirer = this.ruleGroupBackUp.rules[idx]['acquirer'];

    this.getBankId(this.selectedAcquirer);
    //Bank Merchant Id
    this.selectedBankMerchantId = this.ruleGroupBackUp.rules[idx]["bankMerchantId"];

    //Description
    this.description = this.ruleGroupBackUp.rules[idx]['description'];
  }



  deleteAcqRuleBackUp(idx) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Rule ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && idx >= 0) {
          this.ruleGroupBackUp.rules.splice(idx, 1);

        }
      })
  }


  addAcqRuleToRuleGroupBackUp() {
    let iss = this.createIssuer();
    let cardType = this.createCardType();
    let binGroup = this.createBinGroup();
    this.ruleGroupBackUp.rules.push({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'new' });
  }
  editAcqRuleToRuleGroupBackUp() {
    let iss = this.createIssuer();
    let cardType = this.createCardType();
    let binGroup = this.createBinGroup();
    let rule = this.ruleGroupBackUp.rules[this.indexAcqRuleEdit];
    if (rule['status'] === 'new') {
      this.ruleGroupBackUp.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'new' });
    } else if (rule['acquirer'] === this.selectedAcquirer && rule['bin'] === this.bin && rule['binCountry'] === this.selectedCountry && rule['binGroup'] === binGroup && rule['cardType'] === cardType && rule['issuer'] === iss && rule['source'] === this.selectedSource) {
      this.ruleGroupBackUp.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'exist' });
    } else {
      this.ruleGroupBackUp.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'update' });
    }
  }

  private getAcqRuleGroupByApprovalId() {
    if (!this.ruleGroup.approvalId) {
      return null;
    }
    this.acquirerRuleService.GetAcqRuleGroupByApprovalId(this.ruleGroup.approvalId).subscribe(res => {
      if (!res) {
        return null;
      };
      this.setAcqRuleGroupFromApproval(res);
    });
  }

  private onCheckAllMerchant() {
    if (this.isAllMerchant) {
      this.listMerchantIds = this.listFullMerchant
    } else {
      this.listMerchantIds = this.listFullMerchant.filter(merchant => !merchant.acqGroupId)
    }

  }
  private setAcqRuleGroupFromApproval(res: any) {
    if (res.type !== 'DELETE') {
      this.ruleGroup.id = res.newValue.id
      this.ruleGroup.name = res.newValue.groupName ?? "";
      this.ruleGroup.description = res.newValue.description ?? "";
      this.ruleGroup.type = res.newValue.type;
      this.ruleGroup.merchantIds = res.newValue.merchantIds ?? [];
      this.ruleGroup.rules = [];
      this.ruleGroup.isEditable = false;
      this.ruleGroup.isReview = true;
      if (res?.newValue.rules && res?.newValue.rules[0]) {
        res.newValue.rules.forEach(rule => this.ruleGroup.rules.push({
          id: rule?.id,
          source: rule?.source,
          cardType: rule.cardTypes ? rule.cardTypes.join("|") : "",
          acquirer: rule.acquirer ?? "",
          issuer: rule.issuers ? rule.issuers.join("|") : "",
          binGroup: rule.binGroups ? rule.binGroups.join("|") : "",
          bin: rule.bins ? rule.bins.join("|") : "",
          level: rule.level ?? "",
          binCountry: rule.binCountry ?? null,
          bankMerchantId: rule.bankMerchantId ?? null,
          status: rule.status ?? null
        }));
      }
    } else {
      this.ruleGroup.id = res.oldValue.id
      this.ruleGroup.name = res.oldValue.groupName ?? "";
      this.ruleGroup.description = res.oldValue.description ?? "";
      this.ruleGroup.type = res.oldValue.type;
      this.ruleGroup.merchantIds = res.oldValue.merchantIds ?? [];
      this.ruleGroup.rules = [];
      this.ruleGroup.isEditable = false;
      this.ruleGroup.isReview = true;
      if (res?.oldValue.rules && res?.oldValue.rules[0]) {
        res.oldValue.rules.forEach(rule => this.ruleGroup.rules.push({
          id: rule?.id,
          source: rule?.source,
          cardType: rule.cardTypes ? rule.cardTypes.join("|") : "",
          acquirer: rule.acquirer ?? "",
          issuer: rule.issuers ? rule.issuers.join("|") : "",
          binGroup: rule.binGroups ? rule.binGroups.join("|") : "",
          bin: rule.bins ? rule.bins.join("|") : "",
          level: rule.level ?? "",
          binCountry: rule.binCountry ?? null,
          bankMerchantId: rule.bankMerchantId ?? null,
          status: rule.status ?? null
        }));
      }
    }

  }

  reject(): void {
    const message = 'Are you sure want to reject this request?';
    this.confirmService.build()
      .message(message)
      .title('Reject!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          const body = {
            id: Number(this.ruleGroup.approvalId),
            path: '/reject'
          };
          return this.acquirerRuleService.patchReject(body).subscribe(data => {
            this.toastr.success('Successful', "Reject");
            this.router.navigate(['/system-management/acq-group-config']);
          });
        }
      });
  }

  approval(): void {
    const message = 'Are you sure want to approval this request?';
    this.confirmService.build()
      .message(message)
      .title('Approval!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          const body = {
            id: Number(this.ruleGroup.approvalId),
            path: '/approve'
          };
          return this.acquirerRuleService.patchApproval(body).subscribe(data => {
            this.toastr.success('Successful', "Approval");
            this.router.navigate(['/system-management/acq-group-config']);
          });
        }
      });
  }


  private getAcqRuleGroupById() {
    if (!this.ruleGroup.id) {
      return null;
    }
    this.acquirerRuleService.GetAcqRuleGroup(this.ruleGroup.id, true).subscribe(res => {
      if (!res || !res.id) {
        return null;
      };
      this.setFullAcqGroupFromResponse(res);
    });
  }
  private setFullAcqGroupFromResponse(res) {
    this.ruleGroup = this.setAcqRuleGroupFromResponse(res);
    if (res.back_ups && res.back_ups.length > 0) {
      res.back_ups.forEach((b) => {
        this.ruleGroup.backUps.push(this.setAcqRuleGroupFromResponse(b))
      })
    }
  }
  private setAcqRuleGroupFromResponse(res: any) {
    let ruleGroup = {
      id: null,
      approvalId: null,
      name: "",
      type: "Default",
      description: "",
      merchantIds: [],
      merchantIdsText: "",
      rules: [],
      isEditable: true,
      isReview: false,
      backUps: [],
      case: null,
      isBackUpDefault: null,
    }
    ruleGroup.id = res.id
    ruleGroup.name = res.group_name ?? "";
    ruleGroup.description = res.description ?? "";
    ruleGroup.type = res.type;
    ruleGroup.merchantIds = res.merchant_ids ?? [];
    ruleGroup.rules = [];
    ruleGroup.isEditable = res.is_editable ?? true;
    if (res.case && res.case?.length > 0) {
      ruleGroup.merchantIdsText = this.getShortenText(ruleGroup.merchantIds, 10) ?? "";

      ruleGroup.case = res.case
      ruleGroup.isBackUpDefault = res.is_back_up_default ?? false;
    } else {
      if (ruleGroup.merchantIds.length > 0) {
        // get list of merchant backup options
        this.listBackUpMerOpt = this.getListBackUpMerOptions(ruleGroup.merchantIds)
      }
    }
    if (res?.rules && res?.rules[0]) {
      res.rules.forEach(rule => ruleGroup.rules.push({
        id: rule?.id,
        source: rule?.source,
        cardType: rule.card_types ? rule.card_types.join("|") : "",
        acquirer: rule.acquirer ?? "",
        issuer: rule.issuers ? rule.issuers.join("|") : "",
        binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
        bin: rule.bins ? rule.bins.join("|") : "",
        level: rule.level ?? "",
        binCountry: rule.bin_country ?? null,
        bankMerchantId: rule.bank_merchant_id ?? null,
        status: 'exist'
      }));
    }
    return ruleGroup;
  }

  getListBackUpMerOptions(list) {
    console.log(this.listFullMerchant.filter((i) => {
      return list.includes(i.value)
    }));
    return this.listFullMerchant.filter((i) => {
      return list.includes(i.value)
    });
  }
  getShortenText(list, max) {
    if (list == null && list.length == 0) {
      return "";
    }
    let s = list.toString();
    if (s.length <= max) {
      return s;
    }
    return s.slice(0, max) + "...";
  }
  private _onSelectMerchant(value, event) {
    if (this.ruleGroup.merchantIds.length > this.tmpSelectedMerchants.length || (!event.itemValue && event.value && event.value.length > 0 && event.value == this.ruleGroup.merchantIds)) {
      this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
    }


    if (event.itemValue && !this.ruleGroup.merchantIds.includes(event.itemValue) && this.listMerchantIds.length > 0) {
      //delete case
      this.confirmService.build()
        .message(`<div class='content-confirm'> Are you sure to remove this merchant ID ?</div><div>(Merchant ID config in acquirer group backup can be changed)</div>`)
        .title('Notice!')
        .yes('Yes')
        .no('No')
        .confirm().subscribe(result => {
          if (!result) { this.ruleGroup.merchantIds = this.tmpSelectedMerchants } else {
            this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
          };
        })
    }

    if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
      //delete case
      this.confirmService.build()
        .message(`<div class='content-confirm'> Are you sure to remove this merchant ID ?</div><div>(Merchant ID config in acquirer group backup can be changed)</div>`)
        .title('Notice!')
        .yes('Yes')
        .no('No')
        .confirm().subscribe(result => {
          if (!result) { this.ruleGroup.merchantIds = this.tmpSelectedMerchants } else {
            this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
          };
        })
    }
    this.listBackUpMerOpt = this.getListBackUpMerOptions(this.ruleGroup.merchantIds);
    this.resetBackUpMerchants();
  }

  private resetBackUpMerchants() {
    if (this.ruleGroup.backUps && this.ruleGroup.backUps.length > 0) {
      for (var i = 0; i < this.ruleGroup.backUps.length; i++) {
        this.ruleGroup.backUps[i].merchantIds = this.ruleGroup.backUps[i].merchantIds.filter(m => {
          return this.ruleGroup.merchantIds.includes(m)
        })
        this.ruleGroup.backUps[i].merchantIdsText = this.getShortenText(this.ruleGroup.backUps[i].merchantIds, 10) ?? "";
        // for (var j = 0; j < this.ruleGroup.backUps[i].merchantIds; j++) {
        //     if (!this.ruleGroup.merchantIds.includes(this.ruleGroup.backUps[i].merchantIds[j])){
        //       // remove unmatch merchant
        //       this.ruleGroup.backUps[i].merchantIds.splice(j, 1);
        //       //refresh merchant text
        //       this.tu
        //     }
        // }
      }
    }
  }

  private _onSelectMerchantBackUp(value, event) {
    if (this.listMidsBackUp > this.tmpSelectedMerchantsBackUp.length || (!event.itemValue && event.value && event.value.length > 0 && event.value == this.listMidsBackUp)) {
      this.tmpSelectedMerchantsBackUp = this.listMidsBackUp;
    }


    if (event.itemValue && !this.listMidsBackUp.includes(event.itemValue) && this.listBackUpMerOpt.length > 0) {
      //delete case
      this.confirmService.build()
        .message(`<div class='content-confirm'> Are you sure to remove this merchant ID?</div>`)
        .title('Notice!')
        .yes('Yes')
        .no('No')
        .confirm().subscribe(result => {
          if (!result) { this.listMidsBackUp = this.tmpSelectedMerchantsBackUp } else {
            this.tmpSelectedMerchantsBackUp = this.listMidsBackUp;
          };
        })
    }

    if (!event.itemValue && event.value && event.value.length == 0 && this.listBackUpMerOpt.length > 0) {
      //delete case
      this.confirmService.build()
        .message(`<div class='content-confirm'> Are you sure to remove all merchant ID ?</div>`)
        .title('Notice!')
        .yes('Yes')
        .no('No')
        .confirm().subscribe(result => {
          if (!result) { this.listMidsBackUp = this.tmpSelectedMerchantsBackUp } else {
            this.tmpSelectedMerchantsBackUp = this.listMidsBackUp;
          };
        })
    }
  }
  private _filter(value) {
    if (value._filterValue.includes(" ")) {

      let listSearchMerchant = []
      listSearchMerchant = value._filterValue.toLowerCase().split(" ") ?? [];
      if (listSearchMerchant && listSearchMerchant.length > 0) {

        value._filteredOptions = this.listMerchantIds.filter((item) => listSearchMerchant.includes(this._normalizeValue(item.value)));
      }
    } else {
      value._filteredOptions = this.listMerchantIds.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

    }

  }

  private _filterBackUp(value) {
    if (value._filterValue.includes(" ")) {

      let listSearchMerchant = []
      listSearchMerchant = value._filterValue.toLowerCase().split(" ") ?? [];
      if (listSearchMerchant && listSearchMerchant.length > 0) {

        value._filteredOptions = this.listBackUpMerOpt.filter((item) => listSearchMerchant.includes(this._normalizeValue(item.value)));
      }
    } else {
      value._filteredOptions = this.listBackUpMerOpt.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

    }

  }

  private _normalizeValue(value: string): string {
    return value.toLowerCase().replace(/\s/g, '');
  }
  init(param?: any) {
  }



  redirectBack() {
    this.router.navigate(['/system-management/acq-group-config']);
    // this._location.back();
  }

  deleteAcqRule(idx) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Rule ?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && idx >= 0) {
          this.ruleGroup.rules.splice(idx, 1)

        }
      })
  }
  clearValue() {
    this.flgIssuer = false;
    this.flgBinGroup = false;
    this.flgBin = false;
    this.flgBinCountry = false;
    this.isCheckVisa = false;
    this.isCheckMC = false;
    this.isCheckJCB = false;
    this.isCheckAmex = false;
    this.selectedCountry = '';
    this.selectedIssuer = '';
    this.selectedBinGroup = '';
    this.selectedAcquirer = '';
    this.selectedBankMerchantId = '';
    this.selectedSource = '';
    this.bin = '';
    this.level = '';
    this.description = '';
    this.indexAcqRuleEdit = '';
  }

  onDeleteBackUp(idx) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Group Back Up?</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {
          this.ruleGroup.backUps.splice(idx, 1)
        }
      })
  }

  editAcqRule(idx: string) {
    this.clearValue();
    this.indexAcqRuleEdit = idx;
    this.flgCheckEditAcquirerRule = true;
    this.selectedSource = this.ruleGroup.rules[idx]['source'];
    //CardType
    this.ruleGroup.rules[idx]['cardType'].split("|").forEach(element => {
      if (element === 'Visa') this.isCheckVisa = true;
      if (element === 'Mastercard') this.isCheckMC = true;
      if (element === 'Jcb') this.isCheckJCB = true;
      if (element === 'Amex') this.isCheckAmex = true;
    })

    //BinCountry
    this.selectedCountry = this.ruleGroup.rules[idx]['binCountry'];
    //BinGroup
    if (this.ruleGroup.rules[idx]['binGroup']) {
      let binGr = [];
      this.ruleGroup.rules[idx]['binGroup'].split("|").forEach(element => {
        binGr.push(element);
      })
      this.selectedBinGroup = binGr;
      this.flgBin = true;
      this.flgBinCountry = true;
      this.flgIssuer = true;
      this.flgBinGroup = false;
    }
    //Issuer
    if (this.ruleGroup.rules[idx]['issuer']) {
      let issuer = [];
      this.ruleGroup.rules[idx]['issuer'].split("|").forEach(element => {
        issuer.push(element);
      })
      this.selectedIssuer = issuer;
      this.flgBin = true;
      this.flgBinCountry = true;
      this.flgIssuer = false;
      this.flgBinGroup = true;
    }
    //Bin
    if (this.ruleGroup.rules[idx]['bin']) {
      this.bin = this.ruleGroup.rules[idx]['bin'];
      this.flgBin = false;
      this.flgBinCountry = true;
      this.flgIssuer = true;
      this.flgBinGroup = true;
    }

    //Acquirer Id
    this.selectedAcquirer = this.ruleGroup.rules[idx]['acquirer'];

    this.getBankId(this.selectedAcquirer);
    //Bank Merchant Id
    this.selectedBankMerchantId = this.ruleGroup.rules[idx]["bankMerchantId"];

    //Description
    this.description = this.ruleGroup.rules[idx]['description'];
  }
  removeMerchantID(id: String) {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to remove this merchant ID ?</div><div>(Merchant ID config in acquirer group backup can be changed)</div>`)
      .title('Notice!')
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result && id) {
          var index = this.ruleGroup.merchantIds.indexOf(id);
          if (index !== -1) {
            this.ruleGroup.merchantIds.splice(index, 1);
            this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
            this.listBackUpMerOpt = this.getListBackUpMerOptions(this.ruleGroup.merchantIds);
            this.resetBackUpMerchants();
          }

        }
      })

  }
  confirmSaveGroup() {
    this.visibleConfirmSave = true
  }
  saveGroup() {
    this.visibleConfirmSave = false
    if (!this.validateGroup()) {
      return
    }
    if (!this.ruleGroup.id) {
      this.acquirerRuleService.AddAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
        if (data.status && data.status == "Successful") {
          this.toastr.success('Saved Group successfully.');
          return this.redirectBack();
        }
      });
    } else {
      this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
        if (data.status && data.status == "Successful") {
          this.toastr.success('Saved Group successfully.');
          return this.redirectBack();
        }

      });
    }


  }

  prepareRuleGroupData() {
    let jRules = [];
    if (this.ruleGroup.type != "Default") {
      this.ruleGroup.merchantIds = [];
    }
    this.ruleGroup.rules.forEach((rule) => {
      jRules.push({
        "id": rule?.id,
        "source": rule.source,
        "card_types": rule.cardType ? rule.cardType.split("|") : [],
        "acquirer": Number(rule.acquirer),
        "issuers": rule.issuer ? rule.issuer.split("|") : [],
        "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
        "bins": rule.bin ? rule.bin.split("|") : [],
        "level": Number(rule.level),
        "status": rule.status ?? "",
        "bin_country": rule.binCountry,
        "bank_merchant_id": rule.bankMerchantId ?? "",
      })
    })

    let jGroupBackUp = [];
    this.ruleGroup.backUps.forEach(ruleGroupBackUp => {
      let jRuleBK = [];
      ruleGroupBackUp.rules.forEach((rule) => {
        jRuleBK.push({
          "id": rule?.id,
          "source": rule.source,
          "card_types": rule.cardType ? rule.cardType.split("|") : [],
          "acquirer": Number(rule.acquirer),
          "issuers": rule.issuer ? rule.issuer.split("|") : [],
          "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
          "bins": rule.bin ? rule.bin.split("|") : [],
          "level": Number(rule.level),
          "status": rule.status ?? "",
          "bin_country": rule.binCountry,
          "bank_merchant_id": rule.bankMerchantId ?? "",
        })
      })
      jGroupBackUp.push({
        "id": ruleGroupBackUp.name.includes("Current Config") ? -1 : ruleGroupBackUp?.id,
        "group_name": ruleGroupBackUp.name,
        "description": ruleGroupBackUp.description,
        "case": ruleGroupBackUp.case,
        "is_back_up_default": ruleGroupBackUp.isBackUpDefault,
        "rules": jRuleBK,
        "merchant_ids": ruleGroupBackUp.merchantIds
      })
    })
    return {
      "id": this.ruleGroup?.id,
      "type": this.ruleGroup.type,
      "group_name": this.ruleGroup.name,
      "merchant_ids": this.ruleGroup.merchantIds.sort() ?? [],
      "rules": jRules,
      "description": this.ruleGroup.description,
      "back_ups": jGroupBackUp
    }
  }

  validateGroup() {
    if (this.ruleGroup.name == "") {
      this.toastr.error('Group Name is required', 'Error');
      return false;
    }
    if (this.ruleGroup.type == "") {
      this.toastr.error('Group Type is required', 'Error');
      return false;
    }
    if (this.ruleGroup.rules.length == 0) {
      this.toastr.error('Rule is required', 'Error');
      return false;
    }
    return true;

  }

  onCancelDeleteMerchantId() {
    this.deleteMerchantId = ""
    this.visibleConfirmMerchantId = false
  }

  // Add Acquirer Rule
  default() {
    this.selectedSource = this.sources[0]["value"];
    this.isCheckVisa = false;
    this.isCheckMC = false;
    this.isCheckJCB = false;
    this.isCheckAmex = false;
    this.selectedCountry = this.binCountries[0]["value"];
    this.selectedIssuer = '';
    this.selectedBinGroup = '';
    this.bin = '';
    this.level = '';
    this.indexAcqRuleEdit = '';
    this.selectedAcquirer = this.acquirers[0]["value"];
    this.getBankId(this.acquirers[0]["value"]);
    this.selectedBankMerchantId = this.bankMerchantIdByAcq[0]["value"];
    this.description = '';
    this.flgIssuer = false;
    this.flgBinGroup = false;
    this.flgBin = false;
    this.flgBinCountry = false;
    this.flgCheckEditAcquirerRule = false;
  }
  getListMerchantID() {
    let request = new HttpParams()
      .set("id", this.ruleGroup.id)
      .set("source", "ACQUIRER_RULE_GROUP");
    this.acquirerRuleService.GetAllMerchantId(request).subscribe(res => {
      if (res && res.data.length > 0) {
        res.data.forEach((i) => {
          if (i) this.listMerchantIds.push({ name: i.merchant_id + " (" + i.mcc + "_" + i.payment_method + "_" + i.currency + "_" + i.token_cvv + ")", value: i.merchant_id, acqGroupId: i.acq_group_id ?? "" });
        });
        this.listFullMerchant = this.listMerchantIds;
        this.onCheckAllMerchant()
        // this.filteredMerchantIds = this.listMerchantIds;
      }
      if (this.ruleGroup.id) {
        this.getAcqRuleGroupById();
      }
      if (this.ruleGroup.approvalId) {
        this.getAcqRuleGroupByApprovalId();
      }
    })
  }

  getMerchantLabelFromValue(merchantId: string) {
    for (var i = 0; i < this.listFullMerchant.length; i++) {
      let e = this.listFullMerchant[i]
      if (e.value == merchantId) {

        return e.name
      }
    }
  }
  getInfoConfigAcq() {
    this.acquirerRuleService.GetInfomationConfigAcqRule().subscribe(data => {
      let listIssuer = data.issuer.split(';');
      listIssuer.forEach(element => {
        this.issuers.push({ name: element, value: element });
      });

      let listBingroup = data.binGroup.split(';');
      listBingroup.forEach(element => {
        this.binGroups.push({ name: element, value: element });
      });
      let listBankMid = JSON.parse(data.acqBankMid);
      listBankMid.forEach(element => {
        this.acquirers.push({ name: element.id + " " + element.name, value: element.id });

        let bankMid = element.bankMids;
        this.bankMerchantIds.push({ key: element.id, name: bankMid, value: bankMid });
      });
    });

    this.sources.push({ name: 'Card', value: 'Card' });
    this.sources.push({ name: 'Applepay', value: 'Applepay' });
    this.binCountries.push({ name: '', value: '' });
    this.binCountries.push({ name: 'VN', value: 'VN' });
  }

  getBankId(input: any) {
    this.bankMerchantIds.forEach(element => {
      if (input === element.key) {
        this.bankMerchantIdByAcq = [];
        let bankId = element.name.split(';');
        bankId.forEach(e => {
          this.bankMerchantIdByAcq.push({ name: e, value: e });
        })
      }

    })
    if (this.bankMerchantIdByAcq.length !== 0) {
      this.selectedBankMerchantId = this.bankMerchantIdByAcq[0]["value"];
    }
  }

  onChange(key: any) {
    if (key === 1) {
      if (this.selectedCountry) {
        this.flgIssuer = true;
        this.flgBinGroup = true;
        this.flgBin = true;
      } else {
        this.flgIssuer = false;
        this.flgBinGroup = false;
        this.flgBin = false;
      }
    } else if (key === 2) {
      if (Array.isArray(this.selectedIssuer) && this.selectedIssuer.length) {
        this.flgBinCountry = true;
        this.flgBinGroup = true;
        this.flgBin = true;
      } else {
        this.flgBinCountry = false;
        this.flgBinGroup = false;
        this.flgBin = false;
      }
    } else if (key === 3) {
      if (Array.isArray(this.selectedBinGroup) && this.selectedBinGroup.length) {
        this.flgBinCountry = true;
        this.flgIssuer = true;
        this.flgBin = true;
      } else {
        this.flgBinCountry = false;
        this.flgIssuer = false;
        this.flgBin = false;
      }
    } else {
      if (this.bin.length) {
        this.flgBinCountry = true;
        this.flgIssuer = true;
        this.flgBinGroup = true;
      } else {
        this.flgBinCountry = false;
        this.flgIssuer = false;
        this.flgBinGroup = false;
      }
    }
  }
  submitAcquirerRule() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to save this Acquirer Rule?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          this.calculateLevel();
          let messageValidate = this.validateAcquirerRule(this.ruleGroup.rules);
          if (messageValidate) {
            this.toastr.error(messageValidate, 'Error');
          } else {
            this.addAcqRuleToRuleGroup();
            this.toastr.success('Saved successfully.');
            window.document.getElementById("close").click();
          }
        }
      })

  }
  editAcquirerRule() {
    this.confirmService.build()
      .message(`<div class='content-confirm'> Are you sure to edit this Acquirer Rule?</div>`)
      .title('Notice!')
      .yes('Yes')
      .no('No').confirm().subscribe(result => {
        if (result) {
          this.calculateLevel();
          let messageValidate = this.validateAcquirerRule(this.ruleGroup.rules);
          if (messageValidate) {
            this.toastr.error(messageValidate, 'Error');
          } else {
            this.editAcqRuleToRuleGroup();
            this.indexAcqRuleEdit = '';
            this.toastr.success('Saved successfully.');
            window.document.getElementById("close").click();
          }
        }
      })

  }

  validateAcquirerRule(ruleGroup: any) {
    let groupVisa = [];
    let groupMc = [];
    let groupJcb = [];
    let groupAmex = [];
    let message;
    if (!this.isCheckVisa && !this.isCheckMC && !this.isCheckJCB && !this.isCheckAmex) {
      return 'Please Choose Card Type !';
    }
    for (var i = 0; i < ruleGroup.length; i++) {
      if (this.indexAcqRuleEdit === '') {
        if (ruleGroup[i]['cardType'].includes('Visa')) {
          groupVisa.push(ruleGroup[i]);
        }
        if (ruleGroup[i]['cardType'].includes('Mastercard')) {
          groupMc.push(ruleGroup[i]);
        }
        if (ruleGroup[i]['cardType'].includes('Jcb')) {
          groupJcb.push(ruleGroup[i]);
        }
        if (ruleGroup[i]['cardType'].includes('Amex')) {
          groupAmex.push(ruleGroup[i]);
        }
      } else {
        if (i !== Number(this.indexAcqRuleEdit)) {
          if (ruleGroup[i]['cardType'].includes('Visa')) {
            groupVisa.push(ruleGroup[i]);
          }
          if (ruleGroup[i]['cardType'].includes('Mastercard')) {
            groupMc.push(ruleGroup[i]);
          }
          if (ruleGroup[i]['cardType'].includes('Jcb')) {
            groupJcb.push(ruleGroup[i]);
          }
          if (ruleGroup[i]['cardType'].includes('Amex')) {
            groupAmex.push(ruleGroup[i]);
          }
        }
      }
    }
    if (this.level === '5') {
      let flg;
      message = "Source : " + this.selectedSource + ", CardType :";
      if (this.isCheckVisa && groupVisa && this.validateLevel5(groupVisa)) {
        flg = true;
        message += ' Visa';
      }
      if (this.isCheckMC && groupMc && this.validateLevel5(groupMc)) {
        flg = true;
        message += ' Mastercard';
      }
      if (this.isCheckJCB && groupJcb && this.validateLevel5(groupJcb)) {
        flg = true;
        message += ' Jcb';
      }
      if (this.isCheckJCB && groupJcb && this.validateLevel5(groupAmex)) {
        flg = true;
        message += ' Amex';
      }
      if (!flg) {
        message = '';
      }
    } else if (this.level === '4') {
      let flg;
      message = "Source : " + this.selectedSource + ", CardType :";
      if (this.isCheckVisa && groupVisa && this.validateLevel4(groupVisa)) {
        flg = true;
        message += ' Visa';
      }
      if (this.isCheckMC && groupMc && this.validateLevel4(groupMc)) {
        flg = true;
        message += ' Mastercard';
      }
      if (this.isCheckJCB && groupJcb && this.validateLevel4(groupJcb)) {
        flg = true;
        message += ' Jcb';
      }
      if (this.isCheckJCB && groupJcb && this.validateLevel4(groupAmex)) {
        flg = true;
        message += ' Amex';
      }
      if (!flg) {
        return message = '';
      }
    } else if (this.level === '3') {
      if (this.isCheckVisa && groupVisa) {
        let issuers, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Visa";
        issuers = this.validateLevel3(groupVisa);
        if (issuers) {
          msg += ", Issuer: " + issuers.substring(0, issuers.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckMC && groupMc) {
        let issuers, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Mastercard";
        issuers = this.validateLevel3(groupMc);
        if (issuers) {
          msg += ", Issuer: " + issuers.substring(0, issuers.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckJCB && groupJcb) {
        let issuers, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Jcb";
        issuers = this.validateLevel3(groupJcb);
        if (issuers) {
          msg += ", Issuer: " + issuers.substring(0, issuers.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckAmex && groupAmex) {
        let issuers, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Amex";
        issuers = this.validateLevel3(groupAmex);
        if (issuers) {
          msg += ", Issuer: " + issuers.substring(0, issuers.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
    } else if (this.level === '2') {
      if (this.isCheckVisa && groupVisa) {
        let binGroups, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Visa";
        binGroups = this.validateLevel2(groupVisa);
        if (binGroups) {
          msg += ", BinGroup: " + binGroups.substring(0, binGroups.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckMC && groupMc) {
        let binGroups, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Mastercard";
        binGroups = this.validateLevel2(groupMc);
        if (binGroups) {
          msg += ", BinGroup: " + binGroups.substring(0, binGroups.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckJCB && groupJcb) {
        let binGroups, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Jcb";
        binGroups = this.validateLevel2(groupJcb);
        if (binGroups) {
          msg += ", BinGroup: " + binGroups.substring(0, binGroups.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckAmex && groupAmex) {
        let binGroups, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Amex";
        binGroups = this.validateLevel2(groupAmex);
        if (binGroups) {
          msg += ", BinGroup: " + binGroups.substring(0, binGroups.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
    } else if (this.level === '1') {
      if (this.isCheckVisa && groupVisa) {
        let bins, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Visa";
        bins = this.validateLevel1(groupVisa);
        if (bins) {
          msg += ", Bin: " + bins.substring(0, bins.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckMC && groupMc) {
        let bins, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Mastercard";
        bins = this.validateLevel1(groupMc);
        if (bins) {
          msg += ", Bin: " + bins.substring(0, bins.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckJCB && groupJcb) {
        let bins, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Jcb";
        bins = this.validateLevel1(groupJcb);
        if (bins) {
          msg += ", Bin: " + bins.substring(0, bins.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
      if (this.isCheckAmex && groupAmex) {
        let bins, msg;
        msg = "\r\nSource : " + this.selectedSource + ", CardType : Amex";
        bins = this.validateLevel1(groupAmex);
        if (bins) {
          msg += ", BinGroup: " + bins.substring(0, bins.length - 1);
        } else {
          msg = ''
        }
        if (msg) {
          if (message) {
            message += "," + msg;
          } else {
            message = msg;
          }
        }
      }
    }
    if (message) {
      message += " Exist";
    }
    return message;
  }

  validateLevel5(rules: any) {
    let flg;
    rules.forEach(rule => {
      if (rule['level'] === '5' && rule['source'] === this.selectedSource) {
        flg = true;
      }
    })
    return flg;
  }

  validateLevel4(rules: any) {
    let flg;
    rules.forEach(rule => {
      if (rule['level'] === '4' && rule['source'] === this.selectedSource && rule['binCountry'] === this.selectedCountry) {
        flg = true;
      }
    })
    return flg;
  }

  validateLevel3(rules: any) {
    let issuers;
    rules.forEach(rule => {
      if (rule['level'] === '3' && rule['source'] === this.selectedSource) {
        rule['issuer'].split("|").forEach(issuer => {
          this.selectedIssuer.forEach(iss => {
            if (issuer === iss) {
              if (issuers) {
                issuers += iss + "|";
              } else {
                issuers = iss + "|";
              }
            }
          })
        })
      }
    })
    return issuers;
  }

  validateLevel2(rules: any) {
    let binGroups;
    rules.forEach(rule => {
      if (rule['level'] === '2' && rule['source'] === this.selectedSource) {
        rule['binGroup'].split("|").forEach(binGroup => {
          this.selectedBinGroup.forEach(bingr => {
            if (binGroup === bingr) {
              if (binGroups) {
                binGroups += bingr + "|";
              } else {
                binGroups = bingr + "|";
              }
            }
          })
        })
      }
    })
    return binGroups;
  }

  validateLevel1(rules: any) {
    let bins;
    rules.forEach(rule => {
      if (rule['level'] === '1' && rule['source'] === this.selectedSource) {
        rule['bin'].split("|").forEach(bin => {
          this.bin.split("|").forEach(b => {
            if (bin === b) {
              if (bins) {
                bins += b + "|";
              } else {
                bins = b + "|";
              }
            }
          })
        })
      }
    })
    return bins;
  }

  calculateLevel() {
    this.level = '5';
    if (this.selectedCountry && this.selectedCountry.length !== 0) {
      this.level = '4';
    }
    if (this.selectedIssuer && this.selectedIssuer.length !== 0) {
      this.level = '3';
    }
    if (this.selectedBinGroup && this.selectedBinGroup.length !== 0) {
      this.level = '2';
    }
    if (this.bin && this.bin.length !== 0) {
      this.level = '1';
    }
  }

  createIssuer() {
    let iss = '';
    if (this.selectedIssuer) {
      this.selectedIssuer.forEach(element => {
        if (iss) {
          iss += element + "|";
        } else {
          iss = element + "|";
        }
      })
      if (iss) {
        iss = iss.substring(0, iss.length - 1);
      }
    }
    return iss;
  }
  createBinGroup() {
    let binGr = '';
    if (this.selectedBinGroup) {
      this.selectedBinGroup.forEach(element => {
        if (binGr) {
          binGr += element + "|";
        } else {
          binGr = element + "|";
        }
      })
      if (binGr) {
        binGr = binGr.substring(0, binGr.length - 1);
      }
    }
    return binGr;
  }

  addAcqRuleToRuleGroup() {
    let iss = this.createIssuer();
    let cardType = this.createCardType();
    let binGroup = this.createBinGroup();
    this.ruleGroup.rules.push({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'new' });
  }
  editAcqRuleToRuleGroup() {
    let iss = this.createIssuer();
    let cardType = this.createCardType();
    let binGroup = this.createBinGroup();
    let rule = this.ruleGroup.rules[this.indexAcqRuleEdit];
    if (rule['status'] === 'new') {
      this.ruleGroup.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'new' });
    } else if (rule['acquirer'] === this.selectedAcquirer && rule['bin'] === this.bin && rule['binCountry'] === this.selectedCountry && rule['binGroup'] === binGroup && rule['cardType'] === cardType && rule['issuer'] === iss && rule['source'] === this.selectedSource) {
      this.ruleGroup.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'exist' });
    } else {
      this.ruleGroup.rules[this.indexAcqRuleEdit] = ({ source: this.selectedSource, cardType: cardType, binCountry: this.selectedCountry, issuer: iss, binGroup: binGroup, bin: this.bin, acquirer: this.selectedAcquirer, bankMerchantId: this.selectedBankMerchantId, level: this.level, description: this.description, status: 'update' });
    }
  }


  createCardType() {
    let cardType = '';
    if (this.isCheckVisa) {
      if (cardType) {
        cardType += 'Visa|';
      } else {
        cardType = 'Visa|';
      }

    }
    if (this.isCheckMC) {
      if (cardType) {
        cardType += 'Mastercard|';
      } else {
        cardType = 'Mastercard|';
      }
    }
    if (this.isCheckJCB) {
      if (cardType) {
        cardType += 'Jcb|';
      } else {
        cardType = 'Jcb|';
      }
    }
    if (this.isCheckAmex) {
      if (cardType) {
        cardType += 'Amex|';
      } else {
        cardType = 'Amex|';
      }
    }
    if (cardType) {
      cardType = cardType.substring(0, cardType.length - 1);
    }
    return cardType
  }
}
