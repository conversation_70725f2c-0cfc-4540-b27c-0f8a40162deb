import { HttpParams } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { HotSwitchCyberMpgsService } from '@service/hot-switch-cyber-mpgs.service';
import { ToastrService } from 'ngx-toastr';
import { LazyLoadEvent, SelectItem } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subscription } from 'rxjs';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { SwitchScript } from 'app/model/switch-script.model';

@Component({
  selector: 'app-hot-switch-cyber-mpgs',
  templateUrl: './hot-switch-cyber-mpgs.component.html',
  styleUrls: ['./hot-switch-cyber-mpgs.component.css'],
  providers: [DialogService],
})
/**
 * Class hot switch cũ
 */
export class HotSwitchCyberMpgsComponent implements OnInit {
  public checked: boolean = false;
  public checkSetRunningDefault: boolean = false;
  public visibleListScript: boolean = false;
  public loading: boolean;
  public flexScrollHeight = '300px';
  public keyword = '';
  public contractCode = '';
  public groupTypeValue = '';
  public service: Array<string>;
  public selectedData: any[] = [];
  public page = 0;
  public first = 0;
  public tableDataTotal: number;
  public subscription: Subscription;
  public pageSizeList = [
    {
      label: '50',
      value: '50'
    },
    {
      label: '100',
      value: '100'
    },
    {
      label: '200',
      value: '200'
    },
    {
      label: '500',
      value: '500'
    },
    {
      label: '1000',
      value: '1000'
    }
  ];
  public groupTypeArray = [
    {
      label: 'All',
      value: ''
    },
    {
      label: 'Default',
      value: 'default'
    }
  ];
  public scriptList = [
    {
      label: 'Kịch bản',
      value: 0
    }
  ];
  public resultsLength: number;
  public data: Array<any>;
  public pageSize: string;
  public scriptType: number;
  public sub: Subscription;
  public temp: Array<any>;
  public switchScripts: Array<any>
  switchScript : SwitchScript = {
    id: 0,
    script: '',
    createDate: '',
    desc: ''
  };

  constructor(
    public dialogService: DialogService,
    public hotSwitchService: HotSwitchCyberMpgsService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    public global: Globals,
    private confirmService: ConfirmService
  ) { }
  ngOnInit(): void {
    this.sub = this.route
      .queryParams
      .subscribe(params => {
        this.init(params);
        let request = new HttpParams()
        this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
          for(let i=0; i< data.list.length; i++) {
            this.scriptList.push({label: data.list[i].script,value: data.list[i].id});
            this.groupTypeArray.push({label: data.list[i].script,value: data.list[i].id});
          }
        });
      });
  }

  init(param?: any) {
    this.groupTypeValue = param['group_type'];
    this.keyword = param['keyword'];
    this.pageSize = param['page_size'];
    this.page = param['page'] ? parseInt(param['page']) : 0;
    this.first = param['first'] ? parseInt(param['first']) : 0;
  }

  lazyLoad(event: LazyLoadEvent) {
    this.loading = true;
    this.page = event.first / event.rows;
    this.first = event.first;
    this.router.navigate(['/system-management/hot-switch-cyber-mpgs'], { queryParams: this.redirectParams() });
    this.searchData()
  }

  redirectParams() {
    const params = {
      'group_type': this.groupTypeValue ? this.groupTypeValue : '',
      'keyword': this.keyword ? this.keyword.trim() : '',
      'page_size': this.pageSize ? this.pageSize : '50',
      'page': this.page ? this.page : '0',
      'first': this.first
    };
    return params;
  }

  searchData() {
    this.checked = false;
    this.selectedData = [];
    const params = new HttpParams()
      .set('group_type', this.groupTypeValue ? this.groupTypeValue : '')
      .set('keyword', this.keyword ? this.keyword.trim() : '')
      .set('page_size', this.pageSize ? this.pageSize : '50')
      .set('page', this.page ? this.page.toString() : '0');

    this.hotSwitchService.GetHotSwitchCyberMpgsList(params).subscribe(data => {
      this.loading = false;
      this.tableDataTotal = data.totalItems;
      this.data = data.list;
      if (this.data) {
        this.checkSetRunningDefault = this.data.every(a => {
          return a.currentConfig == "Default";
        })
      }
    });
  }

  onSubmit() {
    this.first = 0;
    this.page = 0;
    this.router.navigate(['/system-management/hot-switch-cyber-mpgs'], { queryParams: this.redirectParams() });
    this.loading = true;
    this.searchData()
  }

  getSelectedMids() {
    this.selectedData = this.selectedData.map(item => {
      item.batchApprovalState = true;
      return item;
    });
    let dataSet = new Set(this.selectedData.map(a => { return a.merchantId }))
    return [...dataSet].join(",")
  }

  switchBatch(switch_type: string) {
    this.switch(this.getSelectedMids(), switch_type)
  }

  switch(data: string, switch_type: string) {
    let request = {
      "mids": data,
      "switchScript" : this.scriptType,
      "switchType" : switch_type
    }
    this.hotSwitchService.HotSwitchMerchant(request).subscribe(
      (res) => {
        if (res.body.result == "Change Default") {
          this.toastr.error('The config default has been updated', 'Error');
        } else if (res.body.result == "Success") {
          this.toastr.success('Switch Success', 'Success');
        } else {
          this.toastr.error('Switch Failed', 'Error');
        }
        this.onSubmit();
    })
  }

  switchConfirmDialog(switch_type) {
    const message = 'Are you sure to switch to ' +  switch_type +'?';
    this.confirmService.build().message(message)
    .no('No')
    .yes('Yes').confirm().subscribe(result => {
      if (result) {
        if ((this.scriptType === undefined || this.scriptType == 0) && switch_type != 'Default') {
          this.toastr.error('The switch script is not selected', 'Error');
          return;
        }
        this.switchBatch(switch_type);
      }
    })
  }

  setConfigDefaultDialog() {
    const message = 'Are you sure to set running config to default?';
    this.confirmService.build().message(message)
      .no('No')
      .yes('Yes').confirm().subscribe(result => {
        if (result) {
          this.hotSwitchService.SetRunningToDefault().subscribe(
            (res) => {
              if (res.result == "Success") {
                this.toastr.success('Switch Success', 'Success');
              } else {
                this.toastr.error('Switch Failed', 'Error');
              }
              this.onSubmit();
          })
        }
      })
  }

  selectUnselectAll() {
    if (this.checked) {
      this.selectedData = this.data.slice();
    } else {
      this.selectedData = [];
    }
  }

  displayHistory() {
    this.router.navigate([`/system-management/switch-cyber-mpgs-history`]);
  }
}

