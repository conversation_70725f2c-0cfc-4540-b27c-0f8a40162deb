<div class="wrapper" id="merchant-acq-rule-group">
  <div style="min-height:80px;position:relative; background-color:#F6F6F6;padding-left: 50px;padding-right: 50px;"
      class="row">
      <div class="header-top-center col-sm-4">
          <h2 class="header-top-center">List Merchant ID Config</h2>
      </div>
  </div>
  <div class="container-fluid mt-3">
      <div class="row mt-3">
          <div class="col-1-modify">
              <div class="" style="align-self: center;">
                  <div class="row" style="min-width:100px; float: right;">
                      <p-checkbox binary="true" [(ngModel)]="isAll" [value]="isAll" (onChange)="onCheckIsAll()"></p-checkbox>
                      <div style="padding-left:10px;align-self: center;">All</div>
                  </div>
                  <!-- <div class="form-group">
                      <span class="input-group">
                      <input class="form-control" style="height:30px;margin-right: 10px;" [(ngModel)]="keyword" pInputText />
                      <label class="label-custom fixPositionLabel">Merchant ID</label>
                      </span>
                  </div> -->
              </div>
          </div>
          <div class="col-1-modify">
            <div class="form-group">
              <span class="input-group">
                <p-multiSelect appendTo="body" name="merchants" [options]="filteredMerchants" [(ngModel)]="selectedMerchants" [resetFilterOnHide]="true" [filter]="true"
                [style]="{'width':'100%'}" optionLabel="merchant_id" [virtualScroll]="true" [itemSize]="40" display="chip" #merchants="ngModel">
                </p-multiSelect>
                <label class="label-custom fixPositionLabel">Merchant ID</label>
              </span>
            </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                  <input type="text" pInputText [(ngModel)]="filterMCC" [ngModelOptions]="{standalone: true}"/>
                  <label class="label-custom fixPositionLabel">MCC</label>
                  </span>
              </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                      <p-dropdown [style]="{'width':'100%'}"
                          [(ngModel)]="filterPaymethod" dropdownIcon="pi pi-angle-down" optionLabel="name" [options] = '[{name: "All"}, {name: "3D"}, {name: "2D"}]'>
                      </p-dropdown>
                  <label class="label-custom fixPositionLabel">3D Config</label>
                  </span>
              </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                      <p-dropdown [style]="{'width':'100%'}"
                      [(ngModel)]="filterCVV" filterBy="name" dropdownIcon="pi pi-angle-down" optionLabel="name" [options] = '[{name: "All"}, {name: "1"}, {name: "0"}]'>
                      </p-dropdown>
                  <label class="label-custom fixPositionLabel">CVV</label>
                  </span>
              </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                      <p-dropdown [style]="{'width':'100%'}"
                      [(ngModel)]="filterCurrency" filterBy="name" dropdownIcon="pi pi-angle-down" optionLabel="name" [options] = '[{name: "All"}, {name: "VND"}, {name: "USD"}]'>
                      </p-dropdown>
                  <label class="label-custom fixPositionLabel">Currency</label>
                  </span>
              </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                  <p-dropdown [style]="{'width':'100%'}"
                      filterBy="name" dropdownIcon="pi pi-angle-down" optionLabel="name" [options] = '[{name: "---"}, {name: ">"}, {name: "<"}, {name: "="}, {name: ">="}, {name: "<="}]'>
                  </p-dropdown>
                  <label class="label-custom fixPositionLabel">Operators </label>
                  </span>
              </div>
          </div>
          <div class="col-1-modify">
              <div class="form-group">
                  <span class="input-group">
                  <input type="text" pInputText />
                  <label class="label-custom fixPositionLabel">Ticket Number</label>
                  </span>
              </div>
          </div>
      </div>
  </div>
  <div style="min-height:80px;position:relative; background-color:#F6F6F6;padding-left: 50px;padding-right: 50px;"
      class="row">
      <div class="col-sm-9"></div>
      <div class=" col-sm-3 d-flex justify-content-end">
          <button style="margin-right:10px;" class="action-button" (click)="searchData()">
              SEARCH
          </button>
          <button class="action-button" style="margin-right:10px ; background-color:#689F38" (click)="approval()"
              data-toggle="modal">APPROVAL </button>
          <button class="create-button" style="min-width:220px!important" data-target="#createConfigModal" (click)="onSetUpConfig()"
              data-toggle="modal" [disabled]="!checkedCount">Add Config </button>
      </div>
  </div>


  <div class="row">
      <!-- <p-table [value]="listConfig" [rows]="pageSize" [lazy]="true" [paginator]="true" [rowHover]="true"
          [totalRecords]="tableDataTotal" (onLazyLoad)="loadLazy($event)" scrollHeight="70vh"> -->

      <p-table class="sticky-headers-table hiden_paginator_volume_default" [value]="listConfig" [rows]="pageSize"
          styleClass="p-datatable-gridlines" responsiveLayout="scroll" [first]="first" [lazy]="true"
          [totalRecords]="tableDataTotal" [scrollable]="true" scrollHeight="70vh"
          (onLazyLoad)="loadLazy($event)" [paginator]="true" pageLinks="0" paginatorPosition="top">

          <ng-template pTemplate="paginatorright" let-state>
              <div class="ui-helper-clearfix clearfix-02">
                  <div class="total-item">
                      <table-paginator [state]="state" [totalRecords]="tableDataTotal"></table-paginator>
                  </div>
                  <div class="div-02">
                      <span class="input-group">
                          <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="pageSizeList"
                              [(ngModel)]="pageSize" dropdownIcon="pi pi-angle-down" #display="ngModel"
                              name="display">
                          </p-dropdown>
                      </span>
                  </div>
                  <div class="div-03">
                      <span class="label-custom" for="Display">Display Number</span>
                  </div>
              </div>
          </ng-template>
          <ng-template pTemplate="colgroup" let-columns>
              <colgroup>
                  <col class="mat-column-checkox">
                  <col class="mat-column-no">
                  <col class="mat-column-common">
                  <col class="mat-column-small">
                  <col class="mat-column-small">
                  <col class="mat-column-small">
                  <col class="mat-column-common">
                  <col class="mat-column-common">
                  <col class="mat-column-common">
                  <col class="mat-column-lg">
                  <col class="mat-column-common">
                  <col class="mat-column-common">
                  <col class="mat-column-common">
              </colgroup>
          </ng-template>
          <ng-template pTemplate="header">
              <tr>
                  <th scope="col" class="text-center" >
                      <p-checkbox binary="true" [(ngModel)]="checkedAll" [value]="checkedAll"
                          (onChange)="onCheckAllConfig()"></p-checkbox>
                  </th>
                  <th scope="col" class="text-center" >
                      No
                  </th>
                  <th scope="col" class="text-center">
                      Merchant ID
                  </th>
                  <th scope="col" class="text-center">
                      MCC
                  </th>
                  <th scope="col" class="text-center">
                      3D Config
                  </th>
                  <th scope="col" class="text-center">
                      CVV
                  </th>
                  <th scope="col" class="text-center">
                      Txn Currency
                  </th>
                  <th scope="col" class="text-center">
                      Ticket Number
                  </th>
              </tr>
          </ng-template>
          <ng-template pTemplate="body" let-config let-i='rowIndex'>
              <tr>
                  <td class="text-center">
                      <p-checkbox binary="true" [(ngModel)]="config.isCheck" [value]="config.isCheck"
                          (onChange)="onCheckConfig(i)"></p-checkbox>
                  </td>
                  <td class="text-center" >{{i+1}}</td>
                  <td class="text-center limit-dots"><a class="a-no-href color-link text-center" data-target="#merchant-detail"
                      data-toggle="modal" (click)="merchantDetail(config)" >{{config.merchantId}}</a></td>
                  <td class="text-center">{{config.mcc}}</td>
                  <td class="text-center">{{config.payMethod}}</td>
                  <td class="text-center">{{config.cvv}}</td>
                  <td class="text-center">{{config.currencyCode}}</td>
                  <td class="text-center">{{config.tickerSize | number: '1.0-0'}}</td>
              </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <tr>
                  <td [attr.colspan]="8" class="text-center empty_results">
                      No Results Has Been Shown
                  </td>
              </tr>
          </ng-template>
      </p-table>
  </div>
</div>

<!-- <p-dialog header="List Rule Config" [(visible)]="visibleMerchantBackUps" [modal]="true" [dismissableMask]="true"
  [style]="{ width: '50vw' }" [draggable]="false" [resizable]="false">
  <ng-template pTemplate="content">
      <div class="row" style="margin: auto">
          <p-table [value]="merchantBackUps" [rows]="merchantBackUps.length" [rowHover]="true"
          [totalRecords]="merchantBackUps.length">
          <ng-template pTemplate="header">
              <tr>
                  <th scope="col" class="text-center" >
                      No
                  </th>
                  <th scope="col" class="text-center" >
                      ID
                  </th>
                  <th scope="col" class="text-center">
                      Name
                   </th>
                  <th scope="col" class="text-center">
                      Running Config
                  </th>
              </tr>
          </ng-template>
          <ng-template pTemplate="body" let-backUp let-i='rowIndex'>
              <tr>
                  <td class="text-center">{{i+1}}</td>
                  <td class="text-center"><a class="a-no-href color-link text-center" (click)="redirectDetailConfig(backUp, '')">{{convertTextGroupName(backUp)}}</a></td>
                  <td class="text-center">{{backUp.caseName}}</td>
                  <td class="text-center">
                      <p-checkbox [(ngModel)]="backUp.is_running"
                      [binary]="true"
                      inputId="binary"
                      [disabled]="true"
                      ></p-checkbox></td>
              </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <tr>
                  <td [attr.colspan]="4" class="text-center empty_results">
                      No Results Has Been Shown
                  </td>
              </tr>
          </ng-template>
      </p-table>
      </div>
  </ng-template>
</p-dialog> -->

<!-- Add Acquire Rule  -->
<div class="modal fade" id="createConfigModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel">
  <div class="modal-dialog" role="document" style="max-width:1000px; height:500px!important">
      <div class="modal-content" style="width:100%">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Add Merchant ACQ Group Config</h5>
          </div>
          <div class="modal-body">
              <div class="row" style="margin-bottom: 20px;">
                  <div class="col-sm-2">
                      <p class="label">Merchant ID</p>
                  </div>
                  <div class="col-sm-10">
                      <textarea [(ngModel)]="configForm.merchantIdsText" [disabled]="true" class="form-control h-100"
                          style="width:100%; font-size: 12px !important" maxlength="300" pInputTextarea>
                </textarea>
                  </div>
              </div>

              <div class="row" style="margin-bottom: 20px;">
                  <div class="col-sm-2">
                      <p class="label">Default Group</p>
                  </div>
                  <div class="col-sm-10">
                      <p-dropdown [style]="{'width':'30%'}" dropdownIcon="pi pi-angle-down" [options]="listDefault"
                      (onChange)="onChangeGroup()" [(ngModel)]="configForm.groupDefaultId" optionLabel="name" optionValue="value">
                      </p-dropdown>
                  </div>
              </div>

              <div class="row" *ngIf="listBackUps && listBackUps.length >0" style="margin-bottom: 10px;">
                  <div class="col-sm-2">
                      <p class="label">Back Up Group</p>
                  </div>
                  <div class="col-sm-10 " >
                      <div  *ngFor="let val of listBackUps" class ="row"  style="margin-bottom: 10px;">
                          <div class="col-sm-2" style=" align-self: center;color:#525050;">
                            {{'[ Kịch Bản '+val.caseName +" ]"}}

                          </div>
                          <div class="col-sm-8">
                              <p-dropdown [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down" [options]="val.list" [(ngModel)]="val.selected"
                           optionLabel="name" optionValue="id">
                              </p-dropdown>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <div class="modal-footer">
              <button type="hidden" style="display: none;" class="btn btn-secondary" id="close"
                  data-dismiss="modal">Close Dialog</button>
              <button type="button" class="action-button" style="background-color:rgb(141 141 141);" data-dismiss="modal">Cancel</button>
              <button type="button" class="action-button" data-dismiss="modal" (click)="onSaveConfig()">Save</button>
          </div>
      </div>
  </div>
</div>
<!-- Merchant Detail -->
<div class="modal fade" id="merchant-detail" tabindex="-1" role="dialog" aria-labelledby="createModalLabel">
  <div class="modal-dialog" role="document" style="max-width:1000px; height:500px!important">
      <div class="modal-content" style="width:100%">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Merchant Detail</h5>
              <div class=" col-sm-8 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="close-merchant-detail">Exit</button>
              </div>
          </div>
          <div class="modal-body">
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Active</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{convertTextWithBoolean(merchantInfo.active) === 'TRUE' ? 'Activated' : 'Unactivated'}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Require AVS</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{convertTextWithBoolean(merchantInfo.requiredAVS) === 'TRUE' ? 'Required' : 'Unrequired'}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Partner</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.partnerName}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">MID</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.midId}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Merchant ID</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.merchantId}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Merchant Trading ID</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.tradingId}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Merchant Trading Name</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.tradingName}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Category Code</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.categoryCode}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Merchant Website</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.merchantWebsite}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Receipt Return URL</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.receiptReturnUrl}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Currency Code</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.currencyCode}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Pay Method</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.paymethod}}</p>
                  </div>
              </div>
              <div class="row even" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Card Type</p>
                  </div>
                  <div class="col-sm-3">
                      <p class="label  font-text">{{merchantInfo.cardType}}</p>
                  </div>
              </div>
              <div class="row odd" >
                  <div class=" col-sm-3">
                      <p class="label  font-text" style="font-size: 13px; font-weight: bold;">Fraud Roles</p>
                  </div>
                  <div class="col-sm-8">
                      <p class="label  font-text" style="word-wrap: break-word;">{{merchantInfo.fraudRoles}}</p>
                  </div>
              </div>
              <div class="row" style="padding: 10px; margin-top:10px">
                  <div class="col-sm-2">
                      <button type="button" class="action-button" style="background-color:#689F38" (click) = "redirectRunningConfig(selectedMerchantConfig)"
                       [disabled]="!hasRunningConfig(selectedMerchantConfig)">Running Config</button>
                  </div>
              </div>
              <div class="row" style="padding: 10px; margin-top:10px">
                  <div class="col-sm-2">
                      <button type="button" class="action-button" data-target="#merchant-history"
                      data-toggle="modal" (click) = "merchantConfigHistory(selectedMerchantConfig)">History Config Group</button>
                  </div>
              </div>
              <div class="row" style="padding: 10px; margin-top:10px">
                  <div class="col-sm-2">
                      <button type="button" class="action-button" data-target="#merchant-switch-history"
                      data-toggle="modal" (click) = "merchantSwitchHistory(selectedMerchantConfig)" style="background-color:#FBC02D">History Switch Config</button>
                  </div>
              </div>
          </div>

      </div>
  </div>
</div>
<!-- Modal History Config Merchant -->
<div class="modal fade" id="merchant-history" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">Merchant Config Acq Rule History</h5>
              <div class=" col-sm-8 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="close-merchant-history">Exit</button>
              </div>
          </div>
          <div class="modal-body">
              <div style="padding: 10px;">
                  <div class="row mt-3">
                      <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="merchantConfigFrom" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeFromDate($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [maxDate]="merchantConfigTo">
                              </p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">Từ ngày</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="merchantConfigTo" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeToDate($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [minDate]="merchantConfigFrom"></p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">Đến ngày</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group d-flex justify-content-end">
                            <button type="submit" (click) ="merchantConfigHistory(selectedMerchantConfig)" pButton label="Search" icon="pi pi-search" iconPos="right" class="p-button-primary ml-2"></button>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group d-flex justify-content-end">
                              <button type="button" pButton label="Download" icon="pi pi-download" iconPos="left" id="btn-download"
                              class="download-button p-button-success" style="margin-left:10px;height:30px;width:150px"
                              (click)="download(selectedMerchantConfig)" [disabled]="historyMerchantsConfig.length == 0">
                          </button>
                          </div>
                        </div>
                  </div>
                  <div class="row" style="margin-top: 20px; ">
                      <div class="row" style="padding: 10px;">
                          <p-table class="sticky-headers-table hiden_paginator_volume_default" [value]="historyMerchantsConfig" [rows]="pageSize"
                              styleClass="p-datatable-gridlines" responsiveLayout="scroll" [first]="first" [lazy]="true"
                              [totalRecords]="historyMerchantsConfig.length" [scrollable]="true" scrollHeight="70vh" [paginator]="true" pageLinks="0" paginatorPosition="top">
                              <ng-template pTemplate="paginatorright" let-state>
                                  <div class="ui-helper-clearfix clearfix-02">
                                      <div class="total-item">
                                          <table-paginator [state]="state" [totalRecords]="historyMerchantsConfig.length"></table-paginator>
                                      </div>
                                      <div class="div-02">
                                          <span class="input-group">
                                              <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="pageSizeList"
                                                  [(ngModel)]="pageSizeMerchantConfig" dropdownIcon="pi pi-angle-down" #display="ngModel"
                                                  name="display">
                                              </p-dropdown>
                                          </span>
                                      </div>
                                      <div class="div-03">
                                          <span class="label-custom" for="Display">Display Number</span>
                                      </div>
                                  </div>
                              </ng-template>
                              <ng-template pTemplate="colgroup" let-columns>
                                  <colgroup>
                                      <col class="mat-column-no">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                  </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header">
                                  <tr>
                                      <th scope="col" pSortableColumn="num" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          No  <p-sortIcon field="num"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="source" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Merchant ID  <p-sortIcon field="source"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="cardType" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          AcqRuleGroup Before  <p-sortIcon field="cardType"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binCountry" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          AcqRuleGroup After  <p-sortIcon field="binCountry"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="issuer" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          From Date  <p-sortIcon field="issuer"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          To Date  <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-history let-i='rowIndex'>
                                  <tr>
                                      <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important">
                                          {{history.num}}</td>
                                      <td class="text-center" style="border-right: 1px solid lightgray !important">
                                          {{merchantDetailText}}
                                      </td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          <a class="a-no-href color-link text-center" (click)="redirectDetailConfig(history, 'CONFIGFROM')">{{history.groupNameFrom}}</a></td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          <a class="a-no-href color-link text-center" (click)="redirectDetailConfig(history, 'CONFIGTO')">{{history.groupNameTo}}</a></td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.fromDate}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.toDate}}</td>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                  <tr>
                                      <td [attr.colspan]="6" class="text-center empty_results">
                                          No Results Has Been Shown
                                      </td>
                                  </tr>
                              </ng-template>
                          </p-table>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>
<!-- Modal History Merchant Switch Acq-->
<div class="modal fade" id="merchant-switch-history" tabindex="-1" role="dialog" aria-labelledby="createModalLabel"
  style="z-index:9999">
  <div class="modal-dialog" role="document" style="max-width:1500px">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="createModalLabel">History Hot Switch Cyber/Mpgs</h5>
              <div class=" col-sm-8 d-flex justify-content-end">
                  <button class="action-button " style="margin-right: 10px; background-color:#9B9FA3 ;"
                      data-dismiss="modal" id="close-merchant-history">Exit</button>
              </div>
          </div>
          <div class="modal-body">
              <div style="padding: 10px;">
                  <div class="row mt-3">
                      <div class="col-1-modify">
                          <div class="form-group">
                              <span class="input-group">
                                  <p-dropdown [style]="{'width':'100%'}"
                                  [(ngModel)]="scenarioFrom" filterBy="name" dropdownIcon="pi pi-angle-down" optionLabel="script" [options] = 'scenarioList'>
                                  </p-dropdown>
                              <label class="label-custom fixPositionLabel">Scenario From</label>
                              </span>
                          </div>
                      </div>
                      <div class="col-1-modify">
                          <div class="form-group">
                              <span class="input-group">
                                  <p-dropdown [style]="{'width':'100%'}"
                                  [(ngModel)]="scenarioTo" filterBy="name" dropdownIcon="pi pi-angle-down" optionLabel="script" [options] = 'scenarioList'>
                                  </p-dropdown>
                              <label class="label-custom fixPositionLabel">Scenario To</label>
                              </span>
                          </div>
                      </div>
                      <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="merchantSwitchFrom" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeFromDateSwitch($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [maxDate]="merchantSwitchTo">
                              </p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">From Date</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group">
                            <span class="input-group">
                              <p-calendar [ngModel]="merchantSwitchTo" [ngModelOptions]="{standalone: true}" (ngModelChange)="onChangeToDateSwitch($event)" [showIcon]="true" inputId="icon"
                                dateFormat="dd/mm/yy" [minDate]="merchantSwitchFrom"></p-calendar>
                              <label class="label-custom fixPositionLabel" for="icon">To Date</label>
                            </span>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group d-flex justify-content-end">
                            <button type="submit" (click) ="merchantSwitchHistory(selectedMerchantConfig)" pButton label="Search" icon="pi pi-search" iconPos="right" class="p-button-primary ml-2"></button>
                          </div>
                        </div>
                        <div class="col-1-modify">
                          <div class="form-group d-flex justify-content-end">
                              <button type="button" pButton label="Download" icon="pi pi-download" iconPos="left" id="btn-download"
                              class="download-button p-button-success" style="margin-left:10px;height:30px;width:150px"
                              (click)="downloadMerchantSwitch(selectedMerchantConfig)" [disabled]="historyMerchantsSwitch.length == 0">
                          </button>
                          </div>
                        </div>
                  </div>
                  <div class="row" style="margin-top: 20px; ">
                      <div class="row" style="padding: 10px;">
                          <p-table class="sticky-headers-table hiden_paginator_volume_default" [value]="historyMerchantsSwitch" [rows]="pageSize"
                              styleClass="p-datatable-gridlines" responsiveLayout="scroll" [first]="first" [lazy]="true"
                              [totalRecords]="historyMerchantsSwitch.length" [scrollable]="true" scrollHeight="70vh" [paginator]="true" pageLinks="0" paginatorPosition="top">
                              <ng-template pTemplate="paginatorright" let-state>
                                  <div class="ui-helper-clearfix clearfix-02">
                                      <div class="total-item">
                                          <table-paginator [state]="state" [totalRecords]="historyMerchantsSwitch.length"></table-paginator>
                                      </div>
                                      <div class="div-02">
                                          <span class="input-group">
                                              <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="pageSizeList"
                                                  [(ngModel)]="pageSizeMerchantConfig" dropdownIcon="pi pi-angle-down" #display="ngModel"
                                                  name="display">
                                              </p-dropdown>
                                          </span>
                                      </div>
                                      <div class="div-03">
                                          <span class="label-custom" for="Display">Display Number</span>
                                      </div>
                                  </div>
                              </ng-template>
                              <ng-template pTemplate="colgroup" let-columns>
                                  <colgroup>
                                      <col class="mat-column-no">
                                      <col class="mat-column-lg">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-common">
                                      <col class="mat-column-sm">
                                      <col class="mat-column-common">
                                      <col class="mat-column-sm">
                                      <col class="mat-column-common">
                                      <col class="mat-column-sm">
                                      <col class="mat-column-common">
                                  </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header">
                                  <tr>
                                      <th scope="col" pSortableColumn="num" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          No  <p-sortIcon field="num"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="source" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Merchant ID  <p-sortIcon field="source"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="cardType" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          AcqRuleGroup  <p-sortIcon field="cardType"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binCountry" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Scenario From  <p-sortIcon field="binCountry"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="issuer" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Scenario To  <p-sortIcon field="issuer"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Switch Status  <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Running Config  <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Running Config Type <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Switched Date <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Switch By <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                      <th scope="col" pSortableColumn="binGroup" class="text-center" style="background-color:hsl(129, 37%, 74%) ;">
                                          Actor <p-sortIcon field="binGroup"></p-sortIcon>
                                      </th>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-history let-i='rowIndex'>
                                  <tr>
                                      <td class="text-center" style="width:5%;border-right: 1px solid lightgray !important">
                                          {{history.num}}</td>
                                      <td class="text-center" style="border-right: 1px solid lightgray !important">
                                          <a class="a-no-href color-link text-center" (click)="redirectDetailConfig(history, 'DEFAULT')">{{merchantDetailText}}</a>
                                      </td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.defaultGroupName}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{convertTextScenarioFrom(history)}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{convertTextScenarioTo(history)}}</td>
                                      <td class="text-center" [ngClass]="history.switchStatus === 'FAIL' ? 'text-fail' : '' " style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.switchStatus}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          <a class="a-no-href color-link text-center" (click)="redirectDetailConfig(history, 'BACKUP')">{{history.groupName}}</a></td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{convertTextRunningConfigType(history)}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.switchedDate}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{convertTextSwitchBy(history)}}</td>
                                      <td class="text-center" style="overflow: auto;border-right: 1px solid lightgray !important">
                                          {{history.actor}}</td>
                                  </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                  <tr>
                                      <td [attr.colspan]="11" class="text-center empty_results">
                                          No Results Has Been Shown
                                      </td>
                                  </tr>
                              </ng-template>
                          </p-table>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>

