import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AcquirerRuleService } from '@service/acquirer-rule.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastrService } from 'ngx-toastr';
import { DatePipe, DecimalPipe } from '@angular/common';
import { element } from 'protractor';
import { windowCount } from 'rxjs/operators';
import { HotSwitchCyberMpgsService } from '@service/hot-switch-cyber-mpgs.service';
import { HttpParams } from '@angular/common/http';
import { SwitchScript } from 'app/model/switch-script.model';
import _ from 'lodash';
import { PaymentOPFeeService } from '@service/payment_reconciliation/payment2-op-fee.service';
import { percentageNotDivide } from '@shared/utils/utils';
import { Globals } from '@core/global';


@Component({
    selector: 'app-exception-rule-detail',
    templateUrl: './exception-rule-detail.component.html',
    styleUrls: ['./exception-rule-detail.component.css'],
    providers: [DialogService, PaymentOPFeeService],
})
export class ExceptionRuleDetailComponent implements OnInit {
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        public acquirerRuleService: AcquirerRuleService,
        private toastr: ToastrService,
        private confirmService: ConfirmService,
        public datepipe: DatePipe,
        public hotSwitchService: HotSwitchCyberMpgsService,
        private paymentOPFeeService: PaymentOPFeeService,
        public global: Globals,
    ) { }

    //Add rule
    issuers = [];
    binGroup = [];
    acquirers = [];
    acquirersView = {};
    sources = [{ 'name': 'Direct', 'value': 'Direct' }, { 'name': 'ApplePay', 'value': 'ApplePay' }, { 'name': 'SamsungPay', 'value': 'SamsungPay' }, { 'name': 'GooglePay', 'value': 'GooglePay' }];
    binCountry = [{ 'name': '', 'value': '' }, { 'name': 'VN', 'value': 'VN' }, { 'name': 'FOREIGN', 'value': 'FOREIGN' }];
    bankIdType = [{ 'name': '2B', 'value': '2B' }]
    bankMerchantId = [];
    listBankMid: any;
    sSource: any
    sAcq: any;
    sBankIdType: any;
    sBinCountry: any;
    sIssuer: any;
    sBinGroup: any;
    sDescription: any;
    sBin: any;
    sBankMerchantId: any;
    sBankMerchantIdIndex: any;
    sId: any;
    isCheckVisa: any;
    isCheckMaster: any;
    isCheckJcb: any;
    isCheckAmex: any;
    tempRules = [];
    idEdit: any = '';
    isCreateBackUp: any;
    isCreateBackUpInCompare: any;
    idBackUpAddRule: any;
    //
    listMerchantIds = [];
    isAllMerchant = false;
    listFullMerchant = [];
    detailResponse: any = [];
    isChangeNewRule: any = false;
    defaultRuleGroup: any;
    defaultNewRuleGroup: any;
    isChangeDefaultRuleGroup: any = false;
    isChangeDefaultNewRuleGroup: any = false;
    listMCC = [];
    selectedMcc: any;
    listMerchantFeeInfoData = [];
    historyRules = [];
    pageSize = 50;
    page = 0;

    percentageNotDivide = percentageNotDivide;

    ruleGroup = {
        id: null,
        approvalId: null,
        name: "",
        type: "Default",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        isEditable: true,
        isReview: false,
        backUps: [],
        listCases: [],
        case: null,
        isBackUpDefault: null,
        approval: true,
        status: "new",
        enable: true,
        approvalType: "",
        mcc: "",
    }

    oldRuleGroup = {
        id: null,
        approvalId: null,
        name: "",
        type: "Default",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        isEditable: true,
        isReview: false,
        backUps: [],
        listCases: [],
        case: null,
        isBackUpDefault: null,
        approval: true,
        status: "new",
        enable: true,
        approvalType: "",
        mcc: "",
    }

    newRuleGroup = {
        id: null,
        approvalId: null,
        name: "",
        type: "Default",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        isEditable: true,
        isReview: false,
        backUps: [],
        listCases: [],
        case: null,
        isBackUpDefault: null,
        approval: true,
        status: "new",
        enable: true,
        approvalType: "",
        mcc: "",
    }
    isWaitForApproval: any = false;
    isWaitForApprovalInCompare: any = false;
    ruleGroupId: any;
    isCheckDetailAcqRuleGroup: any = false;
    isCheckAllRule: any = false;
    isCheckAllListCase: any = false;
    isEnableDisableDelete: any = false;
    isChangeDetailRuleGroup: any = false;
    highlightedMerchantIds = [];
    tmpSelectedMerchants = [];
    tmpCompareSelectedMerchants = [];
    // Back Up
    // config back up
    listAllBackUp = [];
    public listCases = []
    public keyword = '';
    public currentCase = 1
    public listConfigCases = {}
    public option = [{ label: 'No Switching', value: '0' }, { label: 'New', value: 'New' }, { label: 'Select', value: 'Select' }]
    public backUpType = [{ label: 'Default', value: true }, { label: 'Exception', value: false }]
    public mapListFullMerchant = {}
    public mapCases = {}
    public defaultFormatBackUp = {
        tmpId: '',
        option: '',
        id: '',
        name: '',
        type: "",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        backUps: null,
        case: null,
        isBackUpDefault: false,
        isAllMerchant: false,
        listMerchantIds: [],
        selectedBackUp: null,
        listBackUpType: [],
        listOption: [],
        status: "",
        approval: true,
        enable: true,
    }
    public oldBackUp = {
        tmpId: '',
        option: '',
        id: null,
        name: '',
        type: "",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        backUps: null,
        case: null,
        isBackUpDefault: false,
        isAllMerchant: false,
        listMerchantIds: [],
        selectedBackUp: null,
        listBackUpType: [],
        listOption: [],
        status: "",
        approval: true,
        enable: true,
    }
    public newBackUp = {
        tmpId: '',
        option: '',
        id: -1,
        name: "",
        type: "",
        description: "",
        merchantIds: [],
        merchantIdsText: "",
        rules: [],
        backUps: null,
        case: this.currentCase,
        isBackUpDefault: true,
        isAllMerchant: false,
        listMerchantIds: [],
        selectedBackUp: null,
        listOption: [],
        status: "",
        listBackUpType: [],
        approval: false,
        enable: true,
        flgApprovalAllBackUp: false,
    }
    public backUpCompareOpt = 1
    public backUpCompareOpts = [{ value: 0, label: 'Compare With Default' }, { value: 1, label: 'Compare With Old' }]
    public listBackUpCompareOpt = []
    //Compare
    isCheckDetailIncompare: any = false;
    isCheckAllRuleInCompare: any = false;

    isCheckDetailBackUpIncompare: any = false;
    isCheckAllRuleBackUpInCompare: any = false;
    public switchScripts: Array<any>
    public loading: boolean;
    switchScript: SwitchScript = {
        id: 0,
        script: '',
        createDate: '',
        desc: ''
    };
    userCreate: any;
    userUpdate: any;
    dateCreate: any;
    dateUpdate: any;
    isSwitching: boolean;
    isShowingSuggest: boolean = false;
    isEnabledButtonSuggest: boolean = false;
    //
    cardListPriority = {
        "VISA": 1,
        "MASTERCARD": 2,
        "JCB": 3,
        "AMEX": 4,
    };
    ruleNamePriority = {
        "BIN": 1,
        "BINGROUP": 2,
        "ISSUER": 3,
        "BINCOUNTRY": 4,
        "CARDTYPE": 5,
    };

    fromDate: Date = new Date();
    toDate: Date = new Date();
    minDate: Date = new Date();

    ngOnInit(): void {
        this.ruleGroupId = this.route.snapshot.paramMap.get("id") ?? null;
        this.getListMerchantID();
        this.getAllInfoConfigRule();
        this.getListCase();
    }
    /**
     * Exit màn chi tiết, quay lại màn list rule group
     */
    navigateListAcquirerRule() {
        this.confirmService.build()
            .message(`<div class='content-confirm'> Are you sure to Exit ?</div>`)
            .title('Notice!')
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    this.router.navigate(['/system-management/acq-group-config']);
                }
            })
    }

    searchSwitchScript() {
        const params = new HttpParams()
            .set('keyword', this.keyword ? this.keyword.trim() : '')
        return this.hotSwitchService.getListSwitchScript(params).subscribe(data => {
            this.loading = false;
            this.switchScripts = data.list;
        });
    }


    clearSwitchScript() {
        this.switchScript.script = '';
        this.switchScript.desc = ''
        this.flgEditCase = false;
    }

    addSwitchScript() {
        const message = 'Are you sure to save this Switch Script?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (!this.flgEditCase) {
                        this.switchScript.id = 0;
                    }
                    this.hotSwitchService.addSwitchScript(this.switchScript).subscribe((res) => {
                        if (res.result == "Failed") {
                            this.toastr.error('An error occurred.');
                        } else {
                            this.toastr.success('Saved successfully.');
                        }
                        this.configSwitchScript();
                        this.getListCase();
                    })
                }
            });
    }
    deleteGroup(switch_id) {
        const message = 'Are you sure to delete switch script?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    this.hotSwitchService.DeleteSwitchScript(switch_id).subscribe(
                        (res) => {
                            if (res.result == "Success") {
                                this.toastr.success('Delete Switch Script Success', 'Success');
                            } else {
                                this.toastr.error('Delete Switch Script Failed', 'Error');
                            }
                            this.configSwitchScript();
                            this.getListCase();
                            this.reloadConfigCaseAfterDelete(switch_id);
                        })
                }
            })
    }
    reloadConfigCaseAfterDelete(id) {
        if (this.currentCase == id) {
            this.prepareConfigBackUp(1);

        } else {
            this.prepareConfigBackUp(this.currentCase);

        }
        delete this.listConfigCases[id];
        let idx = this.ruleGroup.listCases.findIndex(element => { return element.caseId === id })
        this.ruleGroup.listCases.splice(idx, 1);
    }
    configSwitchScript() {
        let request = new HttpParams()
        return this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
            this.loading = false;
            this.switchScripts = data.list;
        });
    }
    flgEditCase: any = false;
    showDetail(switch_id) {
        this.hotSwitchService.getSwitchScript(switch_id).subscribe(
            (res) => {
                this.flgEditCase = true;
                this.switchScript.id = res.id;
                this.switchScript.script = res.script;
                this.switchScript.desc = res.desc;
            })
    }
    getAcqRuleGroupById() {
        this.acquirerRuleService.GetAcqRuleGroup(this.ruleGroupId, true).subscribe(res => {
            if (!res) {
                return null;
            };
            this.setRuleGroup(res);
            if (!res['UPDATE_TYPE'].includes('INSERT')) {
                this.setOldValue(res);
            }

            this.userCreate = res['s_user_id'];
            this.userUpdate = res['s_user_confirm_id'];
            this.dateCreate = res['d_create'];
            this.dateUpdate = res['d_updated'];
            this.defaultRuleGroup = JSON.parse(JSON.stringify(this.ruleGroup));
            this.detailResponse = res;
            if (res['APPOVAL_ID']?.length > 0) this.isWaitForApproval = true;
        });
    }

    onCheckAllMerchant() {
        if (this.isAllMerchant) {
            this.listMerchantIds = _.sortBy(this.listFullMerchant, ['acqGroupId', 'name']);
        } else {
            this.listMerchantIds = this.listFullMerchant.filter(merchant => !merchant.acqGroupId)
        }
    }

    getListMerchantID() {
      let request = new HttpParams()
        .set("id", this.ruleGroupId)
        .set("source", "ACQUIRER_RULE_GROUP");;
        this.acquirerRuleService.GetAllMerchantId(request).subscribe(res => {
            if (res && res.data.length > 0) {
                this._getListMCC(res.data);
                let _data = res.data;
                _data = _.sortBy(_data, ['mcc', 'merchant_id']);
                _data.forEach((i) => {
                    let sGroup = i.acq_group_name != "" && i.acq_group_name != null ? "_(" + i.acq_group_id + "_" + i.acq_group_name + ")" : ""
                    if (i) this.listMerchantIds.push(
                        {
                            name: i.merchant_id
                                + "(" + i.mcc + "_" + i.payment_method + "_" + i.currency + "_" + i.token_cvv + ")" + sGroup,
                            value: i.merchant_id
                                + "(" + i.mcc + "_" + i.payment_method + "_" + i.currency + "_" + i.token_cvv + ")" + sGroup, acqGroupId: i.acq_group_id ?? ""
                        });
                });
                this.listFullMerchant = this.listMerchantIds;
                this.listFullMerchant.forEach((e) => {
                    let merchId = e.value.substring(0, e.value.indexOf('(')).trim();
                    if (merchId && merchId != '') {
                        this.mapListFullMerchant[merchId] = e.value
                    }
                })
                this.onCheckAllMerchant();
                if (this.ruleGroupId) {
                    this.getAcqRuleGroupById();
                }
            }
        })
    }

    _getListMCC(merchants : any) {
        const lstMccUnique = _.uniq(_.map(merchants, 'mcc'));
        this.listMCC = lstMccUnique.map((mcc: any) => ({
            name: mcc,
            value: mcc
        }));
    }

    _onChangeMCC(value, event) {
        if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
            let filterOpt = value?._filteredOptions ?? []
            if (filterOpt && filterOpt.length > 0) {
                filterOpt = filterOpt.map((e) => {
                    return e.value
                })

                this.ruleGroup.merchantIds = this.tmpSelectedMerchants.filter((e) => {
                    return !filterOpt.includes(e)
                })
                this.tmpSelectedMerchants = this.ruleGroup.merchantIds
            } else {
                this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
            }
        } else {
            this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
        }

    }

    _filterMCC(value : any) {
        value._filteredOptions = this.listMCC.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)));
    }

    setOldValue(res: any) {
        let oldValue = res['OLD_RULE_GROUP'];
        this.oldRuleGroup.approvalType = res['UPDATE_TYPE'];
        this.oldRuleGroup.id = Number(this.ruleGroupId);
        if (res['APPOVAL_ID']?.length > 0) this.oldRuleGroup.approvalId = res['APPOVAL_ID'];
        this.oldRuleGroup.name = oldValue['group_name'];
        this.oldRuleGroup.description = oldValue['description'];
        this.oldRuleGroup.merchantIds = this.getListValueFromMerchantIds(oldValue['merchant_ids'] ?? []);
        this.oldRuleGroup.approval = oldValue['approval'] ?? "false";
        if (oldValue['rules'].length > 0) {
            oldValue['rules'].forEach(rule => this.oldRuleGroup.rules.push({
                isChecked: false,
                id: rule?.id,
                source: rule?.source,
                cardType: rule.card_types ? rule.card_types.join("|") : "",
                acquirer: this.acquirersView[rule.acquirer] ? this.acquirersView[rule.acquirer] : "",
                issuer: rule.issuers ? rule.issuers.join("|") : "",
                binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
                bin: rule.bins ? rule.bins.join("|") : "",
                level: rule.level ?? "",
                binCountry: rule.bin_country ?? null,
                bankMerchantId: rule.bank_merchant_id ?? null,
                type: rule.type ?? "",
                status: rule.status ?? "",
                enable: rule.enable,
                approval: rule.approval,
                updateDate: rule.updateDate
            }));
        }
        if (oldValue['cases'].length > 0) {
            let cases = oldValue['cases'];
            let dateCases;
            cases.forEach(element => {
                let listBackUp = element?.back_ups ?? [];
                if (listBackUp.length > 0) {
                    for (let i = 0; i < listBackUp.length; i++) {
                        let rules = [];
                        listBackUp[i].tmpId = this.makeUUID();
                        listBackUp[i].option = listBackUp[i].option ? listBackUp[i].option : listBackUp[i]?.id == -1 ? '0' : 'New';
                        listBackUp[i].name = listBackUp[i]?.group_name;
                        listBackUp[i].merchantIds = this.getListValueFromMerchantIds(listBackUp[i].merchant_ids ?? []);
                        listBackUp[i].isBackUpDefault = listBackUp[i]?.is_back_up_default;
                        dateCases = listBackUp[i].updated_date;
                        listBackUp[i].rules.forEach(rule => rules.push({
                            isChecked: false,
                            id: rule?.id,
                            source: rule?.source,
                            cardType: rule.card_types ? rule.card_types.join("|") : "",
                            acquirer: this.acquirersView[rule.acquirer] ? this.acquirersView[rule.acquirer] : "",
                            issuer: rule.issuers ? rule.issuers.join("|") : "",
                            binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
                            bin: rule.bins ? rule.bins.join("|") : "",
                            level: rule.level ?? "",
                            binCountry: rule.bin_country ?? null,
                            bankMerchantId: rule.bank_merchant_id ?? null,
                            type: rule.type ?? "",
                            status: rule.status ?? "",
                            enable: rule.enable,
                            approval: rule.approval,
                            updateDate: rule?.updateDate
                        }));
                        listBackUp[i].rules = rules;

                        listBackUp[i].isCheckCaseBackUp = false;
                        listBackUp[i].isCheckDetailCaseBackUp = false;
                        listBackUp[i].isCheckAllRuleCaseBackUp = false;
                    }
                    let defaultBackUp = listBackUp.find((x) => {
                        return x.is_back_up_default && x.status != 'delete';
                    })
                    for (let i = 0; i < listBackUp.length; i++) {
                        if (defaultBackUp && defaultBackUp.tmpId == listBackUp[i].tmpId) {
                            listBackUp[i].listOption = this.option;
                            listBackUp[i].listBackUpType = this.backUpType;
                        } else {
                            listBackUp[i].listBackUpType = this.backUpType.filter((x) => {
                                return x.value == false;
                            });
                            listBackUp[i].listOption = defaultBackUp?.option == '0' ? this.option.filter((x) => {
                                return x.value != '0';
                            }) : this.option;


                        }

                    }
                }
                this.oldRuleGroup.listCases.push({
                    isChecked: false,
                    caseId: element?.case_id,
                    caseName: element?.case_name,
                    updatedDate: dateCases ? dateCases : element?.updated_date,
                    enable: element?.enable,
                    approval: element?.approval,
                    status: element?.status,
                    listBackUps: listBackUp

                })
            });
        }
    }
    setRuleGroup(res: any) {
        let newValue = res['NEW_RULE_GROUP'];
        if (res['UPDATE_TYPE'].includes('DISABLE') || res['UPDATE_TYPE'].includes('DELETE')) {
            this.isEnableDisableDelete = true;
            newValue = res['OLD_RULE_GROUP'];
            newValue.approval = "true";
        } else if (res['UPDATE_TYPE'].includes('ENABLE')) {
            this.isEnableDisableDelete = true;
            newValue = res['OLD_RULE_GROUP'];
            newValue.approval = "true";
        }
        // Nếu trạng thái switch của rule group có giá trị => rule group đang ở backup và người dùng không được sửa rule group
        this.isSwitching = newValue['activation_mode'] ? true : false;
        this.ruleGroup.approvalType = res['UPDATE_TYPE'];
        this.ruleGroup.id = Number(this.ruleGroupId);
        if (res['APPOVAL_ID']?.length > 0) this.ruleGroup.approvalId = res['APPOVAL_ID'];
        this.ruleGroup.name = newValue['group_name'];
        this.selectedMcc = {
            'name': newValue['mcc'],
        };
        this.listMCC = _.filter(this.listMCC, (_mcc) => _mcc.name === newValue['mcc']);
        this.ruleGroup.mcc = newValue['mcc'];
        this.ruleGroup.description = newValue['description'];
        this.ruleGroup.merchantIds = this.getListValueFromMerchantIds(newValue['merchant_ids'] ?? []);
        this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
        this.ruleGroup.approval = newValue['approval'] ?? false;
        if (newValue['enable'] != null) this.ruleGroup.enable = newValue['enable']
        if (this.ruleGroup.approvalType != null && this.ruleGroup.approvalType == 'DISABLE_ACQ_RULE_GROUP') this.ruleGroup.enable = false
        if (newValue['rules'].length > 0) {
            // const rules = this._sortRules(newValue['rules']);
            const rules = newValue['rules'];
            rules.forEach(rule => this.ruleGroup.rules.push({
                isChecked: false,
                id: rule?.id,
                source: rule?.source,
                cardType: rule.card_types ? rule.card_types.join("|") : "",
                acquirer: this.acquirersView[rule.acquirer] ? this.acquirersView[rule.acquirer] : "",
                issuer: rule.issuers ? rule.issuers.join("|") : "",
                binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
                bin: rule.bins ? rule.bins.join("|") : "",
                level: rule.level ?? "",
                binCountry: rule.bin_country ?? null,
                bankMerchantId: rule.bank_merchant_id ?? null,
                type: rule.type ?? "",
                status: rule.status ?? "",
                enable: rule.enable,
                approval: rule.approval,
                updateDate: rule.updateDate
            }));
        }
        if (newValue['cases'].length > 0) {
            let cases = newValue['cases'];
            let dateCases;
            cases.forEach(element => {
                let listBackUp = element?.back_ups ?? [];
                if (listBackUp.length > 0) {
                    for (let i = 0; i < listBackUp.length; i++) {
                        let rules = [];
                        listBackUp[i].tmpId = this.makeUUID();
                        listBackUp[i].option = listBackUp[i].option ? listBackUp[i].option : listBackUp[i]?.id == -1 ? '0' : 'New';
                        listBackUp[i].name = listBackUp[i]?.group_name;
                        listBackUp[i].isBackUpDefault = listBackUp[i]?.is_back_up_default;
                        listBackUp[i].merchantIds = this.getListValueFromMerchantIds(listBackUp[i].merchant_ids ?? []);
                        listBackUp[i].tmpSelectedMerchants = listBackUp[i].merchantIds
                        dateCases = listBackUp[i].updated_date;
                        listBackUp[i].rules.forEach(rule => rules.push({
                            isChecked: false,
                            id: rule?.id,
                            source: rule?.source,
                            cardType: rule.card_types ? rule.card_types.join("|") : "",
                            acquirer: this.acquirersView[rule.acquirer] ? this.acquirersView[rule.acquirer] : "",
                            issuer: rule.issuers ? rule.issuers.join("|") : "",
                            binGroup: rule.bin_groups ? rule.bin_groups.join("|") : "",
                            bin: rule.bins ? rule.bins.join("|") : "",
                            level: rule.level ?? "",
                            binCountry: rule.bin_country ?? null,
                            bankMerchantId: rule.bank_merchant_id ?? null,
                            type: rule.type ?? "",
                            status: rule.status ?? "",
                            enable: rule.enable,
                            approval: rule.approval,
                            updateDate: rule?.updateDate
                        }));
                        listBackUp[i].rules = rules;
                        listBackUp[i].flgApprovalAllBackUp = listBackUp[i].approval;
                        if (listBackUp[i].rules.filter(rule => {
                            return rule.approval === true
                        }).length > 0) {
                            listBackUp[i].flgApprovalAllBackUp = true
                        }

                        listBackUp[i].isCheckCaseBackUp = false;
                        listBackUp[i].isCheckDetailCaseBackUp = false;
                        listBackUp[i].isCheckAllRuleCaseBackUp = false;
                    }
                    let defaultBackUp = listBackUp.find((x) => {
                        return x.is_back_up_default && x.status != 'delete';
                    })
                    for (let i = 0; i < listBackUp.length; i++) {
                        if (defaultBackUp && defaultBackUp.tmpId == listBackUp[i].tmpId) {
                            listBackUp[i].listOption = this.option;
                            listBackUp[i].listBackUpType = this.backUpType;
                        } else {
                            listBackUp[i].listBackUpType = listBackUp[i].status != 'delete' ? this.backUpType.filter((x) => {
                                return x.value == false;
                            }) : this.backUpType;
                            listBackUp[i].listOption = defaultBackUp?.option == '0' ? this.option.filter((x) => {
                                return x.value != '0';
                            }) : this.option;


                        }

                    }
                }
                this.ruleGroup.listCases.push({
                    isChecked: false,
                    caseId: element?.case_id,
                    caseName: element?.case_name,
                    updatedDate: dateCases ? dateCases : element?.updated_date,
                    enable: element?.enable,
                    approval: element?.approval,
                    status: element?.status,
                    listBackUps: listBackUp,
                    isWaitForApprovalInCase: element?.approval
                })
            });
        }
    }

    getListValueFromMerchantIds(list) {
        let listValue = [];
        list.forEach((e) => {


            if (e.trim() in this.mapListFullMerchant) {
                listValue.push(this.mapListFullMerchant[e.trim()]);
            }
        })
        return listValue;

    }



    getKeyByValue(object, value) {
        return Object.keys(object).find(key =>
            object[key] === value);
    }

    compareAcquirerRuleGroupDefault() {
        this.newRuleGroup.rules = [];
        this.newRuleGroup = JSON.parse(JSON.stringify(this.ruleGroup));
        if (this.newRuleGroup.approval || this.newRuleGroup.rules.filter(rule => {
            return rule.approval
        }).length > 0) {
            this.isWaitForApprovalInCompare = true;
        }

        this.defaultNewRuleGroup = JSON.stringify(this.newRuleGroup);
        this.highlightedMerchantIds = this.getHighlightMerchantId(this.newRuleGroup?.merchantIds, this.oldRuleGroup?.merchantIds)
        this.tmpCompareSelectedMerchants = this.newRuleGroup.merchantIds
    }

    removeComapareMerchantID(id) {
        var index = this.newRuleGroup.merchantIds.indexOf(id);
        if (index !== -1) {
            this.newRuleGroup.merchantIds.splice(index, 1);
            this.tmpCompareSelectedMerchants = this.newRuleGroup.merchantIds
            // this.onChangeMerchantSelect(true)
        }
        this.highlightedMerchantIds = this.getHighlightMerchantId(this.newRuleGroup?.merchantIds, this.oldRuleGroup?.merchantIds)
        this.changeNewValueDetail()

    }

    selectAllRule() {
        if (this.isCheckAllRule) {
            this.ruleGroup.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = true;
                }
            });
        } else {
            this.ruleGroup.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = false;
                }
            });
        }
    }
    selectCaseBackUp(index: any) {
        if (this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckCaseBackUp) {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp = true;
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckDetailCaseBackUp = true;
            this.selectAllRuleBackUpCase(index);
        } else {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp = false;
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckDetailCaseBackUp = false;
            this.selectAllRuleBackUpCase(index);
        }
    }

    checkDetailCaseBackUp(index: any) {
        if (this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp && this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckDetailCaseBackUp) {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckCaseBackUp = true;
        } else {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckCaseBackUp = false;
        }
    }

    selectAllRuleBackUpCase(index: any) {
        if (this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp) {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = true;
                }
            })
        } else {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = false;
                }
            })
        }
        this.checkDetailCaseBackUp(index);
    }
    onCkeckedRuleBackUpCase(index: any) {
        if (this.listConfigCases[this.currentCase].listBackUps[Number(index)].rules.filter((element) => {
            return element.isChecked === false;
        }).length > 0) {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp = false;
        } else {
            this.listConfigCases[this.currentCase].listBackUps[Number(index)].isCheckAllRuleCaseBackUp = true;
        }
    }

    onChecked(index: any) {
        if (this.ruleGroup.rules.filter((element) => {
            return element.isChecked === false;
        }).length > 0) {
            this.isCheckAllRule = false;
        } else {
            this.isCheckAllRule = true;
        }
    }

    selectAllListCase() {
        if (this.isCheckAllListCase) {
            this.ruleGroup.listCases.forEach(element => {
                if (element.approval) {
                    element.isChecked = true;
                }
            });
        } else {
            this.ruleGroup.listCases.forEach(element => {
                if (element.approval) {
                    element.isChecked = false;
                }
            });
        }
    }

    onCheckedCase(index: any) {
        if (this.ruleGroup.listCases.filter((element) => {
            return element.isChecked === false;
        }).length > 0) {
            this.isCheckAllListCase = false;
        } else {
            this.isCheckAllListCase = true;
        }
    }
    changeValue() {
        if (JSON.stringify(this.ruleGroup) === JSON.stringify(this.defaultRuleGroup)) {
            this.isChangeDefaultRuleGroup = false;
        } else {
            this.isChangeDefaultRuleGroup = true;
        }
    }
    // Kiểm tra khối thông tin cơ bản của acq_rule_group có sự thay đổi hay không
    isChangeBasicInfoBlock() {
      let isChangeGroupName = false;
      let isChangeDescription = false;
      let isChangeMerchantIds = false;
      isChangeGroupName = JSON.stringify(this.ruleGroup.name) === JSON.stringify(this.defaultRuleGroup.name);
      isChangeDescription = JSON.stringify(this.ruleGroup.description) === JSON.stringify(this.defaultRuleGroup.description);
      isChangeMerchantIds = JSON.stringify(this.ruleGroup.merchantIds) === JSON.stringify(this.defaultRuleGroup.merchantIds);
      return isChangeGroupName || isChangeDescription || isChangeMerchantIds;
    }
    // Kiểm tra khối thông tin rule có sự thay đổi hay không
    isChangeRulesBlock() {
      return JSON.stringify(this.ruleGroup.rules) === JSON.stringify(this.defaultRuleGroup.rules);
    }
    // Kiểm tra khối thông tin backup có sự thay đổi hay không
    isChangeBackupsBlock() {
      return JSON.stringify(this.ruleGroup.listCases) === JSON.stringify(this.defaultRuleGroup.listCases);
    }
    changeValueBackUp() {
        if (JSON.stringify(this.listConfigCases) === JSON.stringify(this.defaultListConfigCase)) {
            this.isChangeInConfigBackUp = false;
        } else {
            this.isChangeInConfigBackUp = true;
        }
    }

    changeValueDetailBackUp(index: any) {
        let df;
        let dfApproval;
        let current;
        this.defaultListConfigCase[this.currentCase]?.listBackUps.forEach((backUp, i) => {
            if (i === Number(index)) {
                df = JSON.parse(JSON.stringify(backUp));
                dfApproval = JSON.parse(JSON.stringify(backUp));
                delete df.approval;
                delete df.flgApprovalAllBackUp;
            }
        })
        this.listConfigCases[this.currentCase]?.listBackUps.forEach((backUp, i) => {
            if (i === Number(index)) {
                current = JSON.parse(JSON.stringify(backUp));
                delete current.approval;
                delete current.flgApprovalAllBackUp;
                // if (current.is_back_up_default || current.merchantIds.length === 0) {
                delete current.listMerchantIds;
                // }
                if (current?.description?.length === 0) {
                    delete current.description;
                }
                if (df?.description?.length === 0) {
                    delete df.description;
                }
                if (JSON.stringify(df) === JSON.stringify(current)) {
                    backUp.approval = dfApproval.approval;
                    this.isChangeInConfigBackUp = false;
                } else {
                    backUp.approval = true;
                    this.isChangeInConfigBackUp = true;
                }

                if (backUp.approval || backUp?.rule?.filter(rule => { return rule.approval })) {
                    backUp.flgApprovalAllBackUp = true;
                } else {
                    backUp.flgApprovalAllBackUp = false;
                }
            }
        })
    }

    onChangeCompareBackUpMerchant(value, event) {
        if (!event.itemValue && event.value && event.value.length == 0 && this.newBackUp.listMerchantIds.length > 0) {
            //delete all
            let filterOpt = value?._filteredOptions ?? []
            if (filterOpt && filterOpt.length > 0) {
                // in filtering
                filterOpt = filterOpt.map((e) => {
                    return e.value
                })

                this.newBackUp.merchantIds = this.tmpCompareSelectedMerchants.filter((e) => {
                    return !filterOpt.includes(e)
                })
                this.tmpCompareSelectedMerchants = this.newBackUp.merchantIds


            } else {
                this.tmpCompareSelectedMerchants = this.newBackUp.merchantIds
            }
        } else {
            this.tmpCompareSelectedMerchants = this.newBackUp.merchantIds
        }
        this.highlightedMerchantIds = this.getHighlightMerchantId(this.newBackUp?.merchantIds, this.oldBackUp?.merchantIds)
        this.changeValueDetailBackUpInCompare()
    }
    changeValueDetailBackUpInCompare() {
        let current;
        let df;
        current = JSON.parse(JSON.stringify(this.newBackUp));
        df = JSON.parse(JSON.stringify(this.defaultNewBackUp));
        delete current.approval;
        delete df.approval;
        if (current?.description?.length === 0) {
            delete current.description;
        }
        if (df?.description?.length === 0) {
            delete df.description;
        }

        if (JSON.stringify(current) === JSON.stringify(df)) {
            this.isChangeBackUpBackUpInCompare = false;
            this.newBackUp.approval = this.defaultNewBackUp.approval;
        } else {
            this.isChangeBackUpBackUpInCompare = true;
            this.newBackUp.approval = true;
        }
    }
    changeValueBackUpInCompare() {
        let current;
        current = JSON.stringify(this.newBackUp);
        current = JSON.parse(current);
        if (current?.description?.length === 0) {
            delete current.description;
        }
        if (JSON.stringify(current) === this.defaultNewBackUp) {
            this.isChangeBackUpBackUpInCompare = false;
        } else {
            this.isChangeBackUpBackUpInCompare = true;
        }
    }


    changeValueDetail() {
        let current;
        let df;
        current = JSON.parse(JSON.stringify(this.ruleGroup));
        df = JSON.parse(JSON.stringify(this.defaultRuleGroup));
        delete current?.approval;
        delete df?.approval;
        if (JSON.stringify(current) === JSON.stringify(df)) {
            this.isChangeDefaultRuleGroup = false;
            this.ruleGroup.approval = this.defaultRuleGroup.approval;
        } else {
            this.isChangeDefaultRuleGroup = true;
            this.ruleGroup.approval = true;
        }
    }
    _onChangeMerchant(value, event) {
        // handle delete all

        if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
            //delete all
            let filterOpt = value?._filteredOptions ?? []
            if (filterOpt && filterOpt.length > 0) {
                // in filtering
                filterOpt = filterOpt.map((e) => {
                    return e.value
                })

                this.ruleGroup.merchantIds = this.tmpSelectedMerchants.filter((e) => {
                    return !filterOpt.includes(e)
                })
                this.tmpSelectedMerchants = this.ruleGroup.merchantIds
            } else {
                this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
            }
        } else {
            this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
        }
        this.onChangeGroupMerchantSelect()

    }

    onChangeGroupMerchantSelect() {


        //gen back ups merchant
        for (var i = 0; i < this.ruleGroup?.listCases?.length; i++) {
            if (this.ruleGroup?.listCases[i].listBackUps?.length > 0) {
                for (var j = 0; j < this.ruleGroup?.listCases[i].listBackUps.length; j++) {
                    if (this.ruleGroup?.listCases[i].listBackUps[j]?.merchantIds) {
                        this.ruleGroup.listCases[i].listBackUps[j].merchantIds = this.ruleGroup?.listCases[i].listBackUps[j].merchantIds.filter((e) => { return this.ruleGroup.merchantIds.includes(e) })
                    }
                }
            }
        }
        this.changeValueDetail()
    }

    _filter(value) {
        if (value._filterValue.includes(" ")) {

            let listSearchMerchant = []
            listSearchMerchant = this.getListValueFromMerchantIds(value._filterValue.toUpperCase().split(" ") ?? []);
            if (listSearchMerchant && listSearchMerchant.length > 0) {

                value._filteredOptions = this.listMerchantIds.filter((item) => listSearchMerchant.includes(item.value));
            }
        } else {
            value._filteredOptions = this.listMerchantIds.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

        }

    }
    _remove(value) {
        console.log(value)
    }
    onChangeCompareMerchant(value, event) {
        if (!event.itemValue && event.value && event.value.length == 0 && this.listMerchantIds.length > 0) {
            //delete all
            let filterOpt = value?._filteredOptions ?? []
            if (filterOpt && filterOpt.length > 0) {
                // in filtering
                filterOpt = filterOpt.map((e) => {
                    return e.value
                })

                this.newRuleGroup.merchantIds = this.tmpCompareSelectedMerchants.filter((e) => {
                    return !filterOpt.includes(e)
                })
                this.tmpCompareSelectedMerchants = this.newRuleGroup.merchantIds


            } else {
                this.tmpCompareSelectedMerchants = this.newRuleGroup.merchantIds
            }
        } else {
            this.tmpCompareSelectedMerchants = this.newRuleGroup.merchantIds
        }
        this.highlightedMerchantIds = this.getHighlightMerchantId(this.newRuleGroup?.merchantIds, this.oldRuleGroup?.merchantIds);
        this.changeNewValueDetail()
    }

    changeNewValueDetail() {
        if (JSON.stringify(this.newRuleGroup) === this.defaultNewRuleGroup) {
            this.isChangeDefaultNewRuleGroup = false;
            this.newRuleGroup.approval = false;
        } else {
            this.isChangeDefaultNewRuleGroup = true;
            this.newRuleGroup.approval = true;
        }
    }

    changeNewValue() {
        if (JSON.stringify(this.newRuleGroup) === this.defaultNewRuleGroup) {
            this.isChangeDefaultNewRuleGroup = false;
        } else {
            this.isChangeDefaultNewRuleGroup = true;
        }
    }
    //Add Acquirer Rule
    getAllInfoConfigRule() {
        this.acquirerRuleService.GetInfomationConfigAcqRule().subscribe(data => {
            data.issuer.forEach(value => {
                this.issuers.push({ name: value, value: value });
            });
            data.binGroup.forEach(value => {
                this.binGroup.push({ name: value, value: value });
            })
            data.binGroupInfo.forEach(value => {
                this.binGroupInfo.push({ name: value['groupName'], source: value['source'] == null ? '' :  value['source'] });
            });

            for (const key in data.acquirer) {
                this.acquirers.push({ name: key, value: data.acquirer[key] })
                this.acquirersView[data.acquirer[key]] = key;
            }
            this.listBankMid = data.bankMid;

        });
    }

    selectAllRuleInComapre() {
        if (this.isCheckAllRuleInCompare) {
            this.newRuleGroup.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = true;
                }
            });
        } else {
            this.newRuleGroup.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = false;
                }
            });
        }
    }
    selectAllRuleBackUpInCompare() {
        if (this.isCheckAllRuleBackUpInCompare) {
            this.newBackUp.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = true;
                }
            });
        } else {
            this.newBackUp.rules.forEach(element => {
                if (element.approval) {
                    element.isChecked = false;
                }
            });
        }
    }
    onCheckedBackUpInCompare(index: any) {
        if (this.newBackUp.rules.filter((element) => {
            return element.isChecked === false;
        }).length > 0) {
            this.isCheckAllRuleBackUpInCompare = false;
        } else {
            this.isCheckAllRuleBackUpInCompare = true;
        }
    }
    onCheckedInCompare(index: any) {
        if (this.newRuleGroup.rules.filter((element) => {
            return element.isChecked === false;
        }).length > 0) {
            this.isCheckAllRuleInCompare = false;
        } else {
            this.isCheckAllRuleInCompare = true;
        }
    }
    /**
     * Lấy danh sách list merchant id để gửi request API Approve
     */
    getMerchantIdFromListValue(list) {
        let listMerchantId = [];
        list.forEach((e) => {
            let item = this.getKeyByValue(this.mapListFullMerchant, e.trim());
            if (item && item != '') {
                listMerchantId.push(item);
            }
        })
        return listMerchantId;
    }

    rejectInCompare() {
        const message = 'Are you sure to reject?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.newRuleGroup.approvalType.includes("INSERT") && this.isCheckDetailIncompare) {
                        this.isCheckDetailIncompare = false;
                        this.newRuleGroup.status = 'delete';
                        this.ruleGroup = JSON.parse(JSON.stringify(this.newRuleGroup));
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/reject'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Reject");
                            window.document.getElementById("closeCompare").click();
                            this.router.navigate(['/system-management/acq-group-config']);
                        });
                    }
                    if (this.newRuleGroup.approvalType.includes("INSERT") && !this.isCheckDetailIncompare) {
                        if (this.validateEmptyRuleReject(this.newRuleGroup.rules.filter((element) => {
                            return !element.isChecked;
                        }))) {
                            this.newRuleGroup.rules = this.newRuleGroup.rules.filter((element) => {
                                return !element.isChecked;
                            })
                        } else {
                            return;
                        }

                    } else if (!this.newRuleGroup.approvalType.includes("INSERT")) {
                        if (this.isCheckDetailIncompare) {
                            this.isCheckDetailIncompare = false;
                            this.newRuleGroup.approval = false;
                            this.newRuleGroup.name = this.oldRuleGroup.name;
                            this.newRuleGroup.description = this.oldRuleGroup.description;
                            this.newRuleGroup.merchantIds = this.oldRuleGroup.merchantIds;
                        };
                        if (this.validateEmptyRuleReject(this.newRuleGroup.rules.filter(element => {
                            return element.id !== 0 || !element.isChecked
                        })
                        )) {
                            this.newRuleGroup.rules = this.newRuleGroup.rules.filter(element => {
                                return element.id !== 0 || !element.isChecked
                            })
                        } else {
                            return;
                        }

                        this.newRuleGroup.rules.forEach((rule, i) => {
                            if (rule.id !== 0 && rule.isChecked) {
                                rule.isChecked = false;
                                let idRule = rule.id;
                                let oldRule = this.oldRuleGroup.rules.find((e) => {
                                    return idRule === e.id;
                                })
                                this.newRuleGroup.rules[i] = oldRule;
                            }
                        })
                    }
                    this.ruleGroup = JSON.parse(JSON.stringify(this.newRuleGroup));
                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/reject'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Reject");
                            this.initFlg();
                            window.location.reload();
                        });
                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Reject Group successfully.');
                                if (this.newRuleGroup.approval || this.newRuleGroup.rules.filter(rule => {
                                    return rule.approval
                                }).length > 0) {
                                    this.isWaitForApprovalInCompare = true;
                                } else {
                                    this.isWaitForApprovalInCompare = false;
                                }
                            }
                        });
                    }
                }
            });
    }

    approvalBackUpInCompare() {
        const message = 'Are you sure to approve?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.isCheckDetailBackUpIncompare) {
                        this.isCheckDetailBackUpIncompare = false;
                        this.newBackUp.approval = false;
                    }
                    this.newBackUp.rules.forEach(element => {
                        if (element.isChecked) {
                            element.isChecked = false;
                            element.approval = false;
                        }
                    })
                    if (this.newBackUp.tmpId && this.newBackUp.tmpId != '') {
                        this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
                            if (c.tmpId == this.newBackUp.tmpId) {
                                if (this.newBackUp.approval === true || this.newBackUp.rules.filter(rule => { return rule.approval }).length > 0) {
                                    this.newBackUp.flgApprovalAllBackUp = true;
                                } else {
                                    this.newBackUp.flgApprovalAllBackUp = false;
                                }
                                this.listConfigCases[this.currentCase].listBackUps[index] = this.newBackUp
                                // reset all list merchant option, option, list select, list type
                            } else {
                                if (this.newBackUp.isBackUpDefault && this.newBackUp.status != 'delete') {
                                    this.listConfigCases[this.currentCase].listBackUps[index].isBackUpDefault = false;
                                }
                            }
                        })
                    }
                    // save
                    this.ruleGroup.listCases = []
                    for (let item in this.listConfigCases) {
                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.isCheckDetailCaseBackUp) {
                                backUp.isCheckDetailCaseBackUp = false;
                                backUp.approval = false;
                            }
                            backUp.isCheckAllRuleCaseBackUp = false;
                            backUp.rules.forEach(rule => {
                                if (rule.isChecked) {
                                    rule.isChecked = false;
                                    rule.approval = false;
                                }
                            })
                        })

                        if (this.listConfigCases[item].listBackUps.filter(backUp => {
                            return backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0
                        }).length > 0) {
                            this.listConfigCases[item].isWaitForApprovalInCase = true;
                            this.listConfigCases[item].approval = true;
                        } else {
                            this.listConfigCases[item].isWaitForApprovalInCase = false;
                            this.listConfigCases[item].approval = false;
                        }

                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.approval === true || backUp.rules.filter(rule => { return rule.approval === true }).length > 0) {
                                backUp.flgApprovalAllBackUp = true;
                            } else {
                                backUp.flgApprovalAllBackUp = false;
                            }
                        })

                        if (this.listConfigCases[item].listBackUps.length > 0) {
                            this.ruleGroup.listCases.push(this.listConfigCases[item]);
                        }

                    }

                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/approve'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Approval");
                            this.initFlg();
                            window.location.reload();
                        });
                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Approval Group successfully.');
                            }
                        });
                    }

                    if (!this.newBackUp.approval && this.newBackUp.rules.filter(rule => { return rule.approval }).length === 0) {
                        this.isWaitForApprovalBackUpInCompare = false;
                    } else {
                        this.isWaitForApprovalBackUpInCompare = true;
                    }
                }
            })
    }

    rejectBackUpInCompare() {
        const message = 'Are you sure to reject?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.newBackUp.id === 0) {
                        if (this.isCheckDetailBackUpIncompare) {
                            this.newBackUp.approval = false;
                            this.isCheckDetailBackUpIncompare = false;
                            if (this.newBackUp.tmpId && this.newBackUp.tmpId != '') {
                                this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
                                    if (c.tmpId == this.newBackUp.tmpId) {
                                        this.listConfigCases[this.currentCase].listBackUps.splice(index, 1);
                                    }
                                })
                                this.resetAllBackUpOptList();
                                this.submitRejectBackUpIncompare();
                                return;
                            }
                        } else {
                            this.isCheckAllRuleBackUpInCompare = false;
                            if (this.newBackUp.status != 'delete') {
                                if (this.validateEmptyRuleReject(this.newBackUp.rules.filter((element) => {
                                    return !element.isChecked;
                                }))) {
                                    this.newBackUp.rules = this.newBackUp.rules.filter((element) => {
                                        return !element.isChecked;
                                    })
                                } else {
                                    return;
                                }
                            }

                            // // save
                            if (this.newBackUp.tmpId && this.newBackUp.tmpId != '') {
                                this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
                                    if (c.tmpId == this.newBackUp.tmpId) {
                                        this.listConfigCases[this.currentCase].listBackUps[index] = this.newBackUp
                                    }
                                })
                            }
                            this.resetAllBackUpOptList();
                            this.submitRejectBackUpIncompare();
                        }

                    } else {
                        let detailOldBk;
                        this.oldRuleGroup.listCases.forEach(listcases => {
                            listcases.listBackUps.forEach(backUp => {
                                if (backUp.id === this.newBackUp.id) {
                                    detailOldBk = JSON.parse(JSON.stringify(backUp));

                                }
                            })
                        })

                        this.isCheckAllRuleBackUpInCompare = false;
                        if (this.newBackUp.status != 'delete') {
                            if (this.validateEmptyRuleReject(this.newBackUp.rules.filter(element => {
                                return element.id !== 0 || !element.isChecked
                            }))) {
                                this.newBackUp.rules = this.newBackUp.rules.filter(element => {
                                    return element.id !== 0 || !element.isChecked
                                })
                            } else {
                                return;
                            }
                        }
                        if (this.isCheckDetailBackUpIncompare) {
                            this.isCheckDetailBackUpIncompare = false;
                            this.newBackUp.approval = false;
                            this.newBackUp.name = detailOldBk.group_name;
                            this.newBackUp.isBackUpDefault = detailOldBk.is_back_up_default;
                            this.newBackUp.description = detailOldBk.description;
                            this.newBackUp.merchantIds = detailOldBk.merchantIds;
                            if (this.newBackUp.status === 'delete') this.newBackUp.status = 'enable';
                        }


                        this.newBackUp.rules.forEach((rule, i) => {
                            if (rule.id !== 0 && rule.isChecked) {
                                rule.isChecked = false;
                                let idRule = rule.id;
                                let oldRule = detailOldBk.rules.find((e) => {
                                    return idRule === e.id
                                })
                                this.newBackUp.rules[i] = oldRule;
                            }
                        })
                        if (this.newBackUp.tmpId && this.newBackUp.tmpId != '') {
                            this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
                                if (c.tmpId == this.newBackUp.tmpId) {
                                    this.listConfigCases[this.currentCase].listBackUps[index] = this.newBackUp
                                    if (this.newBackUp.approval === true || this.newBackUp.rules.filter(rule => {
                                        return rule.approval === true;
                                    }).length > 0) {
                                        this.listConfigCases[this.currentCase].listBackUps[index].flgApprovalAllBackUp = true;
                                    } else {
                                        this.listConfigCases[this.currentCase].listBackUps[index].flgApprovalAllBackUp = false;
                                    }
                                }
                            })
                        }
                        this.resetAllBackUpOptList();
                        this.submitRejectBackUpIncompare();
                    }

                }
            })

    }

    submitRejectBackUpIncompare() {
        this.ruleGroup.listCases = [];
        for (let item in this.listConfigCases) {
            if (this.listConfigCases[item].listBackUps.filter(backUp => {
                return backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0
            }).length > 0) {
                this.listConfigCases[item].isWaitForApprovalInCase = true;
                this.listConfigCases[item].approval = true;
            } else {
                this.listConfigCases[item].isWaitForApprovalInCase = false;
                this.listConfigCases[item].approval = false;
            }

            this.listConfigCases[item].listBackUps.forEach(backUp => {
                if (backUp.approval === true || backUp.rules.filter(rule => { return rule.approval === true }).length > 0) {
                    backUp.flgApprovalAllBackUp = true;
                } else {
                    backUp.flgApprovalAllBackUp = false;
                }
            })

            if (this.listConfigCases[item].listBackUps.length > 0) {
                this.ruleGroup.listCases.push(this.listConfigCases[item]);
            }
        }

        this.ruleGroup.listCases.forEach(element => {
            if (element.listBackUps.filter(backup => {
                return backup.flgApprovalAllBackUp === true
            }).length > 0) {
                element.approval = true;
            } else {
                element.approval = false;
            }
        })

        if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
            const body = {
                id: Number(this.ruleGroup.approvalId),
                newValue: this.prepareRuleGroupData(),
                path: '/reject'
            };
            return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                this.toastr.success('Successful', "Reject");
                this.initFlg();
                window.location.reload();
                // window.document.getElementById("closeCompareBackUp").click();
            });

        } else {
            this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                if (data.status && data.status == "Successful") {
                    this.toastr.success('Reject Group successfully.');
                }
            });
        }
    }


    approvalInCompare() {
        const message = 'Are you sure to approve?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.isCheckDetailIncompare) {
                        this.newRuleGroup.approval = false;
                    }
                    this.newRuleGroup.rules.forEach(element => {
                        if (element.isChecked) element.approval = false;
                    })

                    this.isCheckAllRuleInCompare = false;
                    this.ruleGroup = JSON.parse(JSON.stringify(this.newRuleGroup));
                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/approve'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Approval");
                            this.initFlg();
                            window.location.reload();
                        });

                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Saved Group successfully.');
                                if (this.newRuleGroup.approval || this.newRuleGroup.rules.filter(rule => {
                                    return rule.approval
                                }).length > 0) {
                                    this.isWaitForApprovalInCompare = true;
                                } else {
                                    this.isWaitForApprovalInCompare = false;
                                }
                            }
                        });
                    }
                }
            })
    }

    initFlg() {
        this.ruleGroup.listCases.forEach(c => {
            c.isWaitForApprovalInCase = false;
        })
        this.isChangeDefaultNewRuleGroup = false;
        this.isWaitForApprovalInCompare = false;
        this.isWaitForApproval = false;
        this.isChangeDefaultRuleGroup = false;
    }
    /**
     * Reject thay đổi rule group
     */
    rejectAcqRuleGroup() {
        const message = 'Are you sure to reject?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.ruleGroup.approvalType.includes("INSERT") && this.isCheckDetailAcqRuleGroup) {
                        this.isCheckDetailAcqRuleGroup = false;
                        this.ruleGroup.status = 'delete';
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/reject'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Reject");
                            this.router.navigate(['/system-management/acq-group-config']);
                        });
                    }

                    if (this.ruleGroup.approvalType.includes("INSERT") && !this.isCheckDetailAcqRuleGroup) {
                        if (this.validateEmptyRuleReject(this.ruleGroup.rules.filter((element) => {
                            return !element.isChecked;
                        }))) {
                            this.ruleGroup.rules = this.ruleGroup.rules.filter((element) => {
                                return !element.isChecked;
                            })
                        } else {
                            return;
                        }

                        this.ruleGroup.listCases = this.ruleGroup.listCases.filter((element) => {
                            return !element.isChecked;
                        })

                    } else if (!this.ruleGroup.approvalType.includes("INSERT")) {
                        if (this.isCheckDetailAcqRuleGroup) {
                            this.isCheckDetailAcqRuleGroup = false;
                            this.ruleGroup.approval = false;
                            this.ruleGroup.name = this.oldRuleGroup.name;
                            this.selectedMcc = {
                                'name': this.oldRuleGroup.mcc,
                            };
                            this.ruleGroup.mcc = this.oldRuleGroup.mcc;
                            this.ruleGroup.description = this.oldRuleGroup.description;
                            this.ruleGroup.merchantIds = this.oldRuleGroup.merchantIds;
                        };
                        if (this.validateEmptyRuleReject(this.ruleGroup.rules.filter(element => {
                            return element.id !== 0 || !element.isChecked
                        }))) {
                            this.ruleGroup.rules = this.ruleGroup.rules.filter(element => {
                                return element.id !== 0 || !element.isChecked
                            })
                        } else {
                            return;
                        }

                        this.ruleGroup.rules.forEach((rule, i) => {
                            if (rule.id !== 0 && rule.isChecked) {
                                if (!_.includes(this.rejectIds, rule.id))
                                    this.rejectIds = _.concat(this.rejectIds, rule.id);
                                this.actionName = 'REJECT';
                                rule.isChecked = false;
                                let idRule = rule.id;
                                let oldRule = this.oldRuleGroup.rules.find((e) => {
                                    return idRule === e.id;
                                })
                                this.ruleGroup.rules[i] = oldRule;
                            }
                        })
                        this.ruleGroup.listCases = this.ruleGroup.listCases.filter(e => {
                            return !e.isChecked || this.oldRuleGroup?.listCases.find(c => { return c.caseId === e.caseId; });
                        })

                        this.ruleGroup.listCases.forEach((element, index) => {
                            if (element.isChecked) {
                                element.isChecked = false;

                                let id = element.caseId;
                                let oldVl = this.oldRuleGroup?.listCases.find(c => {
                                    return c.caseId === id;
                                })
                                this.ruleGroup.listCases[index] = JSON.parse(JSON.stringify(oldVl));
                            }
                        })
                    }
                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/reject'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Reject");
                            this.initFlg();
                            window.location.reload();
                        });
                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Reject Group successfully.');
                            }
                        });
                    }
                }
            })
    }
    rejectBackUp() {
        const message = 'Are you sure to reject?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    // save
                    this.ruleGroup.listCases = [];
                    let flgValidateRule = true;
                    for (let item in this.listConfigCases) {
                        this.listConfigCases[item].listBackUps = this.listConfigCases[item].listBackUps.filter(backUp => {
                            return backUp.id !== 0 || !backUp.isCheckDetailCaseBackUp
                        })

                        this.listConfigCases[item].listBackUps = this.listConfigCases[item].listBackUps.filter(backUp => {
                            return !(backUp.id == -1 && backUp.isCheckDetailCaseBackUp)
                        })

                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.id === 0 && !backUp.isCheckDetailCaseBackUp) {
                                backUp.isCheckAllRuleCaseBackUp = false;
                                if (backUp.status !== 'delete') {
                                    if (this.validateEmptyRuleReject(backUp.rules.filter(element => {
                                        return element.id !== 0 || !element.isChecked
                                    }))) {
                                        backUp.rules = backUp.rules.filter(element => {
                                            return element.id !== 0 || !element.isChecked
                                        })
                                    } else {
                                        flgValidateRule = false;
                                        return;
                                    }
                                }
                            }
                            if (backUp.id !== 0 && backUp.id !== -1) {
                                let oldBackUp;
                                this.oldRuleGroup.listCases.forEach(c => {
                                    c.listBackUps.forEach(bk => {
                                        if (backUp.id === bk.id) {
                                            oldBackUp = JSON.parse(JSON.stringify(bk));
                                        }
                                    })
                                })

                                backUp.isCheckAllRuleCaseBackUp = false;
                                if (backUp.status !== 'delete') {
                                    if (this.validateEmptyRuleReject(backUp.rules.filter(element => {
                                        return element.id !== 0 || !element.isChecked
                                    }))) {
                                        backUp.rules = backUp.rules.filter(element => {
                                            return element.id !== 0 || !element.isChecked
                                        })
                                    } else {
                                        flgValidateRule = false;
                                        return;
                                    }
                                }

                                if (backUp.isCheckDetailCaseBackUp) {
                                    backUp.approval = false;
                                    backUp.isCheckDetailCaseBackUp = false;
                                    backUp.name = oldBackUp?.group_name;
                                    backUp.isBackUpDefault = oldBackUp?.is_back_up_default;
                                    backUp.description = oldBackUp?.description;
                                    backUp.merchantIds = oldBackUp?.merchantIds;
                                    if (backUp.status === 'delete') backUp.status = 'enable';
                                }


                                backUp.rules.forEach((rule, i) => {
                                    if (rule.id !== 0 && rule.isChecked) {
                                        rule.isChecked = false;
                                        let idRule = rule.id;
                                        let oldRule = oldBackUp.rules.find((e) => {
                                            return idRule === e.id;
                                        })
                                        backUp.rules[i] = oldRule;
                                    }
                                })
                            }
                        })

                        if (this.listConfigCases[item].listBackUps.filter(backUp => {
                            return backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0
                        }).length > 0) {
                            this.listConfigCases[item].isWaitForApprovalInCase = true;
                            this.listConfigCases[item].approval = true;
                        } else {
                            this.listConfigCases[item].isWaitForApprovalInCase = false;
                            this.listConfigCases[item].approval = false;
                        }

                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.approval === true || backUp.rules.filter(rule => { return rule.approval === true }).length > 0) {
                                backUp.flgApprovalAllBackUp = true;
                            } else {
                                backUp.flgApprovalAllBackUp = false;
                            }
                        })

                        if (this.listConfigCases[item].listBackUps.length > 0) {
                            this.ruleGroup.listCases.push(this.listConfigCases[item]);
                        }

                    }
                    if (flgValidateRule) {
                        if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                            const body = {
                                id: Number(this.ruleGroup.approvalId),
                                newValue: this.prepareRuleGroupData(),
                                path: '/reject'
                            };
                            return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                                this.toastr.success('Successful', "Reject");
                                this.initFlg();
                                window.location.reload();
                            });
                        } else {
                            this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                                if (data.status && data.status == "Successful") {
                                    this.toastr.success('Reject Group successfully.');
                                }
                            });
                        }
                    }
                }
            })
    }

    validateEmptyRuleReject(ruleGroup) {
        if (ruleGroup.length === 0) {
            console.log(1)
            console.log(ruleGroup)
            this.toastr.error('Acquirer Rule Can Not Empty', "error");
            return false;
        }
        let lengthRule = ruleGroup.filter(rule => {
            return rule.status !== 'delete' && rule.enable
        }).length;
        if (lengthRule == 0) {
            console.log(2)
            console.log(ruleGroup)
            this.toastr.error('Acquirer Rule Can Not Empty', "error");
            return false;
        }
        return true;
    }
    ApprovalBackUp() {
        const message = 'Are you sure to approve?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    // save
                    this.ruleGroup.listCases = []
                    for (let item in this.listConfigCases) {
                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.isCheckDetailCaseBackUp) {
                                backUp.isCheckDetailCaseBackUp = false;
                                backUp.approval = false;
                            }
                            backUp.isCheckAllRuleCaseBackUp = false;
                            backUp.rules.forEach(rule => {
                                if (rule.isChecked) {
                                    rule.isChecked = false;
                                    rule.approval = false;
                                }
                            })
                        })

                        if (this.listConfigCases[item].listBackUps.filter(backUp => {
                            return backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0
                        }).length > 0) {
                            this.listConfigCases[item].isWaitForApprovalInCase = true;
                            this.listConfigCases[item].approval = true;
                        } else {
                            this.listConfigCases[item].isWaitForApprovalInCase = false;
                            this.listConfigCases[item].approval = false;
                        }

                        this.listConfigCases[item].listBackUps.forEach(backUp => {
                            if (backUp.approval === true || backUp.rules.filter(rule => { return rule.approval === true }).length > 0) {
                                backUp.flgApprovalAllBackUp = true;
                            } else {
                                backUp.flgApprovalAllBackUp = false;
                            }
                        })

                        if (this.listConfigCases[item].listBackUps.length > 0) {
                            this.ruleGroup.listCases.push(this.listConfigCases[item]);
                        }

                    }

                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/approve'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Approval");
                            this.initFlg();
                            window.location.reload();
                            // this.router.navigate(['/system-management/acq-group-config']);
                        });
                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Approval Group successfully.');
                            }
                        });
                    }
                }
            })
    }
    /**
     * approve thay đổi rule group
     */
    approvalAcqRuleGroup() {
        const message = 'Are you sure to approve?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    this.actionName = 'APPROVE';
                    if (this.isCheckDetailAcqRuleGroup) {
                        this.ruleGroup.approval = false;
                        this.isCheckDetailAcqRuleGroup = false;
                    }
                    this.ruleGroup.rules.forEach(element => {
                        // kiểm tra các rule được duyệt (nếu có), reset hiển thị về ban đầu
                        if (element.isChecked) {
                            if (!_.includes(this.approveIds, element.id))
                                this.approveIds = _.concat(this.approveIds, element.id);
                            element.approval = false;
                            element.isChecked = false;
                        }
                    })
                    //kiểm tra các kịch bản backup được duyệt (nếu có), reset hiển thị về ban đầu
                    this.ruleGroup.listCases.forEach(element => {
                        if (element.isChecked) {
                            element.approval = false;
                            element.isChecked = false;
                            element.listBackUps.forEach(backUp => {
                                backUp.approval = false;
                                backUp.rules.forEach(rule => {
                                    rule.approval = false;
                                })
                                if (backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0) {
                                    backUp.flgApprovalAllBackUp = true;
                                } else {
                                    backUp.flgApprovalAllBackUp = false;
                                }
                            })
                        } else {
                            element.listBackUps.forEach(backUp => {
                                if (backUp.isCheckDetailCaseBackUp) {
                                    backUp.approval = false;
                                    backUp.isCheckDetailCaseBackUp = false
                                }
                                backUp.isCheckAllRuleCaseBackUp = false;
                                backUp.rules.forEach(rule => {
                                    if (rule.isChecked) {
                                        rule.isChecked = false;
                                        rule.approval = false;
                                    }
                                })

                                if (backUp.approval || backUp.rules.filter(rule => { return rule.approval }).length > 0) {
                                    backUp.flgApprovalAllBackUp = true;
                                } else {
                                    backUp.flgApprovalAllBackUp = false;
                                }
                            })
                        }
                    });

                    this.isCheckAllRule = false;
                    this.isCheckAllListCase = false;
                    if (!this.ruleGroup.approval && this.ruleGroup.rules.filter(rule => { return rule.approval }).length === 0 && this.ruleGroup.listCases.filter(c => { return c.approval }).length === 0) {
                        const body = {
                            id: Number(this.ruleGroup.approvalId),
                            newValue: this.prepareRuleGroupData(),
                            path: '/approve'
                        };
                        return this.acquirerRuleService.patchApproval(body).subscribe(data => {
                            this.toastr.success('Successful', "Approval");
                            this.initFlg();
                            window.location.reload();
                        });

                    } else {
                        this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                            if (data.status && data.status == "Successful") {
                                this.toastr.success('Approval Group successfully.');
                            }
                        });
                    }

                }
            })
    }


    editAcqRuleGroup() {
        const message = 'Are you sure to edit?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    this.acquirerRuleService.UpdateAcqRuleGroup(this.prepareRuleGroupData()).subscribe(data => {
                        if (data.status && data.status == "Successful") {
                            this.toastr.success('Saved Group successfully.');
                            window.location.reload();
                        }
                    });
                }
            })
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Hàm chuẩn bị dữ liệu rule group để gửi API approve hoặc API save
     */
    prepareRuleGroupData() {
        let jRules = [];
        if (this.ruleGroup.type != "Default") {
            this.ruleGroup.merchantIds = [];
        }

        this.ruleGroup.rules.forEach((rule) => {
            let acquirerId;
            this.acquirers.filter(data => {
                if (data['name'] === rule.acquirer) acquirerId = data['value']
            });
            jRules.push({
                "id": rule?.id,
                "source": rule.source,
                "card_types": rule.cardType ? rule.cardType.split("|") : [],
                "acquirer": Number(acquirerId),
                "issuers": rule.issuer ? rule.issuer.split("|") : [],
                "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
                "bins": rule.bin ? rule.bin.split("|") : [],
                "level": Number(rule.level),
                "status": rule.status ?? "",
                "bin_country": rule.binCountry,
                "bank_merchant_id": rule.bankMerchantId ?? "",
                "type": rule.type ?? "",
                "enable": rule.enable,
                "updated_date": rule.updateDate,
                "approval": rule.approval,
            })
        })
        let jCases = [];
        this.ruleGroup.listCases.forEach((e) => {
            jCases.push(this.prepareCaseToSave(e))
        })
        return {
            "id": this.ruleGroup?.id,
            "type": this.ruleGroup.type,
            "group_name": this.ruleGroup.name,
            "merchant_ids": this.getMerchantIdFromListValue(this.ruleGroup.merchantIds.sort() ?? []),
            "rules": jRules,
            "description": this.ruleGroup.description,
            "cases": jCases,
            "approval": this.ruleGroup.approval,
            "status": this.ruleGroup.status,
            "enable": this.ruleGroup.enable,
            "updated_date": this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss'),
            "mcc": this.ruleGroup.mcc,
            "acqRulesUpdate": this.editIds,
            "acqRulesDelete": this.deleteIds,
            "acqRulesDisable": this.disableIds,
            "acqRulesEnable": this.enableIds,
            "acqRulesApprove": this.approveIds,
            "acqRulesReject": this.rejectIds,
            "actionName": this.actionName,
            "update_date_old": this.detailResponse.NEW_RULE_GROUP.updated_date
        }
    }

    /**
     * Hàm chuẩn bị dữ liệu backup để request API khi duyệt rule group
     */
    prepareCaseToSave(caseItem) {
        if (!caseItem || !caseItem.listBackUps || caseItem.listBackUps.length == 0) {
            return null
        }
        let jListBackUps = []
        caseItem.listBackUps.forEach((e) => {
            jListBackUps.push({
                "id": e.id,
                "group_name": e.name,
                "description": e.description,
                "case": e.case + '',
                "is_back_up_default": e.isBackUpDefault,
                "rules": this.prepareBackUpRules(e.rules),
                "merchant_ids": this.getMerchantIdFromListValue(e.merchantIds?.sort() ?? []),
                "approval": e.approval,
                "enable": e.enable,
                "status": e.status,
                "updated_date": this.datepipe.transform(e.updateDate, 'dd-MM-YYYY HH:mm:ss'),
                "option": e.option
            })
        })

        return {
            "case_id": caseItem.caseId,
            "case_name": caseItem.caseName,
            "status": caseItem.status,
            "updated_date": caseItem.updatedDate,
            "back_ups": jListBackUps,
            "approval": caseItem.approval,
            "enable": !caseItem.isDisable,
        }
    }

    prepareBackUpRules(rules) {

        if (!rules || rules.length == 0) {
            return null
        }
        let jrules = []
        rules.forEach((rule) => {
            let acquirerId;
            this.acquirers.filter(data => {
                if (data['name'] === rule.acquirer) acquirerId = data['value']
            });
            jrules.push({
                "id": rule?.id,
                "source": rule.source,
                "card_types": rule.cardType ? rule.cardType.split("|") : [],
                "acquirer": Number(acquirerId),
                "issuers": rule.issuer ? rule.issuer.split("|") : [],
                "bin_groups": rule.binGroup ? rule.binGroup.split("|") : [],
                "bins": rule.bin ? rule.bin.split("|") : [],
                "level": Number(rule.level),
                "status": rule.status ?? "",
                "type": rule.type ?? "",
                "enable": rule.enable,
                "updated_date": rule.updateDate,
                "approval": rule.approval,
                "bin_country": rule.binCountry,
                "bank_merchant_id": rule.bankMerchantId ?? "",
            })
        })
        return jrules;
    }

    defaultNewData() {
        this.isChangeNewRule = true;
        this.tempRules = JSON.parse(JSON.stringify(this.newRuleGroup.rules));
        this.clearData();
        this.isCreateBackUp = false;
    }

    defaultTempData() {
        this.tempRules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
        this.clearData();
        this.isCreateBackUp = false;
    }
    clearData() {
        this.isSourceDirect = true;
        this.sId = 0;
        this.sAcq = this.acquirers[0]["value"];
        this.sSource = this.sources[0]["value"];
        this.sIssuer = "";
        this.sBinGroup = "";
        this.isCheckVisa = false;
        this.isCheckMaster = false;
        this.isCheckJcb = false;
        this.isCheckAmex = false;
        this.sBin = '';
        this.sDescription = '';
        this.sBankIdType = this.bankIdType[0]["value"];
        this.sBinCountry = "";
        this.getBankMerchantId();
        this.sBankMerchantId = this.bankMerchantId[0]["value"];
    }

    getBankMerchantId() {
        let key = this.sAcq + '-' + this.sBankIdType;
        let sBankMerchantIdIndex = 0;
        this.bankMerchantId = [];
        this.listBankMid[key].forEach((data, index) => {
            if (this.sBankMerchantId === data)
                sBankMerchantIdIndex = index;
            this.bankMerchantId.push({ name: data, value: data });
        });
        this.sBankMerchantId = this.bankMerchantId[sBankMerchantIdIndex]["value"];
    }

    addRule() {
        if (this.validateRule()) {
            let tempRule = {
                id: null,
                source: "",
                cardType: "",
                binCountry: "",
                issuer: "",
                binGroup: "",
                bin: "",
                acquirer: "",
                bankMerchantId: "",
                type: "",
                level: null,
                status: "",
                updateDate: "",
                description: "",
                approval: null,
                enable: null
            };
            tempRule.id = this.sId ? this.sId : null;
            tempRule.source = this.sSource;
            tempRule.cardType = this.getCardType()?.length > 0 ? this.getCardType().join("|") : "";
            tempRule.binCountry = this.sBinCountry?.length > 0 ? this.sBinCountry : "";
            tempRule.issuer = this.sIssuer?.length > 0 ? this.sIssuer.sort().join("|") : "";
            tempRule.binGroup = this.sBinGroup?.length > 0 ? this.sBinGroup.sort().join("|") : "";
            tempRule.bin = this.sBin ? this.sBin : "";
            tempRule.acquirer = this.acquirersView[this.sAcq] ? this.acquirersView[this.sAcq] : "";
            tempRule.bankMerchantId = this.sBankMerchantId ? this.sBankMerchantId : "";
            tempRule.type = this.sBankIdType ? this.sBankIdType : "";
            tempRule.level = this.getLevel();
            tempRule.status = "new";
            tempRule.enable = true;
            tempRule.approval = true;
            tempRule.updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            tempRule.description = this.sDescription ? this.sDescription : "";
            if (this.idEdit?.length === 0) {
                this.tempRules.push(tempRule);
            } else {
                this.tempRules[Number(this.idEdit)] = tempRule;
                this.idEdit = '';
            }
            this.clearData();
            this.toastr.success('Successful', "Add Rule");
        }
    }
    getCardType() {
        let cardType = [];
        if (this.isCheckVisa) cardType.push('Visa');
        if (this.isCheckMaster) cardType.push('Mastercard');
        if (this.isCheckJcb) cardType.push('Jcb');
        if (this.isCheckAmex) cardType.push('Amex');
        return cardType;
    }

    validateRule() {
        //Validate Card Type
        if (!this.isCheckVisa && !this.isCheckMaster && !this.isCheckJcb && !this.isCheckAmex) {
            this.toastr.error('Card type is required', 'Error');
            return false;
        }
        //check Rule Level 5
        var checkRuleEmpty = this.sBinGroup.length == 0 && !this.sBinCountry && !this.sBin && !this.sIssuer;
        //Validate Existed
        if (this.idEdit?.length === 0) {
            if (this.tempRules.filter((data) => {
                if (!data.binCountry) data.binCountry = '';
                return data.status !== 'delete' && data.source === this.sSource &&
                    data.cardType?.split("|").filter(item => { return this.getCardType().join("|").includes(item) }).length > 0
                    && ((data.binCountry === this.sBinCountry
                    && data.issuer?.split("|").filter(item => { return this.sIssuer.includes(item) }).length > 0
                    && data.binGroup?.split("|").filter(item => { return this.sBinGroup.includes(item) }).length > 0
                    && data.bin?.split("|").filter(item => { return this.sBin.split("|").filter(i => { return i === item }).length > 0 }).length > 0
                    && !checkRuleEmpty) || (checkRuleEmpty && !data.issuer && !data.binCountry && data.binGroup.length == 0 && !data.bin))
            }).length > 0) {
                this.toastr.warning('Acquirer Rule Already Existed , It Will Not Be Added To List Acquirer Rule ');
                return false;
            }
        } else {
            if (this.tempRules.filter((data, index) => {
                if (!data.binCountry) data.binCountry = '';
                return Number(index) !== Number(this.idEdit) && data.status !== 'delete' && data.source === this.sSource &&
                    data.cardType?.split("|").filter(item => { return this.getCardType().join("|").includes(item) }).length > 0
                    && ((data.binCountry === this.sBinCountry
                    && data.issuer?.split("|").filter(item => { return this.sIssuer.includes(item) }).length > 0
                    && data.binGroup?.split("|").filter(item => { return this.sBinGroup.includes(item) }).length > 0
                    && data.bin?.split("|").filter(item => { return this.sBin.split("|").filter(i => { return i === item }).length > 0 }).length > 0
                    && !checkRuleEmpty) || (checkRuleEmpty && !data.issuer && !data.binCountry && data.binGroup.length == 0 && !data.bin))
            }).length > 0) {
                this.toastr.warning('Acquirer Rule Is Existed , It Will Not Be Added To List Acquirer Rule');
                return false;
            }
        }

        return true;
    }
    disableRuleTemp(index: any) {
        if (this.validateEmptyRule(this.tempRules)) {
            if (!_.includes(this.disableIds, this.tempRules[Number(index)].id))
                this.disableIds = _.concat(this.disableIds, this.tempRules[Number(index)].id);
            this.actionName = 'DISABLE';
            this.tempRules[Number(index)].enable = false;
            this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.tempRules[Number(index)].approval = true;
        }
    }
    enableRuleTemp(index: any) {
        this.tempRules[Number(index)].enable = true;
        this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
        this.tempRules[Number(index)].approval = true;
        if (_.includes(this.enableIds, this.tempRules[Number(index)].id))
            this.enableIds = _.concat(this.enableIds, this.tempRules[Number(index)].id);
        this.actionName = 'ENABLE';
    }
    deleteRuleTemp(index: any) {
        if (this.tempRules[Number(index)].status === 'new') {
            if (this.validateEmptyRule(this.tempRules)) {
                this.tempRules.splice(Number(index), 1);
            }
        } else {
            if (this.validateEmptyRule(this.tempRules)) {
                if (!_.includes(this.deleteIds, this.tempRules[Number(index)].id))
                    this.deleteIds = _.concat(this.deleteIds, this.tempRules[Number(index)].id);
                this.actionName = 'DELETE';
                this.tempRules[Number(index)].status = 'delete';
                this.tempRules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
                this.tempRules[Number(index)].approval = true;
            }
        }
    }

    disableRule(index: any) {
        if (this.validateEmptyRule(this.ruleGroup.rules)) {
            if (!_.includes(this.disableIds, this.ruleGroup.rules[Number(index)].id))
                this.disableIds = _.concat(this.disableIds, this.ruleGroup.rules[Number(index)].id);
            this.actionName = 'DISABLE';
            this.ruleGroup.rules[Number(index)].enable = false;
            this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.ruleGroup.rules[Number(index)].approval = true;
            this.changeValue();
        }
    }
    enableRule(index: any) {
        if (!_.includes(this.enableIds, this.ruleGroup.rules[Number(index)].id))
            this.enableIds = _.concat(this.enableIds, this.ruleGroup.rules[Number(index)].id);
        this.actionName = 'ENABLE';
        this.ruleGroup.rules[Number(index)].enable = true;
        this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
        this.ruleGroup.rules[Number(index)].approval = true;
        this.changeValue();
    }
    deleteRule(index: any) {
        if (this.ruleGroup.rules[Number(index)].status === 'new') {
            if (this.validateEmptyRule(this.ruleGroup.rules)) {
                this.ruleGroup.rules.splice(Number(index), 1);
            }
        } else {
            if (this.validateEmptyRule(this.ruleGroup.rules)) {
                if (!_.includes(this.deleteIds, this.ruleGroup.rules[Number(index)].id))
                    this.deleteIds = _.concat(this.deleteIds, this.ruleGroup.rules[Number(index)].id);
                this.actionName = 'DELETE';
                this.ruleGroup.rules[Number(index)].status = 'delete';
                this.ruleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
                this.ruleGroup.rules[Number(index)].approval = true;
            }
        }
        this.changeValue();
    }
    validateEmptyRule(ruleGroup) {
        let lengthRule = ruleGroup.filter(rule => {
            return rule.status !== 'delete' && rule.enable
        }).length;
        if (lengthRule == 1) {
            this.toastr.error('Acquirer Rule Can Not Empty', "error");
            return false;
        }
        return true;
    }
    disableRuleCompare(index: any) {
        if (this.validateEmptyRule(this.newRuleGroup.rules)) {
            this.newRuleGroup.rules[Number(index)].enable = false;
            this.newRuleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.newRuleGroup.rules[Number(index)].approval = true;
            this.changeNewValue();
        }
    }
    enableRuleCompare(index: any) {
        this.newRuleGroup.rules[Number(index)].enable = true;
        this.newRuleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
        this.newRuleGroup.rules[Number(index)].approval = true;
        this.changeNewValue();
    }
    deleteRuleCompare(index: any) {
        if (this.newRuleGroup.rules[Number(index)].status === 'new') {
            if (this.validateEmptyRule(this.newRuleGroup.rules)) {
                this.newRuleGroup.rules.splice(Number(index), 1);
            }
        } else {
            if (this.validateEmptyRule(this.newRuleGroup.rules)) {
                this.newRuleGroup.rules[Number(index)].status = 'delete';
                this.newRuleGroup.rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
                this.newRuleGroup.rules[Number(index)].approval = true;
            }
        }
        this.changeNewValue();
    }
    public editIds = [];

    public deleteIds = [];

    public disableIds = [];

    public enableIds = [];

    public approveIds = [];

    public rejectIds = [];

    public actionName = '';

    editRuleTemp(index: any) {
        this.idEdit = index;
        this.clearData();
        if (this.sSource == 'Direct')
            this.isSourceDirect = true;
        this.sId = this.tempRules[Number(index)].id ?? 0;
        if (this.sId != 0) {
            if (!_.includes(this.editIds, this.sId)) {
                this.editIds = _.concat(this.editIds, this.sId);
            }
            this.actionName = 'UPDATE';
        }
        this.sSource = this.tempRules[Number(index)].source;
        let cardType = this.tempRules[Number(index)].cardType?.split("|");
        if (cardType.includes("Visa")) this.isCheckVisa = true;
        if (cardType.includes("Mastercard")) this.isCheckMaster = true;
        if (cardType.includes("Jcb")) this.isCheckJcb = true;
        if (cardType.includes("Amex")) this.isCheckAmex = true;
        this.sBinCountry = this.tempRules[Number(index)].binCountry;
        this.sIssuer = this.tempRules[Number(index)].issuer?.length > 0 ? this.tempRules[Number(index)].issuer?.split("|") : '';
        this.sBinGroup = this.tempRules[Number(index)].binGroup?.length > 0 ? this.tempRules[Number(index)].binGroup?.split("|") : '';
        this.sBin = this.tempRules[Number(index)].bin;
        let acquirer = this.acquirers.filter(data => {
            return data['name'] === this.tempRules[Number(index)].acquirer;
        })
        this.sAcq = acquirer.length > 0 ? acquirer[0].value : '';
        this.sBankIdType = this.tempRules[Number(index)].type;
        this.sBankMerchantId = this.tempRules[Number(index)].bankMerchantId;
        this.sDescription = this.tempRules[Number(index)].description;
        this.getBankMerchantId();
        this.filterBinGroupBySource(this.sSource);
    }

    saveRule() {
        this.ruleGroup.rules = JSON.parse(JSON.stringify(this.tempRules));
        this.changeValue();
        window.document.getElementById("close").click();
        this.toastr.success('Successful', "Save");
    }

    saveNewRule() {
        this.newRuleGroup.rules = JSON.parse(JSON.stringify(this.tempRules));
        window.document.getElementById("close").click();
        this.toastr.success('Successful', "Save");
    }

    saveDefaultInCompare() {
        this.ruleGroup = JSON.parse(JSON.stringify(this.newRuleGroup));
        this.changeValue();
        this.isChangeDefaultNewRuleGroup = false;
        window.document.getElementById("closeCompare").click();
        this.toastr.success('Successful', "Save");
    }

    defaultTempDataBackUp(idx: any) {
        this.idBackUpAddRule = Number(idx);
        this.tempRules = JSON.parse(JSON.stringify(this.listConfigCases[this.currentCase].listBackUps[idx].rules));
        this.clearData();
        this.isCreateBackUp = true;
    }

    defaultTempDataBackUpInCompare(idx: any) {
        this.idBackUpAddRule = Number(this.newBackUp.id);
        this.tempRules = JSON.parse(JSON.stringify(this.newBackUp.rules));
        this.clearData();
        this.isCreateBackUpInCompare = true;
    }

    saveRuleBackUpInCompare() {
        this.newBackUp.rules = JSON.parse(JSON.stringify(this.tempRules));
        window.document.getElementById("close").click();
        this.toastr.success('Successful', "Save");
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
        this.changeValueBackUpInCompare();

    }

    saveRuleBackUp() {
        this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules = JSON.parse(JSON.stringify(this.tempRules));
        if (this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].approval || this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules.filter(rule => {
            return rule.approval
        }).length > 0) {
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].flgApprovalAllBackUp = true;
        } else {
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].flgApprovalAllBackUp = false;
        }

        window.document.getElementById("close").click();
        this.toastr.success('Successful', "Save");
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
        this.changeValueDetailBackUp(this.idBackUpAddRule);
    }

    getLevel() {
        if (this.sBinCountry?.length > 0) return 4;
        if (this.sIssuer?.length > 0) return 3;
        if (this.sBinGroup?.length > 0) return 2;
        if (this.sBin) return 1;
        return 5;
    }
    // END


    // Back Up \
    // back up config
    defaultListCases = [];
    isChangeInConfigBackUp: any = false;
    defaultListConfigCase: any;
    /**
     * Chuẩn bị dữ liệu trên popup cấu hình backup, trigger khi click Config Rule Backup
     */
    prepareConfigBackUp(caseId: any) {
        //reset list merchant
        this.currentCase = 1
        if (caseId) this.currentCase = caseId;
        this.listConfigCases = {}
        if (!this.ruleGroup.listCases || this.ruleGroup.listCases.length === 0) {
            this.onInitCase()
        }
        this.ruleGroup.listCases.forEach((e) => {
            if (e.caseId) {
                // e.tmpSelectedMerchants = e.merchantIds
                this.listConfigCases[e.caseId] = JSON.parse(JSON.stringify(e))
                this.listConfigCases[e.caseId]?.listBackUp?.forEach((x) => {
                    x.tmpSelectedMerchants = x.merchantIds ?? []
                })
            }
        })
        this.sortBackUp();
        this.defaultListConfigCase = JSON.parse(JSON.stringify(this.listConfigCases));
        if (!(this.currentCase in this.listConfigCases)) {
            this.onInitCase()
        }
        this.defaultListCases = JSON.parse(JSON.stringify(this.ruleGroup.listCases));
        this.resetAllMerchantOption();

    }

    sortBackUp() {
        for (let item in this.listConfigCases) {
            for (let i = 0; i < this.listConfigCases[item].listBackUps.length; i++) {
                if (this.listConfigCases[item].listBackUps[i].isBackUpDefault) {
                    this.array_move(this.listConfigCases[item].listBackUps, i, 0);
                }
            }
        }
    }
    array_move(arr, old_index, new_index) {
        if (new_index >= arr.length) {
            var k = new_index - arr.length + 1;
            while (k--) {
                arr.push(undefined);
            }
        }
        arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
        return arr; // for testing
    };

    getListCase() {
        // get list cases
        let request = new HttpParams()
        this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
            if (data && data?.list && data.list.length > 0) {
                this.listCases = data.list.map((c) => {
                    return {
                        label: c.script ?? "",
                        value: c.id
                    }
                })
                this.listCases.forEach((e) => {
                    this.mapCases[e.value] = e.label
                })
                if(this.listCases.length == 1){this.onInitCase()};
            }
        })
    }

    onInitCase() {
        this.listConfigCases[this.currentCase] = {
            caseId: this.currentCase,
            caseName: this.listCases.find((e) => { return e.value == this.currentCase })?.label,
            isDisable: false,
            updatedDate: this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss'),
            status: "new",
            approval: true,
            listBackUps: [
            ],
            isWaitForApprovalInCase: false
        }
    }

    resetAllMerchantOption() {
        for (let item in this.listConfigCases) {
            for (let i = 0; i < this.listConfigCases[item].listBackUps.length; i++) {
                this.listConfigCases[item].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[item].listBackUps[i].merchantIds)
            }
        }

    }

    getAvailableMerchants(selectedMerchants) {

        //get other selected
        let otherSelectedMerchantIds = []
        this.listConfigCases[this.currentCase]?.listBackUps.forEach((e) => {
            if (e.merchantIds && e.merchantIds.length > 0 && e.status != 'delete') {
                let filteredSelectedMerchant = e.merchantIds
                if (selectedMerchants && selectedMerchants.length > 0) {
                    filteredSelectedMerchant = e.merchantIds.filter(x => !selectedMerchants.includes(x));
                }
                otherSelectedMerchantIds.push(...filteredSelectedMerchant);


            }

        })

        return this.ruleGroup.merchantIds.filter(x => !otherSelectedMerchantIds.includes(x)).map((e) => {
            return {
                name: e,
                value: e
            }
        })
    }

    onAddBackUp() {
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
        // check default existed
        let existedDefault = null
        if (this.listConfigCases[this.currentCase].listBackUps && this.listConfigCases[this.currentCase].listBackUps.length > 0) {
            existedDefault = this.listConfigCases[this.currentCase].listBackUps.find((e) => {
                return e.isBackUpDefault && e.status != 'delete'
            })
        }
        let backUpOptions = !existedDefault || (existedDefault && existedDefault.option != '0') ? this.option : this.option.filter((e) => { return e.value != '0' })
        let backUpTypes = !existedDefault ? this.backUpType : this.backUpType.filter((e) => { return e.value == false })
        let tmpId = this.makeUUID()

        if (existedDefault?.length > 0) {
            existedDefault.forEach(rule => {
                rule.approval = true;
            })
        }
        // get current case config
        this.listConfigCases[this.currentCase].listBackUps.push({
            tmpId: tmpId,
            option: existedDefault ? 'New' : '0',
            id: existedDefault ? null : -1,
            name: existedDefault ? this.genBackUpName(this.currentCase) : '',
            type: "",
            description: "",
            merchantIds: [],
            tmpSelectedMerchants: [],
            merchantIdsText: "",
            rules: existedDefault ? JSON.parse(JSON.stringify(existedDefault.rules)) : [],
            backUps: null,
            case: this.currentCase,
            isBackUpDefault: existedDefault ? false : true,
            isAllMerchant: false,
            listMerchantIds: [],
            selectedBackUp: null,
            listBackUpType: backUpTypes,
            listOption: backUpOptions,
            status: "new",
            approval: true,
            enable: true,
            flgApprovalAllBackUp: true
        })
        let idx = this.listConfigCases[this.currentCase].listBackUps.length - 1;
        this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds)
        let rules = JSON.parse(JSON.stringify(this.listConfigCases[this.currentCase].listBackUps[idx].rules));
        // rules.forEach(rule => {rule.approval = true});
        rules.forEach((rule, idx) => {
            rules[idx].id = 0;
            rules[idx].approval = true
        })
        this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(rules));
        this.changeValueBackUp();

    }

    makeUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (a, b) {
            return b = Math.random() * 16, (a == 'y' ? b & 3 | 8 : b | 0).toString(16);
        });
    }

    genBackUpName(scase) {
        return "BACK_UP_" + scase + "_" + Date.now()
    }

    SaveBackUpConfig() {
        if (!this.validateListCase()) {
            return;
        }
        // save

        this.ruleGroup.listCases = []
        for (let item in this.listConfigCases) {
            if (this.listConfigCases[item].listBackUps.length > 0) {
                this.ruleGroup.listCases.push(this.listConfigCases[item]);
            }
        }

        this.defaultListCases.forEach(dfListCase => {
            dfListCase.listBackUps.forEach(dfBk => {
                this.ruleGroup.listCases.forEach(listcase => {
                    listcase.listBackUps.forEach(bk => {
                        if (dfBk.tmpId === bk.tmpId) {
                            if (dfBk.name !== bk.name || JSON.stringify(dfBk.merchantIds?.sort()) !== JSON.stringify(bk.merchantIds?.sort()) || dfBk.description !== bk.description || dfBk.isBackUpDefault !== bk.isBackUpDefault) {
                                bk.approval = true;
                            }
                        }
                    })
                })
            })
        })
        this.ruleGroup.listCases.forEach(element => {
            if (element.listBackUps.filter(backup => {
                return backup.flgApprovalAllBackUp;
            }).length > 0) {
                element.approval = true;
                element.updated_date = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            } else {
                element.approval = false;
            }
        })
        this.toastr.success('Successful', "Config Back Up Successfully");
        this.isChangeInConfigBackUp = false;
        this.changeValue();

    }

    validateListCase() {
        if (Object.keys(this.listConfigCases).length == 0) {
            this.toastr.error('Nothing has changed', 'Error');
            return false;
        }

        for (let item in this.listConfigCases) {
            for (let i = 0; i < this.listConfigCases[item]?.listBackUps?.length; i++) {
                if (!this.validateBackUp(this.listConfigCases[item]?.listBackUps[i], i + 1)) {
                    return false;
                }
            }
        }
        return true;
    }

    validateBackUp(backUp, no) {
        if (!backUp) {
            return true;
        }
        if (backUp?.id == -1 && backUp?.option == '0') {
            return true;
        }
        if (backUp?.name == null || backUp.name == '') {
            this.toastr.error('Back up no.' + no + ' case ' + backUp?.case + ' name is invalid', 'Error');
            return false;
        }
        if (backUp?.rules == null || backUp.rules.length == 0) {
            this.toastr.error('Back up no.' + no + ' case ' + backUp?.case + ' rules is invalid', 'Error');
            return false;
        }
        // TODO:VALIDATE RULES


        return true;

    }

    onChangeCase() {
        if (!this.listConfigCases[this.currentCase]?.listBackUps || this.listConfigCases[this.currentCase].listBackUps.length == 0) {

            this.listConfigCases[this.currentCase] = {
                caseId: this.currentCase,
                caseName: this.mapCases[this.currentCase] ?? "",
                approval: true,
                listBackUps: [
                ]
            }

        }
    }

    onDeleteBackUp(idx) {
        if (idx != null) {
            //
            this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
            let tmpBackUp = this.listConfigCases[this.currentCase]?.listBackUps[idx];

            for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {

                if (tmpBackUp && tmpBackUp.isBackUpDefault) {
                    // reset other back up type
                    if (i != idx) {
                        this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType
                    }
                    // reset order back up option if delete no switching
                    if (tmpBackUp.option == '0') {
                        this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option
                    }
                }
                if (tmpBackUp.tmpId == this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp && this.listConfigCases[this.currentCase].listBackUps[i].option == 'Select') {
                    this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
                    this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp = null
                }
            }

            if (this.ruleGroup?.approvalType && this.ruleGroup.approvalType == '1') {
                this.listConfigCases[this.currentCase].listBackUps.splice(idx, 1);
            } else {

                if (this.listConfigCases[this.currentCase].listBackUps[idx].approval == true && (!this.listConfigCases[this.currentCase].listBackUps[idx].id || this.listConfigCases[this.currentCase].listBackUps[idx].id == -1)) {
                    this.listConfigCases[this.currentCase].listBackUps.splice(idx, 1);
                } else {
                    this.listConfigCases[this.currentCase].listBackUps[idx].status = 'delete'
                    this.listConfigCases[this.currentCase].listBackUps[idx].approval = true
                    this.listConfigCases[this.currentCase].listBackUps[idx].flgApprovalAllBackUp = true
                }


            }

            this.onChangeMerchantSelect(true)
            this.changeValueBackUp();
        }

    }

    onChangeMerchantSelect(isUpdateListSelectedMerchant) {
        for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
            this.listConfigCases[this.currentCase].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)

            if (isUpdateListSelectedMerchant && this.listConfigCases[this.currentCase].listBackUps[i].status != 'delete') this.listConfigCases[this.currentCase].listBackUps[i].merchantIds = this.getAvailableSelectedMerchant(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)
        }
    }

    getAvailableSelectedMerchant(selectedMerchants) {
        if (selectedMerchants && selectedMerchants.length > 0) {
            return selectedMerchants.filter((e) => {
                return this.ruleGroup.merchantIds.includes(e)
            })
        } else {
            return selectedMerchants;
        }
    }
    /**
     * Hàm trigger khi thay đổi value dropdown Option của popup Config Rule Backup
     */
    onChangeBackUpOption(idx) {
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')
        // if (this.listConfigCases[this.currentCase].listBackUps[idx].option
        switch (this.listConfigCases[this.currentCase].listBackUps[idx].option) {
            case '0':
                this.listConfigCases[this.currentCase].listBackUps[idx] = {
                    tmpId: this.listConfigCases[this.currentCase].listBackUps[idx].tmpId,
                    option: '0',
                    id: -1,
                    name: "",
                    type: "",
                    description: "",
                    merchantIds: [],
                    tmpSelectedMerchants: [],
                    merchantIdsText: "",
                    rules: [],
                    backUps: null,
                    case: this.currentCase,
                    isBackUpDefault: true,
                    isAllMerchant: false,
                    listMerchantIds: [],
                    selectedBackUp: null,
                    listOption: this.option,
                    listBackUpType: [],
                    enable: true
                }
                // reset other backup to exp
                for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                    if (i != idx && this.listConfigCases[this.currentCase].listBackUps[i].status != 'delete') {

                        // reset select option of selected back up
                        if (this.listConfigCases[this.currentCase].listBackUps[i].option == 'Select' && this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp == this.listConfigCases[this.currentCase].listBackUps[idx].tmpId) {
                            this.listConfigCases[this.currentCase].listBackUps[i].option = 'New';
                            this.listConfigCases[this.currentCase].listBackUps[i].selectedBackUp = null;
                        }
                        this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
                        // reset option
                        this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option.filter((e) => { return e.value != '0' })
                        // reset back up type no switching
                        this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
                        // if (this.listConfigCases[this.currentCase].listBackUps[i].option == '0') {
                        //   this.listConfigCases[this.currentCase].listBackUps[i].name = this.genBackUpName(this.currentCase)
                        //   this.listConfigCases[this.currentCase].listBackUps[i].id = null
                        //   this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
                        // }
                    }
                }
                this.listAllBackUp = this.getListAllBackUp()
                this.onChangeMerchantSelect(false);

                break;
            case 'Select':
                // get list existed back up
                this.listAllBackUp = this.getListAllBackUp()

                // reset id if old option is no switching
                if (this.listConfigCases[this.currentCase].listBackUps[idx].id == -1) {
                    this.listConfigCases[this.currentCase].listBackUps[idx].id = 0
                    this.listConfigCases[this.currentCase].listBackUps[idx].listBackUpType = this.backUpType
                    // this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
                    // this.listConfigCases[this.currentCase].listBackUps[idx].rules.forEach((e, index) => {
                    //     this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].id = 0
                    //     this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].approval = true;
                    // })
                    // reset full option of other
                    for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                        this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option;
                        if (i != idx && this.listConfigCases[this.currentCase].listBackUps[i].status != 'delete') {
                            this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
                            this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })

                        }
                    }
                }
                if (this.listConfigCases[this.currentCase].listBackUps[idx].name == '') {
                    this.listConfigCases[this.currentCase].listBackUps[idx].name = this.genBackUpName(this.currentCase)
                }

                if (this.listAllBackUp.length > 0) {
                    let firstTmpId = this.listAllBackUp[0].value;
                    let backUp = this.getBackUpByTmpId(firstTmpId)
                    if (backUp != null) {
                        this.listConfigCases[this.currentCase].listBackUps[idx].description = backUp.description ?? ""
                        this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp = backUp.tmpId
                        if (backUp.tmpId != this.listConfigCases[this.currentCase].listBackUps[idx].tmpId) {
                            this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(backUp.rules))
                            this.listConfigCases[this.currentCase].listBackUps[idx].rules.forEach((e, index) => {
                                this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].id = 0
                                this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].approval = true;
                            })
                        }
                    }

                    // get back up by temp id

                }
                break;
            case 'New':
                if (this.listConfigCases[this.currentCase].listBackUps[idx].id == -1) {
                    this.listConfigCases[this.currentCase].listBackUps[idx].id = 0;
                    this.listConfigCases[this.currentCase].listBackUps[idx].isBackUpDefault = true;
                    this.listConfigCases[this.currentCase].listBackUps[idx].listBackUpType = this.backUpType
                    this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
                    this.listConfigCases[this.currentCase].listBackUps[idx].rules.forEach((e, index) => {
                        this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].id = 0
                        this.listConfigCases[this.currentCase].listBackUps[idx].rules[index].approval = true;
                    })
                    for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                        this.listConfigCases[this.currentCase].listBackUps[i].listOption = this.option;
                        if (i != idx && this.listConfigCases[this.currentCase].listBackUps[i].status != 'delete') {
                            this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
                            this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
                        }
                    }
                }
                if (this.listConfigCases[this.currentCase].listBackUps[idx].name == '') {
                    this.listConfigCases[this.currentCase].listBackUps[idx].name = this.genBackUpName(this.currentCase)
                }
                this.listAllBackUp = this.getListAllBackUp()
                break;
        }
        this.changeValueDetailBackUp(idx);
    }


    getBackUpByTmpId(tmpId) {
        let backUp = null;
        if (tmpId != null && tmpId != '') {
            for (let item in this.listConfigCases) {
                this.listConfigCases[item].listBackUps.forEach((e) => {

                    if (e.tmpId == tmpId) {
                        backUp = e
                    }
                })
            }
        }
        return backUp
    }

    getListAllBackUp() {
        let listAllBackUp = []
        for (let item in this.listConfigCases) {
            this.listConfigCases[item].listBackUps.forEach((e) => {
                if (e.name != null && e.name != '') {
                    listAllBackUp.push({ label: e.name, value: e.tmpId })
                }
            })
        }
        return listAllBackUp;
    }

    onChangeBackUpSelect(idx) {
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

        let backUp = this.getBackUpByTmpId(this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp)
        // this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp = this.listAllBackUp[0].value
        this.listConfigCases[this.currentCase].listBackUps[idx].description = backUp?.description ?? ""
        if (backUp?.tmpId && this.listConfigCases[this.currentCase].listBackUps[idx].tmpId != backUp.tmpId) {
            this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(backUp.rules))?.map((e) => {
                let tmpRule = e
                tmpRule.status = 'New'
                tmpRule.id = 0
                tmpRule.approval = true
                return tmpRule
            }) ?? []
        }

        this.changeValueDetailBackUp(idx);
    }
    removeMerchantID(id: String) {

        if (id) {
            var index = this.ruleGroup.merchantIds.indexOf(id);
            if (index !== -1) {
                this.ruleGroup.merchantIds.splice(index, 1);
                this.tmpSelectedMerchants = this.ruleGroup.merchantIds;
                this.onChangeGroupMerchantSelect()
                // this.onChangeMerchantSelect(true)
            }

        }


    }

    onChangeBackUpName() {
        // reload select option dropdown
        //get list

        this.listAllBackUp = this.getListAllBackUp()
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');

    }

    isWaitForApprovalBackUpInCompare: any = false;
    isChangeBackUpBackUpInCompare: any = false;
    defaultNewBackUp: any;
    // compare back up
    prepareCompareBackUp(idx) {
        this.newBackUp = null;
        this.oldBackUp = this.defaultFormatBackUp;
        this.listBackUpCompareOpt = this.backUpCompareOpts
        this.backUpCompareOpt = 1;
        this.newBackUp = JSON.parse(JSON.stringify(this.listConfigCases[this.currentCase].listBackUps[idx]));
        if (this.newBackUp.flgApprovalAllBackUp) {
            this.isWaitForApprovalBackUpInCompare = true;
        } else {
            this.isWaitForApprovalBackUpInCompare = false;
        }
        this.isChangeBackUpBackUpInCompare = false;
        this.onChangeBackUpCompareOpt();
        let defaultBackUp = this.listConfigCases[this.currentCase].listBackUps.find((e) => {
            return e.isBackUpDefault && e.status != 'delete'
        })
        if (!defaultBackUp) {
            this.listBackUpCompareOpt = this.backUpCompareOpts.filter((a) => {
                return a.value != 0
            })
        }
        this.defaultNewBackUp = JSON.parse(JSON.stringify(this.newBackUp));

        // handle highlight merchant id
        this.highlightedMerchantIds = this.getHighlightMerchantId(this.newBackUp?.merchantIds ?? [], this.oldBackUp?.merchantIds ?? [])
        this.tmpCompareSelectedMerchants = this.newBackUp.merchantIds
    }
    getHighlightMerchantId(newMerchantIds, oldMerchantIds) {
        let merchantIds = []
        let isEmptyNew = !newMerchantIds || newMerchantIds.length == 0
        let isEmptyOld = !oldMerchantIds || oldMerchantIds.length == 0
        if (isEmptyNew && isEmptyOld) {
            return []
        }
        // case delete all
        if (isEmptyNew) {
            oldMerchantIds.forEach((e) => {
                merchantIds.push({
                    value: e,
                    status: -1
                })
            })

            return merchantIds
        }
        // case add new all
        if (isEmptyOld) {
            newMerchantIds.forEach((e) => {
                merchantIds.push({
                    value: e,
                    status: 1
                })
            })

            return merchantIds
        }
        let includedList = oldMerchantIds.filter(x => newMerchantIds.includes(x)).map((x) => { return { value: x, status: 0 } }) ?? [];
        let deletedList = oldMerchantIds.filter(x => !newMerchantIds.includes(x)).map((x) => { return { value: x, status: -1 } }) ?? [];
        let newList = newMerchantIds.filter(x => !oldMerchantIds.includes(x)).map((x) => { return { value: x, status: 1 } }) ?? [];
        return [...deletedList, ...newList, ...includedList]
        // newMerchantIds.sort();
        // oldMerchantIds.sort();
        // if (newMerchantIds.length === oldMerchantIds.length && newMerchantIds.every(function(value, index) { return value === oldMerchantIds[index]}) ){
        //     newMerchantIds.forEach((e)=>{
        //         merchantIds.push({
        //             value: e,
        //             status:0,
        //         })
        //     })
        // }

        // return merchantIds
    }
    getOldBackUp(idx, option) {
        // get old case
        let oldCase = this.oldRuleGroup.listCases.find((e) => {
            return e.caseId == this.currentCase
        })

        if (oldCase) {
            return oldCase.listBackUps[idx];
        }
    }


    deleteRuleCompareBackUp(tmpId: any, index: any) {
        if (this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].status === 'new') {
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules.splice(Number(index), 1);
        } else {
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].status = 'delete';
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.listConfigCases[this.currentCase].listBackUps[this.idBackUpAddRule].rules[Number(index)].approval = true;
        }
    }

    deleteRuleNewBackUp(index) {
        this.isChangeBackUpBackUpInCompare = true
        if (!this.newBackUp.rules[index].id) {
            if (this.validateEmptyRule(this.newBackUp.rules)) {
                this.newBackUp.rules.splice(index, 1);
            }
        } else {
            if (this.validateEmptyRule(this.newBackUp.rules)) {
                this.newBackUp.rules[index].status = 'delete';
                this.newBackUp.rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
                this.newBackUp.rules[index].approval = true;
            }
        }
    }

    disableRuleNewBackUp(index) {
        if (this.validateEmptyRule(this.newBackUp.rules)) {
            this.newBackUp.rules[index].enable = false;
            this.newBackUp.rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.newBackUp.rules[index].approval = true;
            this.changeValueBackUpInCompare();
        }
    }

    enableRuleNewBackUp(index) {
        this.newBackUp.rules[index].enable = true;
        this.newBackUp.rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
        this.newBackUp.rules[index].approval = true;
        this.changeValueBackUpInCompare();
    }

    disableRuleBackUp(tmpId: any, backUpIndex, index) {
        this.listConfigCases[this.currentCase].listBackUps.filter((data, idx) => {
            if (data.tmpId === tmpId) {
                this.idBackUpAddRule = idx;
            }
        })
        if (this.validateEmptyRule(this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules)) {
            this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].enable = false;
            this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
            this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].approval = true;
        }
        this.changeValueBackUp();
    }


    enableRuleBackUp(tmpId: any, backUpIndex, index) {
        this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].enable = true;
        this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
        this.listConfigCases[this.currentCase].listBackUps[backUpIndex].rules[index].approval = true;
        this.changeValueBackUp();
    }

    onChangeBackUpCompareOpt() {
        // this.oldBackUp = this.defaultFormatBackUp ;
        if (this.backUpCompareOpt) {
            // compare to old
            let oldCase = this.oldRuleGroup?.listCases.find((e) => {
                return e.caseId == this.currentCase
            })
            if (oldCase) {
                this.oldBackUp = oldCase?.listBackUps.find((e) => {
                    return e.id != null && e.id && e.id == this.newBackUp.id;
                })
                if (this.oldBackUp) return
            }

            this.oldBackUp = this.defaultFormatBackUp
        } else {
            this.backUpCompareOpt = 0
            // compare to default
            let defaultBackUp = this.listConfigCases[this.currentCase].listBackUps.find((e) => {
                return e.isBackUpDefault && e.status != 'delete'
            })
            if (defaultBackUp) {
                this.oldBackUp = defaultBackUp
            }
        }


    }
    onSaveCompareBackUp() {
        // validate back up
        if (!this.validateBackUp(this.newBackUp, 0)) {
            return
        }
        // // save
        if (this.newBackUp.tmpId && this.newBackUp.tmpId != '') {
            this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
                if (c.tmpId == this.newBackUp.tmpId) {
                    if (this.newBackUp.approval === true || this.newBackUp.rules.filter(rule => { return rule.approval }).length > 0) {
                        this.newBackUp.flgApprovalAllBackUp = true;
                    } else {
                        this.newBackUp.flgApprovalAllBackUp = false;
                    }
                    this.listConfigCases[this.currentCase].listBackUps[index] = this.newBackUp
                    // reset all list merchant option, option, list select, list type
                } else {
                    if (this.newBackUp.isBackUpDefault && this.newBackUp.status != 'delete') {
                        this.listConfigCases[this.currentCase].listBackUps[index].isBackUpDefault = false;
                    }
                }
            })
            this.resetAllBackUpOptList();
        }
        this.isChangeBackUpBackUpInCompare = false;
        this.changeValueBackUp();
        // this.SaveBackUpConfig();
        window.document.getElementById("closeCompareBackUp").click();
    }
    onDeleteCase(idx) {
        const message = 'Are you sure to delete this case config?';
        this.confirmService.build().message(message)
            .no('No')
            .yes('Yes').confirm().subscribe(result => {
                if (result) {
                    if (this.ruleGroup.approvalType == '1') {

                        this.ruleGroup.listCases.splice(idx, 1);
                        // this.onChangeMerchantSelect(true)

                    } else {
                        if (this.ruleGroup.approvalType != '1') {
                            this.ruleGroup.listCases[idx].status = 'delete';
                            this.ruleGroup.listCases[idx].approval = true;
                            this.ruleGroup.listCases[idx]?.listBackUps.forEach((e, index) => {
                                this.ruleGroup.listCases[idx].listBackUps[index].status = 'delete';
                                this.ruleGroup.listCases[idx].listBackUps[index].approval = true;
                                this.ruleGroup.listCases[idx].listBackUps[index]?.rules.forEach((r, i) => {
                                    this.ruleGroup.listCases[idx].listBackUps[index].rules[i].status = 'delete';
                                    this.ruleGroup.listCases[idx].listBackUps[index].rules[i].approval = true;
                                })
                            })
                        } else {
                            this.ruleGroup.listCases.splice(idx, 1);
                        }

                    }
                    this.changeValue()
                }
            });
    }
    onEditCase(idx) {
        let caseId = this.ruleGroup.listCases[idx].caseId;
        this.prepareConfigBackUp(caseId);
    }
    deleteRuleBackUp(indexBackUp, index) {
        if (!this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].id) {
            if (this.validateEmptyRule(this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules)) {
                this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules.splice(index, 1);
            }
        } else {
            if (this.validateEmptyRule(this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules)) {
                this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].status = 'delete';
                this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].updateDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss');
                this.listConfigCases[this.currentCase].listBackUps[indexBackUp].rules[index].approval = true;
            }
        }

        this.changeValueBackUp();
    }
    resetAllBackUpOptList() {
        this.listAllBackUp = this.getListAllBackUp()
        // get default
        let defaultBackUp = this.listConfigCases[this.currentCase].listBackUps.find((e) => {
            return e.isBackUpDefault && e.status != 'delete'
        })

        this.listConfigCases[this.currentCase].listBackUps.forEach((c, index) => {
            this.listConfigCases[this.currentCase].listBackUps[index].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[index].merchantIds)
            if (defaultBackUp) {
                if (defaultBackUp.id == -1) {
                    //no switching
                    if (c.tmpId == defaultBackUp.tmpId) {
                        // default back up
                        this.listConfigCases[this.currentCase].listBackUps[index].listBackUpType = this.backUpType
                        this.listConfigCases[this.currentCase].listBackUps[index].listOption = this.option
                    } else {
                        // exp back up
                        // reset option
                        this.listConfigCases[this.currentCase].listBackUps[index].listOption = this.option.filter((e) => { return e.value != '0' })
                        // reset back up type no switching
                        this.listConfigCases[this.currentCase].listBackUps[index].listBackUpType = this.backUpType.filter((e) => { return e.value == false })

                        if (this.listConfigCases[this.currentCase].listBackUps[index].option == 'Select' && this.listConfigCases[this.currentCase].listBackUps[index].selectedBackUp == defaultBackUp.tmpId) {
                            this.listConfigCases[this.currentCase].listBackUps[index].option = 'New'
                            this.listConfigCases[this.currentCase].listBackUps[index].selectedBackUp = null
                        }
                    }
                } else {

                    this.listConfigCases[this.currentCase].listBackUps[index].listOption = this.option
                    this.listConfigCases[this.currentCase].listBackUps[index].listBackUpType = c.tmpId == defaultBackUp.tmpId ? this.backUpType : this.backUpType.filter((e) => { return e.value == false })

                }
            } else {
                this.listConfigCases[this.currentCase].listBackUps[index].listBackUpType = this.backUpType
                this.listConfigCases[this.currentCase].listBackUps[index].listOption = this.option
            }

        });

    }
    onChangeNewBackUpOption() {
        // if (this.listConfigCases[this.currentCase].listBackUps[idx].option
        switch (this.newBackUp.option) {
            case '0':
                this.newBackUp = {
                    tmpId: this.newBackUp.tmpId,
                    option: '0',
                    id: -1,
                    name: "",
                    type: "",
                    description: "",
                    merchantIds: [],
                    merchantIdsText: "",
                    rules: [],
                    backUps: null,
                    case: this.currentCase,
                    isBackUpDefault: true,
                    isAllMerchant: false,
                    listMerchantIds: [],
                    selectedBackUp: null,
                    listOption: this.option,
                    listBackUpType: [],
                    status: this.newBackUp.status,
                    approval: this.newBackUp.approval,
                    flgApprovalAllBackUp: this.newBackUp.flgApprovalAllBackUp,
                    enable: true,
                }
                this.tmpCompareSelectedMerchants = []
                break;
            case 'Select':
                // get list existed back up
                // this.listAllBackUp = this.getListAllBackUp()
                this.listAllBackUp = this.getListAllBackUp()
                // reset id if old option is no switching
                if (this.newBackUp.id == -1) {
                    this.newBackUp.id = 0
                    this.newBackUp.listBackUpType = this.backUpType
                    this.newBackUp.rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
                    this.newBackUp.rules.forEach((e, index) => {
                        this.newBackUp.rules[index].approval = true;
                    })
                    this.newBackUp.listOption = this.option;


                }
                if (this.newBackUp.name == '') {
                    this.newBackUp.name = this.genBackUpName(this.currentCase)
                }
                // if (this.listAllBackUp.length > 0) {
                //     let firstTmpId = this.listAllBackUp[0].value;
                //     let backUp = this.getBackUpByTmpId(firstTmpId)
                //     // get back up by temp id

                // }
                break;
            case 'New':
                if (this.newBackUp.id == -1) {
                    this.newBackUp.id = 0;
                    this.newBackUp.isBackUpDefault = true;
                    this.newBackUp.listBackUpType = this.backUpType
                    this.newBackUp.rules = JSON.parse(JSON.stringify(this.ruleGroup.rules))
                    this.newBackUp.rules.forEach((e, index) => {
                        this.newBackUp.rules[index].approval = true;
                    })
                    this.newBackUp.listOption = this.option;


                }
                if (this.newBackUp.name == '') {
                    this.newBackUp.name = this.genBackUpName(this.currentCase)
                }
                // this.listAllBackUp = this.getListAllBackUp()
                break;
        }
        this.changeValueDetailBackUpInCompare();
    }
    onChangeNewBackUpSelect() {
        let backUp = this.getBackUpByTmpId(this.newBackUp.selectedBackUp)
        // this.listConfigCases[this.currentCase].listBackUps[idx].selectedBackUp = this.listAllBackUp[0].value
        this.newBackUp.description = backUp?.description ?? ""
        this.newBackUp.rules = backUp?.rules ?? [];
        this.changeValueDetailBackUpInCompare();
    }

    onChangeNewBackUpType() {

        switch (this.newBackUp.isBackUpDefault) {
            case true:
                this.newBackUp.merchantIds = []
                this.tmpCompareSelectedMerchants = []
                break;
            case false:



        }
        this.changeValueDetailBackUpInCompare()
    }
    ///////////////
    onChangeBackUpType(idx) {
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

        switch (this.listConfigCases[this.currentCase].listBackUps[idx].isBackUpDefault) {
            case true:
                if (this.listConfigCases[this.currentCase].listBackUps[idx].rules.length > 0) {
                    this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
                }
                this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds = []
                this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = []
                // reset all other back up type to exception
                for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                    if (i != idx) {
                        this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType.filter((e) => { return e.value == false })
                        this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
                    }

                }
                if (!this.listConfigCases[this.currentCase].listBackUps[idx].rules || this.listConfigCases[this.currentCase].listBackUps[idx].rules.length == 0) {
                    this.listConfigCases[this.currentCase].listBackUps[idx].rules = JSON.parse(JSON.stringify(this.ruleGroup.rules));
                }

                // for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                //   if (i != idx) {
                //     this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;
                //     // reset back up type no switching
                //     if (this.listConfigCases[this.currentCase].listBackUps[i].option == '0') {
                //       this.listConfigCases[this.currentCase].listBackUps[i].name = this.genBackUpName(this.currentCase)
                //       this.listConfigCases[this.currentCase].listBackUps[i].id = null
                //       this.listConfigCases[this.currentCase].listBackUps[i].option = 'New'
                //     }
                //   }
                // }
                this.listAllBackUp = this.getListAllBackUp()
                break;
            case false:
                // reset backup types
                for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                    this.listConfigCases[this.currentCase].listBackUps[i].listBackUpType = this.backUpType
                    this.listConfigCases[this.currentCase].listBackUps[i].isBackUpDefault = false;

                }
        }
        this.changeValueDetailBackUp(idx);
    }


    identify(index, item) {
        return item && item.id;
    }

    _filterBackUpMerchant(value, idx) {
        if (value._filterValue.includes(" ")) {

            let listSearchMerchant = []
            listSearchMerchant = this.getListValueFromMerchantIds(value._filterValue.toUpperCase().split(" ") ?? []);
            if (listSearchMerchant && listSearchMerchant.length > 0) {

                value._filteredOptions = this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds.filter((item) => listSearchMerchant.includes(item.value));
            }
        } else {
            value._filteredOptions = this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds.filter((item) => {
                return this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue))
            })
        }

    }

    _filteCompareBackUpMerchant(value) {
        if (value._filterValue.includes(" ")) {

            let listSearchMerchant = []
            listSearchMerchant = this.getListValueFromMerchantIds(value._filterValue.toUpperCase().split(" ") ?? []);
            if (listSearchMerchant && listSearchMerchant.length > 0) {

                value._filteredOptions = this.newBackUp.listMerchantIds.filter((item) => listSearchMerchant.includes(item.value));
            }
        } else {
            value._filteredOptions = this.newBackUp.listMerchantIds.filter((item) => this._normalizeValue(item.value).includes(this._normalizeValue(value._filterValue)))

        }

    }


    private _normalizeValue(value: string): string {
        return value.toUpperCase().replace(/\s/g, '');
    }

    onChangeBackUpMerchantSelect(idx, value, event) {
        if (!event.itemValue && event.value && event.value.length == 0 && this.listConfigCases[this.currentCase].listBackUps[idx].listMerchantIds.length > 0) {
            //delete all
            let filterOpt = value?._filteredOptions ?? []

            if (filterOpt && filterOpt.length > 0) {
                // in filtering
                filterOpt = filterOpt.map((e) => {
                    return e.value
                })

                this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds = this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants.filter((e) => {
                    return !filterOpt.includes(e)
                })

                this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
            } else {
                this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds;
            }
        } else {
            this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
        }
        if (idx != null && idx >= 0) {
            for (let i = 0; i < this.listConfigCases[this.currentCase].listBackUps.length; i++) {
                if (idx != i && this.listConfigCases[this.currentCase].listBackUps[i].status != 'delete') {
                    this.listConfigCases[this.currentCase].listBackUps[i].listMerchantIds = this.getAvailableMerchants(this.listConfigCases[this.currentCase].listBackUps[i].merchantIds)

                }

            }
        }
        this.changeValueDetailBackUp(idx);
    }
    onChangeNewBackUpMerchantSelect(idx) { }
    removeNewBackUpMerchantID(id: String, idx) {
        if (id) {
            var index = this.newBackUp.merchantIds.indexOf(id);
            if (index !== -1) {
                this.newBackUp.merchantIds.splice(index, 1);
                // this.onChangeMerchantSelect(true)
            }

        }
    }
    removeBackUpMerchantID(id: String, idx) {
        this.listConfigCases[this.currentCase].updatedDate = this.datepipe.transform(new Date(), 'dd-MM-YYYY HH:mm:ss')

        if (id) {
            var index = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds.indexOf(id);
            if (index !== -1) {
                this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds.splice(index, 1);
                this.listConfigCases[this.currentCase].listBackUps[idx].tmpSelectedMerchants = this.listConfigCases[this.currentCase].listBackUps[idx].merchantIds
                this.onChangeMerchantSelect(true)
            }

        }
        this.changeValueDetailBackUp(idx);
    }
    removeComapareBackUpMerchantID(id) {
        if (id) {
            var index = this.newBackUp.merchantIds.indexOf(id);
            if (index !== -1) {
                this.newBackUp.merchantIds.splice(index, 1);
                this.tmpCompareSelectedMerchants = this.newBackUp.merchantIds
                this.highlightedMerchantIds = this.getHighlightMerchantId(this.newBackUp?.merchantIds, this.oldBackUp?.merchantIds)
            }
        }
    }

    private _sortRules(rules: Array<any>) {
        try {
            let mapRulesByCardType = {};
            let sortedList = [];
            // const ruleAdded = [];
            rules = _.orderBy(rules, ['level']);
            let isAddedMultiCards = false;
            Object.keys(this.cardListPriority).forEach(cardType => {
                let rulesByCardTypes = [];
                rules.forEach(rule => {
                    //
                    let ruleId = rule.id;
                    let cards = _.map(rule['card_types'], (ele) => ele.toLowerCase());
                    let index = cards.findIndex((item: string) => {
                        return item.toLowerCase() === cardType.toLowerCase();
                    });
                    if (cards.length == 1) {
                        //
                        if (index != -1) {
                            // ruleAdded.push(ruleId);
                            const orderIndex = rule.level;
                            rulesByCardTypes.splice(orderIndex, 0, rule);
                        }
                    } else if (_.includes(cards, cardType.toLowerCase()) && !isAddedMultiCards && index != -1) {
                            // ruleAdded.push(ruleId);
                            const orderIndex = rule.level;
                            rulesByCardTypes.splice(orderIndex, 0, rule);
                            isAddedMultiCards = true;
                    }
                });
                sortedList.push(...rulesByCardTypes);
                mapRulesByCardType[cardType] = rulesByCardTypes;
            });
            return sortedList;
        } catch (error) {
            console.log(error);
        }
        return rules;
    }

    public suggestAcquirerByMCC(mcc) {
        try {
            const cardList = this.getCardType();
            if (_.isEmpty(cardList)) {
                this.toastr.warning('Vui lòng chọn card type.');
                return;
            }
            if (cardList.length > 1) {
                this.toastr.warning('Vui lòng chọn 1 loại card type khi sử dụng chức năng này.');
                return;
            }
            this.loading = true;
            const cardType = cardList[0].toUpperCase();
            const service = 'QT';
            const merchantIds = this.ruleGroup.merchantIds.map(a => a.substring(0, a.indexOf("(")));
            const params = {
                service,
                merchantId: merchantIds,
                findAllBankMerchantId: true,
            }
            this.paymentOPFeeService.getMerchantInfo(params).subscribe(data => {
                this.loading = false;
                const sourceData = data.merchantFeeInfos;
                const processData = _.isEmpty(_.filter(sourceData, (d) => d.mcc === mcc)) ? sourceData : _.filter(sourceData, (d) => d.mcc === mcc);
                this.listMerchantFeeInfoData = _.filter(processData,
                    (mcFee) => this.visibleMerchantFee(mcFee.transactionType) && mcFee.cardType === cardType);
                this.isShowingSuggest = true;
            });
        } catch (error) {
            console.log(error);
            this.loading = false;
            this.isShowingSuggest = false;
        }
    }

    public applySuggest(acq : any) {
        const acqId = acq.suggestAcquirerId;
        const bankMerchantId = acq.suggestBankMerchantId;
        const key2B = acqId + "-2B";
        const key3B = acqId + "-3B";
        const keys = _.concat(key2B, key3B);
        _.forEach(keys, (key, indexKey) => {
            let sBankIdType = indexKey == 0 ? '2B' : '3B';
            this.listBankMid[key].forEach((data, index) => {
                if (data === bankMerchantId) {
                    this.sAcq = acqId.toString();
                    this.sBankIdType = sBankIdType;
                    this.sBankMerchantId = bankMerchantId;
                    this.getBankMerchantId();
                }
            });
        });
    }

    private visibleMerchantFee(tranType : String) {
        if (_.isEmpty(tranType))
            return true;
        if (_.includes(tranType.toLowerCase(), 'purchase') || _.includes(tranType.toLowerCase(), 'all'))
            return true;
        return false;
    }

    loadAcqRuleHistory() {
        this.historyRules = [];
        this.minDate.setMonth(this.minDate.getMonth() - 1);
        const toDateFormated = this.datepipe.transform(this.toDate, "dd/MM/yyyy");
        const fromDateFormated = this.datepipe.transform(this.fromDate, "dd/MM/yyyy");
        const params = new HttpParams()
        .set('groupId', this.ruleGroup.id)
        .set('page', this.page)
        .set('page_size', this.pageSize)
        .set('fromDate', fromDateFormated)
        .set('toDate', toDateFormated)
        this.acquirerRuleService.getListAcquirerRuleHistory(params).subscribe(data => {
            if (_.isEmpty(data) || _.isEmpty(data.list)) {
                return;
            }
            this.historyRules = data.list;
        });
    }

    convertTextIsActivate(value) {
        if (value == 1)
            return "Active";
        else if (value == 0)
            return "Expired";
        return "";
    }

    onChangeFromDate(fromDate: Date) {
        this.fromDate = fromDate;
    }

    onChangeToDate(toDate: Date) {
        this.toDate = toDate;
    }

    binGroupInfo = [];
    isSourceDirect = true;
    binGroupFilter = this.binGroup;
    /**
     * Filter dữ liệu bin group theo source để điền vào combobox, gọi vào khi change combobox source hoặc khi click Edit 1 Rule
     * @params source source bin group (Direct, GooglePay, ApplePay, SamsungPay)
     */
    public filterBinGroupBySource(source) {

        this.binGroupFilter = this.binGroup;
        if ("direct" === source.toLowerCase()) {
            this.isSourceDirect = true;
            return;
      } else
        this.isSourceDirect = false;
        let filterValue = _.filter(this.binGroupInfo,
        ele => _.get(ele, 'source', '').toLowerCase() === source.toLowerCase());

        let filterGroupName = [];
        _.forEach(filterValue, ele => {
        filterGroupName.push(ele["name"]);
        })

        this.binGroupFilter = _.filter(this.binGroup, (ele) => {
        return _.includes(filterGroupName, ele["name"]);
        });
        this.sBinGroup = _.filter(this.sBinGroup, (ele) => {
          return _.includes(filterGroupName, ele)
        });
    }
}
