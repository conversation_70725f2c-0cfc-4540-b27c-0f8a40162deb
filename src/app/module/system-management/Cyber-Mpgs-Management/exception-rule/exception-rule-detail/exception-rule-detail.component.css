.flex-gap-10 {
  gap: 10px !important;
}

.selectMerchant .p-multiselect {
  width: 350px;


}

.header-top-center {
  padding-left: 20px;
  color: #525050;
  /* position: absolute; */
  /* transform: translate(-50%, -50%); */
  /* top: 50%; */
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 24px;
  width: 100%;
  align-self: center;
}

.panel-back-up {
  width: 95%;
  padding-top: 20px;
}

.panel-back-up-header {
  width: 100%;
}

.backup-update ::ng-deep .p-panel-header {
  width: 100%;
  background-color: #ffcd83;
}
.backup-delete ::ng-deep .p-panel-header {
  width: 100%;
  background-color: #EFD6D6;
}
.backup-default ::ng-deep .p-panel-header {
  width: 100%;
  background-color: rgb(212, 212, 212);
}
::ng-deep .p-panel-icons {
  display: flex;
}

.action-button {
  /* position: absolute; */
  /* width: 100px;
  height: 30px; */
  border: none;
  width: 150px !important;
  height: 30px;
  background-color: #2196F3;
  color: white;
  align-self: center
}

.label {}

.dropdown {
  width: 15%;
}
.merchant-delete{
  background:rgb(251 106 106)!important;
}
.merchant-add{
  background: #ffcd83!important;
}
.scroll {
  background-color: #F6F6F6;
  height: 150px;
  overflow-x: scroll;
  padding: 10px !important;
}
/* ::ng-deep .modal-dialog {
  max-width: 1500px !important;
  padding-top: 200px !important;
  height: 500px !important;
} */

.chip {
  border-radius: 10px;
  background-color: #B6B5B5;
  padding-left: 5px;
  padding-right: 5px;
  color: white;
}

.edit-color {
  overflow: auto;
  color: #ffcd83;
}
.edit-color-background {
  text-align: center;
  overflow: auto;
  background-color: #ffcd83;
}
.delete-color {
  overflow: auto;
  color: rgb(251 106 106);
}
.delete-color-background {
  text-align: center;
  overflow: auto;
  background-color: rgb(251 106 106);
}

.default-color{
  overflow: auto;
}
.default-color-background{
  text-align: center;
  overflow: auto;
}

::ng-deep .my-custom-tooltip {
  max-width: unset !important;
}

.font-text {
  font-size:13px;
  font-weight: 600;
}

.background-table{
  background-color:#a4d5ab !important;
};

.border-td-table{
  border-right: 1px solid lightgray !important;
}
tr.suggest:hover {
  background: #e9ecef;
  color: #495057;
}
.col-1-modify {
  flex: 0 1 12.5%;
  max-width: 12.5%;
  position: relative;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
.inactive-status {
  color: red !important;
}
tr.suggest-content > td {
  word-wrap: break-word;
}
#acq-rule-history .mat-column-common {
  width: 70px;
}
#acq-rule-history .mat-column-no {
  width: 50px;
}
#acq-rule-history .mat-column-lg {
  width: 90px;
}
.dropdown-area-disabled {
  background-color: rgba(118, 118, 118, 0.3) !important;
}
