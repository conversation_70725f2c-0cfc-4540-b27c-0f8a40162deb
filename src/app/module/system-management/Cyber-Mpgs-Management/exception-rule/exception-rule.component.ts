import { HttpParams } from '@angular/common/http';
import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { ToastrService } from 'ngx-toastr';
import { LazyLoadEvent } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subscription } from 'rxjs';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { AcquirerRuleService } from '@service/acquirer-rule.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import _ from 'lodash';
import { DatePipe } from '@angular/common';
import { HotSwitchCyberMpgsService } from '@service/hot-switch-cyber-mpgs.service';
import { MerchantService } from '@service/merchant.service';

@Component({
  selector: 'app-exception-rule',
  templateUrl: './exception-rule.component.html',
  styleUrls: ['./exception-rule.component.css'],
  providers: [DialogService],
})
export class ExceptionRuleComponent implements OnInit {
  public page = 0;
  public pageSize = 50;
  public first = 0;
  public loading: boolean;
  public subscription: Subscription;
  public keyword = '';
  public tableDataTotal: number;
  public visibleListMerchantIds: boolean;
  public listConfig = [];
  public checkedAll = false;
  public checked = false;
  public merchantBackUps = [];
  public visibleMerchantBackUps = false;
  public approvalRuleGroups = [
  ];
  public isAll = false;
  //
  public filterMCC = '';
  public filterCurrency = '';
  public filterPaymethod = '';
  public filterCVV = '';
  //
  public listBackUps = [];
  public listDefault = [];
  public checkedCount = 0;
  public currentGroup = {
    id: null,
    name: "",
    backUps: [
    ]
  }
    ;
  public configForm = {
    merchantIds: [],
    merchantIdsText:"",
    groupDefaultId: null,
    backUps: [
    ]
  }

  filteredMerchants: any[] = [];
  listFullMerchant: any[] = [];
  selectedMerchants: any[] = [];

  public pageSizeList = [
    {
      label: '50',
      value: '50'
    },
    {
      label: '100',
      value: '100'
    },
    {
      label: '200',
      value: '200'
    },
    {
      label: '500',
      value: '500'
    },
    {
      label: '1000',
      value: '1000'
    }
  ];

  //
  constructor(
    public dialogService: DialogService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    public global: Globals,

    //Add Acquirer Rule
    public acquirerRuleService: AcquirerRuleService,
    private confirmService: ConfirmService,
    private activatedRouter: ActivatedRoute,
    public datepipe: DatePipe,
    public hotSwitchService: HotSwitchCyberMpgsService,
    private merchantService: MerchantService,
  ) { }
  ngOnInit(): void {
    this.innitParams();
    // get list group
    this.getListGroupDefault();
    this.getListMerchantID();
    this.getListSwitchScript();
    this.getAllPartner();
  }
  onChangeGroup() {
    this.listBackUps = [];
    this.getAcqRuleGroupById();
    // get current group info
  }
  approval(){
    this.router.navigate(['/system-management/merchant-config-rule-approval']);
  }

  onSetUpConfig(){
    this.configForm =  {
      merchantIds: [],
      merchantIdsText:"",
      groupDefaultId: this.listDefault[0].value ?? null,
      backUps: [
      ]
    }
    this.onChangeGroup()
    this.listConfig.forEach((i)=>{
      if (i.isCheck){
        this.configForm.merchantIds.push(i.merchantId);
      }
    })
    this.configForm.merchantIdsText = this.configForm.merchantIds.join(",");

  }

  getAcqRuleGroupById() {
    if (!this.configForm.groupDefaultId) {
      return null;
    }
    this.acquirerRuleService.GetAcqRuleGroup(this.configForm.groupDefaultId, false).subscribe(res => {
      if (!res || !res.id ) {
        return null;
      };
      this.setFullAcqGroupFromResponse(res);

    });
  }
  onSaveConfig(){
    console.log(this.configForm);
    console.log(this.listBackUps);
    this.configForm.backUps = this.listBackUps;
    this.acquirerRuleService.AddMerchantAcqGroup(this.formatAddConfigRequest()).subscribe(res => {
      if (res && res.status =="Successful") {
      this.checkedCount =0;
      this.checkedAll = false;
       this.configForm =  {
        merchantIds: [],
        merchantIdsText:"",
        groupDefaultId: null,
        backUps: [
        ]
      }
      this.listBackUps = [];
      this.toastr.success('Successful', "Add Config");
      this.getMerchantGroupConfig().subscribe(res => {
        this.loading = false;
        this.getListMerchanrGroupConfigFromResponse(res);
      })
      this.router.navigate(['/system-management/merchant-acq-group-config'], { queryParams: this.redirectParams() });

      }else{

      }
    })
  }
  formatAddConfigRequest(){
    let defaultGroup = this.listDefault.find((e)=>{
      return e.value == this.configForm.groupDefaultId
    })
    let body = {
      merchant_ids : this.configForm.merchantIds,
      default :{
        id : defaultGroup.value,
        group_name : defaultGroup.name,
      },
      back_ups : this.configForm.backUps.map((e)=>{
        let mapBackUp = e.list.find((e1)=> {return e1.id == e.selected })
        console.log("mapBackUp")
        console.log(e.selected)
        console.log(mapBackUp)
        if(e.selected === -1){
          return null;
        }
        return {
          case:e.case+"",
          id:e.selected,
          group_name :mapBackUp.name ??"",
          is_back_up_default : mapBackUp.isBackUpDefault ?? null,
        }
      }).filter((e1)=>{
        return e1 != null
      })

    }
    return body;
  }
  private setFullAcqGroupFromResponse(res) {
    this.listBackUps = [];
    if (res.cases && res.cases.length > 0) {
      res.cases.forEach((c) => {
        let backups = [];
        let defaultOpt = null;
        if (c.back_ups && c.back_ups.length > 0) {
          c.back_ups.forEach((b)=>{
            let backup = this.setAcqRuleGroupFromResponse(b)
            defaultOpt = backup && backup.isBackUpDefault ? backup : defaultOpt
            backups.push(backup);
          })
        }
        this.listBackUps.push({

          case : c.case_id ?? 0,
          caseName : c.case_name ?? "",
          list : backups,
          selected : defaultOpt && defaultOpt.id ? defaultOpt.id :backups.length>0 ?backups[0].id : null
        })
      })
    }
  }
  private setAcqRuleGroupFromResponse(res: any) {
    let ruleGroup = {
      id: null,
      approvalId: null,
      name: "",
      type: "Default",
      description: "",
      merchantIds: [],
      merchantIdsText: "",
      rules: [],
      isEditable: true,
      isReview: false,
      backUps: [],
      case: null,
      isBackUpDefault: null,
    }
    ruleGroup.id = res.id
    ruleGroup.name = res.group_name ?? "";
    ruleGroup.description = res.description ?? "";
    ruleGroup.type = res.type;
    ruleGroup.merchantIds = res.merchant_ids ?? [];
    if (res.case && res.case?.length > 0) {

      ruleGroup.case = res.case
      ruleGroup.isBackUpDefault = res.is_back_up_default ?? false;
    }
    return ruleGroup;
  }
  getListGroupDefault() {
    this.acquirerRuleService.GetAllGroupDefault().subscribe(res => {
      if (res && res.data.length > 0) {
        res.data.forEach((i) => {
          if (i) this.listDefault.push({ name: i.id + " - " + i.name, value: i.id });
        });
      }
    })
  }

  onCheckIsAll() {
    if (this.isAll) {
      this.filteredMerchants = _.sortBy(this.listFullMerchant, ['acqGroupId', 'name']);
    } else {
      this.filteredMerchants = this.listFullMerchant.filter(merchant => !merchant.acq_group_id)
    }
    this.searchData();
  }
  innitParams() {
    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.keyword = params['keyword'] === undefined ? '' : params['keyword'].trim();
        this.page = params['page'] === undefined ? '0' : params['page'];
        this.pageSize = params['page_size'] === undefined ? '50' : params['page_size'];
        this.first = params['first'] !== undefined ? parseInt(params['first']) : 0;
      });

  }

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;
    this.page = event.first / event.rows;
    this.first = event.first;
    return this.getMerchantGroupConfig().subscribe(res => {
      this.loading = false;
      this.getListMerchanrGroupConfigFromResponse(res);
    })
  }

  getListMerchanrGroupConfigFromResponse(res: any) {
    if (!res) return;
    this.tableDataTotal = res.totalItems;
    this.listConfig = [];
    if (res.list && res.list.length > 0) {
      res.list.forEach((item) => {
        this.listConfig.push(this.setMerchanrGroupConfigFromResponse(item));
      })
    }
  }


  searchData() {
    this.checkedAll = false;
    this.checkedCount = 0;
    this.first = 0;
    this.page = 0;
    this.loading = true;
    return this.getMerchantGroupConfig().subscribe(res => {
      this.loading = false;
      this.getListMerchanrGroupConfigFromResponse(res);
    });
  }
  setMerchanrGroupConfigFromResponse(res: any) {
    let merchantGroupConfig = {
      merchantId: "",
      default: "",
      createdTime: "",
      updatedTime: "",
      isCheck: false,
      //
      currencyCode: '',
      payMethod: '',
      groupNameDefault: '',
      scriptId: '',
      mcc: '',
      cvv: '',
      runningConfig: '',
      type: '',
      ruleGroupId: '',
      groupIdDefault: '',
      script: '',
      tickerSize: ''
      //
    }
    merchantGroupConfig.merchantId = res.merchantId;
    merchantGroupConfig.default = res?.groupName ?? "";
    merchantGroupConfig.createdTime = res?.createDate ?? "";
    merchantGroupConfig.updatedTime = res?.updateDate ?? "";
    //
    merchantGroupConfig.currencyCode = res?.currencyCode ?? "";
    merchantGroupConfig.payMethod = res?.payMethod ?? "";
    merchantGroupConfig.groupNameDefault = res?.groupNameDefault ?? "";
    merchantGroupConfig.scriptId = res?.scriptId ?? "";
    merchantGroupConfig.mcc = res?.mcc ?? "";
    merchantGroupConfig.cvv = res?.cvv ?? "";
    merchantGroupConfig.runningConfig = res?.runningConfig ?? "";
    merchantGroupConfig.type = res?.type ?? "";
    merchantGroupConfig.ruleGroupId = res?.ruleGroupId ?? "";
    merchantGroupConfig.groupIdDefault = res?.groupIdDefault ?? "";
    merchantGroupConfig.script = res?.script ?? "";
    merchantGroupConfig.tickerSize = res?.tickerSize ?? "";
    return merchantGroupConfig;
  }
  showBackUpsDialog(config) {
    const merchantId = config.merchantId;
    const type = _.get(config, 'type', '').toLowerCase();
    const isDefaultRunning =  type == 'default';
    let buildAllConfig = [];
    if (_.isEmpty(type))
      buildAllConfig = [];
    else if (isDefaultRunning) {
      buildAllConfig = _.concat(buildAllConfig, {
        "group_name": config['default'],
        "id": config['ruleGroupId'],
        "case": "DEFAULT",
        "caseName": config['default'],
        "is_back_up_default": false,
        "type": config['type'],
        "is_running": _.get(config, 'type', '').toLowerCase() == 'default',
      });
    } else {
      buildAllConfig = _.concat(buildAllConfig, {
        "group_name": config['groupNameDefault'],
        "id": config['groupIdDefault'],
        "case": "DEFAULT",
        "is_back_up_default": false,
        "caseName": config['groupNameDefault'],
        "type": "Default",
        "is_running": false,
      }, {
        "group_name": config['default'],
        "id": config['ruleGroupId'],
        "case": config['scriptId'],
        "is_back_up_default": false,
        "caseName": config['default'],
        "type": "Backup",
        "is_running": true,
      });
    }
     this.acquirerRuleService.GetMerchantBackUps(merchantId).subscribe(res => {
        this.merchantBackUps = res ? res?.data ?? [] : [];
        this.visibleMerchantBackUps = true;
        this.merchantBackUps = _.concat(buildAllConfig, this.merchantBackUps);
     })

  }
  getMerchantGroupConfig() {
    const keyword = this.buildListMerchantParams(this.selectedMerchants);
    this.router.navigate(['/system-management/merchant-acq-group-config'], { queryParams: this.redirectParams() });
    var params = new HttpParams()
      .set('keyword', keyword.trim())
      .set('page', this.page)
      .set('page_size', this.pageSize)
      .set('mcc', this.filterMCC)
      .set('currency', _.get(this.filterCurrency, 'name', '') === 'All' ? '' : _.get(this.filterCurrency, 'name', ''))
      .set('paymethod', _.get(this.filterPaymethod, 'name', '') === 'All' ? '' : _.get(this.filterPaymethod, 'name', ''))
      .set('cvv', _.get(this.filterCVV, 'name', '') === 'All' ? '' : _.get(this.filterCVV, 'name', ''))
      .set('is_all', this.isAll);
    return this.acquirerRuleService.SearchMerchantAcqGroup(params);
  }

  buildListMerchantParams(selectedMerchants) {
    if(_.isEmpty(selectedMerchants))
      return this.keyword;
    const mcs = _.map(selectedMerchants, ele => ele.merchant_id);
    const keywords = _.join(mcs, ',');
    if (keywords === 'All')
      return '';
    return keywords;
  }

  redirectParams() {
    const params = {
      'keyword': this.keyword.trim(),
      'page': this.page,
      'page_size': this.pageSize,
      'first': this.first,
      "is_all": this.isAll,
    };

    return params;
  }
  onCheckAllConfig() {
    console.log("oncheckAll");
    console.log(this.checkedAll);
    this.checkedCount = 0;
    for (var i = 0; i < this.listConfig.length; i++) {
      this.listConfig[i].isCheck = this.checkedAll;
    }
    if (this.checkedAll) {
      this.checkedCount = this.listConfig.length;
    }

  }

  onCheckConfig(idx) {
    console.log("oncheck");
    console.log(idx);
    console.log(this.listConfig[idx]);
    if (this.listConfig[idx].isCheck){
      this.checkedCount ++;
      if(this.checkedCount == this.listConfig.length){
        this.checkedAll = true;
      }
    }else {
      this.checkedCount --;
      this.checkedAll =false;
    }


  }

  getListMerchantID() {
    let request = new HttpParams();
    this.acquirerRuleService.GetAllMerchantId(request).subscribe(res => {
        if (res && res.data.length > 0) {
          this.listFullMerchant = res.data;
          this.filteredMerchants = this.listFullMerchant.filter(merchant => !merchant.acq_group_id)
          // this.filteredMerchants.unshift({merchant_id: "All"});
        }
    })
  }

  convertTextRunningConfig(rule) {
    if (_.get(rule, 'ruleGroupId', 0) == 0)
      return '';
    if (rule.type == 'Default')
      return _.get(rule, 'ruleGroupId','Default');
    let nameConvert = 'BK-' + _.get(rule, 'ruleGroupId', '') + '_' + _.get(rule, 'script', '');
    return nameConvert;
  }

  convertTextGroupName(config) {
    if (_.get(config, 'type', '').toLowerCase() === 'default')
      return config['id'];
    return 'CASE' + config['case'];
  }

  redirectDetailConfig(config, type) {
    let acqConfigId = _.get(config, 'id', '');
    if (type == 'DEFAULT') {
      let defaultGroupId = _.get(config, 'defaultGroupId', 0);
      if (defaultGroupId == 0)
        acqConfigId = _.get(config, 'groupId', '');
      else
        acqConfigId = defaultGroupId;
    } else if (type == 'BACKUP') {
        acqConfigId = _.get(config, 'groupId', '');
    } else if (type == 'CONFIGTO') {
      acqConfigId = _.get(config, 'groupIdTo', '');
    } else if (type == 'CONFIGFROM') {
      acqConfigId = _.get(config, 'groupIdFrom', '');
    }
    if (acqConfigId === '')
      return;
    this.router.navigate([]).then(result => {  window.open('/iportal/system-management/acq-group-config/detail/' + acqConfigId, '_blank'); });
  }

  redirectRunningConfig(config) {
    const acqConfigId = _.get(config, 'ruleGroupId', '');
    if (acqConfigId === '' || acqConfigId == 0)
      return;
    this.router.navigate([]).then(result => {  window.open('/iportal/system-management/acq-group-config/detail/' + acqConfigId, '_blank'); });
  }

  hasRunningConfig(config) {
    const acqConfigId = _.get(config, 'ruleGroupId', '');
    if (acqConfigId === '' || acqConfigId == 0)
      return false;
    return true;
  }

  public merchantInfo = {
    "merchantId": "",
    "active": 0,
    "requiredAVS": 0,
    "partnerId": 0,
    "partnerName": "",
    "midId": 0,
    "tradingId": "",
    "tradingName": "",
    "categoryCode": "",
    "merchantWebsite": "",
    "receiptReturnUrl": "",
    "currencyCode": "",
    "paymethod": "",
    "cardType": "",
    "fraudRoles": "",
  };

  public selectedMerchantConfig = null;

  public historyMerchantsConfig = [];

  public historyMerchantsSwitch = [];

  public scenarioList = [];

  public mapPartner;

  public merchantDetailText = '';

  public pageSizeMerchantConfig = 50;

  public merchantConfigFrom: Date = new Date();
  public merchantConfigTo: Date = new Date();
  public merchantSwitchFrom: Date = new Date();
  public merchantSwitchTo: Date = new Date();
  public convertSelectedConfigText = {};

  public scenarioFrom = '';
  public scenarioTo = '';


  onChangeFromDate(fromDate: Date) {
      this.merchantConfigFrom = fromDate;
  }

  onChangeToDate(toDate: Date) {
      this.merchantConfigTo = toDate;
  }

  onChangeFromDateSwitch(fromDate: Date) {
    this.merchantSwitchFrom = fromDate;
  }

  onChangeToDateSwitch(toDate: Date) {
    this.merchantSwitchTo = toDate;
  }

  merchantDetail(config) {
    this.selectedMerchantConfig = config;
    const merchantId = config.merchantId;
    if (_.isEmpty(merchantId))
      return;
    this.acquirerRuleService.getMerchantDetail(merchantId).subscribe(data => {
      if (_.isEmpty(data))
        return;
      const partnerId = _.get(data, 'partnerId', 0);
      const partnerName = _.get(this.mapPartner[partnerId], 'shortName', '');
      data.partnerName = partnerName;
      this.merchantInfo = data;

    })
  }

  convertTextWithBoolean(input) {
    if (1 == input || '1' === input) return 'TRUE';
    if (0 == input || '0' === input) return 'FALSE';
    return '';
  }

  merchantConfigHistory(config) {
    this.historyMerchantsConfig = [];
    this.merchantDetailText = _.get(config, 'merchantId', '')
      + '(' + _.get(config, 'mcc', '')
      + '_' + _.get(config, 'payMethod', '')
      + '_' + _.get(config, 'currencyCode', '')
      + '_' + _.get(config, 'cvv', '') + ')';
    this.selectedMerchantConfig = config;
    const merchantId = config.merchantId;
    if (_.isEmpty(merchantId))
      return;
    //
    const toDateFormated = this.datepipe.transform(this.merchantConfigTo, "dd/MM/yyyy");
    const fromDateFormated = this.datepipe.transform(this.merchantConfigFrom, "dd/MM/yyyy");
    const params = new HttpParams()
      .set('merchantId', merchantId)
      .set('page', this.page)
      .set('page_size', this.pageSizeMerchantConfig)
      .set('fromDate', fromDateFormated)
      .set('toDate', toDateFormated)
    //
    this.acquirerRuleService.getMerchantConfigHistory(params).subscribe(data => {
      if (_.isEmpty(data) || _.get(data, 'totalItems', '0') === '0')
        return;
      this.historyMerchantsConfig = _.get(data, 'list', []);
    })
  }

  merchantSwitchHistory(config) {
    let scenarioFromValue = '';
    let scenarioToValue = '';
    this.historyMerchantsSwitch = [];
    this.merchantDetailText = _.get(config, 'merchantId', '')
      + '(' + _.get(config, 'mcc', '')
      + '_' + _.get(config, 'payMethod', '')
      + '_' + _.get(config, 'currencyCode', '')
      + '_' + _.get(config, 'cvv', '') + ')';
    this.selectedMerchantConfig = config;
    const merchantId = config.merchantId;
    if (_.isEmpty(merchantId))
      return;
    //
    const toDateFormated = this.datepipe.transform(this.merchantSwitchTo, "dd/MM/yyyy");
    const fromDateFormated = this.datepipe.transform(this.merchantSwitchFrom, "dd/MM/yyyy");
    if (!_.isEmpty(this.scenarioFrom) && this.scenarioFrom['id'] != -1)
      scenarioFromValue = this.scenarioFrom['id'];
    if (!_.isEmpty(this.scenarioTo) && this.scenarioFrom['id'] != -1)
      scenarioToValue = this.scenarioTo['id'];
    const params = new HttpParams()
      .set('merchantId', merchantId)
      .set('page', this.page)
      .set('page_size', this.pageSizeMerchantConfig)
      .set('fromDate', fromDateFormated)
      .set('toDate', toDateFormated)
      .set('scenarioTo', scenarioToValue)
      .set('scenarioFrom', scenarioFromValue)
    //
    this.acquirerRuleService.getMerchantSwitchHistory(params).subscribe(data => {
      if (_.isEmpty(data) || _.get(data, 'totalItems', '0') === '0')
        return;
      this.historyMerchantsSwitch = _.get(data, 'list', []);
    })
  }

  convertTextSwitchBy(config) {
    return _.get(config, 'manualSwitchStatus', '').toLowerCase() === 'completed' ? 'Manual' : 'HotSwitch';
  }

  convertTextRunningConfigType(config) {
    const name = this.convertTextScenarioTo(config);
    if (config.switchStatus === 'SUCCESS') {
      return name === 'Default' ? 'Default' : 'Backup';
    }
    return name === 'Default' ? 'Backup' : 'Default';
  }

  convertTextScenarioFrom(config) {
    const name = _.get(config, 'scenarioNameFrom', '');
    return name === '' ? 'Default' : name;
  }

  convertTextScenarioTo(config) {
    const name = _.get(config, 'scenarioNameTo', '');
    return name === '' ? 'Default' : name;
  }

  getListSwitchScript() {
    let request = new HttpParams();
    this.hotSwitchService.getListSwitchScript(request).subscribe(data => {
      if (_.isEmpty(data) || _.isEmpty(data.list) || data.list.length == 0)
        return;
      this.scenarioList = _.concat({id: -1, script: 'All', desc: '', createDate: ''}, { id: 0, script: 'Default', desc: '', createDate: ''}, data.list);
    })
  }

  download(config) {
    this.selectedMerchantConfig = config;
    const merchantId = config.merchantId;
    if (_.isEmpty(merchantId))
      return;
    //
    const toDateFormated = this.datepipe.transform(this.merchantConfigTo, "dd/MM/yyyy");
    const fromDateFormated = this.datepipe.transform(this.merchantConfigFrom, "dd/MM/yyyy");
    const params = new HttpParams()
      .set('merchantId', merchantId)
      .set('page', this.page)
      .set('page_size', this.pageSizeMerchantConfig)
      .set('fromDate', fromDateFormated)
      .set('toDate', toDateFormated)
      .set('type', 'CONFIG')
    //
    return this.acquirerRuleService.downloadMerchantConfigHistory(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    })
  }

  downloadMerchantSwitch(config) {
    this.selectedMerchantConfig = config;
    const merchantId = config.merchantId;
    if (_.isEmpty(merchantId))
      return;
    //
    const toDateFormated = this.datepipe.transform(this.merchantConfigTo, "dd/MM/yyyy");
    const fromDateFormated = this.datepipe.transform(this.merchantConfigFrom, "dd/MM/yyyy");
    const params = new HttpParams()
      .set('merchantId', merchantId)
      .set('page', this.page)
      .set('page_size', this.pageSizeMerchantConfig)
      .set('fromDate', fromDateFormated)
      .set('toDate', toDateFormated)
      .set('type', 'SWITCH')
    //
    return this.acquirerRuleService.downloadMerchantConfigHistory(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    })
  }

  getAllPartner() {
    this.merchantService.getAllPartnerName().subscribe(data => {
      if (_.isEmpty(data) || _.isEmpty(data.listPartner))
        return;
      this.mapPartner = _.keyBy(data.listPartner, 'partnerId');
    });
  }

}
