import { HttpParams } from '@angular/common/http';
import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { ToastrService } from 'ngx-toastr';
import { LazyLoadEvent } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subscription } from 'rxjs';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
// Add Acquire Rull
import { AcquirerRuleService } from '@service/acquirer-rule.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { SortEvent } from 'primeng/api';
@Component({
  selector: 'app-acq-rule-group',
  templateUrl: './acq-rule-group-list.component.html',
  styleUrls: ['./acq-rule-group-list.component.css'],
  providers: [DialogService],
})
export class AcqRuleGroupListComponent implements OnInit {
  public page = 0;
  public pageSize = 20;
  public first = 0;
  public loading: boolean;
  public subscription: Subscription;
  public keyword = '';
  public tableDataTotal: number;
  public visibleListMerchantIds: boolean;
  public merchantIdsData: [];
  searchOption = [
    {
      label :"Group Name",
      value:1,
    },
    {
      label :"Merchant Id",
      value:0,
    }
  ]
  approvalOption = [
    {
      label :"All",
      value:0,
    },
    {
      label :"Wait for approval",
      value:1,
    },
    {
      label :"Approved",
      value:-1,
    }
  ]
  // All và 2B only cùng value (??!)
  configOption = [
    {
      label :"All",
      value: 0,
    },
    {
      label :"2B,3B",
      value: 1,
    },
    {
      label :"2B only",
      value: 0,
    }
  ]
  searchOptionType = 1;
  filterApprovalOption = 0;
  filterConfigOption = 0;
  public approvalRuleGroups = [
  ];
  public status = {
    INSERT_ACQ_RULE_GROUP : "Wait For Insert",
    DELETE_ACQ_RULE_GROUP:"Wait For Delete",
    UPDATE_ACQ_RULE_GROUP:"Wait For Update"

  }
  //
  constructor(
    public dialogService: DialogService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    public global: Globals,

    //Add Acquirer Rule
    public acquirerRuleService: AcquirerRuleService,
    private confirmService: ConfirmService,
    private activatedRouter: ActivatedRoute,
  ) { }
  ngOnInit(): void {
    this.innitParams();
  }
  onCheckSearchAll(){

  }
  innitParams() {
    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.keyword = params['keyword'] === undefined ? '' : params['keyword'].trim();
        this.page = params['page'] === undefined ? '0' : params['page'];
        this.pageSize = params['page_size'] === undefined ? '20' : params['page_size'];
        this.first = params['first'] !== undefined ? parseInt(params['first']) : 0;
      });

  }

  loadLazy(event: LazyLoadEvent) {
    this.loading = true;
    this.page = event.first / event.rows;
    this.first = event.first;
    return this.getAcqRuleGroups().subscribe(res => {
      this.loading = false;
      this.getListApprovalGroupFromRespose(res);
      if(event.sortField) {
        console.log(event);
        console.log(event.sortField);

        this.approvalRuleGroups.sort(function(a, b) {
          var textA = a.approval? "Wait For Approval" : ""
          var textB = b.approval? "Wait For Approval" : ""
          return event.sortOrder == 1? (textA < textB) ? -1 : (textA > textB) ? 1 : 0
          :(textA > textB) ? -1 : (textA < textB) ? 1 : 0;
      });
      }
    });
  }

  getListApprovalGroupFromRespose(res: any) {
    if (!res) return;
    this.tableDataTotal = res.total_row;
    this.approvalRuleGroups = [];
    if (res.data && res.data.length > 0) {
      res.data.forEach(item => {
        this.approvalRuleGroups.push(this.getRuleGroupFromResource(item))
      })
    }
  }
  // private setAcqRuleGroupFromResponse(res: any) {
  //   let approvalGroup = {
  //     approvalId: null,
  //     approvalStatus: "",
  //     ruleGroup: null
  //   }
  //   approvalGroup.approvalId = res.approval_id ?? null;
  //   approvalGroup.approvalStatus = res.approval_status? this.status[res.approval_status] : '';
  //   approvalGroup.ruleGroup = this.getRuleGroupFromResource(res.rule_group);

  //   return approvalGroup;
  // }
  getRuleGroupFromResource(res) {
    if (!res) return null;
    let ruleGroup= {
      id: null,
      name: "",
      type: "",
      description: "",
      merchantIds: null,
      merchantIdsText :"",
      rules: [],
      createdTime: "",
      updatedTime: "",
      approval:false,
      approvalType : 0,
      enable: true,
      merchantIdsOriginal: null,
      merchantIdsOriginalText: '',
    }
    ruleGroup.id = res?.id ?? null;
    ruleGroup.name = res.group_name ?? "";
    ruleGroup.description = res.description ?? "";
    ruleGroup.type = res.type;
    ruleGroup.merchantIds = res.merchant_ids ?? [];
    ruleGroup.merchantIdsText = this.getShortenText(ruleGroup.merchantIds ,10) ?? "";
    ruleGroup.merchantIdsOriginal = res.merchant_ids_original ?? [];
    ruleGroup.merchantIdsOriginalText = this.getShortenText(ruleGroup.merchantIdsOriginal ,10) ?? "";
    ruleGroup.createdTime = res.created_date ?? "";
    ruleGroup.approval = res.approval ?? false;
    ruleGroup.approvalType = res.approval_type ?? 0;
    ruleGroup.enable = res.enable != null &&res.enable === false? false: true;
    ruleGroup.updatedTime = res.updated_date ?? "";
    if (res.rules && res.rules.length > 0){

    }
    console.log(ruleGroup.merchantIdsText);
    return ruleGroup;
  }
  getAcqRuleGroups() {
    this.router.navigate(['/system-management/acq-group-config'], { queryParams: this.redirectParams() });
    var params = new HttpParams()
      .set('keyword', this.keyword.trim())
      .set('page', this.page)
      .set('page_size', this.pageSize)
      .set('by_group_name', this.searchOptionType)
      .set('approval', this.filterApprovalOption)
      .set('config', this.filterConfigOption);
    return this.acquirerRuleService.SearchAcqRuleGroup(params);
  }
  disableGroup(id){
    this.confirmService.build()
    .message(`<div class='content-confirm'> Are you sure to disable this group ?</div>`)
    .title('Notice!')
    .no('No')
    .yes('Yes').confirm().subscribe(result => {
      if (result) {
        // enable all
        this.acquirerRuleService.UpdateAcqRuleGroup({"id":id,"enable_group":false}).subscribe(data => {
          if (data.status && data.status == "Successful") {
            this.toastr.success('Request disabled group successfully. Please approve the rule group on the detail screen.');
            return this.searchData();
          }

        });
      }
    })
  }
  enableGroup(id){
    this.confirmService.build()
    .message(`<div class='content-confirm'> Are you sure to enable this group ?</div>`)
    .title('Notice!')
    .no('No')
    .yes('Yes').confirm().subscribe(result => {
      if (result) {
        // enable all
        this.acquirerRuleService.UpdateAcqRuleGroup({"id":id,"enable_group":true}).subscribe(data => {
          if (data.status && data.status == "Successful") {
            this.toastr.success('Request enabled group successfully. Please approve the rule group on the detail screen.');
            return this.searchData();
          }

        });
      }
    })
  }
  searchData() {
    this.first = 0;
    this.page = 0;
    this.loading = true;
    return this.getAcqRuleGroups().subscribe(res => {
      this.loading = false;
      this.getListApprovalGroupFromRespose(res);
    });
  }

  redirectParams() {
    const params = {
      'keyword': this.keyword.trim(),
      'page': this.page,
      'page_size': this.pageSize,
      'first': this.first,
      "by_group_name": this.searchOptionType,
    };

    return params;
  }

  showDetail(id) {
    this.router.navigate(['/system-management/acq-group-config/detail/' + id]);
  }

  showReview(id) {
    this.router.navigate(['/system-management/acq-group-config/review/' + id]);
  }

  showCreate() {
    this.router.navigate(['/system-management/acq-rule-group']);

  }

  merchantIdsDialog(data: any) {
    this.merchantIdsData = data.map(str => {
      const parts = str.split(/[\(\)]/);
      const finalParts = parts[1].split("_");
      return {
        "merchantId": parts[0],
        "categoryCode": finalParts[0],
        "payMethod": finalParts[1],
        "currency": finalParts[2],
        "tokenCvv": finalParts[3],
        "createDate": finalParts[4],
        "lastUpdateDate": finalParts[5]
      }
    });
    this.visibleListMerchantIds = true;
  }
  deleteGroup(id) {
    this.confirmService.build()
    .message(`<div class='content-confirm'> Are you sure to delete this Acquirer Rule Group?</div>`)
    .title('Notice!')
    .no('No')
    .yes('Yes').confirm().subscribe(result => {
      if (result) {
        this.acquirerRuleService.DeleteAcqRuleGroup(id).subscribe(data => {
          if (data.status && data.status == "Successful" ){
            this.toastr.success('Delete Group Success', 'Success') ;
            return this.searchData();

          }
            this.toastr.error('Somethings went wrong', 'Failed');
        });
      }
    })
  }

  getShortenText(list, max) {
    if (list == null && list.length == 0) {
      return "";
    }
    let s = list.toString();
    if (s.length <= max) {
      return s;
    }
    return s.slice(0, max) + "...";
  }

  download(){
    var params = new HttpParams()
      .set('keyword', this.keyword.trim())
      .set('searchOptionType', this.searchOptionType)
      .set('approvalOption', this.filterApprovalOption)
      .set('configOption', this.filterConfigOption)
    return this.acquirerRuleService.download(params).subscribe(response => {
      this.global.downloadEmit.emit(true);
    });
  }
}
