import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { PaymentAdvanceService } from '@service/payment-advance.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DatePipe, Location } from '@angular/common';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { HttpParams } from '@angular/common/http';
@Component({
  selector: 'pre-advance-component',
  templateUrl: './pre-advance-component.html',
  styleUrls: ['./pre-advance-component.css'],
  providers: [ConfirmationService, PaymentAdvanceService]
})

export class PreAdvanceComponent implements OnInit, OnDestroy {

  subscription: Subscription;
  advanceId: any;
  dataPreAdvance: any;
  dataHeader: any = {};

  //transfer content
  transferContent: any = "";
  isDisableTransferContent: boolean = true;
  flagUpdateContent: boolean = true;

  //Excel detail
  internationalExportOptions: MenuItem[];
  domesticExportOptions: MenuItem[];
  qrExportOptions: MenuItem[];
  smsExportOptions: MenuItem[];
  bilingExportOptions: MenuItem[];
  bnplExportOptions: MenuItem[];
  uposExportOptions: MenuItem[];
  pcExportOptions: MenuItem[];
  vietqrExportOptions: MenuItem[];
  ddExportOptions: MenuItem[];

  //Table A - QT
  dataTableInternational: any[] = [];
  fromDateInternational: string;
  toDateInternational: string;
  toggleInternationalContent: any = "(Ẩn chi tiết)";
  isShowInternational: boolean = true;

  //Table B - ND
  dataTableDomestic: any[] = [];
  fromDateDomestic: string;
  toDateDomestic: string;
  toggleDomesticContent: any = "(Ẩn chi tiết)";
  isShowDomestic: boolean = true;

  //Table C - QR
  dataTableQr: any[] = [];
  fromDateQr: string;
  toDateQr: string;
  toggleQrContent: any = "(Ẩn chi tiết)";
  isShowQr: boolean = true;

  //Table D - SMS
  dataTableSms: any[] = [];
  fromDateSms: string;
  toDateSms: string;
  toggleSmsContent: any = "(Ẩn chi tiết)";
  isShowSms: boolean = true;

  //Table J - BILLING
  dataTableBiling: any[] = [];
  fromDateBiling: string;
  toDateBiling: string;
  toggleBilingContent: any = "(Ẩn chi tiết)";
  isShowBiling: boolean = true;

  //Table K - BNPL
  dataTableBnpl: any[] = [];
  fromDateBnpl: string;
  toDateBnpl: string;
  toggleBnplContent: any = "(Ẩn chi tiết)";
  isShowBnpl: boolean = true;

  //Table L - UPOS
  dataTableUpos: any[] = [];
  fromDateUpos: string;
  toDateUpos: string;
  toggleUposContent: any = "(Ẩn chi tiết)";
  isShowUpos: boolean = true;

  //Table M - PAYCOLLECT
  dataTablePc: any[] = [];
  fromDatePc: string;
  toDatePc: string;
  togglePcContent: any = "(Ẩn chi tiết)";
  isShowPc: boolean = true;

  //Table N - VIETQR
  dataTableVietqr: any[] = [];
  fromDateVietqr: string;
  toDateVietqr: string;
  toggleVietqrContent: any = "(Ẩn chi tiết)";
  isShowVietqr: boolean = true;

  //Table O - DIRECT DEBIT
  dataTableDd: any[] = [];
  fromDateDd: string;
  toDateDd: string;
  toggleDdContent: any = "(Ẩn chi tiết)";
  isShowDd: boolean = true;

  //Table G
  dataTableBalance: any[] = [];
  fromDateBalance: string;
  toDateBalance: string;

  //Table
  dataTableMonthlyFee: any[] = [];

  //Table
  dataTableEstAdvance: any[] = [];

  //Table H
  dataTableAutoHold: any[] = [];
  isShowAutoHold: boolean = true;

  //Table L
  dataTableManualAdjust: any[] = [];

  descAdvanceMaxLength = 117;

  selectedTemplate: FormControl = new FormControl("", [Validators.required]);

  detailAdjustDescriptionMaxLength = 200;
  detailAdjustOrderRefMaxLength = 34;
  detailAdjustMerchantTxnRefMaxLength = 40;
  totalAdjustDescriptionMaxLength = 200;
  breakFdDescriptionMaxLength = 200;
  minDateManualAdjust: Date;
  maxDateManualAdjust: Date;

  descBreakFd: string;
  maxAdjBreakFd: number;
  addModalManualAdjustFormGroup: FormGroup = new FormGroup({
    detailAdjust: new FormGroup({
      totalTrans: new FormControl(0),
      merchantId: new FormControl("", [Validators.required]),
      orderRef: new FormControl("", [Validators.maxLength(this.detailAdjustOrderRefMaxLength)]),
      transId: new FormControl(""),
      merchantTxnRef: new FormControl("", [Validators.maxLength(this.detailAdjustMerchantTxnRefMaxLength)]),
      dateTransaction: new FormControl("", [Validators.required]),
      currency: new FormControl("", [Validators.required]),
      payChannel: new FormControl("", [Validators.required]),
      transactionType: new FormControl("", [Validators.required]),
      exchangeRate: new FormControl(0, [Validators.required]),
      currencyFee: new FormControl("", [Validators.required]),
      fixFee: new FormControl(0, [Validators.required]),
      percentFee: new FormControl(0, [Validators.required]),
      feeFailed: new FormControl(0, [Validators.required]),
      percentFeeIta: new FormControl(0, [Validators.required]),
      amount: new FormControl(0, [Validators.required]),
      state: new FormControl("", [Validators.required]),
      feeEcom: new FormControl(0, [Validators.required]),
      feeIta: new FormControl(0, [Validators.required]),
      description: new FormControl("", [Validators.maxLength(this.detailAdjustDescriptionMaxLength),
      Validators.pattern(/\s*.*\S/)]),
      extendedDescription: new FormControl("")
    }),
    totalAdjust: new FormGroup({
      totalTransaction: new FormControl(0),
      currency: new FormControl(""),
      amount: new FormControl(0),
      amountToVnd: new FormControl(0),
      totalFeeIncVat: new FormControl(0),
      totalAmountAdv: new FormControl(0), //Tong da tru phi
      totalAmountAdvCurrent: new FormControl(0),
      description: new FormControl("", [Validators.maxLength(this.totalAdjustDescriptionMaxLength),
      Validators.pattern(/\s*.*\S/)]),
      extendedDescription: new FormControl("")
    }, this.atLeastOneFormControlValid),
    breakFd: new FormGroup({
      totalTransaction: new FormControl(0),
      currency: new FormControl(""),
      amount: new FormControl(0),
      amountToVnd: new FormControl(0),
      totalFeeIncVat: new FormControl(0),
      totalAmountAdv: new FormControl(0), //Tong da tru phi
      totalAmountAdvCurrent: new FormControl(0),
      description: new FormControl("", [Validators.maxLength(this.breakFdDescriptionMaxLength),
      Validators.pattern(/\s*.*\S/)]),
      extendedDescription: new FormControl("")
    }, this.atLeastOneFormControlValid)
  });

  isShowFormControl = {
    detailAdjust: {
      totalTrans: false,
      merchantId: false,
      orderRef: false,
      transId: false,
      merchantTxnRef: false,
      dateTransaction: false,
      currency: false,
      payChannel: false,
      transactionType: false,
      exchangeRate: false,
      currencyFee: false,
      fixFee: false,
      percentFee: false,
      feeFailed: false,
      percentFeeIta: false,
      feeEcom: false,
      feeIta: false,
      amount: false,
      state: false,
      description: false,
      extendedDescription: false
    },
    totalAdjust: {
      currency: true,
      amount: true,
      amountToVnd: true,
      totalFeeIncVat: true,
      totalAmountAdv: true,
      totalAmountAdvCurrent: true,
      description: true,
      extendedDescription: true
    },
    breakFd: {
      currency: true,
      amount: true,
      amountToVnd: true,
      totalFeeIncVat: true,
      totalAmountAdv: true,
      totalAmountAdvCurrent: true,
      description: true,
      extendedDescription: true
    }
  }

  merchantIds: any;
  merchants: any;
  displayAddModalManualAdjust: boolean = false;
  addModalManualAdjust_Currencies: any[] = [
    { id: 1, name: "VND", value: "VND" },
    { id: 2, name: "USD", value: "USD" }
  ];
  addModalManualAdjust_MerchantIds: any[] = [];
  addModalManualAdjust_PayChannels: any[] = [
    { id: 1, name: "QT", value: "QT" },
    { id: 2, name: "ND", value: "ND" },
    { id: 3, name: "Direct Debit", value: "DD" },
    { id: 4, name: "QR", value: "QR" },
    { id: 5, name: "SMS", value: "SMS" },
    { id: 6, name: "BILLING", value: "BL" },
    { id: 7, name: "BNPL", value: "BNPL" },
    { id: 8, name: "UPOS", value: "UPOS" },
    { id: 9, name: "PayCollect", value: "PC" },
    { id: 10, name: "VietQR", value: "VIETQR" }
  ];
  addModalManualAdjust_Templates: any[] = [
    { id: 1, name: "Tạo giao dịch điều chỉnh", value: "detailAdjust" },
    { id: 2, name: "Điều chỉnh tổng cuối", value: "totalAdjust" },
    { id: 3, name: "Break FD", value: "breakFd" }
  ];

  addModalManualAdjust_TransactionTypes: any[] = [
    { id: 1, name: "PURCHASE", value: "PURCHASE" },
    { id: 2, name: "REFUND", value: "REFUND" },
    { id: 3, name: "FEE", value: "FEE" },
    { id: 4, name: "CAPTURE", value: "CAPTURE" },
    { id: 5, name: "REFUND CAPTURE", value: "REFUND_CAPTURE" },
    { id: 6, name: "VOID CAPTURE", value: "VOID_CAPTURE" },
    { id: 7, name: "VOID REFUND CAPTURE", value: "VOID_REFUND_CAPTURE" },
    { id: 8, name: "VOID PURCHASE", value: "VOID_PURCHASE" }
  ];

  addModalManualAdjust_CurrenciesFee: any[] = [
    { id: 1, name: "VND", value: "VND" },
    { id: 2, name: "USD", value: "USD" }
  ];
  addModalManualAdjust_States: any[] = [
    { id: 1, name: "SUCCESS", value: "SUCCESS" },
    { id: 2, name: "FAILED", value: "FAILED" }
  ];
  addModalManualAdjust_Detail_ExtendedDescription: any[] = [
    { id: 1, name: "ADJ-01: Installment Fee adjustment​", value: "ADJ-01: Installment Fee adjustment​" },
    { id: 2, name: "ADJ-02: Commission Fee adjustment​", value: "ADJ-02: Commission Fee adjustment​" },
    { id: 3, name: "ADJ-03: Net Settlement Amount adjustment​​", value: "ADJ-03: Net Settlement Amount adjustment​​" },
    { id: 4, name: "ADJ-04: Arbitration Fee​", value: "ADJ-04: Arbitration Fee​" }
  ];
  addModalManualAdjust_Total_ExtendedDescription: any[] = [
    { id: 1, name: "ADJ-03: Net Settlement Amount adjustment​​", value: "ADJ-03: Net Settlement Amount adjustment​​" },
    { id: 2, name: "ADJ-04: Arbitration Fee​", value: "ADJ-04: Arbitration Fee​" }
  ];

  //Table TW
  dataTableLastTotal: any[] = [];

  hasAdvancePayout: boolean = false;

  constructor(private paymentAdvanceService: PaymentAdvanceService,
    private router: Router,
    private activatedRouter: ActivatedRoute,
    private global: Globals,
    private toastr: ToastrService,
    private location: Location,
    private datePipe: DatePipe,
    private confirmationService: ConfirmationService) { }

  ngOnInit(): void {
    console.log("--PRE ADVANCE--");
    this.subscription = this.activatedRouter.params.subscribe(params => {
      this.advanceId = params.advanceId;
      if (this.advanceId) {
        console.log("Advance ID | ", this.advanceId);
        this.getPreAdvance(this.advanceId);
      }
    });
    this.initTableButtonAction();
    //Handle formControl onChange
    this.onChangeManualAdjustControl();
    //Init value formControl
    this.onInitManualAdjustControl();
  }

  onInitManualAdjustControl() {
    /////DETAIL////
    //1.detail currency
    (this.addModalManualAdjustFormGroup.get("detailAdjust.currency") as FormControl).setValue("VND");
    //2.detail currency fee
    (this.addModalManualAdjustFormGroup.get("detailAdjust.currencyFee") as FormControl).setValue("VND");
    //4.transaction type
    (this.addModalManualAdjustFormGroup.get("detailAdjust.transactionType") as FormControl).setValue("PURCHASE");
    //5.state
    (this.addModalManualAdjustFormGroup.get("detailAdjust.state") as FormControl).setValue("SUCCESS");
    //6. date transaction
    (this.addModalManualAdjustFormGroup.get('detailAdjust.dateTransaction') as FormControl).setValue(new Date());

    /////TOTAL/////
    //3.total currency
    (this.addModalManualAdjustFormGroup.get("totalAdjust.currency") as FormControl).setValue("VND");
  }

  checkSelectedTemplate($event) {
    this.descBreakFd = undefined;
    this.resetAddModalManualAdjust();
    if ($event.value == 'breakFd') {
      this.selectedTemplate.setValue(undefined);
      var params = new HttpParams()
        .set('id', this.advanceId)
      this.paymentAdvanceService.getBalanceFdByAdvID(params).subscribe(response => {
        if (!response.balance) {
          this.descBreakFd = "Không tồn tại khoản khoanh giữ";
          this.toastr.error(this.descBreakFd);
        } else {
          if (response.balance && response.balance == 0) {
            this.descBreakFd = "Khoản giải khoanh = 0";
            this.toastr.error(this.descBreakFd);
          } else if (response.transactionId && response.transactionId > 0) {
            this.descBreakFd = "Đã tồn tại 1 bản ghi điều chỉnh";
            this.toastr.error(this.descBreakFd);
          } else {
            this.descBreakFd = 'Tổng tiền tạm ứng < ' + this.formatCurrency(response.balance);
            this.maxAdjBreakFd = response.balance;
            this.selectedTemplate.setValue('breakFd');
          }
        }
      });
    }
  }

  onChangeManualAdjustControl() {
    //1. detail currency
    (this.addModalManualAdjustFormGroup.get("detailAdjust.currency") as FormControl).valueChanges.subscribe(value => {
      if (value == "VND") {
        (this.addModalManualAdjustFormGroup.get("detailAdjust.exchangeRate") as FormControl).reset({ value: 1, disabled: true });
      } else {
        (this.addModalManualAdjustFormGroup.get("detailAdjust.exchangeRate") as FormControl).reset({ value: 0, disabled: false });
      }
    });

    //2. detail service (QT,ND,QR,...)
    (this.addModalManualAdjustFormGroup.get("detailAdjust.payChannel") as FormControl).valueChanges.subscribe(value => {
      // process date transaction
      switch (value) {
        case "QT": {
          this.minDateManualAdjust = this.fromDateInternational ? new Date(this.fromDateInternational) : null;
          this.maxDateManualAdjust = this.toDateInternational ? new Date(this.toDateInternational) : null;
          break;
        }
        case "ND": {
          this.minDateManualAdjust = this.fromDateDomestic ? new Date(this.fromDateDomestic) : null;
          this.maxDateManualAdjust = this.toDateDomestic ? new Date(this.toDateDomestic) : null;
          break;
        }
        case "QR": {
          this.minDateManualAdjust = this.fromDateQr ? new Date(this.fromDateQr) : null;
          this.maxDateManualAdjust = this.toDateQr ? new Date(this.toDateQr) : null;
          break;
        }
        case "SMS": {
          this.minDateManualAdjust = this.fromDateSms ? new Date(this.fromDateSms) : null;
          this.maxDateManualAdjust = this.toDateSms ? new Date(this.toDateSms) : null;
          break;
        }
        case "BL": {
          this.minDateManualAdjust = this.fromDateBiling ? new Date(this.fromDateBiling) : null;
          this.maxDateManualAdjust = this.toDateBiling ? new Date(this.toDateBiling) : null;
          break;
        }
        case "BNPL": {
          this.minDateManualAdjust = this.fromDateBnpl ? new Date(this.fromDateBnpl) : null;
          this.maxDateManualAdjust = this.toDateBnpl ? new Date(this.toDateBnpl) : null;
          break;
        }
        case "UPOS": {
          this.minDateManualAdjust = this.fromDateUpos ? new Date(this.fromDateUpos) : null;
          this.maxDateManualAdjust = this.toDateUpos ? new Date(this.toDateUpos) : null;
          break;
        }
        case "PAYCOLLECT": {
          this.minDateManualAdjust = this.fromDatePc ? new Date(this.fromDatePc) : null;
          this.maxDateManualAdjust = this.toDatePc ? new Date(this.toDatePc) : null;
          break;
        }
        case "VIETQR": {
          this.minDateManualAdjust = this.fromDateVietqr ? new Date(this.fromDateVietqr) : null;
          this.maxDateManualAdjust = this.toDateVietqr ? new Date(this.toDateVietqr) : null;
          break;
        }
        case "DD": {
          this.minDateManualAdjust = this.fromDateDd ? new Date(this.fromDateDd) : null;
          this.maxDateManualAdjust = this.toDateDd ? new Date(this.toDateDd) : null;
          break;
        }
        default: {
        }
      }
      this.addModalManualAdjustFormGroup.get("detailAdjust.dateTransaction").setValue(this.maxDateManualAdjust ? new Date(this.maxDateManualAdjust.getTime() - 1000) : new Date());

      // process merchant id
      this.addModalManualAdjust_MerchantIds = this.merchants.filter(item => item.payChannel == value);
      this.addModalManualAdjustFormGroup.get("detailAdjust.merchantId").setValue("");
    });

    //3. merchant id
    (this.addModalManualAdjustFormGroup.get("detailAdjust.merchantId") as FormControl).valueChanges.subscribe(value => {
      this.addModalManualAdjustFormGroup.get("detailAdjust.currency").setValue(value.currency);
    });

    //4. detail state
    (this.addModalManualAdjustFormGroup.get("detailAdjust.state") as FormControl).valueChanges.subscribe(value => {
      switch (value) {
        case "SUCCESS": {
          this.isShowFormControl.detailAdjust.feeFailed = false;
          this.isShowFormControl.detailAdjust.currency = true;
          this.isShowFormControl.detailAdjust.exchangeRate = true;
          this.isShowFormControl.detailAdjust.amount = true;
          this.isShowFormControl.detailAdjust.currencyFee = true;
          this.isShowFormControl.detailAdjust.fixFee = true;
          this.isShowFormControl.detailAdjust.percentFeeIta = true;
          this.isShowFormControl.detailAdjust.percentFee = true;
          break;
        }
        case "FAILED": {
          this.isShowFormControl.detailAdjust.feeFailed = true;
          this.isShowFormControl.detailAdjust.currency = true;
          this.isShowFormControl.detailAdjust.exchangeRate = true;
          this.isShowFormControl.detailAdjust.amount = true;
          this.isShowFormControl.detailAdjust.currencyFee = false;
          this.isShowFormControl.detailAdjust.fixFee = false;
          this.isShowFormControl.detailAdjust.percentFeeIta = false;
          this.isShowFormControl.detailAdjust.percentFee = false;
          break;
        }
      }
    });

    //5. Transaction type
    (this.addModalManualAdjustFormGroup.get("detailAdjust.transactionType") as FormControl).valueChanges.subscribe(value => {
      switch (value) {
        case "FEE": {
          this.isShowFormControl.detailAdjust.feeEcom = true;
          this.isShowFormControl.detailAdjust.feeIta = true;
          this.isShowFormControl.detailAdjust.extendedDescription = true;
          this.isShowFormControl.detailAdjust.currency = false;
          this.isShowFormControl.detailAdjust.exchangeRate = false;
          this.isShowFormControl.detailAdjust.amount = false;
          this.isShowFormControl.detailAdjust.currencyFee = false;
          this.isShowFormControl.detailAdjust.fixFee = false;
          this.isShowFormControl.detailAdjust.percentFeeIta = false;
          this.isShowFormControl.detailAdjust.percentFee = false;
          break;
        }
        default: {
          this.isShowFormControl.detailAdjust.feeEcom = false;
          this.isShowFormControl.detailAdjust.feeIta = false;
          this.isShowFormControl.detailAdjust.currency = true;
          this.isShowFormControl.detailAdjust.exchangeRate = true;
          this.isShowFormControl.detailAdjust.amount = true;
          this.isShowFormControl.detailAdjust.currencyFee = true;
          this.isShowFormControl.detailAdjust.fixFee = true;
          this.isShowFormControl.detailAdjust.percentFeeIta = true;
          this.isShowFormControl.detailAdjust.percentFee = true;
          this.isShowFormControl.detailAdjust.extendedDescription = false;
        }
      }
    });

    //6. Extended description
    (this.addModalManualAdjustFormGroup.get("detailAdjust.extendedDescription") as FormControl).valueChanges.subscribe(value => {
      (this.addModalManualAdjustFormGroup.get("detailAdjust.description") as FormControl).setValue(value);
    });
    (this.addModalManualAdjustFormGroup.get("totalAdjust.extendedDescription") as FormControl).valueChanges.subscribe(value => {
      (this.addModalManualAdjustFormGroup.get("totalAdjust.description") as FormControl).setValue(value);
    });
  }

  atLeastOneFormControlValid(formGroup: FormGroup): ValidationErrors {
    const minRequired = 1;
    let checked = 0;
    Object.keys(formGroup.controls).forEach(key => {
      if (key != "currency") {
        const control = formGroup.controls[key];
        if (control.value) {
          checked++;
        }
      }
    });
    if (checked < minRequired) {
      return {
        'invalid': true
      };
    }
    return {
      'invalid': false
    };
  }

  initTableButtonAction() {
    console.log("--INIT TABLE BUTTON--");
    this.internationalExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternational();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("full", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("qt", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("qr/nd", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("pc", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("dd", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("bnpl", this.fromDateInternational, this.toDateInternational)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelInternationalDetail("pc", this.fromDateInternational, this.toDateInternational)
        }
      }
    ];

    this.domesticExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomestic();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("full", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("qt", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("qr/nd", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("dd", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("pc", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("bnpl", this.fromDateDomestic, this.toDateDomestic)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDomesticDetail("pc", this.fromDateDomestic, this.toDateDomestic)
        }
      }
    ]

    this.qrExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQr();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("full", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("qt", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("qr/nd", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("pc", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("dd", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("bnpl", this.fromDateQr, this.toDateQr)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelQrDetail("pc", this.fromDateQr, this.toDateQr)
        }
      }
    ]

    this.smsExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSms();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("full", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("qt", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("qr/nd", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("pc", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("dd", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("bnpl", this.fromDateSms, this.toDateSms)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelSmsDetail("pc", this.fromDateSms, this.toDateSms)
        }
      }
    ]

    this.bilingExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBiling();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("full", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("qt", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("qr/nd", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("pc", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("dd", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("bnpl", this.fromDateBiling, this.toDateBiling)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBilingDetail("pc", this.fromDateBiling, this.toDateBiling)
        }
      }
    ]

    this.bnplExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnpl();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("full", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("qt", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("qr/nd", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("pc", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("dd", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("bnpl", this.fromDateBnpl, this.toDateBnpl)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelBnplDetail("pc", this.fromDateBnpl, this.toDateBnpl)
        }
      }
    ]

    this.uposExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUpos();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("full", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("qt", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("qr/nd", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("pc", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("dd", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("bnpl", this.fromDateUpos, this.toDateUpos)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelUposDetail("pc", this.fromDateUpos, this.toDateUpos)
        }
      }
    ];

    this.pcExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPc();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("full", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("qt", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("qr/nd", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("pc", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("dd", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("bnpl", this.fromDatePc, this.toDatePc)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelPcDetail("pc", this.fromDatePc, this.toDatePc)
        }
      }
    ];

    this.vietqrExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqr();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("full", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("qt", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("qr/nd", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("pc", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("dd", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("bnpl", this.fromDateVietqr, this.toDateVietqr)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelVietqrDetail("pc", this.fromDateVietqr, this.toDateVietqr)
        }
      }
    ];

    this.ddExportOptions = [
      {
        label: "Tải khối", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDd();
        }
      },
      {
        label: "Tải chi tiết đầy đủ", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("full", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết QT", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("qt", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết QR,ND", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("qr/nd", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết VIETQR", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("pc", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết Direct Debit", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("dd", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết BNPL", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("bnpl", this.fromDateDd, this.toDateDd)
        }
      },
      {
        label: "Tải chi tiết PC", icon: "pi pi-file-excel", command: () => {
          this.exportExcelDdDetail("pc", this.fromDateDd, this.toDateDd)
        }
      }
    ];
  }

  bindDataPayout({payout, total_advance}: {payout: any[], total_advance: number}) {
    if (!payout || !payout.length) {
      return;
    }
    this.dataHeader.payout = payout;
    this.dataHeader.totalBankingAmount = total_advance - payout.filter(x => x.payout_amount).reduce((total, current) => total + current.payout_amount, 0);
  }

  getPreAdvance(advanceId: string) {
    this.paymentAdvanceService.getPreAdvance(advanceId).subscribe(data => {
      console.log("getPreAdvance RESPONSE | ", data);
      this.dataPreAdvance = data;
      this.bindDataHeader(data);
      if (data.merchantIds) {
        this.merchantIds = data.merchantIds;
      }
      if (data.merchants) {
        this.merchants = data.merchants;
        this.bindManualAdjustPayChannel(this.merchants);
      }
      //international
      this.bindDataTableInternational(data.international);
      this.bindDateInternational(data.international);
      //domestic
      this.bindDataTableDomestic(data.domestic);
      this.bindDateDomestic(data.domestic);
      //qr
      this.bindDataTableQr(data.app);
      this.bindDateQr(data.app);
      //sms
      this.bindDataTableSms(data.sms);
      this.bindDateSms(data.sms);
      //biling
      this.bindDataTableBiling(data.biling);
      this.bindDateBiling(data.biling);
      //bnpl
      this.bindDataTableBnpl(data.bnpl);
      this.bindDateBnpl(data.bnpl);
      //upos
      this.bindDataTableUpos(data.upos);
      this.bindDateUpos(data.upos);
      //paycollect
      this.bindDataTablePc(data.paycollect);
      this.bindDatePc(data.paycollect);
      //vietqr
      this.bindDataTableVietqr(data.vietqr);
      this.bindDateVietqr(data.vietqr);
      //dd
      this.bindDataTableDd(data.dd);
      this.bindDateDd(data.dd);
      //balance
      this.bindDataTableBalance(data.balance);
      this.bindDateBalance(data.balance);
      //monthlyFee
      this.bindDataTableMonthlyFee(data.monthlyFee);
      //estAdvance
      this.bindDataTableEstAdvance(data.estAdvance);
      //autoHold
      this.bindDataTableAutoHold(data.autoHold);
      //manualAdjust
      this.bindDataTableManualAdjust(data.manualAdjust);
      //lastTotal
      this.bindDataTableLastTotal(data.lastTotal);
      // advance split payout
      let listPayout: any[] = data.lastTotal.payout;
      if (listPayout?.length) {
        this.hasAdvancePayout = listPayout.some(x => x.active == 1);
      }
      if (this.hasAdvancePayout) {
        this.bindDataPayout(data.lastTotal);
      }
    });
  }

  bindManualAdjustPayChannel(data) {
    let activatedPayChannels = data.map(item => item.payChannel).filter((value, index, self) => {
      return self.indexOf(value) === index;
    });
    // console.log("acv", activatedPayChannels);
    this.addModalManualAdjust_PayChannels = this.addModalManualAdjust_PayChannels.filter(function (e) {
      return activatedPayChannels.includes(e.value);
    });
  }

  /*1. START: HEADER */
  bindDataHeader(data) {
    const dataKeys = ["merchantIds", "partnerName", "contractCode", "taxCode", "advanceId",
      "createDate", "advType", "advanceConfigCode", "status", "isBigMerchant",
      "transferDate", "transferBank", "accountType", "receiveBank",
      "accountNumber", "twLastTotal", "transferContent", "from", "to"];
    for (let key of dataKeys) {
      if (key == "accountType")
        this.dataHeader[key] = this.accountTypePipe(data[key]);
      else
        this.dataHeader[key] = data[key];
    }
    //adjust transferContent
    this.transferContent = this.dataHeader["transferContent"] ? this.dataHeader["transferContent"] : "";
    console.log("dataHeader | ", this.dataHeader);
  }

  accountTypePipe(value: string) {
    return [
      { name: "Công ty", value: "COMPANY" },
      { name: "Cá nhân vô thời hạn", value: "PERSONAL_NO_LIMIT" },
      { name: "Cá nhân", value: "PERSONAL" },
      { name: "Payout", value: "PAYOUT" }
    ].find(item => value ? item.value == value : { name: "" })?.name.toLocaleUpperCase();
  }

  updateTransferContent() {
    console.log("--UPDATE TRANSFER CONTENT--");
    if (this.transferContent.length > 117 || !this.transferContent || this.transferContent.trim().length == 0 || !this.flagUpdateContent) {
      return;
    }
    const params = {
      id: this.dataPreAdvance.advanceId,
      desc: this.transferContent.trim()
    };
    console.log("updateTransferContent params | ", params)

    this.flagUpdateContent = false;
    this.paymentAdvanceService.updateTransferContent(params).subscribe(data => {
      console.log("updateTransferContent response |", data);
      if (!this.isValidDataObject(data)) {
        this.toastr.error("Fail!", "Update Transfer Content");
      }
      if (data.code == "200") {
        this.toastr.success("Success!", "Update Transfer Content");
      }
      this.flagUpdateContent = true;
      this.disableUpdateTransferContent();
    });
  }

  enableUpdateTransferContent() {
    this.isDisableTransferContent = false;
  }

  disableUpdateTransferContent() {
    this.isDisableTransferContent = true;
  }

  cancelUpdateTransferContent() {
    this.transferContent = this.dataHeader["transferContent"];
    this.isDisableTransferContent = true;
  }

  /* END: HEADER */

  viewDetail(payChannel: string, level: number, transType: string, fromDate: string, toDate: string) {
    console.log(`--VIEW DETAIL ${payChannel} LEVEL ${level}--`);
    const params = {
      id: this.advanceId,
      configId: this.dataPreAdvance.advanceConfigCode,
      payChannel: payChannel,
      transType: transType,
      fromDate: fromDate,
      toDate: toDate
    };
    console.log("viewDetail params |", params);
    this.router.navigate(["/payment2-advance/advance-manager/detail"], { queryParams: params });
  }

  viewPreAdvance(advanceId: string) {
    console.log("viewPreAdvance params |", advanceId);
    this.router.navigate(["/payment2-advance/pre-advance/" + advanceId]);
  }

  /*2. START: INTERNATIONAL */
  bindDataTableInternational(data) {
    //clear dataTableInternational
    this.dataTableInternational = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableInternational.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableInternational.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowInternational;
        dataTableLevel3["parent"] = item.index; //set parent
        this.dataTableInternational.push(dataTableLevel3);
      });
    }
    console.log("dataTableInternational | ", this.dataTableInternational);
  }

  bindDateInternational(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateInternational = dataLevel1.from;
    if (dataLevel1.to) this.toDateInternational = dataLevel1.to;
    console.log("International |", "fromDate", this.fromDateInternational, "toDate", this.toDateInternational);
  }

  toggleInternational() {
    this.isShowInternational = !this.isShowInternational;
    if (this.isShowInternational) {
      this.toggleInternationalContent = "(Ẩn chi tiết)";
    } else {
      this.toggleInternationalContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableInternational.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowInternational;
      }
    });
  }

  exportExcelInternational() {
    console.log("--EXPORT EXCEL INTERNATIONAL--");
    const params = {
      advanceId: this.advanceId,
      type: "QT"
    };
    console.log("exportExcelInternational |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelInternationalDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL INTERNATIONAL DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "QT",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelInternationalDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: INTERNATIONAL - TABLE A*/

  /*3. START: DOMESTIC */
  bindDataTableDomestic(data) {
    //clear dataTableDomestic
    this.dataTableDomestic = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableDomestic.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableDomestic.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowInternational;
        dataTableLevel3["parent"] = item.index;
        this.dataTableDomestic.push(dataTableLevel3);
      });
    }
    console.log("dataTableDomestic | ", this.dataTableDomestic);
  }

  bindDateDomestic(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateDomestic = dataLevel1.from;
    if (dataLevel1.to) this.toDateDomestic = dataLevel1.to;
    console.log("Domestic |", "fromDate", this.fromDateDomestic, "toDate", this.toDateDomestic);
  }

  toggleDomestic() {
    this.isShowDomestic = !this.isShowDomestic;
    if (this.isShowDomestic) {
      this.toggleDomesticContent = "(Ẩn chi tiết)";
    } else {
      this.toggleDomesticContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableDomestic.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowDomestic;
      }
    });
  }

  exportExcelDomestic() {
    console.log("--EXPORT EXCEL DOMESTIC--");
    const params = {
      advanceId: this.advanceId,
      type: "ND"
    };
    console.log("exportExcelDomestic |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelDomesticDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL DOMESTIC DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "ND",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelDomesticDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: DOMESTIC - TABLE B*/

  /*4. START: QR - TABLE C*/
  bindDataTableQr(data) {
    //clear dataTableQr
    this.dataTableQr = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableQr.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableQr.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowInternational;
        dataTableLevel3["parent"] = item.index;
        this.dataTableQr.push(dataTableLevel3);
      });
    }
    console.log("dataTableQr | ", this.dataTableQr);
  }

  bindDateQr(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateQr = dataLevel1.from;
    if (dataLevel1.to) this.toDateQr = dataLevel1.to;
    console.log("QR |", "fromDate", this.fromDateQr, "toDate", this.toDateQr);
  }

  toggleQr() {
    this.isShowQr = !this.isShowQr;
    if (this.isShowQr) {
      this.toggleQrContent = "(Ẩn chi tiết)";
    } else {
      this.toggleQrContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableQr.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowQr;
      }
    });
  }

  exportExcelQr() {
    console.log("--EXPORT EXCEL QR--");
    const params = {
      advanceId: this.advanceId,
      type: "QR"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelQrDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL QR DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "QR",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelQrDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: APP */

  /*5. START: SMS - TABLE D*/
  bindDataTableSms(data) {
    //clear dataTableSms
    this.dataTableSms = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableSms.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowSms;
      this.dataTableSms.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowSms;
        dataTableLevel3["parent"] = item.index;
        this.dataTableSms.push(dataTableLevel3);
      });
    }
    console.log("dataTableSms | ", this.dataTableSms);
  }

  bindDateSms(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateSms = dataLevel1.from;
    if (dataLevel1.to) this.toDateSms = dataLevel1.to;
    console.log("Sms |", "fromDate", this.fromDateSms, "toDate", this.toDateSms);
  }

  toggleSms() {
    this.isShowSms = !this.isShowSms;
    if (this.isShowSms) {
      this.toggleSmsContent = "(Ẩn chi tiết)";
    } else {
      this.toggleSmsContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableSms.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowSms;
      }
    });
  }

  viewDetailSms() {
    console.log("--VIEW DETAIL SMS--");
  }

  exportExcelSms() {
    console.log("--EXPORT EXCEL SMS--");
    const params = {
      advanceId: this.advanceId,
      type: "SMS"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelSmsDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL SMS DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "SMS",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelSmsDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: SMS */

  /*6. START: BILING - TABLE J*/
  bindDataTableBiling(data) {
    //clear dataTableBiling
    this.dataTableBiling = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableBiling.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowBiling;
      this.dataTableBiling.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowBiling;
        dataTableLevel3["parent"] = item.index;
        this.dataTableBiling.push(dataTableLevel3);
      });
    }
    console.log("dataTableBiling | ", this.dataTableBiling);
  }

  bindDateBiling(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateBiling = dataLevel1.from;
    if (dataLevel1.to) this.toDateBiling = dataLevel1.to;
    console.log("Biling |", "fromDate", this.fromDateBiling, "toDate", this.toDateBiling);
  }

  toggleBiling() {
    this.isShowBiling = !this.isShowBiling;
    if (this.isShowBiling) {
      this.toggleBilingContent = "(Ẩn chi tiết)";
    } else {
      this.toggleBilingContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableBiling.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowBiling;
      }
    });
  }

  viewDetailBiling() {
    console.log("--VIEW DETAIL BILING--");
  }

  exportExcelBiling() {
    console.log("--EXPORT EXCEL BILING--");
    const params = {
      advanceId: this.advanceId,
      type: "BL"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelBilingDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL BILING DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "BL",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelBilingDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: BILING */

  /*7. START: BNPL - TABLE K*/
  bindDataTableBnpl(data) {
    //clear dataTableBnpl
    this.dataTableBnpl = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableBnpl.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowBnpl;
      this.dataTableBnpl.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowBnpl;
        dataTableLevel3["parent"] = item.index;
        this.dataTableBnpl.push(dataTableLevel3);
      });
    }
    console.log("dataTableBnpl | ", this.dataTableBnpl);
  }

  bindDateBnpl(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateBnpl = dataLevel1.from;
    if (dataLevel1.to) this.toDateBnpl = dataLevel1.to;
    console.log("Bnpl |", "fromDate", this.fromDateBnpl, "toDate", this.toDateBnpl);
  }

  toggleBnpl() {
    this.isShowBnpl = !this.isShowBnpl;
    if (this.isShowBnpl) {
      this.toggleBnplContent = "(Ẩn chi tiết)";
    } else {
      this.toggleBnplContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableBnpl.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowBnpl;
      }
    });
  }

  viewDetailBnpl() {
    console.log("--VIEW DETAIL Bnpl--");
  }

  exportExcelBnpl() {
    console.log("--EXPORT EXCEL Bnpl--");
    const params = {
      advanceId: this.advanceId,
      type: "BNPL"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelBnplDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL BNPL DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "BNPL",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelBnplDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: BNPL */

  /*8. START: UPOS - TABLE L*/
  bindDataTableUpos(data) {
    //clear dataTableUpos
    this.dataTableUpos = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableUpos.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowUpos;
      this.dataTableUpos.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowUpos;
        dataTableLevel3["parent"] = item.index;
        this.dataTableUpos.push(dataTableLevel3);
      });
    }
    console.log("dataTableUpos | ", this.dataTableUpos);
  }

  bindDateUpos(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateUpos = dataLevel1.from;
    if (dataLevel1.to) this.toDateUpos = dataLevel1.to;
    console.log("Upos |", "fromDate", this.fromDateUpos, "toDate", this.toDateUpos);
  }

  toggleUpos() {
    this.isShowUpos = !this.isShowUpos;
    if (this.isShowUpos) {
      this.toggleUposContent = "(Ẩn chi tiết)";
    } else {
      this.toggleUposContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableUpos.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowUpos;
      }
    });
  }

  viewDetailUpos() {
    console.log("--VIEW DETAIL UPOS--");
  }

  exportExcelUpos() {
    console.log("--EXPORT EXCEL UPOS--");
    const params = {
      advanceId: this.advanceId,
      type: "UPOS"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelUposDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL UPOS DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "UPOS",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelUposDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: UPOS */

  /*START: PAYCOLLECT - TABLE M*/
  bindDataTablePc(data) {
    //clear dataTablePc
    this.dataTablePc = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTablePc.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowPc;
      this.dataTablePc.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowPc;
        dataTableLevel3["parent"] = item.index;
        this.dataTablePc.push(dataTableLevel3);
      });
    }
    console.log("dataTablePc | ", this.dataTablePc);
  }

  bindDatePc(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDatePc = dataLevel1.from;
    if (dataLevel1.to) this.toDatePc = dataLevel1.to;
    console.log("Pc |", "fromDate", this.fromDatePc, "toDate", this.toDatePc);
  }

  togglePc() {
    this.isShowPc = !this.isShowPc;
    if (this.isShowPc) {
      this.togglePcContent = "(Ẩn chi tiết)";
    } else {
      this.togglePcContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTablePc.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowPc;
      }
    });
  }

  viewDetailPc() {
    console.log("--VIEW DETAIL PAYCOLLECT--");
  }

  exportExcelPc() {
    console.log("--EXPORT EXCEL PAYCOLLECT--");
    const params = {
      advanceId: this.advanceId,
      type: "PC"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelPcDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL PAYCOLLECT DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "PC",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelPcDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: PAYCOLLECT */

  /*START: VIETQR - TABLE N*/
  bindDataTableVietqr(data) {
    //clear dataTableVietqr
    this.dataTableVietqr = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableVietqr.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowVietqr;
      this.dataTableVietqr.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowVietqr;
        dataTableLevel3["parent"] = item.index;
        this.dataTableVietqr.push(dataTableLevel3);
      });
    }
    console.log("dataTableVietqr | ", this.dataTableVietqr);
  }

  bindDateVietqr(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateVietqr = dataLevel1.from;
    if (dataLevel1.to) this.toDateVietqr = dataLevel1.to;
    console.log("Vietqr |", "fromDate", this.fromDateVietqr, "toDate", this.toDateVietqr);
  }

  toggleVietqr() {
    this.isShowVietqr = !this.isShowVietqr;
    if (this.isShowVietqr) {
      this.toggleVietqrContent = "(Ẩn chi tiết)";
    } else {
      this.toggleVietqrContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableVietqr.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowVietqr;
      }
    });
  }

  viewDetailVietqr() {
    console.log("--VIEW DETAIL VIETQR--");
  }

  exportExcelVietqr() {
    console.log("--EXPORT EXCEL VIETQR--");
    const params = {
      advanceId: this.advanceId,
      type: "VIETQR"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelVietqrDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL VIETQR DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "VIETQR",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelVietqrDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: VIETQR */

  /*START: DIRECT DEBIT - TABLE O*/
  bindDataTableDd(data) {
    //clear dataTableDd
    this.dataTableDd = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableDd.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowDd;
      this.dataTableDd.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowDd;
        dataTableLevel3["parent"] = item.index;
        this.dataTableDd.push(dataTableLevel3);
      });
    }
    console.log("dataTableDd | ", this.dataTableDd);
  }

  bindDateDd(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateDd = dataLevel1.from;
    if (dataLevel1.to) this.toDateDd = dataLevel1.to;
    console.log("Dd |", "fromDate", this.fromDateDd, "toDate", this.toDateDd);
  }

  toggleDd() {
    this.isShowDd = !this.isShowDd;
    if (this.isShowDd) {
      this.toggleDdContent = "(Ẩn chi tiết)";
    } else {
      this.toggleDdContent = "(Hiện chi tiết)";
    }
    //update all isShow property that item level != 1
    this.dataTableDd.forEach(item => {
      if (item.level != 1) {
        item.isShow = this.isShowDd;
      }
    });
  }

  viewDetailDd() {
    console.log("--VIEW DETAIL DIRECT DEBIT--");
  }

  exportExcelDd() {
    console.log("--EXPORT EXCEL DIRECT DEBIT--");
    const params = {
      advanceId: this.advanceId,
      type: "DD"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  exportExcelDdDetail(template, fromDate, toDate) {
    console.log("--EXPORT EXCEL DIRECT DEBIT DETAIl--");
    const params = {
      advanceId: Number(this.advanceId),
      type: "DD",
      fromDate: fromDate,
      toDate: toDate,
      template: template
    };
    console.log("exportExcelDdDetail |", params);
    this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  /* END: DIRECT DEBIT */

  /*8. START: BALANCE - ĐƠN VỊ DƯ CÓ */
  bindDataTableBalance(data) {
    //clear dataTableBalance
    this.dataTableBalance = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id", "refund_hold_txn_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableBalance.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      dataTableLevel2["key"] = key;
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableBalance.push(dataTableLevel2);
    }
    console.log("dataTableBalance | ", this.dataTableBalance);
  }

  bindDateBalance(data) {
    if (!this.isValidDataObject(data)) return;
    const dataLevel1 = { ...data.summary };
    if (dataLevel1.from) this.fromDateBalance = dataLevel1.from;
    if (dataLevel1.to) this.toDateBalance = dataLevel1.to;
    console.log("Balance |", "fromDate", this.fromDateBalance, "toDate", this.toDateBalance);
  }

  isValidDeleteMerchantTopupRefund() {
    return this.global.isActive('delete_merchant_topup_refund_from_advance') && this.dataHeader.merchantIds && this.dataHeader.status == 'CREATED' && !this.dataHeader.isBigMerchant;
  }

  isValidDeleteRefundHold() {
    return this.global.isActive('delete_refund_hold_from_advance') && this.dataHeader.merchantIds && this.dataHeader.status == 'CREATED';
  }

  viewDetailBalance() {
    console.log("--VIEW DETAIL BALANCE--");
  }

  exportExcelBalance() {
    console.log("--EXPORT EXCEL BALANCE--");
    const params = {
      advanceId: this.advanceId,
      type: "BALANCE"
    };
    console.log("exportExcelApp |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  confirmDeleteMTURF() { //MTURF: Merchant topup refund
    this.confirmationService.confirm({
      header: "Xóa Topup khỏi bút toán",
      message: "Bạn chắc chắn muốn xóa giao dịch Topup ra khỏi bút toán hiện tại?",
      acceptLabel: "Xác nhận",
      rejectLabel: "Hủy",
      rejectButtonStyleClass: "p-button-danger",
      accept: () => {
        this.deleteMTURF(this.advanceId);
      }
    });
  }

  deleteMTURF(advanceId) {
    this.paymentAdvanceService.deleteMturfFromAdvance(advanceId).subscribe(data => {
      console.log("RESPONSE deleteTopup:", data);
      const toastrTitle = "Xóa Topup khỏi bút toán";
      if (data) {
        if (data._children.response_code._value === "00") {
          this.toastr.success(data._children.message_vi._value, toastrTitle);
          window.location.reload();
        } else {
          this.toastr.error(data._children.message_vi._value, toastrTitle);
        }
      } else {
        this.toastr.error("Có lỗi xảy ra", toastrTitle);
      }
    });
  }

  confirmDeleteRefundHold(refundHoldId: number) {
    console.log("refund hold txn id:", refundHoldId);
    this.confirmationService.confirm({
      header: "Xóa khoản refund hold khỏi bút toán",
      message: "Bạn chắc chắn muốn xóa khoản refund hold ra khỏi bút toán hiện tại?",
      acceptLabel: "Xác nhận",
      rejectLabel: "Hủy",
      rejectButtonStyleClass: "p-button-danger",
      accept: () => {
        this.deleteRefundHold(this.advanceId, refundHoldId);
      }
    });
  }

  deleteRefundHold(advanceId, refundHoldId: number) {
    const params = {
      advanceId: advanceId,
      refundHoldId: refundHoldId
    };
    this.paymentAdvanceService.deleteRefundHoldFromAdvance(params).subscribe(data => {
      console.log("RESPONSE DELETE REFUND HOLD:", data);
      const toastrTitle = "Xóa refund hold khỏi bút toán";
      if (data) {
        const responseCode = data.response_code;
        const message = data.message_vi;
        if (responseCode === "00") {
          this.toastr.success(message, toastrTitle);
          window.location.reload();
        } else {
          this.toastr.error(message, toastrTitle);
        }
      } else {
        this.toastr.error("Có lỗi xảy ra", toastrTitle);
      }
    });
  }

  // exportExcelBalanceDetail(fromDate, toDate) {
  //   console.log("--EXPORT EXCEL BALANCE DETAIl--");
  //   const params = {
  //     advanceId: Number(this.advanceId),
  //     type: "BALANCE",
  //     fromDate: fromDate,
  //     toDate: toDate
  //   };
  //   console.log("exportExcelBalanceDetail |", params);
  //   this.paymentAdvanceService.exportExecelPreAdvanceDetail(params).subscribe(data => {
  //     this.global.downloadEmit.emit(true);
  //   });
  // }
  /* END: BALANCE - ĐƠN VỊ DƯ CÓ *

  /*9 START: MONTHLY FEE - PHÍ DUY TRÌ THÁNG */
  bindDataTableMonthlyFee(data) {
    //clear dataTableMonthlyFee
    this.dataTableMonthlyFee = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    // let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableMonthlyFee.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableMonthlyFee.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      // const subItemList = item.list;
      // subItemList.sort((firstItem, secondItem) => { //sort by code
      //   return firstItem.code - secondItem.code;
      // });
      // subItemList.forEach((subItem, index) => {
      //   dataTableLevel3 = {};
      //   for (let key2 of dataKeys) {
      //     dataTableLevel3[key2] = subItem[key2];
      //   }
      //   dataTableLevel3["level"] = 3;
      //   dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
      //   dataTableLevel3["isShow"] = this.isShowInternational;
      //   this.dataTableMonthlyFee.push(dataTableLevel3);
      // });
    }
    console.log("dataTableMonthlyFee | ", this.dataTableMonthlyFee);
  }
  /* END: MONTHLY FEE - PHÍ DUY TRÌ THÁNG */

  /*10. START: EST ADVANCE - TẠM ỨNG TẠM TÍNH */
  bindDataTableEstAdvance(data) {
    //clear dataTableEstAdvance
    this.dataTableEstAdvance = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableEstAdvance.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableEstAdvance.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      subItemList.sort((firstItem, secondItem) => { //sort by code
        return firstItem.code - secondItem.code;
      });
      subItemList.forEach((subItem, index) => {
        dataTableLevel3 = {};
        for (let key2 of dataKeys) {
          dataTableLevel3[key2] = subItem[key2];
        }
        dataTableLevel3["level"] = 3;
        dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
        dataTableLevel3["isShow"] = this.isShowInternational;
        this.dataTableEstAdvance.push(dataTableLevel3);
      });
    }
    console.log("dataTableEstAdvance | ", this.dataTableEstAdvance);
  }
  /* END: EST ADVANCE - TẠM ỨNG TẠM TÍNH */

  /*11. START: AUTO HOLD - KHOANH TỰ ĐỘNG */
  bindDataTableAutoHold(data) {
    //clear dataTableAutoHold
    this.dataTableAutoHold = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["id"] = dataLevel1["id"];
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableAutoHold.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowAutoHold;
      this.dataTableAutoHold.push(dataTableLevel2);

      //process dataLevel3 - sub a1,a2,a3
      const subItemList = item.list;
      if (subItemList) {
        subItemList.sort((firstItem, secondItem) => { //sort by code
          return firstItem.code - secondItem.code;
        });
        subItemList.forEach((subItem, index) => {
          dataTableLevel3 = {};
          for (let key2 of dataKeys) {
            dataTableLevel3[key2] = subItem[key2];
          }
          dataTableLevel3["level"] = 3;
          dataTableLevel3["backGroundColorClass"] = "backGroundColor_level-3-row";
          dataTableLevel3["isShow"] = this.isShowAutoHold;
          this.dataTableAutoHold.push(dataTableLevel3);
        });
      }
    }
    console.log("dataTableAutoHold | ", this.dataTableAutoHold);
  }
  /* END: AUTO HOLD - KHOANH TỰ ĐỘNG */

  /*12. START: MANUAL ADJUST - ĐIỀU CHỈNH THỦ CÔNG */
  bindDataTableManualAdjust(data) {
    //clear dataTableManualAdjust
    this.dataTableManualAdjust = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "advance_transaction_id", "state_process", "desc", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "view_to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    const dataLevel1 = { ...data.summary };
    const dataLevel2 = { ...data.group };

    let dataTableLevel1 = {};
    let dataTableLevel2 = {};
    let dataTableLevel3 = {};

    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = dataLevel1[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableManualAdjust.push(dataTableLevel1);

    //process dataLevel2 - a1,a2,a3
    for (let key in dataLevel2) {
      const item = dataLevel2[key];
      dataTableLevel2 = {};
      for (let key1 of dataKeys) {
        dataTableLevel2[key1] = item[key1];
      }
      dataTableLevel2["level"] = 2;
      dataTableLevel2["backGroundColorClass"] = "backGroundColor_level-2-row";
      dataTableLevel2["isShow"] = this.isShowInternational;
      this.dataTableManualAdjust.push(dataTableLevel2);
    }
    console.log("dataTableManualAdjust | ", this.dataTableManualAdjust);
  }

  isValidManualAdjust() {
    return this.global.isActive('manual_adjust_advance') && this.dataHeader.merchantIds && this.dataHeader.status == 'CREATED' && !this.dataHeader.isBigMerchant;
  }

  isValidDeleteManualTotalAdjust() {
    return this.global.isActive('update_advance_transaction_by_delete')
      && this.dataHeader.merchantIds && this.dataHeader.status == 'CREATED' && !this.dataHeader.isBigMerchant;
  }

  isWaitForDeleteApprove(stateProcess: string) {
    return stateProcess === 'WAIT_FOR_DELETE_APPROVE'
  }

  initAddModalManualAdjustMerchantIdsData() {
    this.addModalManualAdjust_MerchantIds = this.merchants;
  }

  initAddModalManualAdjust() {
    // this.initAddModalManualAdjustMerchantIdsData();
    //date trans
    // this.addModalManualAdjustFormGroup.get('detailAdjust.dateTransaction').setValue(new Date());
    //init template value
    this.selectedTemplate.setValue("detailAdjust"); // Dieu chinh giao dich chi tiet
  }

  showAddModalManualAdjust() {
    this.initAddModalManualAdjust();
    this.displayAddModalManualAdjust = true;
  }

  hideAddModalManualAdjust() {
    this.displayAddModalManualAdjust = false;
  }

  resetAddModalManualAdjust() { //reset value, not validators
    this.addModalManualAdjustFormGroup.get("detailAdjust").reset({
      totalTrans: 0,
      merchantId: "",
      orderRef: "",
      transId: "",
      merchantTxnRef: "",
      dateTransaction: "",
      currency: "",
      payChannel: "",
      transactionType: "",
      exchangeRate: 0,
      currencyFee: 0,
      fixFee: 0,
      percentFee: 0,
      feeFailed: 0,
      percentFeeIta: 0,
      amount: 0,
      state: "",
      description: "",
      feeEcom: 0,
      feeIta: 0
    });

    this.addModalManualAdjustFormGroup.get("totalAdjust").reset({
      totalTransaction: 0,
      currency: "",
      amount: 0,
      amountToVnd: 0,
      totalFeeIncVat: 0,
      totalAmountAdv: 0,
      totalAmountAdvCurrent: 0,
      description: ""
    });

    this.addModalManualAdjustFormGroup.get("breakFd").reset({
      totalTransaction: 0,
      currency: "",
      amount: 0,
      amountToVnd: 0,
      totalFeeIncVat: 0,
      totalAmountAdv: 0,
      totalAmountAdvCurrent: 0,
      description: ""
    });
  }

  confirmAddModalManualAdjust() {
    console.log("--CONFIRM ADD MODAL MANUAL ADJUST--");
    let params = {};
    let selectedTemplate = this.selectedTemplate.value;
    if (selectedTemplate == "detailAdjust") {
      params = {
        n_id_advance: Number(this.advanceId),
        s_template: selectedTemplate,
        s_desc: this.addModalManualAdjustFormGroup.get("detailAdjust.description").value,
        s_merchant_id: this.addModalManualAdjustFormGroup.get("detailAdjust.merchantId").value.merchantId,
        s_order_info: this.addModalManualAdjustFormGroup.get("detailAdjust.orderRef").value,
        d_date_transaction: this.datePipe.transform(this.addModalManualAdjustFormGroup.get("detailAdjust.dateTransaction").value, "dd/MM/yyyy HH:mm:ss"),
        s_currency: this.addModalManualAdjustFormGroup.get("detailAdjust.currency").value,
        s_pay_channel: this.addModalManualAdjustFormGroup.get("detailAdjust.payChannel").value,
        s_transaction_type: this.addModalManualAdjustFormGroup.get("detailAdjust.transactionType").value,
        n_exchange_rate: this.addModalManualAdjustFormGroup.get("detailAdjust.exchangeRate").value,
        s_currency_fee: this.addModalManualAdjustFormGroup.get("detailAdjust.currencyFee").value,
        n_fix_fee: this.addModalManualAdjustFormGroup.get("detailAdjust.fixFee").value,
        n_percent_fee: this.addModalManualAdjustFormGroup.get("detailAdjust.percentFee").value,
        n_fee_failed: this.addModalManualAdjustFormGroup.get("detailAdjust.feeFailed").value,
        n_amount: this.addModalManualAdjustFormGroup.get("detailAdjust.amount").value,
        d_from: this.datePipe.transform(new Date(this.processAddModalManualAdjustDate(this.addModalManualAdjustFormGroup.get("detailAdjust.payChannel").value, "from")), "dd/MM/yyyy HH:mm:ss"),
        d_to: this.datePipe.transform(new Date(this.processAddModalManualAdjustDate(this.addModalManualAdjustFormGroup.get("detailAdjust.payChannel").value, "to")), "dd/MM/yyyy HH:mm:ss"),
        s_state: this.addModalManualAdjustFormGroup.get("detailAdjust.state").value,
        n_total_txn: this.addModalManualAdjustFormGroup.get("detailAdjust.totalTrans").value,
        s_transaction_id: this.addModalManualAdjustFormGroup.get("detailAdjust.transId").value,
        s_transaction_ref: this.addModalManualAdjustFormGroup.get("detailAdjust.merchantTxnRef").value,
        n_ita_percent_fee: this.addModalManualAdjustFormGroup.get("detailAdjust.percentFeeIta").value,
        n_fee_ecom: this.addModalManualAdjustFormGroup.get("detailAdjust.feeEcom").value,
        n_fee_ita: this.addModalManualAdjustFormGroup.get("detailAdjust.feeIta").value,
        n_id_config: Number(this.dataPreAdvance.advanceConfigCode)
      };
    }
    if (selectedTemplate == "totalAdjust") {
      params = {
        n_id_advance: Number(this.advanceId),
        s_template: selectedTemplate,
        n_total_txn: this.addModalManualAdjustFormGroup.get("totalAdjust.totalTransaction").value,
        s_currency: this.addModalManualAdjustFormGroup.get("totalAdjust.currency").value,
        n_amount: this.addModalManualAdjustFormGroup.get("totalAdjust.amount").value,
        n_amount_vnd: this.addModalManualAdjustFormGroup.get("totalAdjust.amountToVnd").value,
        n_total_fee_adv: this.addModalManualAdjustFormGroup.get("totalAdjust.totalFeeIncVat").value,
        n_amount_adv: this.addModalManualAdjustFormGroup.get("totalAdjust.totalAmountAdv").value,
        n_amount_adv_current: this.addModalManualAdjustFormGroup.get("totalAdjust.totalAmountAdvCurrent").value,
        d_from: this.datePipe.transform(new Date(this.processAddModalManualAdjustDateForTotalAdjust("from")), "dd/MM/yyyy HH:mm:ss"),
        d_to: this.datePipe.transform(new Date(this.processAddModalManualAdjustDateForTotalAdjust("to")), "dd/MM/yyyy HH:mm:ss"),
        s_desc: this.addModalManualAdjustFormGroup.get("totalAdjust.description").value,
        n_id_config: Number(this.dataPreAdvance.advanceConfigCode)
      };
    }

    if (selectedTemplate == "breakFd") {
      params = {
        n_id_advance: Number(this.advanceId),
        s_template: selectedTemplate,
        n_total_txn: this.addModalManualAdjustFormGroup.get("breakFd.totalTransaction").value,
        s_currency: this.addModalManualAdjustFormGroup.get("breakFd.currency").value,
        n_amount: this.addModalManualAdjustFormGroup.get("breakFd.amount").value,
        n_amount_vnd: this.addModalManualAdjustFormGroup.get("breakFd.amountToVnd").value,
        n_total_fee_adv: this.addModalManualAdjustFormGroup.get("breakFd.totalFeeIncVat").value,
        n_amount_adv: this.addModalManualAdjustFormGroup.get("breakFd.totalAmountAdv").value,
        n_amount_adv_current: this.addModalManualAdjustFormGroup.get("breakFd.totalAmountAdvCurrent").value,
        d_from: this.datePipe.transform(new Date(this.processAddModalManualAdjustDateForTotalAdjust("from")), "dd/MM/yyyy HH:mm:ss"),
        d_to: this.datePipe.transform(new Date(this.processAddModalManualAdjustDateForTotalAdjust("to")), "dd/MM/yyyy HH:mm:ss"),
        s_desc: this.addModalManualAdjustFormGroup.get("breakFd.description").value,
        n_id_config: Number(this.dataPreAdvance.advanceConfigCode)
      };
    }
    console.log("confirmAddModalManualAdjust params |", params);
    this.paymentAdvanceService.addAdvanceManualAdjust(params).subscribe(data => {
      console.log("confirmAddModalManualAdjust response |", data);
      if (this.isValidDataObject(data) && data.response_code == "00") {
        this.hideAddModalManualAdjust();
        this.resetAddModalManualAdjust();
        this.onInitManualAdjustControl();
        this.toastr.success(data.message_vi, "Manual adjust");
      } else {
        this.toastr.error(data.message_vi, "Manual adjust");
      }
    });
  }

  processAddModalManualAdjustDateForTotalAdjust(key: string) {
    let listPayChannel = ["QT", "ND", "QR", "SMS", "BL", "BNPL", "UPOS", "PC", "PO", "DD", "VIETQR"];
    let i = 0;
    let date;
    while (!date && i < listPayChannel.length) {
      date = this.processAddModalManualAdjustDate(listPayChannel[i], key);
      i++;
    }
    return date;
  }

  processAddModalManualAdjustDate(payChannel: string, key: string) {
    let data = null;
    //return when payChannel not in payChannel of advanceId
    if (!this.addModalManualAdjust_PayChannels.find(item => item.value == payChannel)) {
      return;
    }
    const payChannels = this.swap(this.addModalManualAdjust_PayChannels, 0,
      this.addModalManualAdjust_PayChannels.findIndex(item => item.value == payChannel));
    for (let i = 0; i < payChannels.length; i++) {
      const element = payChannels[i];
      switch (element.value) {
        case "QT": {
          data = this.dataTableInternational[0]; //vi tri 0 la summary
          break;
        }
        case "CUP": {
          data = this.dataTableInternational[0];
          break;
        }
        case "ND": {
          data = this.dataTableDomestic[0];
          break;
        }
        case "QR": {
          data = this.dataTableQr[0];
        }
        case "SMS": {
          data = this.dataTableSms[0];
          break;
        }
        case "BL": {
          data = this.dataTableBiling[0];
          break;
        }
        case "BNPL": {
          data = this.dataTableBnpl[0];
          break;
        }
        case "PC": {
          data = this.dataTablePc[0];
          break;
        }
        case "PO": {
          // chưa có khối PO
          data = null;
          break;
        }
        case "DD": {
          data = this.dataTableDd[0];
          break;
        }
        case "VIETQR": {
          data = this.dataTableVietqr[0];
          break;
        }
      }
      if (this.isValidDataObject(data)) break;
    };
    if (this.isValidDataObject(data)) {
      return data[key];
    } else {
      return this.dataHeader[key];
    }
  }

  deleteManualAdjustConfirmation(advanceTransactionId: string) {
    this.confirmationService.confirm({
      header: "Yêu cầu xóa bản ghi điều chỉnh",
      message: "Bạn chắc chắn muốn tạo yêu cầu xóa bản ghi điều chỉnh này?",
      acceptLabel: "Xác nhận",
      rejectLabel: "Hủy",
      rejectButtonStyleClass: "p-button-danger",
      accept: () => {
        this.deleteManualAdjust(advanceTransactionId);
      }
    });
  }

  deleteManualAdjust(advanceTransactionId: string) {
    const params = {
      advanceTransactionId: Number(advanceTransactionId)
    }
    this.paymentAdvanceService.updateAdvTransByDelete(params).subscribe(data => {
      console.log(data);
      if (data.response_code === '00') {
        this.toastr.success(data.message_vi, "Yêu cầu xóa bản ghi điều chỉnh");
        //Update lai cac khoi
        this.getPreAdvance(this.advanceId);
      } else {
        this.toastr.error(data.message_vi, "Yêu cầu xóa bản ghi điều chỉnh");
      }
    });
  }
  /* END: MANUAL ADJUST - ĐIỀU CHỈNH THỦ CÔNG */

  /*11. START: LAST TOTAL - TỔNG CUỐI*/
  bindDataTableLastTotal(data) {
    //clear dataTableLastTotal
    this.dataTableLastTotal = [];

    if (!data) {
      return;
    }

    const dataKeys = ["index", "title", "method", "code", "total_txn",
      "total_usd", "total_vnd", "total_merchant_discount_amount", "total_partner_discount_amount", "total_change_to_vnd", "total_fee_inc_vat",
      "total_vat", "total_minus_vat", "total_advance", "from", "to", "relation_advance_id"];

    if (!this.isValidDataObject(data)) return;

    let dataTableLevel1 = {};
    //process dataLevel1 - summary
    for (let key of dataKeys) {
      dataTableLevel1[key] = data[key];
    }
    dataTableLevel1["level"] = 1;
    dataTableLevel1["backgroundColorClass"] = "backGroundColor_level-1-row";
    this.dataTableLastTotal.push(dataTableLevel1);

    console.log("dataTableLastTotal | ", this.dataTableLastTotal);
  }
  /* END: LAST TOTAL */

  exportExcelAll() {
    console.log("--EXPORT EXCEL ALL--");
    const params = {
      advanceId: this.advanceId,
      type: "ALL"
    }
    console.log("exportExcelAll params |", params);
    this.paymentAdvanceService.exportExcelPreAdvance(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  formatCurrency(cur: string) {
    if (!cur) return "";
    return new Intl.NumberFormat().format(Number(cur));
  }

  isValidDataObject(data: any) {
    for (let key in data) return true;
    return false;
  }

  swap(arr, index1, index2) {
    return arr.map((val, idx) => {
      if (idx === index1) return arr[index2];
      if (idx === index2) return arr[index1];
      return val;
    });
  }

  backToPage() {
    this.location.back();
  };

  isEmptyArray(array: any[]) {
    return !array.length;
  }
}
