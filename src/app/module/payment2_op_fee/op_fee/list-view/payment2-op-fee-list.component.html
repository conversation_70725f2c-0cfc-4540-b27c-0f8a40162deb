<div class="wrapper" id="payment-bank-fee">
    <div class="container-fluid mt-3">
        <div class="row mt-3">
            <div class="col-2-modify">
                <h2 class="header-one">OnePay Data Analysis</h2>
            </div>
            <div style="text-align: right;position: absolute;right: 20px;">
                <span style="color: #1c80cf;font-style: italic;font-weight: bold;">{{favoriteViewName}}</span>
            </div>
        </div>
        <form>
            <div class="row mt-3">
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-calendar [ngModel]="fromDateControl" [ngModelOptions]="{standalone: true}"
                                (ngModelChange)="onChangeFromDate($event)" [showIcon]="true" inputId="icon" view="month"
                                dateFormat="mm/yy" [maxDate]="toDateControl">
                            </p-calendar>
                            <label class="label-custom fixPositionLabel" for="icon">From Month</label>
                        </span>
                    </div>
                </div>
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-calendar [ngModel]="toDateControl" [ngModelOptions]="{standalone: true}"
                                (ngModelChange)="onChangeToDate($event)" [showIcon]="true" inputId="icon" view="month"
                                dateFormat="mm/yy" [minDate]="fromDateControl"></p-calendar>
                            <label class="label-custom fixPositionLabel" for="icon">To Month</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="contractRelationChange()" appendTo="body" defaultLabel="ALL"
                                [options]="contractRelationList" [(ngModel)]="contractRelation"
                                [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel"
                                name="contractRelation" maxSelectedLabels="1" id="ddcontractRelation"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Relation Contract</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect appendTo="body" (onChange)="serviceChange()"
                                selectedItemsLabel="{0} item(s) selected" defaultLabel="ALL"
                                [options]="serviceListBinding" [(ngModel)]="service" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #serviceM="ngModel" name="service" maxSelectedLabels="1"
                                id="ddService"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Service</label>
                        </span>
                    </div>
                </div>
                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body"
                                selectedItemsLabel="{0} item(s) selected" defaultLabel="ALL" [style]="{'width':'100%'}"
                                [options]="bankCardList" [(ngModel)]="bankCard" dropdownIcon="pi pi-angle-down"
                                optionLabel="partnerId" optionValue="sId" #contractTypeM="ngModel" name="bankCard"
                                maxSelectedLabels="1" id="ddBankCard"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Bank/Partner</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" resetFilterOnHide="true" appendTo="body"
                                defaultLabel="ALL" optionLabel="key" optionValue="key" [options]="cardTypeListBinding"
                                [(ngModel)]="cardType" [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down"
                                #contractTypeM="ngModel" name="cardType" maxSelectedLabels="1"
                                id="ddCardType"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Card Type</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <input type="text" placeholder="Merchant ID" pInputText [(ngModel)]="merchantId"
                                [ngModelOptions]="{standalone: true}" />
                            <label class="label-custom fixPositionLabel" for="icon">MerchantId</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify" style="flex: 0 1 20%;max-width: 20%;">
                    <div class="form-group">
                        <span class="input-group">
                            <input type="text" placeholder="Partner Name, Merchant Name ..." pInputText
                                [(ngModel)]="keyword" [ngModelOptions]="{standalone: true}" />
                            <label class="label-custom fixPositionLabel" for="icon">Other Search</label>
                        </span>
                    </div>
                </div>

                <div class="advance-setting">
                    <div class="form-group">
                        <a href="javascript:void(0);" (click)="openDialog()"
                            style="display: inline-block;width: 80%;">Display settings</a>
                        <app-filter-result-output [sortBy]="sortBy" [exchangeRate]="exchangeRate" [currency]="currency"
                            [termSize]="termSize" [display]="displayDialog" [gridViewType]="gridViewType"
                            [terms]="terms" [termsColumn]="termsColumn" [numberLastYear]="numberLastYear"
                            [isGrowth]="isGrowth" [lstSumResult]="lstSumResult" [lstDisplayColumn]="lstDisplayColumn"
                            (displayChange)="onDialogClose($event)"
                            [reloadCheckBoxColumn]="reloadCheckBoxColumn.asObservable()"></app-filter-result-output>
                        <app-list-transaction-output [transactionCurrency]="transactionCurrency" [binBank]="binBank"
                            [binBankList]="binBankList" [transactionState]="transactionState"
                            [viewTestData]="viewTestData" [binCountry]="binCountry" [binCountryList]="binCountryList"
                            [merchantId]="merchantId" [province]="province" [provinceList]="provinceList"
                            [mpgsId]="mpgsId" [mpgsIdList]="mpgsIdList" [mpgsIdListQT]="mpgsIdListQT"
                            [mpgsIdListND]="mpgsIdListND" [serviceListBinding]="serviceListBinding"
                            [cardType]="cardType" [cardTypeList]="cardTypeList"
                            [cardTypeListBinding]="cardTypeListBinding" [mcc]="mcc" [mccList]="mccList"
                            [category]="category" [categoryList]="categoryList" [categoryStan]="categoryStan"
                            [categoryStanList]="categoryStanList" [contractRelation]="contractRelation"
                            [transactionType]="transactionType" [acquirerList]="acquirerList"
                            [acquirerListND]="acquirerListND" [acquirerListQT]="acquirerListQT"
                            [display]="displayDialogTransaction" [keyword]="keyword" [fromDate]="fromDate"
                            [toDate]="toDate" [service]="service" [bankCard]="bankCard" [acquirer]="acquirer"
                            (displayChange)="onDialogTransactionClose($event)"></app-list-transaction-output>
                    </div>
                </div>

            </div>

            <div class="row mt-3" style="margin-top: 0px !important;">
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <!-- <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="transactionDurationTypeList" [(ngModel)]="transactionDurationType"
                                [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel"
                                name="transactionDurationType" maxSelectedLabels="1"
                                id="ddTransactionDurationType"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Duration</label>
                        </span>
                    </div> -->

                    <div class="form-group" style="display: none;">
                        <span class="input-group" style="display: initial;padding-right: 130px;">
                            <p-checkbox name="matchMerchantActiveDate" [binary]="true"
                                [(ngModel)]="matchMerchantActiveDate" inputId="matchMerchantActiveDate">
                            </p-checkbox>
                            <label class="label-custom fixPositionLabel">Merchant Active Date</label>
                        </span>
                    </div>

                    
                        <div class="form-group">
                            <span class="input-group">
                                <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                    [options]="periodList" [(ngModel)]="period"
                                    [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel"
                                    name="period" maxSelectedLabels="1"
                                    id="ddPeriod"></p-multiSelect>
                                <label class="label-custom fixPositionLabel" for="icon">Sale Period</label>
                            </span>
                        </div>
                  

                </div>
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-dropdown appendTo="body" optionLabel="label" optionValue="value"
                                [style]="{'width':'100%'}" [options]="transactionCurrencyList"
                                [(ngModel)]="transactionCurrency" name="transactionCurrency"
                                id="ddTransactionCurency"></p-dropdown>
                            <label class="label-custom fixPositionLabel">Txn Currency</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body"
                                selectedItemsLabel="{0} item(s) selected" defaultLabel="ALL" [style]="{'width':'100%'}"
                                [options]="(service.length == 0 || service.join(',') == 'QT,ND' || service.join(',') == 'ND,QT') ? acquirerList : (service.join(',') == 'ND' ? acquirerListND : acquirerListQT)"
                                [(ngModel)]="acquirer" dropdownIcon="pi pi-angle-down" optionLabel="name"
                                optionValue="name" #contractTypeM="ngModel" name="name" maxSelectedLabels="1"
                                id="txt-search-contractType"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Acquirer</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="mpgsIdList"
                                [options]="(service.length == 0 || service.join(',') == 'QT,ND' || service.join(',') == 'ND,QT') ? mpgsIdList : (service.join(',') == 'QT' || service.join(',') == 'ITA' ? mpgsIdListQT : mpgsIdListND)"
                                [(ngModel)]="mpgsId" [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down"
                                #contractTypeM="ngModel" name="mpgsId" maxSelectedLabels="1"
                                id="ddMPGS"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">MPGS/CyberId/CIAC</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify" style="flex: 0 1 4%;max-width: 4%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-dropdown appendTo="body" (onChange)="loadProvinceByGroup($event.value)" optionLabel="label" optionValue="value"
                                [style]="{'width':'100%'}" [options]="lstGroupProvince"
                                [(ngModel)]="groupProvince" name="groupProvince"
                                id="ddGroupProvince"></p-dropdown>
                            <label class="label-custom fixPositionLabel">Group Province</label>
                        </span>
                    </div>
                </div>
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="provinceList" [(ngModel)]="province" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="province"
                                maxSelectedLabels="1" id="ddProvince"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Partner Province</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <!-- <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" [group]="true" defaultLabel="ALL" [options]="binCountryList" [(ngModel)]="binCountry" [style]="{'width':'100%'}" dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="binCountry" maxSelectedLabels="1" id="ddbinCountry">

                                <ng-template let-group pTemplate="group">
                                    <div class="p-d-flex p-ai-center">
                                        <span>{{group.label}}</span>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Bin Country</label>
                        </span> -->

                        <span class="input-group">
                            <p-dropdown (onChange)="changeValue()" appendTo="body" optionLabel="label"
                                optionValue="value" [style]="{'width':'100%'}" [options]="binCountryList2"
                                [(ngModel)]="binCountry" name="binCountry" id="ddbinCountry"></p-dropdown>
                            <label class="label-custom fixPositionLabel" for="icon">Bin Country</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="binBankList" [(ngModel)]="binBank" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="binBank"
                                maxSelectedLabels="1" id="ddBinBank"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Bin Bank</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group" style="display: initial;padding-right: 90px;">
                            <p-checkbox name="viewTestData" [binary]="true" [(ngModel)]="viewTestData"
                                inputId="viewTestData">
                            </p-checkbox>
                            <label class="label-custom fixPositionLabel">OnePay Test Data</label>
                        </span>

                        <span class="input-group" style="display: initial;">
                            <p-checkbox name="vat" [binary]="true" [(ngModel)]="vat" inputId="vat">
                            </p-checkbox>
                            <label class="label-custom fixPositionLabel">VAT</label>
                        </span>
                    </div>
                </div>
                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-dropdown appendTo="body" optionLabel="label" optionValue="value"
                                [style]="{'width':'100%'}" [options]="currencyList" [(ngModel)]="currency"
                                name="curency" id="ddCurency"></p-dropdown>
                            <label class="label-custom fixPositionLabel">Display Currency</label>
                        </span>
                    </div>
                </div>
                <div class="advance-setting">
                    <div class="form-group">
                        <a href="javascript:void(0);" (click)="openFavoriteViewList()"
                            style="display: inline-block;width: 80%;">Favorite views</a>
                        <p-dialog [position]="position" header="Favorite views" [(visible)]="displayFavoriteView"
                            [modal]="true" [style]="{width: '30vw'}" [baseZIndex]="10000" [draggable]="false"
                            [resizable]="false">

                            <div class="container text-left">

                                <div class="row group1">
                                    <div class="col-md-1">
                                        No
                                    </div>
                                    <div class="col-md-10">
                                        <h5>Name</h5>
                                    </div>
                                    <div class="col-md-1">

                                    </div>
                                    <!-- <div class="col-md-3">
                                        <h5>Created Date</h5>
                                    </div> -->
                                </div>

                                <div class="row group1" *ngFor="let item of lstFavoriteView;let i = index">

                                    <div class="col-md-1">
                                        {{i+1}}
                                    </div>
                                    <div class="col-md-10">
                                        <a
                                            href="/iportal{{item.url}}&execute=1&favoriteViewName={{item.name}}">{{item.name}}</a>
                                    </div>
                                    <div class="col-md-1">
                                        <i *ngIf="item.id > 0" class="pi pi-trash" style="cursor: pointer;color:red"
                                            (click)="deleteFavoriteView(item.id)"></i>
                                    </div>
                                    <!-- <div class="col-md-3">
                                        {{item.date | customDate}}
                                    </div> -->
                                </div>
                            </div>

                            <ng-template pTemplate="footer">
                                <button type="button" pButton label="Close" id="btnClose" class=""
                                    (click)="closeFavoriteViewList()"></button>
                            </ng-template>
                        </p-dialog>
                    </div>
                </div>

            </div>

            <div class="row mt-3" style="margin-top: 0px !important;">
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-dropdown appendTo="body" (onChange)="changeSaleProvince()" optionLabel="label"
                                optionValue="value" [style]="{'width':'100%'}" [options]="saleProvinceList"
                                [(ngModel)]="saleProvince" name="saleProvince" id="ddSaleProvince"></p-dropdown>
                            <label class="label-custom fixPositionLabel">Sale Province</label>
                        </span>
                    </div>
                </div>
                <div class="col-1-modify" style="flex: 0 1 6%;max-width: 6%;">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect resetFilterOnHide="true" appendTo="body" defaultLabel="ALL"
                                [options]="saleListBinding" [(ngModel)]="sale" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="sale"
                                maxSelectedLabels="1" id="ddSale"></p-multiSelect>
                            <label class="label-custom fixPositionLabel">Sale</label>
                        </span>
                    </div>
                </div>


                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeCategory()" appendTo="body" defaultLabel="ALL"
                                [options]="categoryList" [(ngModel)]="category" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="category"
                                maxSelectedLabels="1" id="ddCategory"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Category</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="categoryStanList" [(ngModel)]="categoryStan" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="categoryStan"
                                maxSelectedLabels="1" id="ddCategoryStan"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Category (Stan)</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" resetFilterOnHide="true" appendTo="body"
                                defaultLabel="ALL" [options]="mccList" [(ngModel)]="mcc" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="mcc"
                                maxSelectedLabels="1" id="ddMcc"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">MCC</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-multiSelect (onChange)="changeValue()" appendTo="body" defaultLabel="ALL"
                                [options]="transactionTypeList" [(ngModel)]="transactionType" [style]="{'width':'100%'}"
                                dropdownIcon="pi pi-angle-down" #contractTypeM="ngModel" name="transactionType"
                                maxSelectedLabels="1" id="ddTransactionType"
                                selectedItemsLabel="{0} item(s) selected"></p-multiSelect>
                            <label class="label-custom fixPositionLabel" for="icon">Transaction Type</label>
                        </span>
                    </div>
                </div>

                <div class="col-1-modify">
                    <div class="form-group">
                        <span class="input-group">
                            <p-dropdown (onChange)="changeValue()" appendTo="body" optionLabel="label"
                                optionValue="value" [style]="{'width':'100%'}" [options]="stateList"
                                [(ngModel)]="transactionState" name="transactionState" id="ddState"></p-dropdown>
                            <label class="label-custom fixPositionLabel">Transaction State</label>
                        </span>

                    </div>
                </div>

                <div class="col-05-modify">
                    <div class="form-group">
                        <button type="button" pButton pRipple icon="pi pi-times" styleClass="p-button-warn"
                            id="btn-search" label="Clear" (click)="clearFilter()"
                            class="download-button ui-button-success"></button>

                    </div>
                </div>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <div class="col-2-modify">
                    <div class="form-group d-flex justify-content-end">

                        &nbsp;&nbsp;
                        <button type="submit" pButton pRipple icon="pi pi-desktop" styleClass="p-button-warn"
                            id="btn-search" label="Data Analysis" (click)="searchData()"
                            class="download-button ui-button-success"></button>
                        &nbsp;&nbsp;
                        <button type="button" pButton label="Download" icon="pi pi-download" iconPos="left"
                            id="btn-download" (click)="downloadData()"
                            class=" download-button ui-button-success"></button>
                        &nbsp;&nbsp;
                        <button type="button" pButton label="Transactions" icon="pi pi-table" iconPos="left"
                            id="btn-transaction" (click)="openDialogTransaction()" class="ui-button-success"></button>
                        &nbsp;&nbsp;
                        <button
                            [hidden]="!isActive('DOWNLOAD_UPOS_ANALYSIS')"
                            type="button" pButton label="UPOS Analysis" icon="pi pi-download" iconPos="left"
                            id="btn-download" (click)="downloadDataUPOS()" class=" download-button ui-button-success"></button>
                    </div>                  
                </div>         
            </div>
        </form>
        <div *ngIf="gridViewType == 'row'">
            <p-table [value]="listData"
                styleClass="p-datatable-sm p-datatable-striped p-datatable-gridlines sticky-headers-table"
                [responsive]="true" selectionMode="multiple" [lazy]="true" [resizableColumns]="true"
                columnResizeMode="expand" scrollHeight="calc(100vh - 290px)" [(first)]="first" [rows]="pageSize"
                [scrollable]="true">

                <ng-template pTemplate="colgroup" let-columns>
                    <colgroup>
                        <col class="mat-column-no" style="width: 80px;">
                        <col class="mat-column-no" style="width: 70px;">
                        <col class="mat-column-no" style="width: 50px;">
                        <col class="mat-column-no" [ngStyle]="{'width': currency == 'USD' ? '80px' : '120px'}">
                        <col class="mat-column-no" [ngStyle]="{'width': currency == 'USD' ? '59px' : '99px'}">
                        <col class="mat-column-no" [ngStyle]="{'width': currency == 'USD' ? '59px' : '99px'}"
                            *ngFor="let col of monthList">
                    </colgroup>
                </ng-template>

                <ng-template pTemplate="header" let-columns>
                    <tr class="ml-1">
                        <th class="text-center header">STT
                        <th class="text-center header"></th>
                        <th class="text-center header"></th>
                        <th class="text-center header">Total</th>
                        <th class="text-center header">Average</th>
                        <th class="text-center header" *ngFor="let col of monthList">
                            {{col}}
                        </th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-i="rowIndex" let-data>
                    <!-- <tr *ngIf="data.name !== 'ALL DATA'"> -->
                    <tr>
                        <td *ngIf="data.name !== 'ALL DATA' && listData.length > 1" class="text-center">
                            {{i}}
                        </td>
                        <td *ngIf="data.name !== 'ALL DATA' && listData.length <= 1" class="text-center">

                        </td>
                        <td *ngIf="data.name == 'ALL DATA'" class="text-center">

                        </td>
                        <td [attr.colspan]="colSpanGroup()" appendTo="body">
                            <b> {{data.name}}</b>
                        </td>
                    </tr>

                    <tr *ngFor="let dataview of data.views"
                        [ngStyle]="{'background-color': dataview.displayName.includes(currentYear) ? '#d6eaf8 ' : ''}">
                        <!-- *ngIf="checkReferralITA(dataview.displayName, dataview.total)" -->
                        <ng-container *ngIf="checkReferralITA(dataview.displayName, dataview.total)">
                            <td [attr.colspan]="2" class="text-left" appendTo="body"
                                [ngStyle]="{'color': dataview.displayName.includes(currentYear) ? 'red' : ''}">
                                {{dataview.displayName}}
                            </td>
                            <td class="text-right ui-resizable-column">
                                <a
                                    *ngIf="dataview.type != '%' && dataview.displayName != 'Number of Txn'&& dataview.displayName != 'Transaction Volume'">
                                    {{percentage(dataview.percentToSaleVolume)}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;">
                                <a *ngIf="dataview.statusTotal == true && dataview.type == '%'">
                                    {{percentage(dataview.total)}}
                                </a>
                                <a *ngIf="dataview.statusTotal == false && dataview.type == '%'">

                                </a>
                                <a *ngIf="dataview.displayName != 'Exchange Rate' && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.total) | number}}
                                </a>
                            </td>

                            <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;">                                
                                <a *ngIf="dataview.displayName != 'Exchange Rate' && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.avg) | number}}
                                </a>
                            </td>

                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(0,0)">
                                <a *ngIf="dataview.statusThang1Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang1Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang1Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang1Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(1,0)">
                                <a *ngIf="dataview.statusThang2Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang2Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang2Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang2Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(2,0)">
                                <a *ngIf="dataview.statusThang3Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang3Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang3Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang3Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(3,0)">
                                <a *ngIf="dataview.statusThang4Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang4Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang4Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang4Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(4,0)">
                                <a *ngIf="dataview.statusThang5Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang5Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang5Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang5Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(5,0)">
                                <a *ngIf="dataview.statusThang6Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang6Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang6Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang6Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(6,0)">
                                <a *ngIf="dataview.statusThang7Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang7Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang7Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang7Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(7,0)">
                                <a *ngIf="dataview.statusThang8Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang8Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang8Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang8Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(8,0)">
                                <a *ngIf="dataview.statusThang9Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang9Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang9Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang9Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(9,0)">
                                <a *ngIf="dataview.statusThang10Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang10Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang10Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang10Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(10,0)">
                                <a *ngIf="dataview.statusThang11Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang11Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang11Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang11Old) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(11,0)">
                                <a *ngIf="dataview.statusThang12Old == true && dataview.type == '%'">
                                    {{percentage(dataview.thang12Old)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang12Old == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang12Old) | number}}
                                </a>
                            </td>

                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(0)">
                                <a *ngIf="dataview.statusThang1 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang1)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang1 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang1) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(1)">
                                <a *ngIf="dataview.statusThang2 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang2)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang2 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang2) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(2)">
                                <a *ngIf="dataview.statusThang3 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang3)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang3 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang3) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(3)">
                                <a *ngIf="dataview.statusThang4 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang4)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang4 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang4) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(4)">
                                <a *ngIf="dataview.statusThang5 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang5)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang5 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang5) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(5)">
                                <a *ngIf="dataview.statusThang6 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang6)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang6 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang6) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(6)">
                                <a *ngIf="dataview.statusThang7 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang7)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang7 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang7) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(7)">
                                <a *ngIf="dataview.statusThang8 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang8)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang8 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang8) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(8)">
                                <a *ngIf="dataview.statusThang9 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang9)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang9 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang9) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(9)">
                                <a *ngIf="dataview.statusThang10 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang10)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang10 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang10) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(10)">
                                <a *ngIf="dataview.statusThang11 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang11)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang11 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang11) | number}}
                                </a>
                            </td>
                            <td class="text-right ui-resizable-column" appendTo="body" *ngIf="checkValidateMonth(11)">
                                <a *ngIf="dataview.statusThang12 == true && dataview.type == '%'">
                                    {{percentage(dataview.thang12)}}
                                </a>
                                <a
                                    *ngIf="dataview.statusThang12 == true && (dataview.type == null || dataview.type == '')">
                                    {{roundData(dataview.thang12) | number}}
                                </a>
                            </td>
                        </ng-container>
                    </tr>

                </ng-template>

                <ng-template pTemplate="emptymessage" let-columns>
                    <tr>
                        <td [attr.colspan]="16" class="text-center empty_results">
                            Not found
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
        <div *ngIf="gridViewType == 'column'">
            <p-table id="tbColumn" [columns]="scrollableCols" frozenWidth="280px" [frozenColumns]="frozenCols"
                [value]="listDataByColumn" [tableStyle]="{ 'min-width': '50px' }" scrollHeight="calc(100vh - 330px)"
                [scrollable]="true">

                <ng-template pTemplate="frozencolgroup" let-columns>
                    <colgroup>
                        <col style="width: 30px">
                        <col style="width: 250px">
                        <!-- <col *ngIf="termsColumn == 'partnerId.keyword'" style="width: 200px">
                        <col *ngIf="termsColumn == 'partnerId.keyword'" style="width: 100px">
                        <col *ngIf="termsColumn == 'partnerId.keyword'" style="width: 150px"> -->
                    </colgroup>
                </ng-template>

                <ng-template pTemplate="colgroup" let-columns>
                    <colgroup>
                        <col *ngIf="checkAvg()" style="width: 80px">
                        <col *ngIf="checkAvg()" style="width: 110px">
                        <col style="width: 69px">
                        <col *ngIf="displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '80px' : '119px'}">
                            <col [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                            <col *ngIf="checkAvg()" style="width: 50px">
                        <col *ngIf="displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="displayColumn.indexOf('PercentNumberOfTransaction') > -1" style="width: 60px">
                        <col *ngIf="displayColumn.indexOf('PercentSaleAmount') > -1" style="width: 60px">

                        <col style="width: 59px" *ngIf="checkValidateMonth(0,0)">
                        <col *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(1,0)">
                        <col *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(2,0)">
                        <col *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(3,0)">
                        <col *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(4,0)">
                        <col *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(5,0)">
                        <col *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(6,0)">
                        <col *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(7,0)">
                        <col *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(8,0)">
                        <col *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(9,0)">
                        <col *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(10,0)">
                        <col *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(11,0)">
                        <col *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">

                        <col style="width: 59px" *ngIf="checkValidateMonth(0)">
                        <col *ngIf="checkValidateMonth(0) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(0) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(0) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(1)">
                        <col *ngIf="checkValidateMonth(1) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(1) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(1) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(1) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(2)">
                        <col *ngIf="checkValidateMonth(2) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(2) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(2) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(2) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(3)">
                        <col *ngIf="checkValidateMonth(3) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(3) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(3) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(3) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(4)">
                        <col *ngIf="checkValidateMonth(4) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(4) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(4) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(4) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(5)">
                        <col *ngIf="checkValidateMonth(5) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(5) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(5) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(5) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(6)">
                        <col *ngIf="checkValidateMonth(6) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(6) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(6) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(6) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(7)">
                        <col *ngIf="checkValidateMonth(7) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(7) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(7) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(7) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(8)">
                        <col *ngIf="checkValidateMonth(8) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(8) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(8) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(8) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(9)">
                        <col *ngIf="checkValidateMonth(9) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(9) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(9) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(9) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(10)">
                        <col *ngIf="checkValidateMonth(10) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(10) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(10) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(10) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                        <col style="width: 59px" *ngIf="checkValidateMonth(11)">
                        <col *ngIf="checkValidateMonth(11) && displayColumn.indexOf('SaleAmount') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(11) && displayColumn.indexOf('OnePayFeeRevenue') > -1"
                            [ngStyle]="{'width': currency == 'USD' ? '65px' : '110px'}">
                        <col *ngIf="checkValidateMonth(11) && displayColumn.indexOf('PercentNumberOfTransaction') > -1"
                            style="width: 60px">
                        <col *ngIf="checkValidateMonth(11) && displayColumn.indexOf('PercentSaleAmount') > -1"
                            style="width: 60px">
                    </colgroup>
                </ng-template>

                <ng-template pTemplate="frozenheader" let-columns>



                    <tr class="ml-1">
                        <th rowspan="2" class="text-left header">STT
                        <th rowspan="2" class="text-left header">{{termsColumnName}}</th>
                        <!-- <th *ngIf="termsColumn == 'partnerId.keyword'" class="text-left header">Partner Name</th>
                        <th *ngIf="termsColumn == 'partnerId.keyword'" class="text-left header">City</th>
                        <th *ngIf="termsColumn == 'partnerId.keyword'" class="text-left header">Category</th> -->
                    </tr>
                    <tr class="ml-1">
                    </tr>
                </ng-template>

                <ng-template pTemplate="header" let-columns>
                    <tr class="ml-1">
                        <th rowspan="2" class="text-center header" *ngIf="checkAvg()">
                            Active Date</th>
                        <th rowspan="2" class="text-center header" *ngIf="checkAvg()">
                            Sale</th>
                        <!-- <th rowspan="2" class="text-center header">
                            Txn Volume Average</th>
                        <th rowspan="2" class="text-center header" *ngIf="checkAvg()">
                            Level</th> -->

                        <th [attr.colspan]="colSpanTotal()" class="text-center header">Total</th>

                        <th class="text-center header" [attr.colspan]="colSpan()" class="text-center header"
                            *ngFor="let col of monthList">
                            {{col}}
                        </th>

                        <!-- <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(0)">Jan</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(1)">Feb</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(2)">Mar</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(3)">Apr</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(4)">May</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(5)">Jun</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(6)">Jul</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(7)">Aug</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(8)">Sep</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(9)">Oct</th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(10)">Nov
                        </th>
                        <th [attr.colspan]="colSpan()" class="text-center header" *ngIf="checkValidateMonth(11)">Dec
                        </th> -->
                    </tr>
                    <tr class="ml-1">

                        <th class="text-center header">Number of Txn</th>
                        <th class="text-center header" *ngIf="displayColumn.indexOf('SaleAmount') > -1">Txn
                            Volume</th>
                        <th class="text-center header">
                            Txn Volume Average</th>
                        <th class="text-center header" *ngIf="checkAvg()">
                            Level</th>
                            
                        <th class="text-center header" *ngIf="displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header" *ngIf="displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header" *ngIf="displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>

                        <th class="text-center header" *ngIf="checkValidateMonth(0,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('SaleAmount') > -1">Txn
                            Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(1,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(2,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(3,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(4,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('SaleAmount') > -1">Txn
                            Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(5,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(6,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(7,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(8,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(9,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(10,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay
                            Fee Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(11,0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay
                            Fee Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            Percentage of Total Txn Volume</th>

                        <th class="text-center header" *ngIf="checkValidateMonth(0)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(0) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(1)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(1) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(2)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(2) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(3)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(3) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(4)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(4) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(5)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(5) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(6)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(6) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(7)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(7) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(8)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(8) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(9)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9) && displayColumn.indexOf('SaleAmount') > -1">Txn Volume
                        </th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(9) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(10)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10) && displayColumn.indexOf('SaleAmount') > -1">Txn
                            Volume</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(10) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                        <th class="text-center header" *ngIf="checkValidateMonth(11)">Number of Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11) && displayColumn.indexOf('SaleAmount') > -1">Txn
                            Volume</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11) && displayColumn.indexOf('OnePayFeeRevenue') > -1">OnePay Fee
                            Revenue</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            Percentage of Total Txn</th>
                        <th class="text-center header"
                            *ngIf="checkValidateMonth(11) && displayColumn.indexOf('PercentSaleAmount') > -1">Percentage
                            of Total Txn Volume</th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="frozenbody" let-i="rowIndex" let-data let-columns="columns">
                    <tr>

                        <td *ngIf="data.name !== 'ALL DATA' && listDataByColumn.length > 1" class="text-center">
                            {{i}}
                        </td>
                        <td *ngIf="data.name !== 'ALL DATA' && listDataByColumn.length <= 1" class="text-center">

                        </td>
                        <td *ngIf="data.name == 'ALL DATA'" class="text-center">

                        </td>

                        <td class="text-left" appendTo="body">
                            {{data.name}}
                        </td>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-data let-columns="columns">
                    <tr>
                        <td class="text-left" appendTo="body" class="text-center" *ngIf="checkAvg()">
                            {{data.summary.activeDate | customOnlyDate}}
                        </td>
                        <td class="text-left" appendTo="body" class="text-left" *ngIf="checkAvg()">
                            {{data.summary.sale}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;">
                            {{data.summary.numberOfTransaction | number}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;"
                            *ngIf="displayColumn.indexOf('SaleAmount') > -1">
                            {{roundData(data.summary.totalVolume) | number}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;">
                            {{roundData(data.summary.avgVolume) | number}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;"
                            *ngIf="checkAvg()">
                            {{data.summary.level}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;"
                            *ngIf="displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            {{roundData(data.summary.totalFeeOP) | number}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;"
                            *ngIf="displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            {{percentage(data.summary.percentNumberOfTransaction)}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body" style="font-weight: bold;"
                            *ngIf="displayColumn.indexOf('PercentSaleAmount') > -1">
                            {{percentage(data.summary.percentTotalVolume)}}
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,0)">
                            <a *ngIf="data.months[0].active == 1">
                                {{data.months[0].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,0) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[0].active == 1">
                                {{roundData(data.months[0].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,0) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[0].active == 1">
                                {{roundData(data.months[0].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,0) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[0].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[0].active == 1">
                                {{percentage(data.months[0].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,0) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[0].active == 1">
                                {{percentage(data.months[0].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,1)">
                            <a *ngIf="data.months[1].active == 1">
                                {{data.months[1].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,1) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[1].active == 1">
                                {{roundData(data.months[1].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,1) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[1].active == 1">
                                {{roundData(data.months[1].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,1) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[1].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[1].active == 1">
                                {{percentage(data.months[1].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,1) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[1].active == 1">
                                {{percentage(data.months[1].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,2)">
                            <a *ngIf="data.months[2].active == 1">
                                {{data.months[2].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,2) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[2].active == 1">
                                {{roundData(data.months[2].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,2) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[2].active == 1">
                                {{roundData(data.months[2].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,2) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[2].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[2].active == 1">
                                {{percentage(data.months[2].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,2) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[2].active == 1">
                                {{percentage(data.months[2].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,3)">
                            <a *ngIf="data.months[3].active == 1">
                                {{data.months[3].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,3) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[3].active == 1">
                                {{roundData(data.months[3].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,3) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[3].active == 1">
                                {{roundData(data.months[3].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,3) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[3].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[3].active == 1">
                                {{percentage(data.months[3].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,3) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[3].active == 1">
                                {{percentage(data.months[3].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,4)">
                            <a *ngIf="data.months[4].active == 1">
                                {{data.months[4].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,4) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[4].active == 1">
                                {{roundData(data.months[4].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,4) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[4].active == 1">
                                {{roundData(data.months[4].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,4) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[4].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[4].active == 1">
                                {{percentage(data.months[4].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,4) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[4].active == 1">
                                {{percentage(data.months[4].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,5)">
                            <a *ngIf="data.months[5].active == 1">
                                {{data.months[5].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,5) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[5].active == 1">
                                {{roundData(data.months[5].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,5) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[5].active == 1">
                                {{roundData(data.months[5].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,5) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[5].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[5].active == 1">
                                {{percentage(data.months[5].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,5) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[5].active == 1">
                                {{percentage(data.months[5].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,6)">
                            <a *ngIf="data.months[6].active == 1">
                                {{data.months[6].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,6) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[6].active == 1">
                                {{roundData(data.months[6].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,6) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[6].active == 1">
                                {{roundData(data.months[6].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,6) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[6].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[6].active == 1">
                                {{percentage(data.months[6].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,6) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[6].active == 1">
                                {{percentage(data.months[6].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,7)">
                            <a *ngIf="data.months[7].active == 1">
                                {{data.months[7].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,7) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[7].active == 1">
                                {{roundData(data.months[7].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,7) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[7].active == 1">
                                {{roundData(data.months[7].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,7) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[7].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[7].active == 1">
                                {{percentage(data.months[7].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,7) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[7].active == 1">
                                {{percentage(data.months[7].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,8)">
                            <a *ngIf="data.months[8].active == 1">
                                {{data.months[8].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,8) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[8].active == 1">
                                {{roundData(data.months[8].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,8) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[8].active == 1">
                                {{roundData(data.months[8].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,8) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[8].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[8].active == 1">
                                {{percentage(data.months[8].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,8) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[8].active == 1">
                                {{percentage(data.months[8].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,9)">
                            <a *ngIf="data.months[9].active == 1">
                                {{data.months[9].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,9) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[9].active == 1">
                                {{roundData(data.months[9].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,9) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[9].active == 1">
                                {{roundData(data.months[9].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,9) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[9].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[9].active == 1">
                                {{percentage(data.months[9].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,9) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[9].active == 1">
                                {{percentage(data.months[9].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,10)">
                            <a *ngIf="data.months[10].active == 1">
                                {{data.months[10].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,10) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[10].active == 1">
                                {{roundData(data.months[10].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,10) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[10].active == 1">
                                {{roundData(data.months[10].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,10) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[10].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[10].active == 1">
                                {{percentage(data.months[10].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,10) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[10].active == 1">
                                {{percentage(data.months[10].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,11)">
                            <a *ngIf="data.months[11].active == 1">
                                {{data.months[11].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,11) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[11].active == 1">
                                {{roundData(data.months[11].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,11) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[11].active == 1">
                                {{roundData(data.months[11].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,11) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[11].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[11].active == 1">
                                {{percentage(data.months[11].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,11) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[11].active == 1">
                                {{percentage(data.months[11].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,12)">
                            <a *ngIf="data.months[12].active == 1">
                                {{data.months[12].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,12) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[12].active == 1">
                                {{roundData(data.months[12].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,12) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[12].active == 1">
                                {{roundData(data.months[12].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,12) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[12].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[12].active == 1">
                                {{percentage(data.months[12].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,12) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[12].active == 1">
                                {{percentage(data.months[12].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,13)">
                            <a *ngIf="data.months[13].active == 1">
                                {{data.months[13].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,13) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[13].active == 1">
                                {{roundData(data.months[13].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,13) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[13].active == 1">
                                {{roundData(data.months[13].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,13) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[13].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[13].active == 1">
                                {{percentage(data.months[13].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,13) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[13].active == 1">
                                {{percentage(data.months[13].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,14)">
                            <a *ngIf="data.months[14].active == 1">
                                {{data.months[14].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,14) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[14].active == 1">
                                {{roundData(data.months[14].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,14) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[14].active == 1">
                                {{roundData(data.months[14].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,14) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[14].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[14].active == 1">
                                {{percentage(data.months[14].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,14) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[14].active == 1">
                                {{percentage(data.months[14].percentTotalVolume)}}
                            </a>
                        </td>

                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,15)">
                            <a *ngIf="data.months[15].active == 1">
                                {{data.months[15].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,15) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[15].active == 1">
                                {{roundData(data.months[15].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,15) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[15].active == 1">
                                {{roundData(data.months[15].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,15) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[15].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[15].active == 1">
                                {{percentage(data.months[15].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,15) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[15].active == 1">
                                {{percentage(data.months[15].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,16)">
                            <a *ngIf="data.months[16].active == 1">
                                {{data.months[16].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,16) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[16].active == 1">
                                {{roundData(data.months[16].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,16) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[16].active == 1">
                                {{roundData(data.months[16].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,16) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[16].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[16].active == 1">
                                {{percentage(data.months[16].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,16) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[16].active == 1">
                                {{percentage(data.months[16].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,17)">
                            <a *ngIf="data.months[17].active == 1">
                                {{data.months[17].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,17) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[17].active == 1">
                                {{roundData(data.months[17].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,17) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[17].active == 1">
                                {{roundData(data.months[17].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,17) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[17].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[17].active == 1">
                                {{percentage(data.months[17].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,17) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[17].active == 1">
                                {{percentage(data.months[17].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,18)">
                            <a *ngIf="data.months[18].active == 1">
                                {{data.months[18].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,18) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[18].active == 1">
                                {{roundData(data.months[18].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,18) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[18].active == 1">
                                {{roundData(data.months[18].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,18) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[18].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[18].active == 1">
                                {{percentage(data.months[18].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,18) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[18].active == 1">
                                {{percentage(data.months[18].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,19)">
                            <a *ngIf="data.months[19].active == 1">
                                {{data.months[19].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,19) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[19].active == 1">
                                {{roundData(data.months[19].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,19) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[19].active == 1">
                                {{roundData(data.months[19].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,19) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[19].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[19].active == 1">
                                {{percentage(data.months[19].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,19) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[19].active == 1">
                                {{percentage(data.months[19].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,20)">
                            <a *ngIf="data.months[20].active == 1">
                                {{data.months[20].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,20) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[20].active == 1">
                                {{roundData(data.months[20].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,20) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[20].active == 1">
                                {{roundData(data.months[20].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,20) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[20].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[20].active == 1">
                                {{percentage(data.months[20].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,20) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[20].active == 1">
                                {{percentage(data.months[20].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,21)">
                            <a *ngIf="data.months[21].active == 1">
                                {{data.months[21].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,21) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[21].active == 1">
                                {{roundData(data.months[21].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,21) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[21].active == 1">
                                {{roundData(data.months[21].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,21) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[21].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[21].active == 1">
                                {{percentage(data.months[21].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,21) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[21].active == 1">
                                {{percentage(data.months[21].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,22)">
                            <a *ngIf="data.months[22].active == 1">
                                {{data.months[22].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,22) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[22].active == 1">
                                {{roundData(data.months[22].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,22) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[22].active == 1">
                                {{roundData(data.months[22].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,22) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[22].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[22].active == 1">
                                {{percentage(data.months[22].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,22) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[22].active == 1">
                                {{percentage(data.months[22].percentTotalVolume)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,23)">
                            <a *ngIf="data.months[23].active == 1">
                                {{data.months[23].numberOfTransaction | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,23) && displayColumn.indexOf('SaleAmount') > -1">
                            <a *ngIf="data.months[23].active == 1">
                                {{roundData(data.months[23].totalVolume) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,23) && displayColumn.indexOf('OnePayFeeRevenue') > -1">
                            <a *ngIf="data.months[23].active == 1">
                                {{roundData(data.months[23].totalFeeOP) | number}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,23) && displayColumn.indexOf('PercentNumberOfTransaction') > -1">
                            <!-- {{data.months[23].dateTimeSpan | convertEpochToDate}} -->
                            <a *ngIf="data.months[23].active == 1">
                                {{percentage(data.months[23].percentNumberOfTransaction)}}
                            </a>
                        </td>
                        <td class="text-right ui-resizable-column" appendTo="body"
                            *ngIf="checkValidateMonthRow(data.months,23) && displayColumn.indexOf('PercentSaleAmount') > -1">
                            <a *ngIf="data.months[23].active == 1">
                                {{percentage(data.months[23].percentTotalVolume)}}
                            </a>
                        </td>
                    </tr>

                </ng-template>

                <ng-template pTemplate="emptymessage" let-columns>
                    <tr>
                        <td [attr.colspan]="15" class="text-center empty_results">
                            Not found
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>