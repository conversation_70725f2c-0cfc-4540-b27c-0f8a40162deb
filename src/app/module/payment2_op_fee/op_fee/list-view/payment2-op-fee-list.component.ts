import { HttpParams } from '@angular/common/http';
import { Component, EventEmitter, HostListener, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { PaymentOPFeeService } from '@service/payment_reconciliation/payment2-op-fee.service';
import { LazyLoadEvent } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DatePipe } from '@angular/common';
import { Subject } from 'rxjs';
@Component({
  selector: 'app-payment2-op-fee-list',
  templateUrl: './payment2-op-fee-list.component.html',
  styleUrls: ['./payment2-op-fee-list.component.css'],
  providers: [PaymentOPFeeService]
})
export class Payment2OPFeeListComponent implements OnInit {

  // public formResetter: Subject<boolean> = new Subject<boolean>();

  // resetChildForm() {
  //   this.formResetter.next(true);
  // }

  constructor(
    private paymentOPFeeService: PaymentOPFeeService,
    private global: Globals,
    private activatedRouter: ActivatedRoute,
    private router: Router,
    private datePipe: DatePipe,
  ) {
  }

  reloadCheckBoxColumn: Subject<Array<any>> = new Subject<Array<any>>();
  public loading: boolean;
  public listData: Array<any>;
  public listDataByColumn: Array<any>;
  public keyword = '';
  isGrowth: boolean;
  fromDate: Date;
  toDate: Date;

  fromDateControl: Date;
  toDateControl: Date;

  public fromDateCache: Date;
  public toDateCache: Date;

  public displayDialog: boolean;
  public displayDialogTransaction: boolean;

  public currentYear = 2023;
  public numberLastYear = 1;
  public service: Array<string>;
  public bankCard: Array<string>;
  public acquirer: Array<string>;
  public terms: Array<string>;
  public termSize: number;
  public sumResult: Array<string>;
  public displayColumn: Array<string>;
  public termsColumn: string;
  public termsColumnName: string;
  public gridViewType: string;
  public contractRelation: Array<string>;
  public transactionType: Array<string>;
  public transactionDurationType: Array<string>;
  public sale: Array<string>;
  public saleProvince = "";
  public mcc: Array<string>;
  public category: Array<string>;
  public categoryStan: Array<string>;
  public currency = "USD";
  public transactionCurrency = "";
  public exchangeRate: number;
  public cardType: Array<string>;
  public province: Array<string>;
  public binCountry: string;
  public binBank: Array<string>;
  public merchantId = '';
  public transactionState = "";
  public viewTestData: boolean;
  public matchMerchantActiveDate: boolean;
  public vat = false;
  public mpgsId: Array<string>;
  frozenCols: any[];
  scrollableCols: any[];
  public page = 0;
  public pageSize = 20;
  public first = 0;
  public tableDataTotal: number;
  private offsetHeight = 360;
  public flexScrollHeight = '360px';
  public bankPartnerName = '';
  public sortBy = 'Default';
  public subscription: Subscription;
  public favoriteViewName = '';
  public period: Array<string>;
  public pageSizeList = [
    {
      label: '20',
      value: '20'
    },
    {
      label: '50',
      value: '50'
    },
    {
      label: '100',
      value: '100'
    },
    {
      label: '200',
      value: '200'
    }
  ];

  public periodList = [];
  public monthList = [];
  public acquirerList = [];
  public acquirerListND = [];
  public acquirerListQT = [];
  public acquirerListDetail = [];
  public saleList = [];
  public saleListBinding = [];
  public bankCardList = [];
  public binBankList = [];
  public mccList = [];
  public categoryList = [];
  public categoryStanList = [];
  public provinceList = [];
  public mpgsIdList = [];
  public mpgsIdListQT = [];
  public mpgsIdListND = [];
  public groupMCC = [];
  public transactionDurationTypeList = [
    {
      label: 'PARTNERSHIP DURATION = 0-5 MONTHS',
      value: 'PARTNERSHIP DURATION = 0-5 MONTHS'
    },
    {
      label: 'PARTNERSHIP DURATION = 6-11 MONTHS',
      value: 'PARTNERSHIP DURATION = 6-11 MONTHS'
    },
    {
      label: 'PARTNERSHIP DURATION = 12 MONTHS OR MORE',
      value: 'PARTNERSHIP DURATION = 12 MONTHS OR MORE'
    },
    {
      label: 'PARTNERSHIP DURATION = NOT AVAILABLE',
      value: 'PARTNERSHIP DURATION = NOT AVAILABLE'
    },
  ];
  //public binCountryList = [];

  public binCountryList2 = [
    {
      label: 'ALL',
      value: ''
    },
    {
      label: 'VIETNAM',
      value: 'VIETNAM'
    },
    {
      label: 'NOT VIETNAM',
      value: 'NOT VIETNAM'
    },
  ];

  public saleProvinceList = [
    {
      label: 'ALL',
      value: ''
    },
    {
      label: 'Hà Nội',
      value: 'Hà Nội'
    },
    {
      label: 'Hồ Chí Minh',
      value: 'Hồ Chí Minh'
    },
  ];

  public binCountryList = [
    {
      label: 'VIETNAM', value: 'VIET NAM',
      items: [
        { label: 'VIET NAM', value: 'VIET NAM' }
      ]
    },
    {
      label: 'NOT VIETNAM', value: '',
      items: [
        { label: 'AFGHANISTAN', value: 'AFGHANISTAN' },
        { label: 'ALBANIA', value: 'ALBANIA' },
        { label: 'ALGERIA', value: 'ALGERIA' },
        { label: 'AMERICAN SAMOA', value: 'AMERICAN SAMOA' },
        { label: 'ANDORRA', value: 'ANDORRA' },
        { label: 'ANGOLA', value: 'ANGOLA' },
        { label: 'ANGUILLA', value: 'ANGUILLA' },
        { label: 'ANTIGUA AND BARBUDA', value: 'ANTIGUA AND BARBUDA' },
        { label: 'ARGENTINA', value: 'ARGENTINA' },
        { label: 'ARMENIA', value: 'ARMENIA' },
        { label: 'ARUBA', value: 'ARUBA' },
        { label: 'AUSTRALIA', value: 'AUSTRALIA' },
        { label: 'AUSTRIA', value: 'AUSTRIA' },
        { label: 'AZERBAIJAN', value: 'AZERBAIJAN' },
        { label: 'BAHAMAS', value: 'BAHAMAS' },
        { label: 'BAHRAIN', value: 'BAHRAIN' },
        { label: 'BANGLADESH', value: 'BANGLADESH' },
        { label: 'BARBADOS', value: 'BARBADOS' },
        { label: 'BELARUS', value: 'BELARUS' },
        { label: 'BELGIUM', value: 'BELGIUM' },
        { label: 'BELIZE', value: 'BELIZE' },
        { label: 'BENIN', value: 'BENIN' },
        { label: 'BERMUDA', value: 'BERMUDA' },
        { label: 'BHUTAN', value: 'BHUTAN' },
        { label: 'BOLIVIA, PLURINATIONAL STATE OF', value: 'BOLIVIA, PLURINATIONAL STATE OF' },
        { label: 'BONAIRE, SINT EUSTATIUS AND SABA', value: 'BONAIRE, SINT EUSTATIUS AND SABA' },
        { label: 'BOSNIA AND HERZEGOVINA', value: 'BOSNIA AND HERZEGOVINA' },
        { label: 'BOTSWANA', value: 'BOTSWANA' },
        { label: 'BRAZIL', value: 'BRAZIL' },
        { label: 'BRUNEI DARUSSALAM', value: 'BRUNEI DARUSSALAM' },
        { label: 'BULGARIA', value: 'BULGARIA' },
        { label: 'BURKINA FASO', value: 'BURKINA FASO' },
        { label: 'BURUNDI', value: 'BURUNDI' },
        { label: 'CAMBODIA', value: 'CAMBODIA' },
        { label: 'CAMEROON', value: 'CAMEROON' },
        { label: 'CANADA', value: 'CANADA' },
        { label: 'CAPE VERDE', value: 'CAPE VERDE' },
        { label: 'CAYMAN ISLANDS', value: 'CAYMAN ISLANDS' },
        { label: 'CENTRAL AFRICAN REPUBLIC', value: 'CENTRAL AFRICAN REPUBLIC' },
        { label: 'CHAD', value: 'CHAD' },
        { label: 'CHILE', value: 'CHILE' },
        { label: 'CHINA', value: 'CHINA' },
        { label: 'COLOMBIA', value: 'COLOMBIA' },
        { label: 'COMOROS', value: 'COMOROS' },
        { label: 'CONGO', value: 'CONGO' },
        { label: 'CONGO, THE DEMOCRATIC REPUBLIC OF THE', value: 'CONGO, THE DEMOCRATIC REPUBLIC OF THE' },
        { label: 'COOK ISLANDS', value: 'COOK ISLANDS' },
        { label: 'COSTA RICA', value: 'COSTA RICA' },
        { label: 'COTE D\'IVOIRE', value: 'COTE D\'IVOIRE' },
        { label: 'CROATIA', value: 'CROATIA' },
        { label: 'CURACAI', value: 'CURACAI' },
        { label: 'CURACAO', value: 'CURACAO' },
        { label: 'CYPRUS', value: 'CYPRUS' },
        { label: 'CZECH REPUBLIC', value: 'CZECH REPUBLIC' },
        { label: 'DENMARK', value: 'DENMARK' },
        { label: 'DJIBOUTI', value: 'DJIBOUTI' },
        { label: 'DOMINICA', value: 'DOMINICA' },
        { label: 'DOMINICAN REPUBLIC', value: 'DOMINICAN REPUBLIC' },
        { label: 'ECUADOR', value: 'ECUADOR' },
        { label: 'EGYPT', value: 'EGYPT' },
        { label: 'EL SALVADOR', value: 'EL SALVADOR' },
        { label: 'EQUATORIAL GUINEA', value: 'EQUATORIAL GUINEA' },
        { label: 'ESTONIA', value: 'ESTONIA' },
        { label: 'ETHIOPIA', value: 'ETHIOPIA' },
        { label: 'FIJI', value: 'FIJI' },
        { label: 'FINLAND', value: 'FINLAND' },
        { label: 'FRANCE', value: 'FRANCE' },
        { label: 'FRENCH POLYNESIA', value: 'FRENCH POLYNESIA' },
        { label: 'GABON', value: 'GABON' },
        { label: 'GAMBIA', value: 'GAMBIA' },
        { label: 'GEORGIA', value: 'GEORGIA' },
        { label: 'GERMANY', value: 'GERMANY' },
        { label: 'GHANA', value: 'GHANA' },
        { label: 'GIBRALTAR', value: 'GIBRALTAR' },
        { label: 'GREECE', value: 'GREECE' },
        { label: 'GRENADA', value: 'GRENADA' },
        { label: 'GUAM', value: 'GUAM' },
        { label: 'GUATEMALA', value: 'GUATEMALA' },
        { label: 'GUERNSEY', value: 'GUERNSEY' },
        { label: 'GUINEA', value: 'GUINEA' },
        { label: 'GUINEA-BISSAU', value: 'GUINEA-BISSAU' },
        { label: 'GUYANA', value: 'GUYANA' },
        { label: 'HAITI', value: 'HAITI' },
        { label: 'HOLY SEE (VATICAN CITY STATE)', value: 'HOLY SEE (VATICAN CITY STATE)' },
        { label: 'HONDURAS', value: 'HONDURAS' },
        { label: 'HONG KONG', value: 'HONG KONG' },
        { label: 'HUNGARY', value: 'HUNGARY' },
        { label: 'ICELAND', value: 'ICELAND' },
        { label: 'INDIA', value: 'INDIA' },
        { label: 'INDONESIA', value: 'INDONESIA' },
        { label: 'IRAN, ISLAMIC REPUBLIC OF', value: 'IRAN, ISLAMIC REPUBLIC OF' },
        { label: 'IRAQ', value: 'IRAQ' },
        { label: 'IRELAND', value: 'IRELAND' },
        { label: 'ISLE OF MAN', value: 'ISLE OF MAN' },
        { label: 'ISRAEL', value: 'ISRAEL' },
        { label: 'ITALY', value: 'ITALY' },
        { label: 'JAMAICA', value: 'JAMAICA' },
        { label: 'JAPAN', value: 'JAPAN' },
        { label: 'JERSEY', value: 'JERSEY' },
        { label: 'JORDAN', value: 'JORDAN' },
        { label: 'KAZAKHSTAN', value: 'KAZAKHSTAN' },
        { label: 'KENYA', value: 'KENYA' },
        { label: 'KIRIBATI', value: 'KIRIBATI' },
        { label: 'KOREA, DEMOCRATIC PEOPLE\'S REPUBLIC OF', value: 'KOREA, DEMOCRATIC PEOPLE\'S REPUBLIC OF' },
        { label: 'KOREA, REPUBLIC OF', value: 'KOREA, REPUBLIC OF' },
        { label: 'KOSOVO, REPUBLIC OF', value: 'KOSOVO, REPUBLIC OF' },
        { label: 'KR', value: 'KR' },
        { label: 'KUWAIT', value: 'KUWAIT' },
        { label: 'KYRGYZSTAN', value: 'KYRGYZSTAN' },
        { label: 'LAO PEOPLE\'S DEMOCRATIC REPUBLIC', value: 'LAO PEOPLE\'S DEMOCRATIC REPUBLIC' },
        { label: 'LATVIA', value: 'LATVIA' },
        { label: 'LEBANON', value: 'LEBANON' },
        { label: 'LESOTHO', value: 'LESOTHO' },
        { label: 'LIBERIA', value: 'LIBERIA' },
        { label: 'LIBYAN ARAB JAMAHIRIYA', value: 'LIBYAN ARAB JAMAHIRIYA' },
        { label: 'LIECHTENSTEIN', value: 'LIECHTENSTEIN' },
        { label: 'LITHUANIA', value: 'LITHUANIA' },
        { label: 'LUXEMBOURG', value: 'LUXEMBOURG' },
        { label: 'MACAO', value: 'MACAO' },
        { label: 'MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF', value: 'MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF' },
        { label: 'MADAGASCAR', value: 'MADAGASCAR' },
        { label: 'MALAWI', value: 'MALAWI' },
        { label: 'MALAYSIA', value: 'MALAYSIA' },
        { label: 'MALDIVES', value: 'MALDIVES' },
        { label: 'MALI', value: 'MALI' },
        { label: 'MALTA', value: 'MALTA' },
        { label: 'MARSHALL ISLANDS', value: 'MARSHALL ISLANDS' },
        { label: 'MAURITANIA', value: 'MAURITANIA' },
        { label: 'MAURITIUS', value: 'MAURITIUS' },
        { label: 'MEXICO', value: 'MEXICO' },
        { label: 'MICRONESIA, FEDERATED STATES OF', value: 'MICRONESIA, FEDERATED STATES OF' },
        { label: 'MOLDOVA, REPUBLIC OF', value: 'MOLDOVA, REPUBLIC OF' },
        { label: 'MONACO', value: 'MONACO' },
        { label: 'MONGOLIA', value: 'MONGOLIA' },
        { label: 'MONTENEGRO', value: 'MONTENEGRO' },
        { label: 'MONTSERRAT', value: 'MONTSERRAT' },
        { label: 'MOROCCO', value: 'MOROCCO' },
        { label: 'MOZAMBIQUE', value: 'MOZAMBIQUE' },
        { label: 'MYANMAR', value: 'MYANMAR' },
        { label: 'NAMIBIA', value: 'NAMIBIA' },
        { label: 'NEPAL', value: 'NEPAL' },
        { label: 'NETHERLANDS', value: 'NETHERLANDS' },
        { label: 'NEW ZEALAND', value: 'NEW ZEALAND' },
        { label: 'NICARAGUA', value: 'NICARAGUA' },
        { label: 'NIGER', value: 'NIGER' },
        { label: 'NIGERIA', value: 'NIGERIA' },
        { label: 'NORTHERN MARIANA ISLANDS', value: 'NORTHERN MARIANA ISLANDS' },
        { label: 'NORWAY', value: 'NORWAY' },
        { label: 'OMAN', value: 'OMAN' },
        { label: 'PAKISTAN', value: 'PAKISTAN' },
        { label: 'PALAU', value: 'PALAU' },
        { label: 'PALESTINIAN TERRITORY, OCCUPIED', value: 'PALESTINIAN TERRITORY, OCCUPIED' },
        { label: 'PANAMA', value: 'PANAMA' },
        { label: 'PAPUA NEW GUINEA', value: 'PAPUA NEW GUINEA' },
        { label: 'PARAGUAY', value: 'PARAGUAY' },
        { label: 'PERU', value: 'PERU' },
        { label: 'PHILIPPINES', value: 'PHILIPPINES' },
        { label: 'POLAND', value: 'POLAND' },
        { label: 'PORTUGAL', value: 'PORTUGAL' },
        { label: 'PUERTO RICO', value: 'PUERTO RICO' },
        { label: 'QATAR', value: 'QATAR' },
        { label: 'ROMANIA', value: 'ROMANIA' },
        { label: 'RUSSIAN FEDERATION', value: 'RUSSIAN FEDERATION' },
        { label: 'RWANDA', value: 'RWANDA' },
        { label: 'SAINT KITTS AND NEVIS', value: 'SAINT KITTS AND NEVIS' },
        { label: 'SAINT LUCIA', value: 'SAINT LUCIA' },
        { label: 'SAINT VINCENT AND THE GRENADINES', value: 'SAINT VINCENT AND THE GRENADINES' },
        { label: 'SAMOA', value: 'SAMOA' },
        { label: 'SAN MARINO', value: 'SAN MARINO' },
        { label: 'SAUDI ARABIA', value: 'SAUDI ARABIA' },
        { label: 'SENEGAL', value: 'SENEGAL' },
        { label: 'SERBIA', value: 'SERBIA' },
        { label: 'SEYCHELLES', value: 'SEYCHELLES' },
        { label: 'SIERRA LEONE', value: 'SIERRA LEONE' },
        { label: 'SINGAPORE', value: 'SINGAPORE' },
        { label: 'SINT MAARTEN (DUTCH PART)', value: 'SINT MAARTEN (DUTCH PART)' },
        { label: 'SLOVAKIA', value: 'SLOVAKIA' },
        { label: 'SLOVENIA', value: 'SLOVENIA' },
        { label: 'SOLOMON ISLANDS', value: 'SOLOMON ISLANDS' },
        { label: 'SOMALIA', value: 'SOMALIA' },
        { label: 'SOUTH AFRICA', value: 'SOUTH AFRICA' },
        { label: 'SOUTH SUDAN', value: 'SOUTH SUDAN' },
        { label: 'SPAIN', value: 'SPAIN' },
        { label: 'SRI LANKA', value: 'SRI LANKA' },
        { label: 'SUDAN', value: 'SUDAN' },
        { label: 'SURIlabel', value: 'SURIlabel' },
        { label: 'SWAZILAND', value: 'SWAZILAND' },
        { label: 'SWEDEN', value: 'SWEDEN' },
        { label: 'SWITZERLAND', value: 'SWITZERLAND' },
        { label: 'SYRIAN ARAB REPUBLIC', value: 'SYRIAN ARAB REPUBLIC' },
        { label: 'TAIWAN, PROVINCE OF CHINA', value: 'TAIWAN, PROVINCE OF CHINA' },
        { label: 'TAJIKISTAN', value: 'TAJIKISTAN' },
        { label: 'TANZANIA, UNITED REPUBLIC OF', value: 'TANZANIA, UNITED REPUBLIC OF' },
        { label: 'THAILAND', value: 'THAILAND' },
        { label: 'TIMOR-LESTE', value: 'TIMOR-LESTE' },
        { label: 'TOGO', value: 'TOGO' },
        { label: 'TONGA', value: 'TONGA' },
        { label: 'TRINIDAD AND TOBAGO', value: 'TRINIDAD AND TOBAGO' },
        { label: 'TUNISIA', value: 'TUNISIA' },
        { label: 'TURKEY', value: 'TURKEY' },
        { label: 'TURKMENISTAN', value: 'TURKMENISTAN' },
        { label: 'TURKS AND CAICOS ISLANDS', value: 'TURKS AND CAICOS ISLANDS' },
        { label: 'UGANDA', value: 'UGANDA' },
        { label: 'UKRAINE', value: 'UKRAINE' },
        { label: 'UNITED ARAB EMIRATES', value: 'UNITED ARAB EMIRATES' },
        { label: 'UNITED KINGDOM', value: 'UNITED KINGDOM' },
        { label: 'UNITED STATES', value: 'UNITED STATES' },
        { label: 'URUGUAY', value: 'URUGUAY' },
        { label: 'UZBEKISTAN', value: 'UZBEKISTAN' },
        { label: 'VANUATU', value: 'VANUATU' },
        { label: 'VENEZUELA, BOLIVARIAN REPUBLIC OF', value: 'VENEZUELA, BOLIVARIAN REPUBLIC OF' },
        { label: 'VIRGIN ISLANDS, BRITISH', value: 'VIRGIN ISLANDS, BRITISH' },
        { label: 'VIRGIN ISLANDS, U.S.', value: 'VIRGIN ISLANDS, U.S.' },
        { label: 'YEMEN', value: 'YEMEN' },
        { label: 'ZAMBIA', value: 'ZAMBIA' },
        { label: 'ZIMBABWE', value: 'ZIMBABWE' }
      ]
    }
  ];

  public displayFavoriteView: boolean;
  public position = "top-right";

  public lstFavoriteViewFixed = [
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=service.keyword,contractRelation.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=service.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=service.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=service.keyword&termsColumnName=Service&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Service',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=QT&bankCard=&terms=acquirer.keyword,contractRelation.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=service.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by International Gateway and Relation Contract',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=partnerFeeConfigITAName.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=service.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=false&vat=false&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount&sortBy=Default',
      name: 'Summary view by Installment',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=ND&bankCard=&terms=acquirer.keyword,contractRelation.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=service.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Local Debit Gateway and Relation Contract',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=QR&bankCard=&terms=partnerFeeConfigName.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=partnerFeeConfigName.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Mobile Banking/E-wallet and Bank Partner',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=BL&bankCard=&terms=partnerFeeConfigName.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=partnerFeeConfigName.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Billing and Bank Partner',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=BNPL&bankCard=&terms=partnerFeeConfigName.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=partnerFeeConfigName.keyword&termsColumnName=Service&gridViewType=row&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by BNPL and Bank Partner',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=categoryStan.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=categoryStan.keyword&termsColumnName=Service&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Category (Stan)',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=category.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=category.keyword&termsColumnName=Category&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Summary view by Category',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=binBank.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=binBank.keyword&termsColumnName=Bin Bank&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=VIETNAM&merchantId=&transactionState=SUCCESS&viewTestData=false&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount&sortBy=Default',
      name: 'Summary view by Vietnam Bin Bank ',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=binCountry.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=binCountry.keyword&termsColumnName=Bin Country&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=false&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount&sortBy=Default',
      name: 'Summary view by Bin Country',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=bankCard.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=bankCard.keyword&termsColumnName=Bin Country&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=false&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount&sortBy=Default',
      name: 'Summary view by Card Type',
      id: 0
    },
    {
      url: '/payment2-op-fee?keyword=&page=0&page_size=20&first=0&service=&bankCard=&terms=partnerShortName.keyword&termSize=1000&transactionType=&contractRelation=&mcc=&category=&categoryStan=&termsColumn=partnerShortName.keyword&termsColumnName=Partner Name&gridViewType=column&acquirer=&province=&cardType=&mpgsId=&binCountry=&merchantId=&transactionState=SUCCESS&viewTestData=falsetrue&numberLastYear=0&isGrowth=false&currency=USD&exchangeRate=23700&sumResult=NumberOfTransaction,SaleAmount,MerchantFee,BankCost,ITAFee,OtherFee,OnePayFeeRevenue&displayColumn=NumberOfTransaction,SaleAmount,OnePayFeeRevenue',
      name: 'Top 100 Merchant Partner with the most transaction',
      id: 0
    }
  ];

  public lstFavoriteView = [];
  public serviceListBinding = [];

  public serviceList = [
    {
      label: 'International Gateway',
      value: 'QT',
      contract: ''
    },
    {
      label: 'Installment - International Gateway',
      value: 'ITA-QT',
      contract: ''
    },
    {
      label: 'Local Debit Gateway',
      value: 'ND',
      contract: ''
    },
    {
      label: 'Direct Debit',
      value: 'DD',
      contract: '2B' 
    },
    {
      label: 'Mobile Banking / E-wallet',
      value: 'QR',
      contract: '2B'
    },
    {
      label: 'Billing',
      value: 'BL',
      contract: '2B'
    },
    {
      label: 'BNPL',
      value: 'BNPL',
      contract: '2B'
    },
    {
      label: 'UPOS',
      value: 'UPOS',
      contract: '2B'
    },
    {
      label: 'Installment - UPOS',
      value: 'ITA-UPOS',
      contract: '2B'
    },
    {
      label: 'PayOut',
      value: 'PO',
      contract: '2B'
    },
    {
      label: 'PayCollect',
      value: 'PC',
      contract: '2B'
    },
    {
      label: 'VietQR',
      value: 'VIETQR',
      contract: '2B'
    },  
    {
      label: 'Referral Partner',
      value: 'PARTNER',
      contract: '2B'
    }
  ];

  public stateList = [
    {
      label: 'ALL',
      value: ''
    },
    {
      label: 'Success',
      value: 'SUCCESS'
    },
    {
      label: 'Failed',
      value: 'FAILED'
    }
  ];

  public currencyList = [
    {
      value: 'VND',
      label: 'VND'
    },
    {
      value: 'USD',
      label: 'USD'
    },
    {
      value: 'THB',
      label: 'THB'
    },
    {
      value: 'SGD',
      label: 'SGD'
    },
    {
      value: 'MYR',
      label: 'MYR'
    },
    {
      value: 'IDR',
      label: 'IDR'
    },
    {
      value: 'JPY',
      label: 'JPY'
    },
    {
      value: 'KRW',
      label: 'KRW'
    },
    {
      value: 'TWD',
      label: 'TWD'
    },
    {
      value: 'CNY',
      label: 'CNY'
    }
  ];

  public transactionCurrencyList = [
    {
      label: 'ALL',
      value: ''
    },
    {
      value: 'VND',
      label: 'VND'
    },
    {
      value: 'USD',
      label: 'USD'
    },
    {
      value: 'THB',
      label: 'THB'
    },
    {
      value: 'SGD',
      label: 'SGD'
    },
    {
      value: 'MYR',
      label: 'MYR'
    },
    {
      value: 'IDR',
      label: 'IDR'
    },
    {
      value: 'JPY',
      label: 'JPY'
    },
    {
      value: 'KRW',
      label: 'KRW'
    },
    {
      value: 'TWD',
      label: 'TWD'
    },
    {
      value: 'CNY',
      label: 'CNY'
    }
  ];

  public cardTypeListBinding: Array<any>;

  public cardTypeList = [
    {
      "key": "VISA",
      "service": "QT,ITA-QT"
    },
    {
      "key": "MASTERCARD",
      "service": "QT,ITA-QT"
    },
    {
      "key": "JCB",
      "service": "QT,ITA-QT"
    },
    {
      "key": "AMEX",
      "service": "QT,ITA-QT"
    },
    {
      "key": "CUP",
      "service": "QT,ITA-QT"
    },
    {
      "key": "ABB",
      "service": "ND"
    },
    {
      "key": "ABBANK",
      "service": "QR"
    },
    {
      "key": "ACB",
      "service": "ND,QR"
    },
    {
      "key": "AGRIBANK",
      "service": "ND,QR"
    },

    {
      "key": "APP",
      "service": ""
    },
    {
      "key": "BACABANK",
      "service": "ND,QR"
    },
    {
      "key": "BAOVIETBANK",
      "service": "ND,QR"
    },
    {
      "key": "BIDV",
      "service": "ND,QR"
    },
    {
      "key": "BIDVSMARTBANKING",
      "service": "QR"
    },
    {
      "key": "BL",
      "service": "BL"
    },
    {
      "key": "COOPBANK",
      "service": "QR"
    },
    {
      "key": "VIKKIBANK",
      "service": "ND"
    },
    {
      "key": "EXIMBANK",
      "service": "ND,QR"
    },
    {
      "key": "FOXPAY",
      "service": "QR"
    },
    {
      "key": "GPBANK",
      "service": "ND"
    },
    {
      "key": "HDBANK",
      "service": "ND,QR"
    },
    {
      "key": "INDOVINABANK",
      "service": "ND"
    },
    {
      "key": "IVB",
      "service": "QR"
    },
    {
      "key": "IVBMOBILE",
      "service": "QR"
    },
    {
      "key": "KIENLONGBANK",
      "service": "ND,QR"
    },
    {
      "key": "KREDIVO",
      "service": "BNPL"
    },
    {
      "key": "LPBANK",
      "service": "ND"
    },

    {
      "key": "MB",
      "service": "ND"
    },
    {
      "key": "MBBANK",
      "service": "QR"
    },
    {
      "key": "MOMO",
      "service": "QR"
    },
    {
      "key": "MSB",
      "service": "ND"
    },
    {
      "key": "MSBANK",
      "service": "QR"
    },
    {
      "key": "MSBMBANK",
      "service": "QR"
    },
    {
      "key": "MYVIB",
      "service": "QR"
    },
    {
      "key": "NAMABANK",
      "service": "ND,QR"
    },
    {
      "key": "NCB",
      "service": "ND"
    },
    {
      "key": "NCBSMART",
      "service": "QR"
    },
    {
      "key": "OCBOMNI",
      "service": "QR"
    },
    {
      "key": "MBV",
      "service": "ND,QR"
    },
    {
      "key": "MBVEASY",
      "service": "QR"
    },
    {
      "key": "ORICOMBANK",
      "service": "ND"
    },
    {
      "key": "PGBANK",
      "service": "ND"
    },
    {
      "key": "PUBLICBANK",
      "service": "ND"
    },
    {
      "key": "PVCOMBANK",
      "service": "ND,QR"
    },
    {
      "key": "PVMOBILEBANKING",
      "service": "QR"
    },
    {
      "key": "SACOMBANK",
      "service": "ND,QR"
    },
    {
      "key": "SACOMBANKPAY",
      "service": "QR"
    },
    {
      "key": "SAIGONBANK",
      "service": "QR"
    },
    {
      "key": "SCB",
      "service": "ND,QR"
    },
    {
      "key": "SCBMOBILE",
      "service": "QR"
    },
    {
      "key": "SEABANK",
      "service": "ND"
    },
    {
      "key": "SGB",
      "service": "ND"
    },
    {
      "key": "SHB",
      "service": "ND,QR"
    },
    {
      "key": "SHBMOBILE",
      "service": "QR"
    },
    {
      "key": "SHINHAN",
      "service": "ND"
    },
    {
      "key": "SHINHANBANK",
      "service": "QR"
    },
    {
      "key": "SHOPEEPAY",
      "service": "QR"
    },
    {
      "key": "SMARTPAY",
      "service": "QR"
    },
    {
      "key": "TECHCOMBANK",
      "service": "ND,QR"
    },
    {
      "key": "TIENPHONGBANK",
      "service": "ND"
    },
    {
      "key": "TPBANK",
      "service": "QR"
    },
    {
      "key": "TPBANKQUICKPAY",
      "service": "QR"
    },
    {
      "key": "UNIONPAY",
      "service": "QR"
    },
    {
      "key": "UOB",
      "service": "ND"
    },
    {
      "key": "VBSP",
      "service": "QR"
    },
    {
      "key": "VCBPAY",
      "service": "QR"
    },
    {
      "key": "VCCB",
      "service": "ND"
    },
    {
      "key": "VIB",
      "service": "ND"
    },
    {
      "key": "VIETABANK",
      "service": "ND,QR"
    },
    {
      "key": "VIETBANK",
      "service": "ND,QR"
    },
    {
      "key": "VIETBANKDIGITAL",
      "service": "QR"
    },
    {
      "key": "VIETCAPITALBANK",
      "service": "QR"
    },
    {
      "key": "VIETCOMBANK",
      "service": "ND,QR"
    },
    {
      "key": "VIETCREDIT",
      "service": "ND"
    },
    {
      "key": "VIETINBANK",
      "service": "ND,QR"
    },
    {
      "key": "VIETINBANKIPAY",
      "service": "QR"
    },
    {
      "key": "VIETTELPAY",
      "service": "ND,QR"
    },
    {
      "key": "VINID",
      "service": "QR"
    },
    {
      "key": "VIVNPAY",
      "service": "QR"
    },
    {
      "key": "VNPAY",
      "service": "QR"
    },
    {
      "key": "VNPAYEWALLET",
      "service": "QR"
    },
    {
      "key": "VNPTMONEY",
      "service": "ND"
    },
    {
      "key": "VNPTPAY",
      "service": "QR"
    },
    {
      "key": "VPBANK",
      "service": "ND,QR"
    },
    {
      "key": "VRBANK",
      "service": "ND"
    },
    {
      "key": "VTCPAY",
      "service": "QR"
    },
    {
      "key": "WOORIBANK",
      "service": "ND,QR"
    },
    {
      "key": "ZALOPAY",
      "service": "QR"
    }
  ];

  public lstYears = [
    {
      label: '0',
      value: 0
    },
    {
      label: '1',
      value: 1
    }
    ,
    {
      label: '2',
      value: 2
    },
    {
      label: '3',
      value: 3
    },
    {
      label: '4',
      value: 4
    },
    {
      label: '5',
      value: 5
    }
  ];

  public contractRelationList = [
    {
      label: '2B',
      value: '2B'
    },
    {
      label: '3B',
      value: '3B'
    }
  ];

  public transactionTypeList = [
    {
      label: 'PURCHASE',
      value: 'PURCHASE'
    },
    {
      label: 'AUTHORIZE',
      value: 'AUTHORIZE'
    },
    {
      label: 'CAPTURE',
      value: 'CAPTURE'
    },
    {
      label: 'REFUND',
      value: 'REFUND'
    },
    {
      label: 'REFUND_CAPTURE',
      value: 'REFUND_CAPTURE'
    },
    {
      label: 'VOID_PURCHASE',
      value: 'VOID_PURCHASE'
    },
    {
      label: 'VOID_AUTHORIZE',
      value: 'VOID_AUTHORIZE'
    },
    {
      label: 'VOID_CAPTURE',
      value: 'VOID_CAPTURE'
    },
    {
      label: 'VOID_REFUND',
      value: 'VOID_REFUND'
    },
    {
      label: 'VOID_REFUND_CAPTURE',
      value: 'VOID_REFUND_CAPTURE'
    },
    {
      label: 'SETTLEMENT',
      value: 'SETTLEMENT'
    }
  ];

  public lstSumResult = [
    {
      value: "NumberOfTransaction",
      text: "Number of Txn",
      checked: true
    },
    {
      value: "SaleAmount",
      text: "Transaction Volume",
      checked: true
    },
    {
      value: "OtherFee",
      text: "Referral Partner Fee",
      checked: true
    },
    {
      value: "MerchantFixFee",
      text: "Merchant - Txn Fee",
      checked: false
    },
    {
      value: "MerchantPercentFee",
      text: "Merchant - Discount Rate",
      checked: false
    },
    // {
    //   value: "MerchantFixFailedFee",
    //   text: "Merchant Fix Failed Fee",
    //   checked: false
    // },
    {
      value: "MerchantFee",
      text: "Merchant - Total Fee",
      checked: true
    },
    // {
    //   value: "BankCollectedFeeFix",
    //   text: "Bank Collected Fix Fee (3B)",
    //   checked: false
    // },
    // {
    //   value: "BankCollectedFeePercent",
    //   text: "Bank Collected Percent Fee (3B)",
    //   checked: false
    // },
    // {
    //   value: "BankCollectedFee",
    //   text: "Bank Collected Fee (3B)",
    //   checked: false
    // },
    {
      value: "BankCostFix",
      text: "Bank Fee - Txn Fee",
      checked: false
    },
    {
      value: "BankCostPercent",
      text: "Bank Fee - MDR",
      checked: false
    },
    {
      value: "BankCost",
      text: "Bank Fee - Total Fee",
      checked: true
    },
    {
      value: "OnePayFeeRevenueFix",
      text: "OnePay - Txn Fee Revenue",
      checked: false
    },
    {
      value: "OnePayFeeRevenuePercent",
      text: "OnePay - MDR Revenue",
      checked: false
    },
    {
      value: "OnePayFeeRevenue",
      text: "OnePay - Toal Revenue",
      checked: true
    }
    //,
    // {
    //   value: "BankRevenueFix",
    //   text: "Bank Fix Fee Revenue",
    //   checked: false
    // },
    // {
    //   value: "BankRevenuePercent",
    //   text: "Bank Percent Fee Revenue",
    //   checked: false
    // },
    // {
    //   value: "BankRevenue",
    //   text: "Bank Revenue",
    //   checked: false
    // }
  ];

  public lstDisplayColumn = [
    {
      value: "NumberOfTransaction",
      text: "Number of Txn ",
      checked: true
    },
    {
      value: "SaleAmount",
      text: "Transaction Volume",
      checked: true
    },
    {
      value: "OnePayFeeRevenue",
      text: "OnePay Fee Revenue ",
      checked: true
    },
    {
      value: "PercentNumberOfTransaction",
      text: "Percentage of Total Txn",
      checked: true
    }
    ,
    {
      value: "PercentSaleAmount",
      text: "Percentage of Total Txn Volume",
      checked: true
    }
  ];

   public lstGroupProvince = [
    {
      label: 'ALL',
      value: ''
    },
    {
      label: 'Miền bắc',
      value: 'MB'
    },
    {
      label: 'Miền nam',
      value: 'MN'
    },
  ];
  public groupProvince = "";

  public lstGroupProvinceMN = [
    {id: '31', name: 'TP. Hồ Chí Minh'},
    {id: '1', name: 'An Giang'},
    {id: '2', name: 'Bà Rịa - Vũng Tàu'},
    {id: '3', name: 'Bạc Liêu'},
    {id: '7', name: 'Bến Tre'},
    {id: '8', name: 'Bình Dương'},
    {id: '9', name: 'Bình Định'},
    {id: '10', name: 'Bình Phước'},
    {id: '11', name: 'Bình Thuận'},
    {id: '12', name: 'Cà Mau'},
    {id: '14', name: 'Cần Thơ'},
    {id: '15', name: 'Đà Nẵng'},
    {id: '16', name: 'Đắk Lắk'},
    {id: '17', name: 'Đắk Nông'},
    {id: '18', name: 'Đồng Nai'},
    {id: '19', name: 'Đồng Tháp'},
    {id: '21', name: 'Gia Lai'},
    {id: '29', name: 'Hậu Giang'},
    {id: '32', name: 'Khánh Hòa'},
    {id: '33', name: 'Kiên Giang'},
    {id: '34', name: 'Kon Tum'},
    {id: '38', name: 'Lâm Đồng'},
    {id: '39', name: 'Long An'},
    {id: '43', name: 'Ninh Thuận'},
    {id: '45', name: 'Phú Yên'},
    {id: '47', name: 'Quảng Nam'},
    {id: '48', name: 'Quảng Ngãi'},
    {id: '51', name: 'Sóc Trăng'},
    {id: '53', name: 'Tây Ninh'},
    {id: '58', name: 'Tiền Giang'},
    {id: '59', name: 'Trà Vinh'},
    {id: '61', name: 'Vĩnh Long'}
  ];

  public lstGroupProvinceMB = [
    {id: '24', name: 'Hà Nội'},
    {id: '4', name: 'Bắc Kạn'},
    {id: '5', name: 'Bắc Giang'},
    {id: '6', name: 'Bắc Ninh'},
    {id: '13', name: 'Cao Bằng'},
    {id: '20', name: 'Điện Biên'},
    {id: '22', name: 'Hà Giang'},
    {id: '23', name: 'Hà Nam'},
    {id: '25', name: 'Hà Tĩnh'},
    {id: '26', name: 'Hải Dương'},
    {id: '27', name: 'Hải Phòng'},
    {id: '28', name: 'Hòa Bình'},
    {id: '30', name: 'Hưng Yên'},
    {id: '35', name: 'Lai Châu'},
    {id: '36', name: 'Lào Cai'},
    {id: '37', name: 'Lạng Sơn'},
    {id: '40', name: 'Nam Định'},
    {id: '41', name: 'Nghệ An'},
    {id: '42', name: 'Ninh Bình'},
    {id: '44', name: 'Phú Thọ'},
    {id: '46', name: 'Quảng Bình'},
    {id: '49', name: 'Quảng Ninh'},
    {id: '50', name: 'Quảng Trị'},
    {id: '52', name: 'Sơn La'},
    {id: '54', name: 'Thái Bình'},
    {id: '55', name: 'Thái Nguyên'},
    {id: '56', name: 'Thanh Hóa'},
    {id: '57', name: 'Thừa Thiên - Huế'},
    {id: '60', name: 'Tuyên Quang'},
    {id: '62', name: 'Vĩnh Phúc'},
    {id: '63', name: 'Yên Bái'}
  ];

  convertServiceName(service: string) {
    if (service == 'PARTNER') {
      return 'Referral Program';
    } else if (service == 'PLATFORM') {
      return 'Referral Platform';
    } else return service;
  }
  ngOnInit() {
    this.innitParams();
    this.loadMccGroup();
    this.loadFavoriteView();
    this.loadBankCard([]);
    this.loadService();
    this.loadCardType();
    // this.paymentOPFeeService.getAcquirer().subscribe(res => {
    //   this.acquirerList = res;
    // })

    this.paymentOPFeeService.getAcquirer().subscribe(res => {
      if (res && res.response && res.response.state && res.response.state == 'success') {
        this.acquirerList = res.response.data;
        this.acquirerListND = this.acquirerList.filter(item => item.id == 100 || item.id == 101);
        this.acquirerListQT = this.acquirerList.filter(item => item.id != 100 && item.id != 101);
      }

    })

    for (var i = 0; i < this.lstSumResult.length; i++) {
      for (var j = 0; j < this.sumResult.length; j++) {
        this.lstSumResult[i].checked = false;
        if (this.lstSumResult[i].value == this.sumResult[j]) {
          this.lstSumResult[i].checked = true;
          break;
        }
      }
    }

    for (var i = 0; i < this.lstDisplayColumn.length; i++) {
      for (var j = 0; j < this.displayColumn.length; j++) {
        this.lstDisplayColumn[i].checked = false;
        if (this.lstDisplayColumn[i].value == this.displayColumn[j]) {
          this.lstDisplayColumn[i].checked = true;
          break;
        }
      }
    }
    this.reloadCheckBoxColumn.next(this.lstDisplayColumn);

    this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
    this.searchData();
  }

  serviceChange() {
    var sBankCard1 = [];
    this.loadBankCard(sBankCard1);
    this.loadCardType();
    this.changeValue();
  }

  loadCardType() {
    if (this.service.length > 0) {
      for (var i = 0; i < this.service.length; i++) {
        this.cardTypeListBinding = this.cardTypeList.filter(x => x.service.includes(this.service[i]));
        this.cardType = [];
      }
    }
    else
      this.cardTypeListBinding = this.cardTypeList;
  }

  loadService() {
    if (this.contractRelation.length == 1) {
      var contractRelationTemp = this.contractRelation[0];
      this.serviceListBinding = this.serviceList.filter(x => x.contract == contractRelationTemp || x.contract == '');
      this.service = [];
    }
    else
      this.serviceListBinding = this.serviceList;
  }

  contractRelationChange() {
    this.loadService();
    this.changeValue();
  }
  loadBankCard(sBankCard) {
    const params = {
      'service': this.service.join(',')
    };
    this.paymentOPFeeService.getBankCard(params).subscribe(res => {
      this.bankCardList = res;
      console.log('sBankCard: ' + sBankCard);
      this.bankCard = sBankCard;
      this.loadBankPartnerName();
    })
  }

  openDialog() {
    this.displayDialog = true;
    // this.fromDateCache = this.fromDate;
    // this.toDateCache = this.fromDate;
    // console.log('from parent: fromDate: ' + this.fromDate);
    // console.log('from parent: toDate: ' + this.toDate);
  }

  onDialogClose(event) {
    this.displayDialog = event.display;
    if (event.isSave == true) {
      this.sumResult = [];

      var lstSumCache = event.lstSumCache;
      for (var i = 0; i < this.lstSumResult.length; i++) {
        for (var j = 0; j < lstSumCache.length; j++) {
          if (this.lstSumResult[i].value == lstSumCache[j].value) {
            this.lstSumResult[i].checked = lstSumCache[j].checked;
            break;
          }
        }
        if (this.lstSumResult[i].checked)
          this.sumResult.push(this.lstSumResult[i].value.toString());
      }

      this.displayColumn = [];
      var lstDisplayColumnCache = event.lstDisplayColumnCache;
      for (var i = 0; i < this.lstDisplayColumn.length; i++) {
        for (var j = 0; j < lstDisplayColumnCache.length; j++) {
          if (this.lstDisplayColumn[i].value == lstDisplayColumnCache[j].value) {
            this.lstDisplayColumn[i].checked = lstDisplayColumnCache[j].checked;
            break;
          }
        }
        if (this.lstDisplayColumn[i].checked)
          this.displayColumn.push(this.lstDisplayColumn[i].value.toString());
      }

      this.terms = event.terms;
      this.termSize = event.termSize;
      this.termsColumn = event.termsColumn;
      this.gridViewType = event.gridViewType;
      this.numberLastYear = event.numberLastYear;
      this.isGrowth = event.isGrowth;
      this.termsColumnName = event.termsColumnName;
      this.currency = event.currency;
      this.exchangeRate = event.exchangeRate;
      this.sortBy = event.sortBy;
      if (event.favoriteView != null && event.favoriteView != undefined && event.favoriteView.length > 0) {
        this.lstFavoriteView.unshift({ url: event.favoriteView[0].url, name: event.favoriteView[0].name, id: event.favoriteView[0].id })
      }
      this.searchData();
    }
    //console.log('lstSumResult: ' + JSON.stringify(this.lstSumResult));
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
  }
  getList() {
    this.router.navigate(['/payment2-op-fee'], { queryParams: this.redirectParams() });

    console.log("fromDate |", this.fromDate);
    console.log("toDate |", this.toDate);

    console.log('from Date via pipe: ' + this.datePipe.transform(this.fromDate, "dd/MM/yyyy"));
    console.log('from Date via pipe: ' + this.datePipe.transform(this.toDate, "dd/MM/yyyy"));
    var params = new HttpParams()
      .set('type', 'SEARCH')
      .set('type_search', 'LIKE')
      .set('keyword', this.keyword.trim())
      .set('service', this.service.join(','))
      .set('bankCard', this.bankCard == undefined ? '' : this.bankCard.join(','))
      .set('transactionType', this.transactionType == undefined ? '' : this.transactionType.join(','))
      .set('contractRelation', this.contractRelation == undefined ? '' : this.contractRelation.join(','))
      .set('mcc', this.mcc == undefined ? '' : this.mcc.join(','))
      .set('category', this.category == undefined ? '' : this.category.join('###'))
      .set('categoryStan', this.categoryStan == undefined ? '' : this.categoryStan.join(','))
      .set('binBank', this.binBank == undefined ? '' : this.binBank.join(','))
      .set('acquirer', this.acquirer.join(','))
      .set('terms', this.gridViewType == 'row' ? this.terms.join(',') : this.termsColumn)
      .set('termSize', this.termSize)
      .set('fromDate', this.datePipe.transform(this.fromDate, "dd/MM/yyyy"))
      .set('toDate', this.datePipe.transform(this.toDate, "dd/MM/yyyy"))
      .set('numberLastYear', this.numberLastYear)
      .set('currency', this.currency)
      .set('transactionCurrency', this.transactionCurrency)
      .set('exchangeRate', this.exchangeRate)
      .set('isGrowth', this.isGrowth)
      .set('sumResult', this.gridViewType == 'row' ? this.sumResult.join(',') : this.displayColumn.join(','))
      .set('termsColumn', this.termsColumn)
      .set('gridViewType', this.gridViewType)
      .set('province', this.province == undefined ? '' : this.province.join(','))
      .set('cardType', this.cardType == undefined ? '' : this.cardType.join(','))
      .set('mpgsId', this.mpgsId == undefined ? '' : this.mpgsId.join(','))
      //.set('binCountry', this.binCountry == undefined ? '' : this.binCountry.join('###'))
      .set('binCountry', this.binCountry)
      .set('sale', this.sale == undefined ? '' : this.sale.join(','))
      .set('saleProvince', this.saleProvince)
      .set('transactionDurationType', this.transactionDurationType == undefined ? '' : this.transactionDurationType.join(','))
      .set('period', this.period == undefined ? '' : this.period.join(','))
      .set('viewTestData', this.viewTestData)
      .set('matchMerchantActiveDate', this.matchMerchantActiveDate)
      .set('vat', this.vat)
      .set('transactionState', this.transactionState)
      .set('merchantId', this.merchantId.trim())
      .set('sortBy', this.sortBy)
      .set('page', this.page)
      .set('page_size', this.pageSize);

    return this.paymentOPFeeService.getPaymentOPFee(params);
  }

  changePageSize() {
    this.searchData();
  }

  isActive(functionName: string): boolean {
    return this.global.isActive(functionName);
  }
  
  redirectParams() {
    const params = {
      'keyword': this.keyword.trim(),
      'page': this.page,
      'page_size': this.pageSize,
      'first': this.first,
      'service': this.service.join(','),
      'bankCard': this.bankCard == undefined ? '' : this.bankCard.join(','),
      'terms': this.terms.join(','),
      'termSize': this.termSize,
      'transactionType': this.transactionType == undefined ? '' : this.transactionType.join(','),
      'contractRelation': this.contractRelation == undefined ? '' : this.contractRelation.join(','),
      'mcc': this.mcc == undefined ? '' : this.mcc.join(','),
      'category': this.category == undefined ? '' : this.category.join('###'),
      'categoryStan': this.categoryStan == undefined ? '' : this.categoryStan.join(','),
      'binBank': this.binBank == undefined ? '' : this.binBank.join(','),
      'termsColumn': this.termsColumn.trim(),
      'termsColumnName': this.termsColumnName.trim(),
      'gridViewType': this.gridViewType.trim(),
      'acquirer': this.acquirer.join(','),
      'province': this.province.join(','),
      'cardType': this.cardType.join(','),
      'mpgsId': this.mpgsId.join(','),
      //'binCountry': this.binCountry.join('###'),
      'binCountry': this.binCountry,
      'sale': this.sale.join(','),
      'saleProvince': this.saleProvince,
      'transactionDurationType': this.transactionDurationType.join(','),
      'period': this.period.join(','),      
      'merchantId': this.merchantId.trim(),
      'transactionState': this.transactionState,
      'viewTestData': this.viewTestData,
      'matchMerchantActiveDate': this.matchMerchantActiveDate,
      'vat': this.vat,
      'fromDate': this.datePipe.transform(this.fromDate, "dd/MM/yyyy"),
      'toDate': this.datePipe.transform(this.toDate, "dd/MM/yyyy"),
      'numberLastYear': this.numberLastYear,
      'isGrowth': this.isGrowth,
      'currency': this.currency,
      'transactionCurrency': this.transactionCurrency,
      'exchangeRate': this.exchangeRate,
      'sumResult': this.sumResult.join(','),
      'displayColumn': this.displayColumn.join(','),
      'sortBy': this.sortBy
    };

    return params;
  }

  innitParams() {
    let today = new Date();
    let month = today.getMonth();
    let year = today.getFullYear();
    let prevMonth = (month === 0) ? 11 : month - 1;

    this.frozenCols = [
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 }
    ];

    this.scrollableCols = [
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 },
      { field: 'STT', header: 'STT', width: 100 },
      { field: 'MCC', header: 'MCC', width: 100 },
      { field: 'PartnerID', header: 'Partner ID', width: 100 },
      { field: 'PartnerName', header: 'Partner Name', width: 100 },
      { field: 'City', header: 'City', width: 100 },
      { field: 'Category', header: 'Category', width: 100 }
    ];

    for (var i = new Date().getFullYear() + 1; i >= 2019; i--) {
      if (i == new Date().getFullYear() + 1) {
        if(new Date().getMonth() >  10)
        {
          this.periodList.push({ 'value': 'MERCHANTID IS ACTIVATED AT FIRST-' + i, 'label': 'FIRST-' + i });
        }
        else
        {

        }
      }
      else {
        this.periodList.push({ 'value': 'MERCHANTID IS ACTIVATED AT LAST-' + i, 'label': 'LAST-' + i });
        this.periodList.push({ 'value': 'MERCHANTID IS ACTIVATED AT FIRST-' + i, 'label': 'FIRST-' + i });
      }
    }

    this.subscription = this.activatedRouter
      .queryParams
      .subscribe(params => {
        this.keyword = params['keyword'] === undefined ? '' : params['keyword'].trim();
        this.exchangeRate = params['exchangeRate'] === undefined || params['exchangeRate'] == null || params['exchangeRate'].toString() == '' ? 23700 : parseInt(params['exchangeRate'].trim());
        console.log('exchange rate: ' + this.exchangeRate + " .params['exchangeRate']=" + params['exchangeRate']);
        this.currency = params['currency'] === undefined ? 'USD' : params['currency'].trim();
        this.transactionCurrency = params['transactionCurrency'] === undefined ? '' : params['transactionCurrency'].trim();
        this.termsColumnName = params['termsColumnName'] === undefined ? 'Service' : params['termsColumnName'].trim();
        this.termsColumn = params['termsColumn'] === undefined ? 'service.keyword' : params['termsColumn'].trim();
        this.service = (params['service'] !== '' && params['service'] !== undefined) ? params['service'].split(',') : [];
        if (params['terms'] === undefined)
          this.terms = ["service.keyword"];
        else
          this.terms = (params['terms'] !== '') ? params['terms'].split(',') : [];
        this.termSize = (params['termSize'] !== '' && params['termSize'] !== undefined) ? parseInt(params['termSize']) : 1000;
        this.acquirer = (params['acquirer'] !== '' && params['acquirer'] !== undefined) ? params['acquirer'].split(',') : [];
        this.transactionType = (params['transactionType'] !== '' && params['transactionType'] !== undefined) ? params['transactionType'].split(',') : [];
        this.contractRelation = (params['contractRelation'] !== '' && params['contractRelation'] !== undefined) ? params['contractRelation'].split(',') : [];
        this.mcc = (params['mcc'] !== '' && params['mcc'] !== undefined) ? params['mcc'].split(',') : [];
        this.category = (params['category'] !== '' && params['category'] !== undefined) ? params['category'].split('###') : [];
        this.categoryStan = (params['categoryStan'] !== '' && params['categoryStan'] !== undefined) ? params['categoryStan'].split(',') : [];
        this.binBank = (params['binBank'] !== '' && params['binBank'] !== undefined) ? params['binBank'].split(',') : [];
        this.province = (params['province'] !== '' && params['province'] !== undefined) ? params['province'].split(',') : [];
        this.mpgsId = (params['mpgsId'] !== '' && params['mpgsId'] !== undefined) ? params['mpgsId'].split(',') : [];
        //this.binCountry = (params['binCountry'] !== '' && params['binCountry'] !== undefined) ? params['binCountry'].split('###') : [];
        this.binCountry = params['binCountry'] === undefined ? '' : params['binCountry'].trim();
        this.cardType = (params['cardType'] !== '' && params['cardType'] !== undefined) ? params['cardType'].split(',') : [];
        this.sale = (params['sale'] !== '' && params['sale'] !== undefined) ? params['sale'].split(',') : [];
        this.transactionDurationType = (params['transactionDurationType'] !== '' && params['transactionDurationType'] !== undefined) ? params['transactionDurationType'].split(',') : [];
        this.period = (params['period'] !== '' && params['period'] !== undefined) ? params['period'].split(',') : [];
        this.saleProvince = params['saleProvince'] === undefined ? '' : params['saleProvince'].trim();
        this.merchantId = params['merchantId'] === undefined ? '' : params['merchantId'].trim();
        var sBankCard = (params['bankCard'] !== '' && params['bankCard'] !== undefined) ? params['bankCard'].split(',') : [];
        if (this.service.length > 0) {
          this.loadBankCard(sBankCard);
        }
        else {
          if (sBankCard != undefined && sBankCard.length > 0)
            this.bankCard = sBankCard;
          else
            this.bankCard = [];
        }

        this.page = params['page'] === undefined ? '0' : params['page'];
        this.pageSize = params['page_size'] === undefined ? '20' : params['page_size'];
        this.first = params['first'] !== undefined ? parseInt(params['first']) : 0;
        this.numberLastYear = params['numberLastYear'] !== undefined ? parseInt(params['numberLastYear']) : 0;
        this.isGrowth = params['isGrowth'] !== undefined ? (params['isGrowth'] == "true") : false;
        this.viewTestData = params['viewTestData'] !== undefined ? (params['viewTestData'] == "true") : false;
        this.matchMerchantActiveDate = params['matchMerchantActiveDate'] !== undefined ? (params['matchMerchantActiveDate'] == "true") : false;
        this.vat = params['vat'] !== undefined ? (params['vat'] == "true") : false;
        this.transactionState = params['transactionState'] === undefined ? 'SUCCESS' : params['transactionState'].trim();
        this.sumResult = (params['sumResult'] !== '' && params['sumResult'] !== undefined) ? params['sumResult'].split(',') : [];
        this.sortBy = params['sortBy'] === undefined ? 'Default' : params['sortBy'];
        if (this.sumResult.length == 0) {
          for (var i = 0; i < this.lstSumResult.length; i++) {
            if (this.lstSumResult[i].checked == true) {
              this.sumResult.push(this.lstSumResult[i].value);
            }
          }
        }

        this.displayColumn = (params['displayColumn'] !== '' && params['displayColumn'] !== undefined) ? params['displayColumn'].split(',') : ["NumberOfTransaction", "SaleAmount"];
        this.gridViewType = params['gridViewType'] === undefined ? 'row' : params['gridViewType'].trim();
        console.log("numberLastYear |", this.numberLastYear);
        if (params.fromDate) {
          let fromDateArr = params.fromDate.split("/");
          let fromDateMMddYYYY = fromDateArr[1] + "/" + "01" + "/" + fromDateArr[2];
          this.fromDate = new Date(fromDateMMddYYYY);
        } else {
          this.fromDate = new Date("01/01/" + year);
        }

        if (params.toDate) {
          let toDateArr = params.toDate.split("/");
          var monthCache = parseInt(toDateArr[1]);
          var yearCache = parseInt(toDateArr[2]);
          var date = 30;
          if (monthCache == 0 || monthCache == 2 || monthCache == 4 || monthCache == 6 ||
            monthCache == 7 || monthCache == 9 || monthCache == 11)
            date = 31;
          else if (monthCache == 1) {
            if (yearCache % 4 == 0)
              date = 29;
            else
              date = 28;
          }

          let toDateMMddYYYY = toDateArr[1] + "/" + "01" + "/" + toDateArr[2];
          this.toDate = new Date(toDateMMddYYYY);
        } else {
          //this.toDate = new Date(year, 11, 31);
          this.toDate = new Date(year, 11, 1);
        }
        this.currentYear = this.toDate.getFullYear();

        if (params['execute'] == "1" && params['favoriteViewName'] != undefined) {
          this.favoriteViewName = params['favoriteViewName'];
        }
        this.fromDateControl = this.fromDate;
        this.toDateControl = this.toDate;
      });

    //console.log('url=' + this.router.url);
  }

  getMonths(fromDate: Date, toDate: Date) {
    const fromYear = fromDate.getFullYear();
    const fromMonth = fromDate.getMonth();
    const toYear = toDate.getFullYear();
    const toMonth = toDate.getMonth();
    const months = [];

    for (let year = fromYear; year <= toYear; year++) {
      let monthNum = year === fromYear ? fromMonth : 0;
      const monthLimit = year === toYear ? toMonth : 11;

      for (; monthNum <= monthLimit; monthNum++) {
        let month = monthNum + 1;
        months.push({ year, month });

      }
    }
    return months;
  }

  convertMonth(i) {
    var kq = "Jan";
    switch (i) {
      case 1:
        kq = "Jan";
        break;
      case 2:
        kq = "Feb";
        break;
      case 3:
        kq = "Mar";
        break;
      case 4:
        kq = "Apr";
        break;
      case 5:
        kq = "May";
        break;
      case 6:
        kq = "Jun";
        break;
      case 7:
        kq = "Jul";
        break;
      case 8:
        kq = "Aug";
        break;
      case 9:
        kq = "Sep";
        break;
      case 10:
        kq = "Oct";
        break;
      case 11:
        kq = "Nov";
        break;
      case 12:
        kq = "Dec";
        break;
    }
    return kq;
  }

  searchData() {
    this.fromDate = this.fromDateControl;
    this.toDate = this.toDateControl;
    var years = this.toDate.getFullYear() - this.fromDate.getFullYear();
    if (0 <= years && years <= 1) {
      var months = this.getMonths(this.fromDate, this.toDate);
      this.monthList = [];
      for (var i = 0; i < months.length; i++)
        this.monthList.push(this.convertMonth(months[i].month) + "-" + months[i].year);



      this.first = 0;
      this.page = 0;
      return this.getList().subscribe(responses => {
        if (this.gridViewType == 'row') {
          this.listData = responses.data;
          this.listDataByColumn = [];
        }
        else {
          this.listData = [];
          this.listDataByColumn = responses.data;
          this.makeRowsSameHeight();
        }
        //this.tableDataTotal = 100; // responses.response.data.total;

      });
    }
    else {
      alert("Only supports a period of 2 years");
    }
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-datatable-scrollable-wrapper').length) {
        let wrapper = document.getElementsByClassName('p-datatable-scrollable-wrapper');
        for (var i = 0; i < wrapper.length; i++) {
          let w = wrapper.item(i) as HTMLElement;
          let frozen_rows: any = w.querySelectorAll('.p-datatable-frozen-view tr');
          let unfrozen_rows: any = w.querySelectorAll('.p-datatable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + "px";
            }
            else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + "px";
            }
          }
        }
      }
    });
  }

  downloadDataUPOS(){
    const params = {
      'type': 'DOWNLOAD',
      'type_search': 'LIKE',
      'keyword': this.keyword ? this.keyword.trim() : '',
      'service': this.service.join(','),
      'bankCard': this.bankCard == undefined ? '' : this.bankCard.join(','),
      'terms': this.gridViewType == 'row' ? this.terms.join(',') : this.termsColumn,
      'termSize': this.termSize,
      'acquirer': this.acquirer.join(','),
      'province': this.province.join(','),
      'cardType': this.cardType.join(','),
      'mpgsId': this.mpgsId.join(','),
      'binCountry': this.binCountry,
      'merchantId': this.merchantId.trim(),
      'transactionType': this.transactionType == undefined ? '' : this.transactionType.join(','),
      'contractRelation': this.contractRelation == undefined ? '' : this.contractRelation.join(','),
      'mcc': this.mcc == undefined ? '' : this.mcc.join(','),
      'category': this.category == undefined ? '' : this.category.join('###'),
      'categoryStan': this.categoryStan == undefined ? '' : this.categoryStan.join(','),
      'binBank': this.binBank == undefined ? '' : this.binBank.join(','),
      'sale': this.sale.join(','),
      'saleProvince': this.saleProvince,
      'transactionDurationType': this.transactionDurationType.join(','),
      'period': this.period.join(','),
      'transactionState': this.transactionState,
      'viewTestData': this.viewTestData,
      'matchMerchantActiveDate': this.matchMerchantActiveDate,
      'vat': this.vat,
      'bankPartnerName': this.bankPartnerName,
      'page': this.page,
      'page_size': this.pageSize,
      'fromDate': this.datePipe.transform(this.fromDate, "dd/MM/yyyy"),
      'toDate': this.datePipe.transform(this.toDate, "dd/MM/yyyy"),
      'numberLastYear': this.numberLastYear,
      'isGrowth': this.isGrowth,
      'sumResult': this.gridViewType == 'row' ? this.sumResult.join(',') : this.displayColumn.join(','),
      'termsColumn': this.termsColumn.trim(),
      'currency': this.currency,
      'transactionCurrency': this.transactionCurrency,
      'exchangeRate': this.exchangeRate,
      'gridViewType': this.gridViewType.trim(),
      'sortBy': this.sortBy,
      'excelTitle': this.favoriteViewName
    };
    this.paymentOPFeeService.dowloadUposAnalysisReport(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }
  downloadData() {
    const params = {
      'type': 'DOWNLOAD',
      'type_search': 'LIKE',
      'keyword': this.keyword ? this.keyword.trim() : '',
      'service': this.service.join(','),
      'bankCard': this.bankCard == undefined ? '' : this.bankCard.join(','),
      'terms': this.gridViewType == 'row' ? this.terms.join(',') : this.termsColumn,
      'termSize': this.termSize,
      'acquirer': this.acquirer.join(','),
      'province': this.province.join(','),
      'cardType': this.cardType.join(','),
      'mpgsId': this.mpgsId.join(','),
      //'binCountry': this.binCountry.join('###'),
      'binCountry': this.binCountry,
      'merchantId': this.merchantId.trim(),
      'transactionType': this.transactionType == undefined ? '' : this.transactionType.join(','),
      'contractRelation': this.contractRelation == undefined ? '' : this.contractRelation.join(','),
      'mcc': this.mcc == undefined ? '' : this.mcc.join(','),
      'category': this.category == undefined ? '' : this.category.join('###'),
      'categoryStan': this.categoryStan == undefined ? '' : this.categoryStan.join(','),
      'binBank': this.binBank == undefined ? '' : this.binBank.join(','),
      'sale': this.sale.join(','),
      'saleProvince': this.saleProvince,
      'transactionDurationType': this.transactionDurationType.join(','),
      'period': this.period.join(','),
      'transactionState': this.transactionState,
      'viewTestData': this.viewTestData,
      'matchMerchantActiveDate': this.matchMerchantActiveDate,
      'vat': this.vat,
      'bankPartnerName': this.bankPartnerName,
      'page': this.page,
      'page_size': this.pageSize,
      'fromDate': this.datePipe.transform(this.fromDate, "dd/MM/yyyy"),
      'toDate': this.datePipe.transform(this.toDate, "dd/MM/yyyy"),
      'numberLastYear': this.numberLastYear,
      'isGrowth': this.isGrowth,
      'sumResult': this.gridViewType == 'row' ? this.sumResult.join(',') : this.displayColumn.join(','),
      'termsColumn': this.termsColumn.trim(),
      'currency': this.currency,
      'transactionCurrency': this.transactionCurrency,
      'exchangeRate': this.exchangeRate,
      'gridViewType': this.gridViewType.trim(),
      'sortBy': this.sortBy,
      'excelTitle': this.favoriteViewName
    };
    this.paymentOPFeeService.downloadExcel(params).subscribe(data => {
      this.global.downloadEmit.emit(true);
    });
  }

  onChangeFromDate(fromDate: Date) {    
    this.fromDateControl = new Date(fromDate.getFullYear(), fromDate.getMonth(), 1);
    //this.searchData();
  }

  onChangeToDate(toDate: Date) {
    this.toDateControl = new Date(toDate.getFullYear(), toDate.getMonth(), 1);
    //this.searchData();
  }

  checkValidateMonth(value: number, isOld: number): boolean {
    var kq = false;
    if (this.fromDate.getFullYear() < this.toDate.getFullYear()) {
      // fromdate: 06/2022
      // todate: 09/2023
      if (isOld == 0) // old
      {
        if (value >= this.fromDate.getMonth())
          kq = true;
      }
      else {
        if (value <= this.toDate.getMonth())
          kq = true;
      }
    }
    else {
      if (isOld == 0)
        kq = false;
      else {
        if (this.fromDate.getMonth() <= value && value <= this.toDate.getMonth())
          kq = true;
      }
    }
    return kq;
  }

  checkValidateMonthRow(lstData, index): boolean {
    var kq = false;
    if (lstData.length == 12) {
      if (lstData.length > index) {
        if (this.fromDate.getMonth() <= index && index <= this.toDate.getMonth())
          kq = true;
      }
    }
    else if (lstData.length == 24) {
      if (lstData.length > index) {
        if (this.fromDate.getMonth() <= index && index <= this.toDate.getMonth() + 12)
          kq = true;
      }
    }
    return kq;
  }

  percentage(value): String {
    if (value == 1)
      return '100%';
    else if (value == 0 || value == '0')
      return '';
    else if (value == 'NaN' || value == 'Infinity' || value == '-Infinity')
      return '';
    else {
      if ((value * 100).toFixed(2) == "0.00") {
        if ((value * 100).toFixed(3) == "0.000") {
          return (value * 100).toFixed(4) + "%";
        }
        else
          return (value * 100).toFixed(3) + "%";
      }
      else
        return (value * 100).toFixed(2) + "%";
    }
  }

  colSpan(): number {
    var number = 1;
    if (this.displayColumn.indexOf('PercentNumberOfTransaction') > -1)
      number++
    if (this.displayColumn.indexOf('PercentSaleAmount') > -1)
      number++
    if (this.displayColumn.indexOf('OnePayFeeRevenue') > -1)
      number++
    if (this.displayColumn.indexOf('SaleAmount') > -1)
      number++
    return number;
  }

  colSpanTotal(): number {
    var number = 1;
    if (this.displayColumn.indexOf('PercentNumberOfTransaction') > -1)
      number++
    if (this.displayColumn.indexOf('PercentSaleAmount') > -1)
      number++
    if (this.displayColumn.indexOf('OnePayFeeRevenue') > -1)
      number++
    if (this.displayColumn.indexOf('SaleAmount') > -1)
      number = number + 2;
    if (this.checkAvg())
      number++;
    //   number = number + 4;
    return number;
  }

  checkAvg(): boolean {
    if (this.termsColumn != null && (this.termsColumn == 'partnerName.keyword' ||
    this.termsColumn == 'partnerShortName.keyword' ||
    this.termsColumn == 'merchantId.keyword')
    )
      return true;
    else
      return false;
  }

  openDialogTransaction() {
    this.displayDialogTransaction = true;
  }

  onDialogTransactionClose(event) {
    this.displayDialogTransaction = event.display;
  }

  colSpanGroup(): number {
    var number = 3;
    number = number + (this.toDate.getFullYear() - this.fromDate.getFullYear()) * 12 + this.toDate.getMonth() - this.fromDate.getMonth();
    return number;
  }

  loadMccGroup() {
    this.paymentOPFeeService.lstMccGroup().subscribe(res => {
      // this.mccList = res.mcc.map((item) => ({ label: item, value: item }));
      // this.categoryList = res.category.map((item) => ({ label: item, value: item }));
      // this.categoryStanList = res.categoryStan.map((item) => ({ label: item, value: item }));

      this.groupMCC = res.groupMCC;

      this.mccList = [...new Set(this.groupMCC.map(item => item.mcc))].sort().map((item) => ({ label: item, value: item }));
      this.categoryList = [...new Set(this.groupMCC.map(item => item.mccIportal))].sort().map((item) => ({ label: item, value: item }));
      this.categoryStanList = [...new Set(this.groupMCC.map(item => item.mccStan))].sort().map((item) => ({ label: item, value: item }));

      this.provinceList = res.province.map((item) => ({ label: item.name, value: item.name }));
      this.mpgsIdList = res.mpgs.map((item) => ({ label: item.name, value: item.id, payChannel: item.payChannel }));
      this.mpgsIdListQT = this.mpgsIdList.filter(x => x.payChannel == 'QT');
      this.mpgsIdListND = this.mpgsIdList.filter(x => x.payChannel == 'ND');
      this.binBankList = res.binBank.map((item) => ({ label: item.name, value: item.name }));
      this.saleList = res.sale.map((item) => ({ label: item.name, value: item.name, province: item.province }));
      this.saleListBinding = this.saleList;
    })
  }

  loadProvinceByGroup(selectedGroup: any){
    switch (selectedGroup) {
    case 'MB':
      this.provinceList = this.lstGroupProvinceMB.map((item) => ({ label: item.name, value: item.name }));
      this.province = this.provinceList.map(p => p.value);
      this.changeValue();
      break;
    case 'MN':
      this.provinceList = this.lstGroupProvinceMN.map((item) => ({ label: item.name, value: item.name }));
      this.province = this.provinceList.map(p => p.value);
      this.changeValue();
      break;
    default:
      this.loadMccGroup();
      this.province = [];
      break;
    }
  }

  clearFilter() {
    this.keyword = '';
    this.service = [];
    this.acquirer = [];
    this.transactionType = [];
    this.contractRelation = [];
    this.mcc = [];
    this.category = [];
    this.categoryStan = [];
    this.province = [];
    this.mpgsId = [];
    this.binCountry = '';
    this.cardType = [];
    this.binBank = [];
    this.merchantId = '';
    this.bankCard = [];
    this.transactionCurrency = '';
    this.sale = [];
    this.saleProvince = '';
    this.viewTestData = false;
    this.matchMerchantActiveDate = false;
    this.transactionState = 'SUCCESS';
    this.contractRelationChange();
    this.loadBankCard([]);
    this.changeSaleProvince();
    this.changeCategory();
    this.groupProvince = '';
  }

  getSelectedOptionText(event) {
    this.loadBankPartnerName();
  }

  loadBankPartnerName() {
    this.bankPartnerName = '';
    for (var i = 0; i < this.bankCard.length; i++) {
      console.log(this.bankCardList.find(x => x.sId == this.bankCard[i]).partnerId);
      if (this.bankPartnerName == '')
        this.bankPartnerName = this.bankCardList.find(x => x.sId == this.bankCard[i]).partnerId;
      else
        this.bankPartnerName = this.bankPartnerName + "," + this.bankCardList.find(x => x.sId == this.bankCard[i]).partnerId;
    }
  }

  openFavoriteViewList() {
    this.displayFavoriteView = true;
  }

  closeFavoriteViewList() {
    this.displayFavoriteView = false;
  }

  loadFavoriteView() {
    this.paymentOPFeeService.lstFavoriteView().subscribe(res => {
      this.lstFavoriteView = res;
      this.lstFavoriteView = this.lstFavoriteView.concat(this.lstFavoriteViewFixed);
    })
  }

  deleteFavoriteView(id: number) {
    if (confirm('Are you sure you want to delete it?')) {

      const params = {
        'id': id
      };

      this.paymentOPFeeService.deleteFavoriteView(params).subscribe(res => {
        if (res.status == true) {
          let index = this.lstFavoriteView.findIndex(d => d.id === id);
          this.lstFavoriteView.splice(index, 1);
        }
      })
    }
  }

  roundData(value: number): number {
    return Math.round(value);
  }

  changeValue() {
    this.favoriteViewName = "";
  }

  // loadAcqByBankPartner()
  // {    
  //   if(this.service.join(',') == 'QT')
  //   {
  //      if(this.bankCard.length > 0)
  //      {

  //       this.acquirerListDetail = this.acquirerListQT.find(x => x.name.toLowerCase() == this.bankCard[i]).partnerId;
  //      }
  //   }

  // }

  // checkReferralITA(name, total) {
  //   if (name != null && name != undefined) {
  //     if (name.toLowerCase().includes("referral partner fee") || name.toLowerCase().includes("ita fee")) {
  //       if (total > 0)
  //         return true;
  //       else
  //         return false;
  //     }
  //     else
  //       return true;
  //   }
  //   return false;
  // }

  checkReferralITA(name, total) {
    if (total != 0)
      return true;
    else {
      if (name != null && name != undefined && (name.toLowerCase().includes("growth of txn volume") || name.toLowerCase().includes("exchange rate")))
        return true;
      return false;
    }
    //return true;
  }

  changeSaleProvince() {
    if (this.saleProvince == '')
      this.saleListBinding = this.saleList;
    else
      this.saleListBinding = this.saleList.filter(x => x.province == this.saleProvince);
    this.changeValue();
  }

  changeCategory() {
    var arrayCategory = this.category.join('###');
    // const params = {
    //   'category': arrayCategory
    // };
    // this.paymentOPFeeService.getCagegoryStan_MCC(params).subscribe(res => {
    //   this.mccList = res.mcc.map((item) => ({ label: item, value: item }));      
    //   this.categoryStanList = res.categoryStan.map((item) => ({ label: item, value: item }));      
    // })  

    if (this.category.length == 0) {
      this.mccList = [...new Set(this.groupMCC.map(item => item.mcc))].sort().map((item) => ({ label: item, value: item }));
      this.categoryStanList = [...new Set(this.groupMCC.map(item => item.mccStan))].sort().map((item) => ({ label: item, value: item }));
    }
    else {
      this.mccList = [...new Set(this.groupMCC.filter(x => arrayCategory.includes(x.mccIportal)).map(item => item.mcc))].sort().map((item) => ({ label: item, value: item }));
      this.categoryStanList = [...new Set(this.groupMCC.filter(x => arrayCategory.includes(x.mccIportal)).map(item => item.mccStan))].sort().map((item) => ({ label: item, value: item }));
    }
    this.changeValue();
  }
}
