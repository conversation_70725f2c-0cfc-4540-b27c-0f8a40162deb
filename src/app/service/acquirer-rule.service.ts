import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';


@Injectable({
    providedIn: 'root'
})

export class AcquirerRuleService {
    constructor(private _http: HttpClient) { }
    private url_get_infomation_config_acq = 'acquirer-rule/get-info-config';
    private url_acq_rule_group = 'system-management/acq-rule-group';
    private url_merchant_acq_group = 'system-management/merchant-group-config';
    private url_acq_rule_group_by_approval_id = 'system-management/acq-rule-group_by_approval_id';
    private url_list_merchant_id = 'system-management/merchant-ids';
    private url_list_group_default= 'system-management/default-group-ids';
    private URL_ACQ_GROUP_APPROVAL = 'system-management/acq-group-approval/approval';
    private URL_ACQ_GROUP_GET_APPROVAL_BY_ID = 'system-management/acq-group-approval/';
    private url_download = 'acquirer-rule-group/download';
    private URL_GET_ACQ_GROUP_NAME = 'system-management/list-acq-rule-group-name';

    //
    private URL_LIST_MERCHANT_CONFIG_RULE_APPROVE = 'system-management/merchant-config-rule-approval';
    private URL_MERCHANT_CONFIG_RULE_APPROVAL_BY_ID = 'system-management/merchant-config-rule-approval/';
    private URL_MERCHANT_CONFIG_RULE_APPROVAL = 'system-management/merchant-config-rule-approval/approval';
    private URL_GET_ACQ_RULE_HISTORY = 'system-management/acq-rule/history';
    private URL_GET_MERCHANT_DETAIL = 'system-management/merchant/';
    private URL_GET_MERCHANT_CONFIG_HISTORY = 'system-management/acq-rule/merchant/history';
    private URL_GET_MERCHANT_SWITCH_HISTORY = 'hot-switch-cyber-mpgs/merchant/history';
    private URL_DOWNLOAD_MERCHANT_CONFIG_HISTORY = 'system-management/acq-rule/merchant/download';

    GetInfomationConfigAcqRule(): Observable<any> {
        return this._http.get(this.url_get_infomation_config_acq);
    }

    AddAcqRuleGroup(body: any): Observable<any> {
        return this._http.post(this.url_acq_rule_group, body);
    }

    UpdateAcqRuleGroup(body: any): Observable<any> {
        if (!body.id || body.id == 0) {
            return null;
        }
        return this._http.put(this.url_acq_rule_group + "/" + body.id, body);
    }
    DeleteAcqRuleGroup(id)
        : Observable<any> {
        if (!id || id == 0) {
            return null;
        }
        return this._http.delete(this.url_acq_rule_group + "/" + id);
    }
    GetAcqRuleGroup(id, isPending): Observable<any> {
        if (!id || id == 0) {
            return null;
        }
        return this._http.get(this.url_acq_rule_group + "/" + id, {
            params: {
                "is_pending": isPending ?? false,
            }
        });
    }
    GetAcqRuleGroupByApprovalId(id)
        : Observable<any> {
        if (!id || id == 0) {
            return null;
        }
        return this._http.get(this.url_acq_rule_group_by_approval_id + "/" + id);
    }
    SearchAcqRuleGroup(params: HttpParams): Observable<any> {

        return this._http.get(this.url_acq_rule_group, { params: params });
    }

    SearchMerchantAcqGroup(params: HttpParams): Observable<any> {

        return this._http.get(this.url_merchant_acq_group, { params: params });
    }

    AddMerchantAcqGroup(body: any): Observable<any> {

        return this._http.post(this.url_merchant_acq_group, body);
    }

    GetMerchantBackUps(id: any): Observable<any> {

        return this._http.get(this.url_merchant_acq_group + "/" + id + "/back_ups");
    }


    GetAllMerchantId(params: HttpParams): Observable<any> {
        return this._http.get(this.url_list_merchant_id, { params: params });
    }

    GetAllGroupDefault(): Observable<any> {
        return this._http.get(this.url_list_group_default);
    }
    patchApproval(body): Observable<any> {
        return this._http.patch(this.URL_ACQ_GROUP_APPROVAL, body);
    }
    patchReject(body): Observable<any> {
        return this._http.patch(this.URL_ACQ_GROUP_APPROVAL, body);
    }
    getAcqGroupById(id: string): Observable<any> {
        return this._http.get(this.URL_ACQ_GROUP_GET_APPROVAL_BY_ID + id);
    }
    download(params: HttpParams): Observable<any> {
        return this._http.get(this.url_download, { params: params });
    }

    //approval merchant config rule
    getAllMerchantConfigRuleApproval(keyword: string, page: number, page_size: number): Observable<any> {
        keyword = encodeURIComponent(keyword);
        return this._http.get(`${this.URL_LIST_MERCHANT_CONFIG_RULE_APPROVE}?keyword=${keyword}&page=${page}&page_size=${page_size}`);
    }
    getMerchantConfigRuleApprovalById(id: string): Observable<any> {
        return this._http.get(this.URL_MERCHANT_CONFIG_RULE_APPROVAL_BY_ID + id);
    }
    patchMerchantConfigRuleApproval(body): Observable<any> {
        return this._http.patch(this.URL_MERCHANT_CONFIG_RULE_APPROVAL, body);
    }
    patchMerchantConfigRuleReject(body): Observable<any> {
        return this._http.patch(this.URL_MERCHANT_CONFIG_RULE_APPROVAL, body);
    }

    getListAcquirerRuleGroupName(): Observable<any> {
        return this._http.get(this.URL_GET_ACQ_GROUP_NAME);
    }
    //end
    getListAcquirerRuleHistory(params: HttpParams): Observable<any> {
        return this._http.get(this.URL_GET_ACQ_RULE_HISTORY, { params: params });
    }

    getConfigsBackup(id: any): Observable<any> {
        return this._http.get(this.url_merchant_acq_group + "/" + id + "/back-up");
    }

    getMerchantDetail(id: any): Observable<any> {
        return this._http.get(this.URL_GET_MERCHANT_DETAIL + "/" + id);
    }

    getMerchantConfigHistory(params: HttpParams): Observable<any> {
        return this._http.get(this.URL_GET_MERCHANT_CONFIG_HISTORY, { params: params });
    }

    getMerchantSwitchHistory(params: HttpParams): Observable<any> {
        return this._http.get(this.URL_GET_MERCHANT_SWITCH_HISTORY, { params: params });
    }

    downloadMerchantConfigHistory(params: HttpParams): Observable<any> {
        return this._http.get(this.URL_DOWNLOAD_MERCHANT_CONFIG_HISTORY, { params: params });
    }
}
