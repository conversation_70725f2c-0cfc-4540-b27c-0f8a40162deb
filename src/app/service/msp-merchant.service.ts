import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MspBinGroup, MspMerchant } from 'app/model/msp-merchant';

@Injectable()
export class MspMerchantService {

  private URL_MSP_MERCHANT = 'msp/merchant';
  private URL_MSP_MERCHANT_APPROVAL_REQUEST = 'msp/merchant-approval';
  private URL_MSP_MERCHANT_APPROVAL_CHANGE_STATE = 'msp/merchant-approval/change-state';
  private URL_MSP_MERCHANT_CHECK_APPROVAL = 'msp/check-approval';
  private URL_MSP_CLIENT = 'msp/client';
  private URL_MSP_INSTALLMENT = 'msp/installment';
  private URL_MSP_INSTALLMENT_FEE = 'msp/installment-fee';
  private URL_MSP_CHECK_MERCHANT = 'msp/merchant/check';
  private URL_MSP_CHECK_TRANS_NOT_VIETINQR = 'msp/merchant/check-trans-not-vietinqr';
  private URL_MSP_CHECK_MERCHANT_CONFIG_VIETINQR = 'msp/merchant/check-merchant-config-vietinqr';
  private URL_MSP_HASH_CODE = 'msp/msp-merchant/generate-hash-code';
  private URL_MSP_ACCESS_CODE = 'msp/msp-merchant/generate-access-code';
  private URL_MSP_BIN_GROUP = 'msp/bin-group';
  private URL_BIN_BANK = 'msp/bin-list';
  private REFERRAL_PARTNER_DROPDOWN = 'dropdown-referral-partners';
  public mspInstallmentFee: any;
  private URL_DIRECT_DEBIT = 'msp/direct-debit';
  private URL_GET_PARTNER = 'list-partner';

  constructor(private _http: HttpClient) {
  }

  searchReferralPartner(): Observable<any> {
    return this._http.get(this.REFERRAL_PARTNER_DROPDOWN, {});
  }

  getMspMerchants(keyword: string, page: number, page_size: number): Observable<any> {
    return this._http.get(`${this.URL_MSP_MERCHANT}?keyword=${keyword}&page=${page}&page_size=${page_size}`);
  }

  checkMspMerchants(merchant_id: string, merchant_name: string): Observable<any> {
    return this._http.get(`${this.URL_MSP_CHECK_MERCHANT}/${merchant_id}/${merchant_name}`);
  }

  checkTransNotVIETINQR(merchant_id: string): Observable<any> {
    return this._http.get(`${this.URL_MSP_CHECK_TRANS_NOT_VIETINQR}/${merchant_id}`);
  }

  checkMerchantConfigVIETINQR(merchant_id: string): Observable<any> {
    return this._http.get(`${this.URL_MSP_CHECK_MERCHANT_CONFIG_VIETINQR}/${merchant_id}`);
  }

  getMspMerchantById(merchant_id: string): Observable<MspMerchant> {
    return this._http.get<MspMerchant>(`${this.URL_MSP_MERCHANT}/${merchant_id}`);
  }

  checkApprovalByMerchant(merchant_id: string): Observable<any> {
    return this._http.get<any>(`${this.URL_MSP_MERCHANT_CHECK_APPROVAL}/${merchant_id}`);
  }

  upsertMspMerchant(merchant: MspMerchant): Observable<any> {
    return this._http.post(this.URL_MSP_MERCHANT, merchant);
  }

  requestMspMerchantApproval(merchant: MspMerchant): Observable<any> {
    return this._http.post(this.URL_MSP_MERCHANT_APPROVAL_REQUEST, merchant);
  }

  deleteMspMerchant(merchant_id: string): Observable<any> {
    return this._http.delete(`${this.URL_MSP_MERCHANT}/${merchant_id}`);
  }

  changeState(body: any): Observable<any> {
    return this._http.put(this.URL_MSP_MERCHANT_APPROVAL_CHANGE_STATE, body);
  }

  getMspClients(): Observable<any> {
    return this._http.get(`${this.URL_MSP_CLIENT}`);
  }

  getMspInstallments(): Observable<any> {
    return this._http.get(`${this.URL_MSP_INSTALLMENT}`);
  }

  setMspInstallmentsFeeData(data: any) {
    this.mspInstallmentFee = data;
  }

  getMspInstallmentsFeeData() {
    return this.mspInstallmentFee;
  }

  getMspInstallmentsFee(): Observable<any> {
    if (this.mspInstallmentFee && this.mspInstallmentFee.list && this.mspInstallmentFee.list.length > 0) {
      return this.mspInstallmentFee;
    } else {
      return this._http.get(`${this.URL_MSP_INSTALLMENT_FEE}`);
    }
  }

  getHashCode(): Observable<any> {
    return this._http.get(`${this.URL_MSP_HASH_CODE}`);
  }

  getAccessCode(): Observable<any> {
    return this._http.get(`${this.URL_MSP_ACCESS_CODE}`);
  }

  getMspAllBinGroup(): Observable<any> {
    return this._http.get(`${this.URL_MSP_BIN_GROUP}`);
  }

  upsertMspBinGroup(binGroupList : MspBinGroup): Observable<any> {
    return this._http.put(this.URL_MSP_BIN_GROUP, binGroupList);
  }

  deleteMspBinGroup(binGroupId: string): Observable<any> {
    return this._http.delete(`${this.URL_MSP_BIN_GROUP}/${binGroupId}`);
  }

  getBinBankList(): Observable<any> {
    return this._http.get(`${this.URL_BIN_BANK}`);
  }

  getDirectDebitBanks(): Observable<any> {
    return this._http.get(`${this.URL_DIRECT_DEBIT}`);
  }

  getDropdownPartner() {
    return this._http.get(`${this.URL_GET_PARTNER}`);
  }
}
