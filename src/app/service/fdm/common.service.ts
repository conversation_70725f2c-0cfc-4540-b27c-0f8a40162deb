import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FraudType } from 'app/model/fdm/fraud-type';
import { Merchant } from 'app/model/fdm/merchant';
import { Group } from 'app/model/group';
import { MCC } from 'app/model/mcc';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CommonService {

  constructor(private _http: HttpClient) { }

  listMCC(columns: string[]): Observable<MCC[]> {
    return this._http.get<MCC[]>("api/v2/fdm/domestic/common/mcc?columns=" + columns.join(","));
  }

  listGroup(columns: string[]): Observable<Group[]> {
    return this._http.get<Group[]>("api/v2/fdm/domestic/common/group?columns=" + columns.join(","));
  }

  listMerchant(columns: string[]): Observable<Merchant[]> {
    return this._http.get<Merchant[]>("api/v2/fdm/domestic/common/merchant?columns=" + columns.join(","));
  }

  listMember(): Observable<any> {
    return this._http.get("api/v2/fdm/domestic/transactionSearch/filter/pic", {});
  }

  listReviewFraudType(columns: string[]): Observable<FraudType[]> {
    return this._http.get<FraudType[]>("api/v2/fdm/domestic/common/fraud-type/review?columns=" + columns.join(","));
  }

  listBlockFraudType(columns: string[]): Observable<FraudType[]> {
    return this._http.get<FraudType[]>("api/v2/fdm/domestic/common/fraud-type/block?columns=" + columns.join(","));
  }

  listPartner(columns: string[]): Observable<any[]> {
    return this._http.get<any[]>("api/v2/fdm/domestic/common/partner?columns=" + columns.join(","));
  }

}
