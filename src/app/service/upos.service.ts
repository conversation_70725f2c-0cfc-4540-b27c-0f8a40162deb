import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()

export class UposService {

    constructor(private _http: HttpClient) {
    }

    // --------- Start Transaction Search ---------
    private url_transaction_search = 'upos/transaction';
    private api_download_transaction = 'upos/transaction/file';
    // --------- End Transaction Search -----------

    // --------- Start Report Transaction  --------
    private url_search_report = 'upos/report';
    private url_download_report = 'upos/report/file';
    private url_download_sale_report = 'upos/report/sale/file';
    // --------- End Report Transaction  --------

    // --------- Start Refund Approval -----------
    private url_refund_approval = 'upos/refund-approval-qr';
    private url_refund_approval_card = 'upos/refund-approval-card-ita';
    private url_refund_approval_manual = 'upos/refund-manual';
    // --------- End Refund Approval -----------
    private qr_payment_purchase_detail = 'upos/qr-payment-purchase';
    private vietqr_payment_detail = 'upos/vietqr-payment-detail';
    private card_payment_purchase_detail = 'upos/card-payment-purchase';
    private ita_purchase_detail = 'upos/ita-payment-purchase';
    private api_request_refund_ita = 'upos/ita-request-refund';
    private api_refund_approval_ita = 'upos/ita-approval-refund';
    private url_response_code = 'upos/response-code';
    private api_dowload_refund_template = '';
    private api_domestic_issuer_list = 'upos/domestic-issuer-list';
    private api_domestic_cardtype_list = 'upos/domestic-card-type-list';
    private api_vietqr_force_sync = '/upos/vietqr-force-sync';

    getUposTransactionList(params: HttpParams): Observable<any> {
        return this._http.get(this.url_transaction_search, { params: params });
    }

    downloadUposTransactionList(body: any): Observable<any> {
        return this._http.post(this.api_download_transaction, body);
    }

    getUposReportList(params: HttpParams): Observable<any> {
        return this._http.get(this.url_search_report, { params: params });
    }
    getListPartner(): Observable<any> {
        return this._http.get('upos/partner-list');
    }

    getResponseCode(): Observable<any> {
        return this._http.get(this.url_response_code);
    }
    downloadReport(body: any): Observable<any> {
        return this._http.post(this.url_download_report, body);
    }
    downloadSaleReport(body: any): Observable<any> {
        return this._http.post(this.url_download_sale_report, body);
    }

    patchRefundApprovalQR(body: any, id: string): Observable<any> {
        return this._http.patch(this.url_refund_approval + '/' + id, body);
    }

    patchRefundApprovalCardITA(body: any, id: string): Observable<any> {
        return this._http.patch(this.url_refund_approval_card + '/' + id, body);
    }

    patchRefundApproval(body: any, id: string): Observable<any> {
        return this._http.post(this.url_refund_approval_manual + '/' + id, body);
    }

    getDetailVietQR(id: string): Observable<any> {
        const params = new HttpParams().set('id', id);
        return this._http.get(this.vietqr_payment_detail + "/" + id, { params: params });
    }

    getDetailPurchseQR(id: string): Observable<any> {
        const params = new HttpParams().set('id', id);
        return this._http.get(this.qr_payment_purchase_detail + "/" + id, { params: params });
    }

    getDetailRefundQR(id: string): Observable<any> {
        const params = new HttpParams().set('id', id);
        return this._http.get(this.qr_payment_purchase_detail + "/" + id, { params: params });
    }
    
    getTransactionHistory(transaction_id: string): Observable<any> {
        const params = new HttpParams().set('transaction_id', transaction_id);
        return this._http.get(this.url_transaction_search + "/" + transaction_id + '/history', { params });
    }

    getTransactionList(params: HttpParams): Observable<any> {
        return this._http.get(this.url_transaction_search, { params: params });
    }

    getTransactionCardPurchaseID(transaction_id: string): Observable<any> {
        return this._http.get(this.card_payment_purchase_detail + "/" + transaction_id, {});
    }

    getTransactionCardVoidID(transaction_id: string): Observable<any> {
        return this._http.get(this.card_payment_purchase_detail + "/" + transaction_id, {});
    }

    getTransactionCardRefundID(transaction_id: string): Observable<any> {
        return this._http.get(this.card_payment_purchase_detail + "/" + transaction_id, {});
    }

    getTransactionCardRequestRefundID(transaction_id: string): Observable<any> {
        return this._http.get(this.card_payment_purchase_detail + "/" + transaction_id, {});
    }

    refundApprovalCard(body: any): Observable<any> {
        return this._http.post(this.card_payment_purchase_detail, body);
    }

    requestRefundCardOrITA(body: any): Observable<any> {
        return this._http.post(this.api_request_refund_ita, body);
    }

    getDetailPurchaseInstallment(id: string): Observable<any> {
        return this._http.get(this.ita_purchase_detail + "/" + id, {});
    }

    refundApprovalITA(body: any): Observable<any> {
        return this._http.post(this.api_refund_approval_ita, body);
    }

    downloadRefundTemplate(body: any): Observable<any> {
        return this._http.post(this.api_dowload_refund_template, body);
    }

    getDomesticIssuerList(): Observable<any> {
        return this._http.get(this.api_domestic_issuer_list);
    }

    getDomesticCardTypeList(): Observable<any> {
        return this._http.get(this.api_domestic_cardtype_list);
    }

    vietQrForceSync(): Observable<any> {
        return this._http.get(this.api_vietqr_force_sync);
    }

}
