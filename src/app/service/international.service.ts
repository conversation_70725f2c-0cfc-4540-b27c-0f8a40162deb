import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class InternationalService {
    // bien chung
    constructor(private _http: HttpClient) {
    }
    // end bien chung

    // ---------- URL Order Search ----------

    private query_ordersearch = 'international/purchase';

    private download_refundApprovalsearch = 'refund/international/file';

    private download_reportSearch = 'report/international/file';

    private download_reconcileSearch = 'reconcile/international/file';

    private download_authSearch = '/payment-authentication/file';

    private download_financialsearch = 'transaction/financial/file';

    private download_ordersearch = 'international/transaction/file';

    private api_detail_ordersearch = 'international/purchase/';

    private api_detail_installment = 'installment/transaction/';

    private api_update_purchase_status = 'international/purchase/update/';

    private api_update_refund_status = 'international/transaction/update/';

    private api_ip = '/ss-trans-management/risk/ip/';

    private api_approve_refund = 'international/refund-approval/';
    /*---------- end order search ----------*/

    // ---------- URL refund international ----------
    private api_url_refund_international = 'international/refund-approval';
    private api_url_refund_detail_international = 'international/refund-approval/';
    private api_url_refund_nternational_download = 'international/refund-approval/file';
    private api_url_refund_nternational_download_v2 = 'international/refund-approval2/file';
    private api_url_refund_list_international = 'international/list-transaction';

    // V2
    private api_url_refund_international_v2 = 'international/refund-approval2';
    private api_url_refund_international_approve = 'international/refund-approval2/approve';
    private api_url_refund_international_auto = 'international/refund-approval2/auto';
    private api_url_refund_international_reject = 'international/refund-approval2/reject';
    private api_url_refund_international_update_state = 'international/refund-approval2/update';
    private api_url_refund_international_manual = 'international/refund-approval2/manual';
    private api_url_refund_international_check = 'international/refund-approval2/check-auto';
    private api_url_reverseDue_v2 = 'international/refund-approval2/reverseDue';
    /*---------- end refund international ----------*/

    // --------- Financical Transaction Search --------
    private api_url_financical = 'international/transaction';

    private api_url_financical_search = 'international/transaction/';

    private api_url_financical_download = 'international/transaction/file';

    // --------- End Financical Transaction Search --------

  // --------- Start Installment Bank Report --------

  private api_url_search_installment_bank = 'international/search-installment-bank';

  private api_url_search_installment_merchant = 'international/search-installment-merchant';

  private api_url_installment_bank_report_download = 'installment/bank/file';

  private api_url_installment_merchant_report_download = 'installment/merchant/file';

  // --------- End Installment Bank Report --------

    // --------- URL Payment Authentication ----------

    // authentication_code: string, authentication_state: string,
    private api_url_payment = 'payment-authentication';

    private api_url_payment_search = 'payment-authentication/';

    private api_status = 'app/data/status/status.json'

    // --------- END Payment Authentication ----------

    // ---------- Report ----------
    private query_report = 'report/international';
    private query_reconcile = 'reconcile/international';
    // ---------- Order ----------
    private order_approval = 'order/approval/international';

    // ---------- Intallment ----------
    private installment_transaction = 'installment/transaction/';
    private installment_batch = 'installment/batch/';
    private api_trans_refund_by_id = 'installment/transaction-refund/';
    private installment_transaction_download = 'installment/transaction/file';
    // private installment_transaction_id = 'installment/transaction/';

    //--------Authorize Transaction------
    private api_history_authorize_transaction = 'international/transaction-authorize/';
    
    private REFERRAL_PARTNER_DROPDOWN = 'dropdown-referral-partners';
    // send file to bank
    private installment_export_file_bank = 'installment/export-file-bank';

    private API_URL_GET_BANK_MERCHANT_ID = 'international-report/get-bank-merchant-id';

    getBankMerchantIdByAcq(params: HttpParams): Observable<any> {
        return this._http.get(this.API_URL_GET_BANK_MERCHANT_ID, { params: params });
    }

    getInstallmenttransaction(params: HttpParams): Observable<any> {
        return this._http.get(this.installment_transaction, { params: params });
    }
    patchInstallmenttransaction(body: any, id: string): Observable<any> {
        return this._http.patch(this.installment_transaction + id, body);
    }
    postInstallmentBatch(body: any): Observable<any> {
        return this._http.post(this.installment_batch, body);
    }

    downloadInstallmentTransaction(body: any): Observable<any> {
        return this._http.post(this.installment_transaction_download, body);
    }
    PatchRefundApproval(body: any, id: string): Observable<any> {

        return this._http.patch(this.api_approve_refund + id, body);
    }
    PatchRefundApproval2(body: any): Observable<any> {

        return this._http.post(this.api_url_reverseDue_v2, body);
    }

    Downloadlist_authsearch(body: any): Observable<any> {

        return this._http.post(this.download_authSearch, body);
    }


    Downloadlist_reconcilesearch(body: any): Observable<any> {

        return this._http.post(this.download_reconcileSearch, body);
    }


    Getlist_Financical_TransactionID(transaction_id: string): Observable<any> {
        return this._http.get(this.api_url_financical_search + transaction_id);
    }

    Getlist_Transaction_byIds(params: HttpParams): Observable<any> {
        return this._http.get(this.api_url_refund_list_international, { params: params });
    }

    Downloadlist_reportsearch(body: any): Observable<any> {

        return this._http.post(this.download_reportSearch, body);
    }

    Downloadlist_financialsearch(body: any): Observable<any> {

        return this._http.post(this.api_url_financical_download, body);
    }

  dowloadInstallmentBank(body: any): Observable<any> {

    return this._http.post(this.api_url_installment_bank_report_download, body);
  }

  dowloadInstallmentMerchant(body: any): Observable<any> {

    return this._http.post(this.api_url_installment_merchant_report_download, body);
  }

    Downloadlist_refundApprovalsearch(body: any): Observable<any> {

        return this._http.post(this.download_refundApprovalsearch, body);
    }

    Downloadlist_ordersearch(body: any): Observable<any> {

        return this._http.post(this.download_ordersearch, body);
    }

    Getlist_ordersearch(params: HttpParams): Observable<any> {

        return this._http.get(this.query_ordersearch, { params: params });
    }

    GetIPAddress(ip_address: string): Observable<any> {
        return this._http.get(this.api_ip + ip_address, {});
    }

    GetlistTransactionID(transaction_id: string): Observable<any> {
        return this._http.get(this.api_detail_ordersearch + transaction_id, {});
    }
    /**
     * Insert transaction refund ita 
     * Creator: Duynp
     * @param body 
     * @returns 
     */
    insertTransRefundIta(body: any): Observable<any> {
        return this._http.post(this.api_trans_refund_by_id,body);
    }

    GetInstallmentTransDetail(transaction_id: string): Observable<any> {
        return this._http.get(this.api_detail_installment + transaction_id, {});
    }

    UpdateTransactionStatus(params: HttpParams): Observable<any> {
        return this._http.get(this.api_update_purchase_status, {params: params});
    }

    UpdateRefundStatus(params: HttpParams): Observable<any> {
        return this._http.get(this.api_update_refund_status, {params: params});
    }

    GetlistHistoryTransaction(transaction_id: string): Observable<any> {
        // var params = new HttpParams().set('transaction_id',transaction_id);
        return this._http.get(this.api_url_financical_search + transaction_id + '/history', {});
    }
    PatchRefundInternational(body: any, transaction_id: string): Observable<any> {
        return this._http.patch(this.api_detail_ordersearch + transaction_id, body);
    }
    DoRechargeInternational(body: any, transaction_id: string): Observable<any> {
        return this._http.patch(this.api_detail_ordersearch + transaction_id, body);
    }
    DoVoidPurchaseInternational(body: any, transaction_id: string): Observable<any> {
        return this._http.patch(this.api_detail_ordersearch + transaction_id, body);
    }

    GetRefundInternational(params: HttpParams): Observable<any> {
        return this._http.get(this.api_url_refund_international, { params: params });
    }

    //refund v2
    GetRefundInternationalV2(params: HttpParams): Observable<any> {
        return this._http.get(this.api_url_refund_international_v2, { params: params });
    }

    approveOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_approve, body);
    }
    autoOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_auto, body);
    }

    rejectOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_reject, body);
    }

    UpdateStateOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_update_state, body);
    }

    manualOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_manual, body);
    }

    checkAutoOP(body: any): Observable<any> {
        return this._http.post(this.api_url_refund_international_check, body);
    }

    // --------- END REFUND SEARCH 2  ----------------

    DownloadRefundInternational(body: any,): Observable<any> {
        return this._http.post(this.api_url_refund_nternational_download, body);
    }

    DownloadRefundInternational2(body: any,): Observable<any> {
        return this._http.post(this.api_url_refund_nternational_download_v2, body);
    }


    GetRefundDetailInternational(transaction_id: string): Observable<any> {
        let params = new HttpParams().set('transaction_id', transaction_id);
        return this._http.get(this.api_url_refund_detail_international + transaction_id, {});
    }
    GetHistoryRefundDetailInternational(transaction_id: string): Observable<any> {
        return this._http.get(this.api_url_financical_search + transaction_id + '/history', {});
    }

    Getlist_Financical_Transaction_Search(params: HttpParams): Observable<any> {
        return this._http.get(this.api_url_financical, { params: params });
    }

  searchInstallmentBankReport(params: HttpParams): Observable<any> {
    return this._http.get(this.api_url_search_installment_bank, { params: params });
  }

  searchInstallmentMerchantReport(params: HttpParams): Observable<any> {
    return this._http.get(this.api_url_search_installment_merchant, { params: params });
  }


    Getlist_payment_authentication(params: HttpParams): Observable<any> {

        return this._http.get(this.api_url_payment, { params: params });
    }

    Getlist_Payment_TransactionID(transaction_id: string): Observable<any> {
        let params = new HttpParams().set('transaction_id', transaction_id);
        return this._http.get(this.api_url_payment_search + transaction_id, {});
    }

    getStatus(): Observable<any> {
        return this._http.get(this.api_status).pipe(map(status => status));
    }

    Getlist_report(params: HttpParams): Observable<any> {
        return this._http.get(this.query_report, { params: params });
    }


    Getlist_reconcile(params: HttpParams): Observable<any> {
        return this._http.get(this.query_reconcile, { params: params });
    }


    postOrder(body: any): Observable<any> {
        return this._http.post(this.order_approval, body);
    }


    // ---------- Order ----------
    private installment_bank = 'installment/bank';
    getListInstallmentBank(): Observable<any> {
        return this._http.get(this.installment_bank);
    }

    getListHistoryAuthorizeTransaction(transaction_id: string): Observable<any> {
        // var params = new HttpParams().set('transaction_id',transaction_id);
        return this._http.get(this.api_history_authorize_transaction + transaction_id + '/history', {});
    }
    searchReferralPartner(): Observable<any> {
        return this._http.get(this.REFERRAL_PARTNER_DROPDOWN, {});
    }

    postExportFileToBank(body:any): Observable<any> {
        // var params = new HttpParams().set('transaction_id',transaction_id);
        return this._http.post(this.installment_export_file_bank, body);
    }

    private partners_ipp = 'installment/partner-ipp';
    getListPartnerIpp(): Observable<any> {
        return this._http.get(this.partners_ipp);
    }
}
