export interface ContractTemplate {
  contractCode: string;
  contractName: string;
  S_VERSION: string;
  N_ID_VERSION: string;
  N_ID_TEMPLATE: string;
}

export interface SubContractTemplate {
  subContractCode: string;
  subContractName: string;
  S_VERSION: string;
  N_ID_VERSION: string;
  N_ID_TEMPLATE: string;
}

export class ContractOriginal {
  public id: string;
  public partnerId: string;
  public parentId: string;
  public contractCode: string;
  public contractName: string;
  public contractNumber: string;
  public signatureDate: string;
  public businessName: string;
  public state: string;
  public userAction: string;
  public contractType: string;
  public rangeDate: string;
  public shortname: string;
  public representative: string;
  public idForm: string;
  public idTemplate: string;
  public idVersion: string;

  constructor(data?: ContractOriginal) {
    // super();
    //   if (!data) { return; }
    this.id = data === undefined ? '' : this.getDataValue(data.id);
    this.partnerId = data === undefined ? '' : this.getDataValue(data.partnerId);
    this.parentId = data === undefined ? '' : this.getDataValue(data.parentId);
    this.contractCode = data === undefined ? '' : this.getDataValue(data.contractCode);
    this.contractName = data === undefined ? '' : this.getDataValue(data.contractName);
    this.contractNumber = data === undefined ? '' : this.getDataValue(data.contractNumber);
    this.signatureDate = data === undefined ? '' : this.getDataValue(data.signatureDate);
    this.businessName = data === undefined ? '' : this.getDataValue(data.businessName);
    this.state = data === undefined ? '' : this.getDataValue(data.state);
    this.userAction = data === undefined ? '' : this.getDataValue(data.userAction);
    this.contractType = data === undefined ? '' : this.getDataValue(data.contractType);
    this.rangeDate = data === undefined ? '' : this.getDataValue(data.rangeDate);
    this.shortname = data === undefined ? '' : this.getDataValue(data.shortname);
    this.representative = data === undefined ? '' : this.getDataValue(data.representative);
    this.idForm = data === undefined ? '' : this.getDataValue(data.idForm);
    this.idTemplate = data === undefined ? '' : this.getDataValue(data.idTemplate);
    this.idVersion = data === undefined ? '' : this.getDataValue(data.idVersion);
    console.log(data);
  }

  private getDataValue(val) {
    return val ? val : '';
  }
}

export class ContractDetailModel {

  public peopleId: string;
  public carrer: string;
  public branch: string;
  public shortName: string;
  public addressBusiness: string;
  public addressOffice: string;
  public website: string;
  public email: string;
  public phone: string;
  public numberBusiness: string;
  public accountBank: string;
  public signaturer: string;
  public position: string;
  public cardType: string;
  public hinhThucBaoCo01: string;
  public hinhThucThuPhiTheoNgay01: string;
  public hinhThucThuPhiTheoThang01: string;
  public hinhThucBaoCo02: string;
  public hinhThucThuPhiTheoNgay02: string;
  public hinhThucThuPhiTheoThang02: string;
  public hinhThucBaoCo03: string;
  public hinhThucThuPhiTheoNgay03: string;
  public hinhThucThuPhiTheoThang03: string;
  public accountFee01: string;
  public accountFee02: string;
  public accountFee03: string;
  public secure01: string;
  public secure02: string;
  public secure03: string;
  public infoCard: string;
  public khoanDamBao: string;
  public khoanDamBaoMoi: string;
  public kyHanFD: string;
  public otherInfo: string;
  public subTableMerchant: Array<SubTableMerchant>;
  public subTableNoMerchant01: Array<SubTableNoMerchant>;
  public subTableNoMerchant001: Array<SubTableNoMerchant>;
  public subTableNoMerchant02: Array<SubTableNoMerchant>;
  public subTableNoMerchant002: Array<SubTableNoMerchant>;
  public subTableNoMerchant03: Array<SubTableNoMerchant>;
  public subTableNoMerchant003: Array<SubTableNoMerchant>;
  public subTableMerchantID: Array<SubTableMerchantID>;
  public subTableShopifyMerchantID: Array<SubTableShopifyMerchantID>;
  public subTableNonShopifyMerchantID: Array<SubTableShopifyMerchantID>;
  public subTableFee: Array<SubTableFee>;
  public subTablePause: Array<SubTablePause>;
  public cardListArray = [];
  public paygateListArray: [];
  public cardList: string;
  public paygateList: string;
  public stkGiaiKhoanh: string;
  public tenTaiKhoanGiaiKhoanh: string;
  public thoigianGiaiKhoanh: string;
  public tgTamUng: string;
  public tgTamUngSelection: string;
  public ptTamUng: string;
  public ptTamUngMoi: string;
  public otherCard: string;
  public vietNamCard: string;
  public cardTransactionFee: string;
  public monthFee: string;
  public registerFee: string;
  public shopifyFee: string;
  public feeForCard: string;
  public feeForMobile: string;
  public domesticCardToken01: string;
  public internationalCard01: string;
  public domesticCard01: string;
  public domesticCardToken02: string;
  public internationalCard02: string;
  public domesticCard02: string;
  public visaCard: string;
  public masterCard: string;
  public jcbCard: string;
  public americanCard: string;
  public khoanhGiu: string;
  public khoanhGiuValue: string;
  public kyQuy: string;
  public kyQuyValue: string;
  public alertEmailAddress: string;
  public detailEmailAddress: string;
  public internationalCard03: string;
  public internationalCard04: string;
  public domesticCard03: string;
  public domesticCard04: string;
  public changeContent: string;
  public typeFeeInstallment: string;
  public autoFillStandardFee: string;
  public autoFillStandardFeeShopify: string;
  public feeTransDomesticAndApp: string;
  public feeApp: string;
  public feeVietQR: string;
  public feePaymentDomesticAndApp: string;
  public feeTransInternational: string;
  public approveCardTypeDomestic: string;
  public approveCardType01International: string;
  public americanExpress01International: string;
  public approveCardType02International: string;
  public americanExpress02International: string;
  public ngayThanhLy: string;
  public thoiGianApDung: string;



  //Phu luc shopify PL16
  public feeTransDomesticAndAppShopify: string;
  public feeAppShopify: string;
  public feePaymentDomesticAndAppShopify: string;
  public feeTransInternationalShopify: string;
  public approveCardType01InternationalShopify: string;
  public americanExpress01InternationalShopify: string;
  public approveCardType02InternationalShopify: string;
  public americanExpress02InternationalShopify: string;
  public feeServiceShopify: string;

  public insideDomestic: string;
  public outsideDomestic: string;
  public hinhThucThuPhi: string;
  public khoanDamBaoSelection: string;
  public khoanDamBaoInput: string;
  public feeService: string;
  public inputTgTamUngKhac: string;
  public inputTgTamUngKhacKetThucPhien: string;
  public inputHinhThucThuPhiKhac: string;
  public kyQuyType: string;
  public kyQuyAutoFill: string;
  public openByBank: string;
  public inputKyQuyKhac: string;
  public danhXung: string;
  public keepPercent: string;
  public accountNumnber: string;
  public accountNumberOther: string;
  // public templateName: string;
  // Văn bản ủy quyền
  public authorizedPersonName: string;
  public authorizedPersonId: string;
  public authorizedIssuedDate: string;
  public authorizedIssuedBy: string;
  public accountName: string;
  public accountNumber: string;
  public authorizationPeriodFrom: string;
  public authorizationPeriodTo: string;
  public authorizedBirthDate: string;
  public authorizedAddress: string;
  public authorizationNumber: string;

  //ung dung dien tu
  public percentQrMobile: string;
  public percentQrGrab: string;
  public percentQrShopee: string;
  public percentQrZalo: string;
  public percentQrMoMo: string;
  public percentQrOther: string;
  public inforOther: string;
  public percentVietQR: string;


  public percentQrMobileShopify: string;
  public percentQrGrabShopify: string;
  public percentQrShopeeShopify: string;
  public percentQrZaloShopify: string;
  public percentQrMoMoShopify: string;
  public percentQrOtherShopify: string;
  public inforOtherShopify: string;

  public noiDungThayDoi: string;
  public uyQuyen: string;

  //bnpl
  public bnplFee: string;
  public bnplFeeHomeCredit: string;
  public bnplFeeFundiin: string;
  public bnplFeeAmigo: string;
  public bnplFeeKredivo: string;
  public dayApprove: string;

  public version: string;

  //HD14
  public representative: string;
  public permanentAddress: string;
  public issuedBy: string;
  public hoaDonVAT: string;

  //CV DCHD
  public adjustment: any [];
  public contractContentBasis: string;

  constructor(data?: ContractDetailModel) {
    // super();
    //   if (!data) { return; }

    this.peopleId = data === undefined ? '' : this.getDataValue(data.peopleId);
    this.carrer = data === undefined ? '' : this.getDataValue(data.carrer);
    this.branch = data === undefined ? '' : this.getDataValue(data.branch);
    this.shortName = data === undefined ? '' : this.getDataValue(data.shortName);
    this.addressBusiness = data === undefined ? '' : this.getDataValue(data.addressBusiness);
    this.addressOffice = data === undefined ? '' : this.getDataValue(data.addressOffice);
    this.website = data === undefined ? '' : this.getDataValue(data.website);
    this.email = data === undefined ? '' : this.getDataValue(data.email);
    this.phone = data === undefined ? '' : this.getDataValue(data.phone);
    this.numberBusiness = data === undefined ? '' : this.getDataValue(data.numberBusiness);
    this.accountBank = data === undefined ? '' : this.getDataValue(data.accountBank);
    this.signaturer = data === undefined ? '' : this.getDataValue(data.signaturer);
    this.position = data === undefined ? '' : this.getDataValue(data.position);
    this.accountFee01 = data === undefined ? '' : this.getDataValue(data.accountFee01);
    this.accountFee02 = data === undefined ? '' : this.getDataValue(data.accountFee02);
    this.accountFee03 = data === undefined ? '' : this.getDataValue(data.accountFee03);
    this.secure01 = data === undefined ? '' : this.getDataValue(data.secure01);
    this.secure02 = data === undefined ? '' : this.getDataValue(data.secure02);
    this.secure03 = data === undefined ? '' : this.getDataValue(data.secure03);
    this.cardType = data === undefined ? '' : this.getDataValue(data.cardType);
    this.hinhThucBaoCo01 = data === undefined ? '' : this.getDataValue(data.hinhThucBaoCo01);
    this.hinhThucThuPhiTheoNgay01 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoNgay01);
    this.hinhThucThuPhiTheoThang01 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoThang01);
    this.hinhThucBaoCo02 = data === undefined ? '' : this.getDataValue(data.hinhThucBaoCo02);
    this.hinhThucThuPhiTheoNgay02 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoNgay02);
    this.hinhThucThuPhiTheoThang02 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoThang02);
    this.hinhThucBaoCo03 = data === undefined ? '' : this.getDataValue(data.hinhThucBaoCo03);
    this.hinhThucThuPhiTheoNgay03 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoNgay03);
    this.hinhThucThuPhiTheoThang03 = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhiTheoThang03);
    this.infoCard = data === undefined ? '' : this.getDataValue(data.infoCard);
    this.khoanDamBao = data === undefined ? '' : this.getDataValue(data.khoanDamBao);
    this.khoanDamBaoMoi = data === undefined ? '' : this.getDataValue(data.khoanDamBaoMoi);
    this.kyHanFD = data === undefined ? '' : this.getDataValue(data.kyHanFD);
    this.otherInfo = data === undefined ? '' : this.getDataValue(data.otherInfo);
    this.dayApprove = data === undefined ? '' : this.getDataValue(data.dayApprove);
    this.subTableMerchant = data === undefined ? [] : data.subTableMerchant ? data.subTableMerchant : [];
    this.subTableMerchantID = data === undefined ? [] : data.subTableMerchantID ? data.subTableMerchantID : [];
    this.subTableShopifyMerchantID = data === undefined ? [] : data.subTableShopifyMerchantID ? data.subTableShopifyMerchantID : [];
    this.subTableNonShopifyMerchantID = data === undefined ? [] : data.subTableNonShopifyMerchantID ? data.subTableNonShopifyMerchantID : [];
    this.subTablePause = data === undefined ? [] : data.subTablePause ? data.subTablePause : [];
    this.subTableNoMerchant01 = data === undefined ? [] : data.subTableNoMerchant01 ? data.subTableNoMerchant01 : [];
    this.subTableNoMerchant001 = data === undefined ? [] : data.subTableNoMerchant001 ? data.subTableNoMerchant001 : [];
    this.subTableNoMerchant02 = data === undefined ? [] : data.subTableNoMerchant02 ? data.subTableNoMerchant02 : [];
    this.subTableNoMerchant002 = data === undefined ? [] : data.subTableNoMerchant002 ? data.subTableNoMerchant002 : [];
    this.subTableNoMerchant03 = data === undefined ? [] : data.subTableNoMerchant03 ? data.subTableNoMerchant03 : [];
    this.subTableNoMerchant003 = data === undefined ? [] : data.subTableNoMerchant003 ? data.subTableNoMerchant003 : [];
    this.subTableFee = data === undefined ? [] : data.subTableFee ? data.subTableFee : [];
    this.cardListArray = data === undefined ? [] : data.cardListArray ? data.cardListArray : [];
    this.paygateListArray = data === undefined ? [] : data.paygateListArray ? data.paygateListArray : [];
    this.cardList = data === undefined ? '' : this.getDataValue(data.cardList);
    this.paygateList = data === undefined ? '' : this.getDataValue(data.paygateList);
    this.stkGiaiKhoanh = data === undefined ? '' : this.getDataValue(data.stkGiaiKhoanh);
    this.tenTaiKhoanGiaiKhoanh = data === undefined ? '' : this.getDataValue(data.tenTaiKhoanGiaiKhoanh);
    this.thoigianGiaiKhoanh = data === undefined ? '' : this.getDataValue(data.thoigianGiaiKhoanh);
    this.tgTamUng = data === undefined ? '' : this.getDataValue(data.tgTamUng);
    this.tgTamUngSelection = data === undefined ? '' : this.getDataValue(data.tgTamUngSelection);
    this.ptTamUng = data === undefined ? '' : this.getDataValue(data.ptTamUng);
    this.ptTamUngMoi = data === undefined ? '' : this.getDataValue(data.ptTamUngMoi);
    this.otherCard = data === undefined ? '' : this.getDataValue(data.otherCard);
    this.vietNamCard = data === undefined ? '' : this.getDataValue(data.vietNamCard);
    this.cardTransactionFee = data === undefined ? '' : this.getDataValue(data.cardTransactionFee);
    this.monthFee = data === undefined ? '' : this.getDataValue(data.monthFee);
    this.registerFee = data === undefined ? '' : this.getDataValue(data.registerFee);
    this.shopifyFee = data === undefined ? '' : this.getDataValue(data.shopifyFee);
    this.feeForCard = data === undefined ? '' : this.getDataValue(data.feeForCard);
    this.feeForMobile = data === undefined ? '' : this.getDataValue(data.feeForMobile);
    this.domesticCardToken01 = data === undefined ? '' : this.getDataValue(data.domesticCardToken01);
    this.internationalCard01 = data === undefined ? '' : this.getDataValue(data.internationalCard01);
    this.domesticCard01 = data === undefined ? '' : this.getDataValue(data.domesticCard01);
    this.domesticCardToken02 = data === undefined ? '' : this.getDataValue(data.domesticCardToken02);
    this.internationalCard02 = data === undefined ? '' : this.getDataValue(data.internationalCard02);
    this.domesticCard02 = data === undefined ? '' : this.getDataValue(data.domesticCard02);
    this.visaCard = data === undefined ? '' : this.getDataValue(data.visaCard);
    this.masterCard = data === undefined ? '' : this.getDataValue(data.masterCard);
    this.jcbCard = data === undefined ? '' : this.getDataValue(data.jcbCard);
    this.americanCard = data === undefined ? '' : this.getDataValue(data.americanCard);
    this.khoanhGiu = data === undefined ? '' : this.getDataValue(data.khoanhGiu);
    this.khoanhGiuValue = data === undefined ? '' : this.getDataValue(data.khoanhGiuValue);
    this.kyQuy = data === undefined ? '' : this.getDataValue(data.kyQuy);
    this.kyQuyValue = data === undefined ? '' : this.getDataValue(data.kyQuyValue);
    this.alertEmailAddress = data === undefined ? '' : this.getDataValue(data.alertEmailAddress);
    this.detailEmailAddress = data === undefined ? '' : this.getDataValue(data.detailEmailAddress);
    this.internationalCard03 = data === undefined ? '' : this.getDataValue(data.internationalCard03);
    this.internationalCard04 = data === undefined ? '' : this.getDataValue(data.internationalCard04);
    this.domesticCard03 = data === undefined ? '' : this.getDataValue(data.domesticCard03);
    this.domesticCard04 = data === undefined ? '' : this.getDataValue(data.domesticCard04);
    this.changeContent = data === undefined ? '' : this.getDataValue(data.changeContent);
    this.typeFeeInstallment = data === undefined ? '' : this.getDataValue(data.typeFeeInstallment);
    this.feeService = data === undefined ? '' : this.getDataValue(data.feeService);
    this.autoFillStandardFee = data === undefined ? '' : this.getDataValue(data.autoFillStandardFee);
    this.autoFillStandardFeeShopify = data === undefined ? '' : this.getDataValue(data.autoFillStandardFeeShopify);
    this.insideDomestic = data === undefined ? '' : this.getDataValue(data.insideDomestic);
    this.outsideDomestic = data === undefined ? '' : this.getDataValue(data.outsideDomestic);
    this.hinhThucThuPhi = data === undefined ? '' : this.getDataValue(data.hinhThucThuPhi);
    this.khoanDamBaoSelection = data === undefined ? '' : this.getDataValue(data.khoanDamBaoSelection);
    this.khoanDamBaoInput = data === undefined ? '' : this.getDataValue(data.khoanDamBaoInput);
    this.inputHinhThucThuPhiKhac = data === undefined ? '' : this.getDataValue(data.inputHinhThucThuPhiKhac);
    this.inputTgTamUngKhac = data === undefined ? '' : this.getDataValue(data.inputTgTamUngKhac);
    this.inputTgTamUngKhacKetThucPhien = data === undefined ? '' : this.getDataValue(data.inputTgTamUngKhacKetThucPhien);
    this.kyQuyType = data === undefined ? '' : this.getDataValue(data.kyQuyType);
    this.kyQuyAutoFill = data === undefined ? '' : this.getDataValue(data.kyQuyAutoFill);
    this.openByBank = data === undefined ? '' : this.getDataValue(data.openByBank);
    this.inputKyQuyKhac = data === undefined ? '' : this.getDataValue(data.inputKyQuyKhac);
    this.danhXung = data === undefined ? '' : this.getDataValue(data.danhXung);

    this.authorizedPersonName = data === undefined ? '' : this.getDataValue(data.authorizedPersonName);
    this.authorizedPersonId = data === undefined ? '' : this.getDataValue(data.authorizedPersonId);
    this.authorizedIssuedDate = data === undefined ? '' : this.getDataValue(data.authorizedIssuedDate);
    this.authorizedIssuedBy = data === undefined ? '' : this.getDataValue(data.authorizedIssuedBy);
    this.accountName = data === undefined ? '' : this.getDataValue(data.accountName);
    this.accountNumber = data === undefined ? '' : this.getDataValue(data.accountNumber);
    this.authorizationPeriodFrom = data === undefined ? '' : this.getDataValue(data.authorizationPeriodFrom);
    this.authorizationPeriodTo = data === undefined ? '' : this.getDataValue(data.authorizationPeriodTo);
    this.authorizedBirthDate = data === undefined ? '' : this.getDataValue(data.authorizedBirthDate);
    this.authorizedAddress = data === undefined ? '' : this.getDataValue(data.authorizedAddress);
    this.authorizationNumber = data === undefined ? '' : this.getDataValue(data.authorizationNumber);
    this.keepPercent = data === undefined ? '' : this.getDataValue(data.keepPercent);
    this.accountNumberOther = data === undefined ? '' : this.getDataValue(data.accountNumberOther);
    this.accountNumnber = data === undefined ? '' : this.getDataValue(data.accountNumnber);


    //ung dung dien tu
    this.percentQrMobile = data === undefined ? '' : this.getDataValue(data.percentQrMobile);
    this.percentQrGrab = data === undefined ? '' : this.getDataValue(data.percentQrGrab);
    this.percentQrShopee = data === undefined ? '' : this.getDataValue(data.percentQrShopee);
    this.percentQrZalo = data === undefined ? '' : this.getDataValue(data.percentQrZalo);
    this.percentQrMoMo = data === undefined ? '' : this.getDataValue(data.percentQrMoMo);
    this.percentQrOther = data === undefined ? '' : this.getDataValue(data.percentQrOther);
    this.inforOther = data === undefined ? '' : this.getDataValue(data.inforOther);
    this.percentVietQR = data === undefined ? '' : this.getDataValue(data.percentVietQR);

      //ung dung dien tu phu phi shopify PL16
    this.percentQrMobileShopify = data === undefined ? '' : this.getDataValue(data.percentQrMobileShopify);
    this.percentQrGrabShopify = data === undefined ? '' : this.getDataValue(data.percentQrGrabShopify);
    this.percentQrShopeeShopify = data === undefined ? '' : this.getDataValue(data.percentQrShopeeShopify);
    this.percentQrZaloShopify = data === undefined ? '' : this.getDataValue(data.percentQrZaloShopify);
    this.percentQrMoMoShopify = data === undefined ? '' : this.getDataValue(data.percentQrMoMoShopify);
    this.percentQrOtherShopify = data === undefined ? '' : this.getDataValue(data.percentQrOtherShopify);
    this.inforOther = data === undefined ? '' : this.getDataValue(data.inforOther);
    this.feeServiceShopify = data === undefined ? '' : this.getDataValue(data.feeServiceShopify);
    this.feeVietQR = data === undefined ? '' : this.getDataValue(data.feeVietQR);

    //bnpl
    this.bnplFee = data === undefined ? '' : this.getDataValue(data.bnplFee);
    this.bnplFeeHomeCredit = data === undefined ? '' : this.getDataValue(data.bnplFeeHomeCredit);
    this.bnplFeeFundiin = data === undefined ? '' : this.getDataValue(data.bnplFeeFundiin);
    this.bnplFeeAmigo = data === undefined ? '' : this.getDataValue(data.bnplFeeAmigo);
    this.bnplFeeKredivo = data === undefined ? '' : this.getDataValue(data.bnplFeeKredivo);

    this.noiDungThayDoi = data === undefined ? '' : this.getDataValue(data.noiDungThayDoi);
    this.uyQuyen = data === undefined ? '' : this.getDataValue(data.uyQuyen);

    this.version = data === undefined ? '' : this.getDataValue(data.version);

    //HD14
    this.representative = data === undefined ? '' : this.getDataValue(data.representative);
    this.permanentAddress = data === undefined ? '' : this.getDataValue(data.permanentAddress);
    this.issuedBy = data === undefined ? '' : this.getDataValue(data.issuedBy);
    this.hoaDonVAT = data === undefined ? '' : this.getDataValue(data.hoaDonVAT);

    //CV DCHD
    this.adjustment = data === undefined ? [] : data.adjustment ? data.adjustment : [];
    this.contractContentBasis = data === undefined ? '' : this.getDataValue(data.contractContentBasis);

    //BBTLv2
    this.ngayThanhLy = data === undefined ? '' : this.getDataValue(data.ngayThanhLy);
    //PL15v2 phụ lục 3D
    this.thoiGianApDung = data === undefined ? '' : this.getDataValue(data.thoiGianApDung);
  }

  private getDataValue(val) {
    return val ? val : '';
  }
}

export class SubTableMerchant {
  public merchantId: string;
  public merchant: string;
  public accountNumber: string;
  public accountName: string;
  public bank: string;
}

export class SubTableBbnt {
  public merchantId: string;
  public merchant: string;
  public accountNumber: string;
  public accountName: string;
  // public bank: string;
}

export class SubTableMerchantID {
  public merchantId: string;
  public merchantIDS: string;
  public merchant: string;
  public accountNumber: string;
  public accountName: string;
  public bank: string;
}
export class SubTableShopifyMerchantID {
  public merchantId: string;
  public merchantIDS: string;
  public merchant: string;
  // public accountNumber: string;
  // public accountName: string;
  // public bank: string;
}

export class SubTableNoMerchant {
  public dataId: string;
  public accountNumber: string;
  public accountName: string;
  public bank: string;
}

export class SubTablePause {
  public dataId: string;
  public merchantId: string;
  public merchantName: string;
  public partnerNumber: string;
}

export class SubTableFee {
  public id: string;
  public order: string;
  public bankName: string;
  public shortName: string;
  public threeMonths: string;
  public sixMonths: string;
  public nineMonths: string;
  public twelveMonths: string;
  public fifteenMonths: string;
  public eighteenMonths: string;
  public twentyFourMonths: string;
  public thirtySixMonths: string;
  public state: number;
  public feeType: number;
}
