import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Globals } from '@core/global';




@Injectable()
export class StartupService {


    constructor(private http: HttpClient, private global: Globals) { }

    // This is the method you want to call at bootstrap
    // Important: It should return a Promise
    load(): Promise<any> {

        return new Promise((resolve, reject) => {
            // document.cookie = 'auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lvAYYPeEMozWpK37Rjf2aTOc5xNiEmf7ndVbbeTGut8'
            this.http.get('user-profile?domain=core&2=aaaaa',{ withCredentials: true }).subscribe(response => {
                this.http.get('app/data/config.json').subscribe(config => {
                    this.global.config = config;
                    this.global.activeProfile = response;
                    resolve(true);
                });
            });
        });
    }

}