import { RouterModule, Routes } from '@angular/router';
import { PortalRoleGuard } from '@core/routerGuards/portal-guard.service';
import { InitPageComponent } from './initPage/init-page.component';
// import {HomePageComponent} from '@module/HomePage/HomePage.Component';
// import {MerchantManagementComponent} from '@module/MerchantManagement/merchantManagement/merchant_management_component';


const routing: Routes = [
  // ve trang chu
  // {path:'',component:HomePageComponent },
  // redirect to sang trang order search international
  { path: '', component: InitPageComponent },
  {
    path: 'international',
    canActivate: [PortalRoleGuard],
    data: { functionName: 'international_card' },
    loadChildren: () => import('@module/international/international.module').then(m => m.InternationalModule),
  },
  {
    path: 'domestic',
    canActivate: [PortalRoleGuard],
    data: { functionName: 'domestic_card' },
    loadChildren: () => import('@module/domestic/domestic.module').then(m => m.DomesticModule),
  },
  // {
  //   path: 'old-portal',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'poratl1' },
  //   loadChildren: () => import('@module/old-portal/old-portal.module').then(m => m.OldPortalModule),
  // },
  // {
  //   path: 'qr',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'qr_payment' },
  //   loadChildren: () => import('@module/qr/qr.module').then(m => m.QrModule),
  // },
  // {
  //   path: 'black-list-merchant',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'black_list_merchant' },
  //   loadChildren: () => import('@module/BlackListMerchant/black-list-merchant_component.module').then(m => m.BlackListMerchant_componentModule),
  // },
  // {
  //   path: 'merchant-management/msp-merchant',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'msp_merchant' },
  //   loadChildren: () => import('@module/msp-merchant/msp-merchant.module').then(m => m.MspMerchantModule),
  // },
  // {
  //   path: 'merchant-management/merchant-approval',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'merchant_approval' },
  //   loadChildren: () => import('@module/merchant_approval/merchant_approval.module').then(m => m.MerchantApprovalModule),
  // },
  // {
  //   path: 'quick-link',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'quick_link' },
  //   loadChildren: () => import('@module/quick-link/quick-link.module').then(m => m.QuickLinkModule),
  // },
  // {
  //   path: 'onboard-partner',
  //   // canActivate: [PortalRoleGuard],
  //   // data: { functionName: 'quick_link' },
  //   loadChildren: () => import('@module/third-partner/third-partner.module').then(m => m.ThirdPartnerModule),
  // },
  // {
  //   path: 'merchant-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'merchant_management' },
  //   loadChildren: () => import('@module/MerchantManagement/merchant_management_component.module').then(m => m.MerchantManagementModule),
  // },
  // {
  //   path: 'partner-bank-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'partner_management' },
  //   loadChildren: () => import('@module/payment-management/payment-bank-management-component.module').then(m => m.ParnterBankModule),
  // },
  // {
  //   path: 'volume-alert',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'volume_alert' },
  //   loadChildren: () => import('@module/volume-alert/volume-alert.module').then(m => m.VolumeAlertModule),
  // },
  // {
  //   path: 'fee-configuration',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'merchant_management' },
  //   loadChildren: () => import('@module/fee-configuration/fee-configuration.module').then(m => m.FeeConfigurationModule),
  // },
  // {
  //   path: 'system-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'system_management' },
  //   loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  // },
  // {
  //   path: 'document-tracking',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'document_tracking' },
  //   loadChildren: () => import('@module/document-tracking/document-tracking.module').then(m => m.DocumentTrackingModule),
  // },
  // {
  //   path: 'bank-mid-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'bank_mid_management' },
  //   loadChildren: () => import('@module/bank-mid-management/bank-mid-management.module').then(m => m.BankMidManagementModule),
  // },
  // {
  //   path: 'exchange-rate-admin',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'exchange_rate_search' },
  //   loadChildren: () => import('@module/exchange-rate-admin/exchange-rate-admin.module').then(m => m.ExchangeRateAdminModule),

  // },
  // {
  //   path: 'merchant-config-view',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'merchant_config_view' },
  //   loadChildren: () => import('@module/merchant-config-view/merchant-config-view.module').then(m => m.MerchantConfigViewModule),
  // },
  // {
  //   path: 'payment2-advance-config',
  //   loadChildren: () => import('@module/merchant-advance-payment-config/merchant-advance-payment-config.module').then(m => m.MerchantAdvancePaymentConfigModule),
  // },
  // {
  //   path: 'payment2-advance-config-mm',
  //   loadChildren: () => import('@module/merchant-advance-payment-config-mm/merchant-advance-payment-config-mm.module').then(m => m.MerchantAdvancePaymentConfigMMModule),
  // },
  // {
  //   path: 'lead-provider',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'lead_provider' },
  //   loadChildren: () => import('@module/app-lead-provider/lead-provider.module').then(m => m.LeadProviderModule),
  // },
  // {
  //   path: 'paycollect',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'paycollect' },
  //   loadChildren: () => import('@module/paycollect/pay-collect.module').then(m => m.PayCollectModule),
  // }, {
  //   path: 'merchant-transfer',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'merchant_transfer' },
  //   loadChildren: () => import('@module/merchant-transfer/merchant-transfer.module').then(m => m.MerchantTransferModule),
  // },
  // {
  //   path: 'fixed-deposit',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'fixed_deposit' },
  //   loadChildren: () => import('@module/fixed-deposit/fixed-deposit.module').then(m => m.FixedDepositModule),
  // },
  // {
  //   path: 'payment2-transaction-check',
  //   loadChildren: () => import('@module/transaction-check/transaction-check.module').then(m => m.TransactionCheckModule),
  // },
  // {
  //   path: 'ss-trans-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'ss_trans_management' },
  //   loadChildren: () => import('@module/service-support/ss-transaction-management/ss-transaction-management.module').then(m => m.SSTransactionManagementModule),
  // },
  // {
  //   path: 'response-code',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'ss_trans_management' },
  //   loadChildren: () => import('@module/reposonse-code/reponse-code.module').then(m => m.ResponseCodeModule),
  // },
  // {
  //   path: 'statistic',
  //   data: { functionName: 'ss_domestic_card' },
  //   loadChildren: () => import('@module/service-support/ss-transaction-statistic/statistic.module').then(m => m.StatisticModule),
  // },
  // {
  //   path: 'ss-international-trans-report',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'ss_international_trans_management' },
  //   loadChildren: () => import('@module/service-support/ss-internation-transaction-report/ss-internation-trans-report.module').then(m => m.SSInternationTransReportModule),
  // },
  // {
  //   path: 'ss-merchant-trans-report',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'ss_mer_trans_management' }, //sua sau
  //   loadChildren: () => import('@module/service-support/ss-merchant-transaction-management/ss-transaction-management.module').then(m => m.SSMerchantManagementModule),
  // },
  // {
  //   path: 'payment-reconciliation/receipt-request',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'receipt_request' },
  //   loadChildren: () => import('@module/receipt_request/receipt-request.module').then(m => m.ReceiptRequestModule),
  // },
  // {
  //   path: 'payment2-exchange-rate-configuration',
  //   loadChildren: () => import('@module/exchange-rate-config/exchange-rate-config.module').then(m => m.ExchangeRateConfigModule),
  // },
  // {
  //   path: 'payment2-exchange-rate',
  //   loadChildren: () => import('@module/exchange-rate/exchange-rate.module').then(m => m.ExchangeRateModule),
  // },
  // {
  //   path: 'payment2-merchant-account',
  //   loadChildren: () => import('@module/merchant_account/merchant-account.module').then(m => m.MerchantAccountModule),
  // },
  // {
  //   path: 'refund-approval',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'refund_approval' },
  //   loadChildren: () => import('@module/refund-approval/refund-approval.module').then(m => m.RefundApprovalModule),
  // },
  // {
  //   path: 'payout',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'payout_config' },
  //   loadChildren: () => import('@module/payout/payout.module').then(m => m.PayOutModule),
  // },
  // {
  //   path: 'reconciliation',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'cdr' },
  //   loadChildren: () => import('@module/reconciliation/reconciliation.module').then(m => m.ReconciliationModule),
  // },
  // {
  //   path: 'upos/reconciliation',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'cdr_upos' },
  //   loadChildren: () => import('@module/reconciliation-upos/reconciliation-upos.module').then(u => u.ReconciliationUposModule),
  // },
  // {
  //   path: 'accountant',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'accountant_management' },
  //   loadChildren: () => import('@module/accountant/accountant.module').then(m => m.AccountantModule)
  // },
  // {
  //   path: 'billing-settlement',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'pm_billing_settlement' },
  //   loadChildren: () => import('@module/debt-clearance/debt-clearance.module').then(m => m.DebtClearanceModule)
  // },
  // {
  //   path: 'promotion',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'promotion_list' },
  //   loadChildren: () => import('@module/promotion/promotion.module').then(m => m.PromotionModule)
  // },
  // {
  //   path: 'bnpl',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'bnpl_root' },
  //   loadChildren: () => import('@module/bnpl/bnpl.module').then(m => m.BNPLModule)
  // },
  // {
  //   path: 'upos',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'upos' },
  //   loadChildren: () => import('@module/upos/upos.module').then(m => m.UposModule)
  // },
  // {
  //   path: 'vietqr',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'vietqr_payment' },
  //   loadChildren: () => import('@module/vietqr/vietqr.module').then(m => m.VietQRModule),
  // },
  {
    path: 'fdm',
    canActivate: [PortalRoleGuard],
    data: { functionName: 'fdm' },
    loadChildren: () => import('@module/fdm/fdm.module').then(m => m.FdmModule)
  },
  {
    path: 'direct-debit',
    canActivate: [PortalRoleGuard],
    data: { functionName: 'direct_debit' },
    loadChildren: () => import('@module/direct-debit/direct-debit.module').then(m => m.DirectDebitModule)
    // loadChildren: () => import('@module/fdm/fdm.module').then(m => m.FdmModule)
  },
  // {
  //   path: 'payment2-monthly-fee-report-approve',
  //   loadChildren: () => import('@module/payment/payment.module').then(m => m.PaymentModule),
  // },
  // {
  //   path: 'bank-config',
  //   loadChildren: () => import('@module/bank-config/bank-config.module').then(m => m.BankConfigModule)
  // },
  // {
  //   path: 'payment2-monthly-fee-report',
  //   loadChildren: () => import('@module/payment-monthly-fee-report/payment-monthly-fee-report.module').then(m => m.PaymentMonthlyFeeReportModule)
  // },
  // {
  //   path: 'payment2-daily-fee-report',
  //   loadChildren: () => import('@module/payment-daily-fee-report/payment-daily-fee-report.module').then(m => m.PaymentDailyFeeReportModule)
  // },
  // {
  //   path: 'payment2-fee-config',
  //   loadChildren: () => import('@module/payment_reconciliation/payment-reconciliation.module').then(m => m.PaymentReconciliationModule)
  // },
  // {
  //   path: 'payment2-end-user-fee-config-2b',
  //   loadChildren: () => import('@module/payment2_end_user_fee_config/payment2-end-user-fee.module').then(m => m.PaymentEndUserFeeConfigModule)
  // },
  // {
  //   path: 'payment2-fee-config-3b',
  //   loadChildren: () => import('@module/payment2_merchant_fee_config_3b/payment2-merchant-fee-config-3b.module').then(m => m.Payment2MerchantFeeConfig3bModule)
  // },
  // {
  //   path: 'payment2-transaction-accounting',
  //   loadChildren: () => import('@module/payment_merchant_accounting/payment-transaction-accounting.module').then(m => m.PaymentTransactionAccountingModule)
  // },
  // {
  //   path: 'over-limit',
  //   loadChildren: () => import('@module/over-limit/over-limit.module').then(m => m.OverLimitModule)
  // },
  // {
  //   path: 'payment2-bank-fee-config-2b',
  //   loadChildren: () => import('@module/payment2_bank_fee_config/payment2-bank-fee-config.module').then(m => m.Payment2BankFeeConfigModule)
  // },
  // {
  //   path: 'payment2-bank-fee-config-3b',
  //   loadChildren: () => import('@module/payment2_bank_fee_config_3b/payment2-bank-fee-config-3b.module').then(m => m.Payment2BankFeeConfig3BModule)
  // },
  // {
  //   path: 'payment2-op-fee',
  //   loadChildren: () => import('@module/payment2_op_fee/payment2-op-fee.module').then(m => m.Payment2OPFeeModule)
  // },
  // {
  //   path: 'payment2-dashboard',
  //   loadChildren: () => import('@module/payment2_op_fee/payment2-op-fee.module').then(m => m.Payment2OPFeeModule)
  // },
  // {
  //   path: 'payment2-advance',
  //   loadChildren: () => import('@module/advance-payment/create-advance.module').then(m => m.PaymentCreateAdvanceModule)
  // },
  // { //Invoice Tool Payment2 (db118)
  //   path: 'accountant-payment2',
  //   loadChildren: () => import('@module/accountant-payment2/accountant-payment2.module').then(m => m.AccountantModuleP2)
  // }, {
  //   path: 'ss-dispute-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'shared_dispute_management_menu', department: 'SERVICE_SUPPORT' },
  //   loadChildren: () => import('@module/service-support/ss-dispute-management/ss-dispute-management.module').then(m => m.SSDisputeManagementModule)
  // }, {
  //   path: 'risk-dispute-management',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'shared_dispute_management_menu' , department: 'RISK_MANAGEMENT' },
  //   loadChildren: () => import('@module/service-support/ss-dispute-management/ss-dispute-management.module').then(m => m.SSDisputeManagementModule)
  // }, {
  //   path: 'ss-user-search',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'ss_do_user_search' , department: 'SERVICE_SUPPORT' }, //tam the phan quyen sua sau
  //   loadChildren: () => import('@module/service-support/ss-user-search/ss-user-search.module').then(m => m.SSUserSearchModule)
  // },
  // {
  //   path: 'inquiry',
  //   // canActivate:[PortalRoleGuard],
  //   // data:{ functionName: 'inquiry'},
  //   loadChildren:()=> import('@module/inquiry/inquiry.module').then(m => m.InquiryModule)
  // },
  //   //ipn
  //   {
  //     path: 'system-management/ipn-view',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'ipn_config_view' },
  //     loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  //   },
  //   {
  //     path: 'system-management/ipn-approve',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'ipn_config_approve' },
  //     loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  //   },
  //   {
  //     path: 'system-management/ipn-resend',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'ipn_resend' },
  //     loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  //   },
  //   //shopify
  //   {
  //     path: 'system-management/shopify-config',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'shopify_detail' },
  //     loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  //   },
  //   {
  //     path: 'system-management/shopify-approval',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'shopify_approval' },
  //     loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule),
  //   },
  //   {
  //     path: 'shopify',
  //     canActivate: [PortalRoleGuard],
  //     data: { functionName: 'shopify' },
  //     loadChildren: () => import('@module/shopify/shopify.module').then(m => m.ShopifyModule),
  //   },
  // //payment auto confirm refund
  // {
  //   path: 'payment2-auto-confirm-refund',
  //   loadChildren: () => import('@module/auto-confirm-refund/auto-confirm-refund.module').then(m => m.PaymentAutoConfirmRefundModule)
  // },
  //   {
  //     path:'payment2-partner-fee-configs',
  //     loadChildren: ()=> import('@module/payment2_partner_fee_config/partner-fee-config/payment2-partner-fee-config.module').then(m=> m.Payment2PartnerFeeConfigModule)
  //   },
  // {
  //   path: 'website-onepay',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'website_onepay' },
  //   loadChildren: () => import('@module/website-onepay/website-onepay.module').then(m => m.WebsiteOnePayModule),
  // },
  // //hot switch qr
  // {
  //   path: 'system-management/hot-switch-qr',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'hot_switch_qr' },
  //   loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule)
  // },
  // {
  //   path: 'system-management/hot-switch-atm',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'hot_switch_atm' },
  //   loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule)
  // },
  // {
  //   path: 'system-management/history-default',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'history_default' },
  //   loadChildren: () => import('@module/system-management/system-management.module').then(m => m.SystemManagementModule)
  // },
  // {
  //   path: 'e-wallet',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'e_wallet' },
  //   loadChildren: () => import('@module/e-wallet/ewallet.module').then(m => m.EWalletModule)
  // },
  // {
  //   path: 'ss-direct-debit-registration',
  //   canActivate: [PortalRoleGuard],
  //   data: { functionName: 'direct-debit-do-search-registration', department: 'SERVICE_SUPPORT' },
  //   loadChildren: () => import('@module/service-support/ss-direct-debit-registration/ss-direct-debit-registration.module').then(m => m.SSDirectDebitRegistrationModule)
  // },
  // {
  //   path: 'payment2-holiday-config',
  //   loadChildren: () => import('@module/holiday/holiday.module').then(m=> m.HolidayModule)
  // },
  // {
  //   path: 'payment2-msp-bank-fee-config',
  //   loadChildren: () => import('@module/bank-fee-config/bank-fee-config.module').then(m=> m.BankFeeConfigModule)
  // },
  // {
  //   path: 'payment2-payment-bank-fee-config',
  //   loadChildren: () => import('@module/payment2_bank_fee_config_auto/payment2_bank_fee_config_auto.module').then(m=> m.Payment2BankFeeConfigAutoModule)
  // },
  // {
  //   path:'payment2-transaction-volume-target',
  //   loadChildren: () => import('@module/transaction_volume_target/transaction_volume_target.module').then(m=> m.TransactionVolumeTargetModule)
  // },
  // {
  //   path:'payment2-invoice-individual',
  //   loadChildren: () => import('@module/invoice-individual/invoice-individual.module').then(m=> m.InvoiceIndividualModule)
  // },
  // {
  //   path:'payment2-invoice-individual-mr',
  //   loadChildren: () => import('@module/invoice-individual-mr/invoice-individual-mr.module').then(m=> m.InvoiceIndividualMrModule)
  // },
  // {
  //   path:'payment2-referral-partner-configure',
  //   loadChildren: () => import('@module/referral-partner/referral-partner.module').then(m=> m.ReferralPartnerModule)

  // },
  {
    path:'notification-system',
    loadChildren: () => import('@module/notification-system/notification-system.module').then(m=> m.NotificationModule)
  },
  {
    path: 'service-supports/installments',
    loadChildren: () => import('@module/service-support/ss-installment/ss-installment.module').then(m => m.SsInstallmentModule)
  },
  {
    path:'contract-template',
    loadChildren: () => import('@module/contract-template/contract-template.module').then(m=> m.ContractTemplateModule)
  }

];

export const appRouter = RouterModule.forRoot(routing, { relativeLinkResolution: 'legacy' });
