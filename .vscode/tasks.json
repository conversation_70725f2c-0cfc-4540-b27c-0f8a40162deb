{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "install",
            "type": "shell",
            "command": "rm -fR node_modules && npm install --legacy-peer-deps --ignore-scripts",
            "group": "build"
        },
        {
            "label": "prod iportal-angular (sync DC 76.4)",
            "type": "shell",
            "command": "./sync_prod",
            "group": "build"
        },
        {
            "label": "build-dev",
            "type": "shell",
            "command": "ng build --base-href=/iportal/ --watch --poll=5000 --output-path=dist/iportal",
            "group": "build",
            "problemMatcher": [
                "$msCompile"
            ]
        },
    ]
}